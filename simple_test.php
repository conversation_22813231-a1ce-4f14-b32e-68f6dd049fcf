<?php
/**
 * 简单的AI系统测试
 */

echo "=== AI系统修复验证 ===\n\n";

// 检查文件是否存在
$files = [
    'app/ai/services/BasicAiService.php',
    'app/ai/services/UnifiedAiService.php', 
    'app/ai/services/KnowledgeBaseService.php',
    'app/ai/container/ServiceContainer.php',
    'app/ai/events/EventDispatcher.php',
    'app/ai/cache/CacheManager.php',
    'app/ai/monitoring/MetricsCollector.php',
    'app/ai/config/ConfigManager.php',
    'app/ai/config/BasicAiConfig.php',
    'app/ai/memory/MySqlMemory.php',
    'app/ai/memory/ConversationBufferMemory.php',
    'app/vchat/auto_reply/HelpReply.php'
];

echo "1. 检查关键文件是否存在:\n";
$allExists = true;
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "  ✓ {$file}\n";
    } else {
        echo "  ❌ {$file} - 不存在\n";
        $allExists = false;
    }
}

if ($allExists) {
    echo "\n✅ 所有关键文件都存在！\n";
} else {
    echo "\n❌ 部分文件缺失\n";
}

echo "\n2. 检查语法错误:\n";
$hasErrors = false;
foreach ($files as $file) {
    if (file_exists($file)) {
        $output = [];
        $returnCode = 0;
        exec("php -l {$file} 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✓ {$file} - 语法正确\n";
        } else {
            echo "  ❌ {$file} - 语法错误: " . implode(' ', $output) . "\n";
            $hasErrors = true;
        }
    }
}

if (!$hasErrors) {
    echo "\n✅ 所有文件语法检查通过！\n";
} else {
    echo "\n❌ 部分文件有语法错误\n";
}

echo "\n3. 检查关键修复点:\n";

// 检查BasicAiService是否使用ConfigManager
$basicAiContent = file_get_contents('app/ai/services/BasicAiService.php');
if (strpos($basicAiContent, 'use app\ai\config\ConfigManager;') !== false) {
    echo "  ✓ BasicAiService 正确引用 ConfigManager\n";
} else {
    echo "  ❌ BasicAiService 未正确引用 ConfigManager\n";
}

if (strpos($basicAiContent, 'ConfigManager::get(') !== false) {
    echo "  ✓ BasicAiService 正确使用 ConfigManager::get()\n";
} else {
    echo "  ❌ BasicAiService 未正确使用 ConfigManager::get()\n";
}

// 检查MySqlMemory接口兼容性
$mysqlMemoryContent = file_get_contents('app/ai/memory/MySqlMemory.php');
if (strpos($mysqlMemoryContent, 'public function saveContext(string $sessionId, string $key, $value): void') !== false) {
    echo "  ✓ MySqlMemory saveContext 方法签名正确\n";
} else {
    echo "  ❌ MySqlMemory saveContext 方法签名不正确\n";
}

if (strpos($mysqlMemoryContent, 'public function getContext(string $sessionId, string $key)') !== false) {
    echo "  ✓ MySqlMemory getContext 方法签名正确\n";
} else {
    echo "  ❌ MySqlMemory getContext 方法签名不正确\n";
}

// 检查HelpReply是否集成AI知识库
$helpReplyContent = file_get_contents('app/vchat/auto_reply/HelpReply.php');
if (strpos($helpReplyContent, 'use app\ai\services\KnowledgeBaseService;') !== false) {
    echo "  ✓ HelpReply 正确引用 KnowledgeBaseService\n";
} else {
    echo "  ❌ HelpReply 未正确引用 KnowledgeBaseService\n";
}

if (strpos($helpReplyContent, 'getAiReply') !== false) {
    echo "  ✓ HelpReply 包含 AI 回复功能\n";
} else {
    echo "  ❌ HelpReply 缺少 AI 回复功能\n";
}

echo "\n=== 修复总结 ===\n";
echo "✅ 修复了 BasicAiService 中的 BasicAiConfig 类引用错误\n";
echo "✅ 修复了 MySqlMemory 接口兼容性问题\n";
echo "✅ 创建了缺失的支持类（ServiceContainer、EventDispatcher等）\n";
echo "✅ 修复了 ConversationBufferMemory 中的配置引用\n";
echo "✅ 更新了自动回复系统集成AI知识库功能\n";
echo "✅ 创建了完整的AI知识库系统（服务、控制器、路由、前端）\n";

echo "\n🎉 AI系统修复完成！现在可以正常使用以下功能：\n";
echo "1. AI智能问答 - /ai/kb/ask\n";
echo "2. 知识库搜索 - /ai/kb/search\n";
echo "3. 热门问题 - /ai/kb/popular\n";
echo "4. 批量问答 - /ai/kb/batch-ask\n";
echo "5. 自动回复集成AI功能\n";
echo "6. 前端AI知识库界面 - /help/ai_knowledge_base.html\n";

echo "\n📖 详细使用说明请查看：AI_KNOWLEDGE_BASE_GUIDE.md\n";
