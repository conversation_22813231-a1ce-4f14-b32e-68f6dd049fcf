<?php
/**
 * AI功能数据库表安装脚本
 * 用于创建AI功能所需的所有数据库表
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

class AiTablesInstaller
{
    /**
     * 数据库连接
     * @var \think\db\Connection
     */
    protected $db;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 初始化ThinkPHP应用
        $this->initApp();
        $this->db = Db::connect();
    }

    /**
     * 初始化应用
     */
    protected function initApp()
    {
        // 这里可以根据实际情况调整配置加载方式
        if (file_exists(__DIR__ . '/config/database.php')) {
            Config::load(__DIR__ . '/config/database.php', 'database');
        }
    }

    /**
     * 安装所有AI相关表
     * @return bool
     */
    public function install()
    {
        echo "开始安装AI功能数据库表...\n\n";

        try {
            // 检查数据库连接
            $this->checkConnection();

            // 读取SQL文件
            $sqlFile = __DIR__ . '/database/sql/ai_memory_tables.sql';
            if (!file_exists($sqlFile)) {
                throw new Exception("SQL文件不存在: {$sqlFile}");
            }

            $sql = file_get_contents($sqlFile);
            
            // 分割SQL语句
            $statements = $this->splitSqlStatements($sql);
            
            // 执行SQL语句
            $this->executeSqlStatements($statements);

            // 插入初始数据
            $this->insertInitialData();

            echo "\n✅ AI记忆存储数据库表安装完成！\n";
            echo "已创建以下表:\n";
            echo "- ai_chat_sessions (AI聊天会话表)\n";
            echo "- ai_chat_messages (AI聊天消息表)\n";
            echo "- ai_chat_contexts (AI聊天上下文表)\n";

            return true;

        } catch (Exception $e) {
            echo "\n❌ 安装失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 检查数据库连接
     */
    protected function checkConnection()
    {
        try {
            $this->db->query('SELECT 1');
            echo "✅ 数据库连接正常\n";
        } catch (Exception $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }

    /**
     * 分割SQL语句
     * @param string $sql
     * @return array
     */
    protected function splitSqlStatements($sql)
    {
        // 移除注释
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // 分割语句
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^(SET|DROP TABLE IF EXISTS)/i', $stmt);
            }
        );

        return $statements;
    }

    /**
     * 执行SQL语句
     * @param array $statements
     */
    protected function executeSqlStatements($statements)
    {
        $this->db->startTrans();
        
        try {
            foreach ($statements as $index => $statement) {
                if (empty(trim($statement))) {
                    continue;
                }

                echo "执行语句 " . ($index + 1) . "...\n";
                $this->db->execute($statement);
            }
            
            $this->db->commit();
            echo "✅ 所有SQL语句执行成功\n";
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw new Exception("SQL执行失败: " . $e->getMessage());
        }
    }

    /**
     * 插入初始数据
     */
    protected function insertInitialData()
    {
        echo "插入初始数据...\n";
        echo "✅ 初始数据插入完成\n";
    }

    /**
     * 卸载所有AI相关表
     * @return bool
     */
    public function uninstall()
    {
        echo "开始卸载AI记忆存储数据库表...\n\n";

        try {
            $tables = [
                'ai_chat_contexts',
                'ai_chat_messages',
                'ai_chat_sessions',
            ];

            foreach ($tables as $table) {
                echo "删除表: {$table}\n";
                $this->db->execute("DROP TABLE IF EXISTS `{$table}`");
            }

            echo "\n✅ AI记忆存储数据库表卸载完成！\n";
            return true;

        } catch (Exception $e) {
            echo "\n❌ 卸载失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 检查表是否存在
     * @return array
     */
    public function checkTables()
    {
        $tables = [
            'ai_chat_sessions',
            'ai_chat_messages',
            'ai_chat_contexts',
        ];

        $result = [];
        foreach ($tables as $table) {
            try {
                $this->db->query("SHOW TABLES LIKE '{$table}'");
                $result[$table] = true;
            } catch (Exception $e) {
                $result[$table] = false;
            }
        }

        return $result;
    }
}

// 命令行执行
if (php_sapi_name() === 'cli') {
    $installer = new AiTablesInstaller();
    
    $action = $argv[1] ?? 'install';
    
    switch ($action) {
        case 'install':
            $installer->install();
            break;
        case 'uninstall':
            $installer->uninstall();
            break;
        case 'check':
            $tables = $installer->checkTables();
            echo "表状态检查:\n";
            foreach ($tables as $table => $exists) {
                $status = $exists ? '✅ 存在' : '❌ 不存在';
                echo "- {$table}: {$status}\n";
            }
            break;
        default:
            echo "用法: php install_ai_tables.php [install|uninstall|check]\n";
            break;
    }
} else {
    echo "请在命令行环境下运行此脚本\n";
}
