<?php
/**
 * AI系统修复脚本
 * 修复类引用错误和优化旧版本代码
 */

require_once __DIR__ . '/vendor/autoload.php';

class AiSystemFixer
{
    /**
     * 需要检查的目录
     */
    protected $directories = [
        'app/ai',
        'app/vchat/auto_reply',
        'app/im/controller'
    ];

    /**
     * 类映射关系
     */
    protected $classMapping = [
        'app\ai\config\BasicAiConfig' => 'app\ai\config\ConfigManager',
        'app\ai\services\BasicAiService' => 'app\ai\services\UnifiedAiService',
    ];

    /**
     * 需要创建的缺失类
     */
    protected $missingClasses = [
        'app\ai\container\ServiceContainer',
        'app\ai\events\EventDispatcher',
        'app\ai\cache\CacheManager',
        'app\ai\monitoring\MetricsCollector',
        'app\ai\providers\AiServiceProvider',
        'app\ai\events\AiRequestStartedEvent',
        'app\ai\events\AiRequestCompletedEvent',
        'app\ai\events\AiRequestFailedEvent',
    ];

    /**
     * 运行修复
     */
    public function run()
    {
        echo "开始修复AI系统...\n\n";

        try {
            // 1. 检查和修复类引用
            $this->fixClassReferences();

            // 2. 创建缺失的类
            $this->createMissingClasses();

            // 3. 验证修复结果
            $this->validateFixes();

            echo "\n✅ AI系统修复完成！\n";

        } catch (Exception $e) {
            echo "\n❌ 修复失败: " . $e->getMessage() . "\n";
            return false;
        }

        return true;
    }

    /**
     * 修复类引用
     */
    protected function fixClassReferences()
    {
        echo "1. 检查和修复类引用...\n";

        foreach ($this->directories as $dir) {
            if (!is_dir($dir)) {
                continue;
            }

            $this->scanDirectory($dir);
        }
    }

    /**
     * 扫描目录
     */
    protected function scanDirectory($dir)
    {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'php') {
                $this->checkFile($file->getPathname());
            }
        }
    }

    /**
     * 检查文件
     */
    protected function checkFile($filePath)
    {
        $content = file_get_contents($filePath);
        $originalContent = $content;
        $modified = false;

        // 检查use语句
        if (preg_match_all('/use\s+([^;]+);/', $content, $matches)) {
            foreach ($matches[1] as $index => $className) {
                $className = trim($className);
                
                // 检查是否是需要映射的类
                if (isset($this->classMapping[$className])) {
                    $newClassName = $this->classMapping[$className];
                    $content = str_replace($matches[0][$index], "use {$newClassName};", $content);
                    $modified = true;
                    echo "  修复: {$filePath} - {$className} -> {$newClassName}\n";
                }

                // 检查类是否存在
                if (!class_exists($className) && !interface_exists($className) && !trait_exists($className)) {
                    echo "  警告: {$filePath} - 类不存在: {$className}\n";
                }
            }
        }

        // 保存修改
        if ($modified) {
            file_put_contents($filePath, $content);
        }
    }

    /**
     * 创建缺失的类
     */
    protected function createMissingClasses()
    {
        echo "\n2. 创建缺失的类...\n";

        // 创建ServiceContainer
        $this->createServiceContainer();

        // 创建EventDispatcher
        $this->createEventDispatcher();

        // 创建CacheManager
        $this->createCacheManager();

        // 创建MetricsCollector
        $this->createMetricsCollector();

        // 创建事件类
        $this->createEventClasses();

        // 创建ServiceProvider
        $this->createServiceProvider();
    }

    /**
     * 创建ServiceContainer
     */
    protected function createServiceContainer()
    {
        $dir = 'app/ai/container';
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        $content = '<?php

namespace app\ai\container;

/**
 * 简化的服务容器
 */
class ServiceContainer
{
    protected static $instance;
    protected $bindings = [];
    protected $instances = [];

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function bind($abstract, $concrete = null)
    {
        $this->bindings[$abstract] = $concrete ?: $abstract;
    }

    public function make($abstract)
    {
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        // 简化的实例化逻辑
        switch ($abstract) {
            case "ai.basic":
                return $this->instances[$abstract] = new \app\ai\services\BasicAiService();
            case "ai.langchain":
                return $this->instances[$abstract] = new \app\ai\services\LangChainService();
            case "ai.unified":
                return $this->instances[$abstract] = new \app\ai\services\UnifiedAiService();
            case "ai.events":
                return $this->instances[$abstract] = new \app\ai\events\EventDispatcher();
            case "ai.cache":
                return $this->instances[$abstract] = new \app\ai\cache\CacheManager();
            case "ai.metrics":
                return $this->instances[$abstract] = new \app\ai\monitoring\MetricsCollector();
            default:
                throw new \Exception("Service not found: " . $abstract);
        }
    }
}';

        file_put_contents($dir . '/ServiceContainer.php', $content);
        echo "  创建: ServiceContainer\n";
    }

    /**
     * 创建EventDispatcher
     */
    protected function createEventDispatcher()
    {
        $dir = 'app/ai/events';
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        $content = '<?php

namespace app\ai\events;

/**
 * 简化的事件调度器
 */
class EventDispatcher
{
    protected $listeners = [];

    public function listen($event, $listener)
    {
        $this->listeners[$event][] = $listener;
    }

    public function dispatch($event, $data = [])
    {
        if (isset($this->listeners[$event])) {
            foreach ($this->listeners[$event] as $listener) {
                call_user_func($listener, $data);
            }
        }
    }
}';

        file_put_contents($dir . '/EventDispatcher.php', $content);
        echo "  创建: EventDispatcher\n";
    }

    /**
     * 创建CacheManager
     */
    protected function createCacheManager()
    {
        $dir = 'app/ai/cache';
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        $content = '<?php

namespace app\ai\cache;

use think\facade\Cache;

/**
 * AI缓存管理器
 */
class CacheManager
{
    protected $prefix = "ai_cache_";

    public function getCachedAiResponse($provider, $model, $messages, $options = [])
    {
        $key = $this->generateCacheKey($provider, $model, $messages, $options);
        return Cache::get($key);
    }

    public function cacheAiResponse($provider, $model, $messages, $options, $response, $ttl = null)
    {
        $key = $this->generateCacheKey($provider, $model, $messages, $options);
        $ttl = $ttl ?: 3600; // 默认1小时
        return Cache::set($key, $response, $ttl);
    }

    public function recordCacheOperation($operation, $hit = null)
    {
        // 记录缓存操作统计
    }

    public function getStats()
    {
        return [
            "hits" => 0,
            "misses" => 0,
            "hit_rate" => 0
        ];
    }

    protected function generateCacheKey($provider, $model, $messages, $options)
    {
        $data = [
            "provider" => $provider,
            "model" => $model,
            "messages" => $messages,
            "options" => $options
        ];
        return $this->prefix . md5(json_encode($data));
    }
}';

        file_put_contents($dir . '/CacheManager.php', $content);
        echo "  创建: CacheManager\n";
    }

    /**
     * 创建MetricsCollector
     */
    protected function createMetricsCollector()
    {
        $dir = 'app/ai/monitoring';
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        $content = '<?php

namespace app\ai\monitoring;

/**
 * 指标收集器
 */
class MetricsCollector
{
    protected $metrics = [];

    public function recordAiRequest($provider, $model, $duration, $success)
    {
        $this->metrics[] = [
            "provider" => $provider,
            "model" => $model,
            "duration" => $duration,
            "success" => $success,
            "timestamp" => time()
        ];
    }

    public function recordCacheOperation($operation, $hit = null)
    {
        // 记录缓存操作
    }

    public function getSummary()
    {
        return [
            "total_requests" => count($this->metrics),
            "avg_duration" => $this->calculateAverageDuration(),
            "success_rate" => $this->calculateSuccessRate()
        ];
    }

    protected function calculateAverageDuration()
    {
        if (empty($this->metrics)) {
            return 0;
        }
        
        $total = array_sum(array_column($this->metrics, "duration"));
        return $total / count($this->metrics);
    }

    protected function calculateSuccessRate()
    {
        if (empty($this->metrics)) {
            return 0;
        }
        
        $successful = array_filter($this->metrics, function($m) {
            return $m["success"];
        });
        
        return count($successful) / count($this->metrics);
    }
}';

        file_put_contents($dir . '/MetricsCollector.php', $content);
        echo "  创建: MetricsCollector\n";
    }

    /**
     * 创建事件类
     */
    protected function createEventClasses()
    {
        $dir = 'app/ai/events';
        
        $events = [
            'AiRequestStartedEvent' => [
                'provider', 'model', 'options', 'requestId'
            ],
            'AiRequestCompletedEvent' => [
                'provider', 'model', 'response', 'requestId', 'duration'
            ],
            'AiRequestFailedEvent' => [
                'provider', 'model', 'error', 'requestId', 'duration'
            ]
        ];

        foreach ($events as $className => $properties) {
            $content = "<?php

namespace app\\ai\\events;

class {$className}
{
    protected \$data;

    public function __construct(" . implode(', ', array_map(function($p) { return "\${$p}"; }, $properties)) . ")
    {
        \$this->data = [
            " . implode(",\n            ", array_map(function($p) { return "'{$p}' => \${$p}"; }, $properties)) . "
        ];
    }

    public function getEventName()
    {
        return strtolower(str_replace('Event', '', '{$className}'));
    }

    public function getEventData()
    {
        return \$this->data;
    }
}";

            file_put_contents($dir . "/{$className}.php", $content);
            echo "  创建: {$className}\n";
        }
    }

    /**
     * 创建ServiceProvider
     */
    protected function createServiceProvider()
    {
        $dir = 'app/ai/providers';
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        $content = '<?php

namespace app\ai\providers;

/**
 * AI服务提供者
 */
class AiServiceProvider
{
    public function register($container)
    {
        // 注册服务
    }

    public function boot()
    {
        // 启动服务
    }
}';

        file_put_contents($dir . '/AiServiceProvider.php', $content);
        echo "  创建: AiServiceProvider\n";
    }

    /**
     * 验证修复结果
     */
    protected function validateFixes()
    {
        echo "\n3. 验证修复结果...\n";

        $errors = [];

        // 检查关键类是否存在
        $keyClasses = [
            'app\ai\services\BasicAiService',
            'app\ai\services\UnifiedAiService',
            'app\ai\services\KnowledgeBaseService',
            'app\ai\container\ServiceContainer',
            'app\ai\events\EventDispatcher',
        ];

        foreach ($keyClasses as $class) {
            if (!class_exists($class)) {
                $errors[] = "类不存在: {$class}";
            } else {
                echo "  ✓ {$class}\n";
            }
        }

        if (!empty($errors)) {
            throw new Exception("验证失败:\n" . implode("\n", $errors));
        }

        echo "  ✓ 所有关键类验证通过\n";
    }
}

// 运行修复
if (php_sapi_name() === 'cli') {
    $fixer = new AiSystemFixer();
    $fixer->run();
} else {
    echo "请在命令行环境下运行此脚本\n";
    echo "使用方法: php fix_ai_system.php\n";
}
