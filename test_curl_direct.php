<?php

echo "🔧 直接测试DeepSeek API连接\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// DeepSeek API配置
$apiKey = '***********************************';
$url = 'https://api.deepseek.com/chat/completions';

// 请求数据
$data = [
    'model' => 'deepseek-chat',
    'messages' => [
        ['role' => 'user', 'content' => 'Hello, please respond with just "OK"']
    ],
    'max_tokens' => 10,
    'temperature' => 0.1
];

echo "📡 测试信息:\n";
echo "  🌐 URL: {$url}\n";
echo "  🔑 API密钥: " . substr($apiKey, 0, 10) . "...\n";
echo "  📤 请求数据: " . json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

echo "🚀 开始测试...\n";

$startTime = microtime(true);

// 初始化cURL
$ch = curl_init();

if (!$ch) {
    echo "❌ cURL初始化失败\n";
    exit(1);
}

// 设置cURL选项
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ],
    CURLOPT_TIMEOUT => 15,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_VERBOSE => true,
    CURLOPT_STDERR => fopen('php://temp', 'w+')
]);

echo "📡 发送请求...\n";

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
$curlErrno = curl_errno($ch);
$info = curl_getinfo($ch);

$endTime = microtime(true);
$duration = round(($endTime - $startTime) * 1000, 2);

// 获取详细错误信息
rewind(curl_getinfo($ch, CURLINFO_PRIVATE) ?: fopen('php://temp', 'w+'));
$verboseLog = stream_get_contents(curl_getinfo($ch, CURLINFO_PRIVATE) ?: fopen('php://temp', 'w+'));

curl_close($ch);

echo "\n📊 请求结果:\n";
echo "  ⏱️  执行时间: {$duration}ms\n";
echo "  🔢 HTTP状态码: {$httpCode}\n";
echo "  🔧 cURL错误码: {$curlErrno}\n";
echo "  📝 cURL错误信息: " . ($curlError ?: '无') . "\n";

if ($curlErrno !== 0) {
    echo "\n❌ cURL错误详情:\n";
    echo "  错误码: {$curlErrno}\n";
    echo "  错误信息: {$curlError}\n";
    
    // 常见错误码解释
    $errorExplanations = [
        6 => '无法解析主机名',
        7 => '无法连接到服务器',
        28 => '操作超时',
        35 => 'SSL连接错误',
        51 => 'SSL证书验证失败',
        52 => '服务器返回空响应',
        56 => '接收数据失败'
    ];
    
    if (isset($errorExplanations[$curlErrno])) {
        echo "  解释: " . $errorExplanations[$curlErrno] . "\n";
    }
    
    echo "\n💡 建议解决方案:\n";
    if ($curlErrno === 6) {
        echo "  - 检查网络连接\n";
        echo "  - 检查DNS设置\n";
        echo "  - 尝试使用其他DNS服务器\n";
    } elseif ($curlErrno === 7) {
        echo "  - 检查防火墙设置\n";
        echo "  - 检查代理配置\n";
        echo "  - 确认服务器地址正确\n";
    } elseif ($curlErrno === 28) {
        echo "  - 增加超时时间\n";
        echo "  - 检查网络速度\n";
        echo "  - 稍后重试\n";
    } elseif (in_array($curlErrno, [35, 51])) {
        echo "  - 禁用SSL验证（仅测试用）\n";
        echo "  - 更新CA证书\n";
        echo "  - 检查系统时间\n";
    }
} else {
    echo "\n✅ cURL执行成功\n";
}

if ($httpCode > 0) {
    echo "\n📥 HTTP响应:\n";
    echo "  状态码: {$httpCode}\n";
    
    if ($httpCode === 200) {
        echo "  ✅ 请求成功\n";
        
        if ($response) {
            $responseData = json_decode($response, true);
            if ($responseData) {
                echo "  📝 响应内容: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n";
                
                if (isset($responseData['choices'][0]['message']['content'])) {
                    echo "  💬 AI回复: " . $responseData['choices'][0]['message']['content'] . "\n";
                }
            } else {
                echo "  ❌ JSON解析失败\n";
                echo "  原始响应: " . substr($response, 0, 200) . "...\n";
            }
        } else {
            echo "  ❌ 响应为空\n";
        }
    } else {
        echo "  ❌ HTTP错误\n";
        echo "  响应内容: " . substr($response, 0, 500) . "\n";
        
        // HTTP错误码解释
        $httpErrors = [
            400 => '请求格式错误',
            401 => 'API密钥无效或未提供',
            403 => '权限不足或账户余额不足',
            404 => 'API端点不存在',
            429 => '请求频率超限',
            500 => '服务器内部错误',
            502 => '网关错误',
            503 => '服务不可用'
        ];
        
        if (isset($httpErrors[$httpCode])) {
            echo "  解释: " . $httpErrors[$httpCode] . "\n";
        }
    }
}

echo "\n📋 连接信息:\n";
echo "  总时间: " . round($info['total_time'] * 1000, 2) . "ms\n";
echo "  DNS解析时间: " . round($info['namelookup_time'] * 1000, 2) . "ms\n";
echo "  连接时间: " . round($info['connect_time'] * 1000, 2) . "ms\n";
echo "  SSL握手时间: " . round(($info['appconnect_time'] ?? 0) * 1000, 2) . "ms\n";
echo "  传输开始时间: " . round($info['starttransfer_time'] * 1000, 2) . "ms\n";
echo "  下载大小: " . ($info['size_download'] ?? 0) . " bytes\n";

if ($verboseLog) {
    echo "\n🔍 详细日志:\n";
    echo $verboseLog . "\n";
}

echo "\n🎯 测试完成!\n";
echo "=" . str_repeat("=", 40) . "\n";
