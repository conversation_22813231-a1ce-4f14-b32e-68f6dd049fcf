-- 等级礼活动表
CREATE TABLE `level_gift` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '活动名称',
  `level_id` int(11) unsigned NOT NULL COMMENT '会员等级ID',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '礼品库存',
  `receive_limit` int(11) NOT NULL DEFAULT '0' COMMENT '领取限制(0表示不限制)',
  `description` text COMMENT '活动描述',
  `goods_ids` varchar(255) DEFAULT '' COMMENT '关联商品ID，多个用逗号分隔',
  `coupon_ids` varchar(255) DEFAULT '' COMMENT '关联优惠券ID，多个用逗号分隔',
  `start_time` int(11) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(11) DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=未开始，1=进行中，2=已结束',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级礼活动表';

-- 等级礼领取记录表
CREATE TABLE `level_gift_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `gift_id` int(11) unsigned NOT NULL COMMENT '活动ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL DEFAULT '' COMMENT '会员姓名',
  `user_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '会员手机',
  `level_id` int(11) unsigned NOT NULL COMMENT '会员等级ID',
  `receive_time` int(11) DEFAULT NULL COMMENT '领取时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=已领取，1=已使用',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_gift_id` (`gift_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级礼领取记录表';