-- 优惠券表
CREATE TABLE `coupon` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优惠券类型：1=固定金额，2=百分比折扣',
  `value` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠券面值',
  `min_order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低订单金额',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '优惠券库存',
  `per_limit` int(11) NOT NULL DEFAULT '1' COMMENT '每人限领数量',
  `description` varchar(255) DEFAULT '' COMMENT '优惠券描述',
  `start_time` int(11) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(11) DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券表';

-- 用户优惠券表
CREATE TABLE `user_coupon` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户优惠券ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `coupon_id` int(11) unsigned NOT NULL COMMENT '优惠券ID',
  `order_id` int(11) unsigned DEFAULT NULL COMMENT '使用订单ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=未使用，2=已使用，3=已过期',
  `use_time` int(11) DEFAULT NULL COMMENT '使用时间',
  `create_time` int(11) DEFAULT NULL COMMENT '领取时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- 订单表添加优惠券相关字段
ALTER TABLE `order` 
ADD COLUMN `coupon_id` int(11) unsigned DEFAULT NULL COMMENT '优惠券ID' AFTER `total_amount`,
ADD COLUMN `coupon_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券抵扣金额' AFTER `coupon_id`,
ADD COLUMN `pay_amount` decimal(10,2) DEFAULT '0.00' COMMENT '实际支付金额' AFTER `coupon_amount`;