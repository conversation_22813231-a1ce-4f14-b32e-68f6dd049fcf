-- 附件表
CREATE TABLE `attachment` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '文件名',
  `path` varchar(255) NOT NULL DEFAULT '' COMMENT '文件路径',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '文件URL',
  `size` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小(字节)',
  `mime_type` varchar(100) NOT NULL DEFAULT '' COMMENT '文件MIME类型',
  `extension` varchar(50) NOT NULL DEFAULT '' COMMENT '文件扩展名',
  `storage` varchar(50) NOT NULL DEFAULT 'local' COMMENT '存储位置(local:本地, oss:云存储)',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上传用户ID',
  `user_type` varchar(50) NOT NULL DEFAULT '' COMMENT '用户类型(admin:管理员, user:用户)',
  `download_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '下载次数',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:正常)',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`, `user_type`),
  KEY `idx_path` (`path`),
  KEY `idx_created_at` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附件表';