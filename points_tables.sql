-- 积分商品表
CREATE TABLE `points_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '商品名称',
  `points` int(11) NOT NULL DEFAULT '0' COMMENT '所需积分',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '商品库存',
  `exchange_limit` int(11) NOT NULL DEFAULT '0' COMMENT '兑换限制(0表示不限制)',
  `description` text COMMENT '商品描述',
  `image` varchar(255) DEFAULT '' COMMENT '商品图片',
  `start_time` int(11) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(11) DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=下架，1=上架',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_points` (`points`),
  KEY `idx_stock` (`stock`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商品表';

-- 积分订单表
CREATE TABLE `points_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `goods_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
  `points` int(11) NOT NULL DEFAULT '0' COMMENT '消耗积分',
  `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '兑换数量',
  `receiver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `receiver_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '收货人电话',
  `receiver_address` varchar(255) NOT NULL DEFAULT '' COMMENT '收货地址',
  `express_company` varchar(50) DEFAULT '' COMMENT '快递公司',
  `express_no` varchar(50) DEFAULT '' COMMENT '快递单号',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '订单状态：pending=待发货，delivered=已发货，completed=已完成',
  `remark` varchar(255) DEFAULT '' COMMENT '备注信息',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分订单表';