-- 拼团活动表
CREATE TABLE `group_buy` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '拼团名称',
  `type` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '拼团类型：normal=普通拼团，newUser=新人团',
  `group_size` int(11) NOT NULL DEFAULT '2' COMMENT '成团人数',
  `goods_count` int(11) NOT NULL DEFAULT '0' COMMENT '参与商品数',
  `goods_ids` varchar(255) DEFAULT '' COMMENT '关联商品ID，多个用逗号分隔',
  `start_time` int(11) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(11) DEFAULT NULL COMMENT '结束时间',
  `success_count` int(11) NOT NULL DEFAULT '0' COMMENT '已成团数',
  `ongoing_count` int(11) NOT NULL DEFAULT '0' COMMENT '进行中团数',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=未开始，1=进行中，2=已结束',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拼团活动表';

-- 拼团记录表
CREATE TABLE `group_buy_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `group_buy_id` int(11) unsigned NOT NULL COMMENT '活动ID',
  `group_no` varchar(32) NOT NULL DEFAULT '' COMMENT '团编号',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `goods_title` varchar(255) NOT NULL DEFAULT '' COMMENT '商品标题',
  `is_leader` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否团长：0=否，1=是',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=待成团，1=已成团，2=已失败',
  `join_time` int(11) DEFAULT NULL COMMENT '参团时间',
  `success_time` int(11) DEFAULT NULL COMMENT '成团时间',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_group_buy_id` (`group_buy_id`),
  KEY `idx_group_no` (`group_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拼团记录表';