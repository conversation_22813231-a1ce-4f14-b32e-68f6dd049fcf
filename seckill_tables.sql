-- 限时秒杀表
CREATE TABLE `seckill` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '秒杀活动ID',
  `title` varchar(255) NOT NULL COMMENT '活动名称',
  `description` text COMMENT '活动说明',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '活动状态：0未开始 1进行中 2已结束',
  `start_time` int(11) NOT NULL COMMENT '开始时间',
  `end_time` int(11) NOT NULL COMMENT '结束时间',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `seckill_price` decimal(10,2) NOT NULL COMMENT '秒杀价格',
  `stock_quantity` int(11) NOT NULL COMMENT '秒杀商品数量',
  `limit_quantity` int(11) NOT NULL DEFAULT '1' COMMENT '每人限购数量',
  `sold_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '已售数量',
  `createtime` int(11) NOT NULL COMMENT '创建时间',
  `updatetime` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods` (`goods_id`),
  KEY `idx_time` (`start_time`, `end_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='限时秒杀活动表';