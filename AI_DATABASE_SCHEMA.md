# AI记忆存储数据库表结构说明

## 概述

本文档描述了AI记忆存储功能所需的MySQL数据库表结构，基于MySqlMemory类的实际需求创建。只包含AI功能实际使用的3张表。

## 表结构概览

### AI记忆存储表（MySqlMemory类使用）

#### 1. ai_chat_sessions (AI聊天会话表)
存储AI聊天会话的基本信息和状态。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int(11) | 主键ID |
| session_id | varchar(255) | 会话唯一标识 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |
| last_activity | timestamp | 最后活跃时间 |
| message_count | int(11) | 消息数量 |
| status | tinyint(1) | 会话状态：1-活跃，0-已结束 |

**索引:**
- session_id (唯一)
- last_activity
- status

#### 2. ai_chat_messages (AI聊天消息表)
存储AI聊天的具体消息内容和相关信息。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int(11) | 主键ID |
| session_id | varchar(255) | 会话ID |
| message_type | enum | 消息类型：input-用户输入，output-AI输出 |
| content | text | 消息内容 |
| metadata | json | 消息元数据 |
| created_at | timestamp | 创建时间 |
| token_count | int(11) | Token数量 |

**索引:**
- session_id
- created_at
- message_type

#### 3. ai_chat_contexts (AI聊天上下文表)
存储AI聊天的上下文信息，用于保持对话连贯性。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int(11) | 主键ID |
| session_id | varchar(255) | 会话ID |
| context_key | varchar(255) | 上下文键名 |
| context_value | json | 上下文值 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

**索引:**
- session_context: session_id, context_key (唯一)
- session_id



## 安装说明

### 1. 使用SQL文件安装
```bash
mysql -u username -p database_name < database/sql/ai_memory_tables.sql
```

### 2. 使用安装脚本
```bash
php install_ai_tables.php install
```

### 3. 检查安装状态
```bash
php install_ai_tables.php check
```

### 4. 卸载表
```bash
php install_ai_tables.php uninstall
```

## 维护建议

### 1. 定期清理
- ai_chat_messages: 建议保留30天
- ai_chat_sessions: 建议保留90天
- ai_chat_contexts: 建议保留30天

### 2. 索引优化
- 根据查询模式调整索引
- 定期分析慢查询
- 考虑分区表（按日期）

### 3. 备份策略
- 会话数据建议实时备份
- 消息数据每日备份
- 上下文数据可按需备份

## 注意事项

1. **字符集**: 所有表使用utf8mb4字符集，支持emoji等特殊字符
2. **JSON字段**: 需要MySQL 5.7+版本支持
3. **时间戳**: 使用timestamp类型，自动维护时间
4. **外键**: 暂未设置外键约束，便于数据清理
5. **分区**: 大数据量表建议考虑按日期分区
