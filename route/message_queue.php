<?php
/**
 * 消息队列管理路由配置
 * 提供HTTP接口来管理和发送消息到队列
 */

use think\facade\Route;

// 消息队列管理路由组
Route::group('admin/message-queue', function () {
    
    // 发送Socket.IO事件消息到队列
    Route::post('send-socketio', 'app\\im\\controller\\customer\\MessageQueueController@sendSocketIoMessage');
    
    // 发送常规消息到队列
    Route::post('send-message', 'app\\im\\controller\\customer\\MessageQueueController@sendMessage');
    
    // 获取消息状态
    Route::get('status/:messageId', 'app\\im\\controller\\customer\\MessageQueueController@getMessageStatus');
    
    // 获取消息详情
    Route::get('detail/:messageId', 'app\\im\\controller\\customer\\MessageQueueController@getMessageDetail');
    
    // 获取队列状态
    Route::get('queue-status', 'app\\im\\controller\\customer\\MessageQueueController@getQueueStatus');
    
    // 重试失败的消息
    Route::post('retry/:messageId', 'app\\im\\controller\\customer\\MessageQueueController@retryFailedMessage');
    
    // 清理过期消息
    Route::post('clean-expired', 'app\\im\\controller\\customer\\MessageQueueController@cleanExpiredMessages');
    
    // 批量发送通知消息
    Route::post('batch-notify', 'app\\im\\controller\\customer\\MessageQueueController@batchNotify');
    
})->middleware(['admin_auth']); // 添加管理员认证中间件

// 如果需要公开的API接口（不需要认证）
Route::group('api/message-queue', function () {
    
    // 获取队列状态（公开接口，用于监控）
    Route::get('status', 'app\\im\\controller\\customer\\MessageQueueController@getQueueStatus');
    
});