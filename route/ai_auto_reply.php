<?php

use think\facade\Route;

/**
 * AI自动回复路由配置
 */

// AI自动回复管理路由组
Route::group('ai-auto-reply', function () {
    
    // 基础管理接口
    Route::get('status', 'app\vchat\controller\AiAutoReplyController@status');
    Route::get('health', 'app\vchat\controller\AiAutoReplyController@health');
    Route::get('config', 'app\vchat\controller\AiAutoReplyController@getConfig');
    Route::post('config', 'app\vchat\controller\AiAutoReplyController@updateConfig');
    Route::get('stats', 'app\vchat\controller\AiAutoReplyController@getStats');
    
    // 测试接口
    Route::post('test', 'app\vchat\controller\AiAutoReplyController@test');
    Route::post('batch-test', 'app\vchat\controller\AiAutoReplyController@batchTest');
    
    // 会话管理
    Route::delete('session/:from_id', 'app\vchat\controller\AiAutoReplyController@clearSession');
    
})->middleware([
    // 这里可以添加认证中间件
    // 'auth',
    // 'permission'
]);

// 兼容性路由（如果需要）
Route::group('vchat/ai-reply', function () {
    
    Route::get('status', 'app\vchat\controller\AiAutoReplyController@status');
    Route::post('test', 'app\vchat\controller\AiAutoReplyController@test');
    Route::get('health', 'app\vchat\controller\AiAutoReplyController@health');
    
});

// API文档路由
Route::get('ai-auto-reply/docs', function () {
    $docs = [
        'title' => 'AI自动回复API文档',
        'version' => '1.0.0',
        'description' => 'AI智能自动回复系统接口文档',
        'endpoints' => [
            'management' => [
                'GET /ai-auto-reply/status' => '获取服务状态',
                'GET /ai-auto-reply/health' => '健康检查',
                'GET /ai-auto-reply/config' => '获取配置',
                'POST /ai-auto-reply/config' => '更新配置',
                'GET /ai-auto-reply/stats' => '获取统计信息',
            ],
            'testing' => [
                'POST /ai-auto-reply/test' => '单条消息测试',
                'POST /ai-auto-reply/batch-test' => '批量消息测试',
            ],
            'session' => [
                'DELETE /ai-auto-reply/session/{from_id}' => '清除用户会话',
            ]
        ],
        'examples' => [
            'test_request' => [
                'url' => 'POST /ai-auto-reply/test',
                'body' => [
                    'content' => '@AI 你好，请问今天天气怎么样？',
                    'from_id' => 12345
                ]
            ],
            'batch_test_request' => [
                'url' => 'POST /ai-auto-reply/batch-test',
                'body' => [
                    'messages' => [
                        '@AI 你好',
                        '智能助手：请帮我查询订单',
                        '机器人：今天天气如何？'
                    ]
                ]
            ]
        ],
        'configuration' => [
            'environment_variables' => [
                'AI_AUTO_REPLY_ENABLED' => '是否启用AI自动回复 (true/false)',
                'AI_AUTO_REPLY_PROVIDER' => 'AI服务提供商 (deepseek/openai/claude)',
                'AI_AUTO_REPLY_MODEL' => 'AI模型名称',
                'AI_AUTO_REPLY_TEMPERATURE' => '回复创造性 (0.0-2.0)',
                'AI_AUTO_REPLY_MAX_TOKENS' => '最大回复长度',
                'AI_AUTO_REPLY_TIMEOUT' => '请求超时时间（秒）',
                'AI_AUTO_REPLY_CACHE_TTL' => '缓存时间（秒）',
                'AI_AUTO_REPLY_USE_MEMORY' => '是否使用记忆功能 (true/false)',
                'AI_AUTO_REPLY_ALWAYS' => '是否对所有消息回复 (true/false)',
                'AI_AUTO_REPLY_MIN_INTERVAL' => '最小回复间隔（秒）',
            ],
            'trigger_keywords' => [
                '@AI', '@ai', '智能助手', '机器人', 'AI助手',
                '人工智能', 'chatbot', 'bot', '小助手'
            ],
            'trigger_patterns' => [
                '/^AI[：:]/i',
                '/^智能助手[：:]/i',
                '/^机器人[：:]/i',
                '/^小助手[：:]/i'
            ]
        ],
        'features' => [
            '智能触发检测',
            '上下文记忆',
            '频率限制',
            '内容过滤',
            '缓存优化',
            '批量测试',
            '实时监控',
            '配置管理',
            '会话管理',
            '健康检查'
        ],
        'response_format' => [
            'success' => [
                'code' => 200,
                'message' => '请求成功',
                'data' => '响应数据'
            ],
            'error' => [
                'code' => '错误码',
                'message' => '错误信息',
                'data' => null
            ]
        ]
    ];
    
    return json([
        'code' => 200,
        'message' => '获取成功',
        'data' => $docs
    ]);
});
