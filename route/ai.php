<?php

use think\facade\Route;

/**
 * AI服务路由配置
 */

// AI聊天相关路由
Route::group('ai', function () {
    // 发送聊天消息
    Route::post('chat', 'AiController@chat');
    
    // 流式聊天
    Route::post('stream-chat', 'AiController@streamChat');
    
    // 获取支持的AI服务提供商列表
    Route::get('providers', 'AiController@getProviders');
    
    // 获取可用模型列表
    Route::get('models', 'AiController@getModels');
    
    // 检查服务健康状态
    Route::get('health', 'AiController@health');
    
})->middleware([
    // 可以在这里添加中间件，如：
    // 'auth',        // 身份验证
    // 'throttle',    // 限流
    // 'cors',        // 跨域
]);

// 管理员专用路由（需要管理员权限）
Route::group('admin/ai', function () {
    // 清除AI服务缓存
    Route::post('clear-cache', 'admin\AiAdminController@clearCache');
    
    // 更新AI服务配置
    Route::post('config', 'admin\AiAdminController@updateConfig');
    
    // 获取AI服务统计信息
    Route::get('stats', 'admin\AiAdminController@getStats');
    
    // 测试AI服务连接
    Route::post('test-connection', 'admin\AiAdminController@testConnection');
    
})->middleware([
    // 'auth',
    // 'admin',  // 管理员权限验证
]);