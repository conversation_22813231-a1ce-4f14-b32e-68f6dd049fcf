<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 分组路由
Route::group('admin', function () {
    Route::import(['admin/route/route']);
});

Route::group('api', function () {
    Route::import(['api/route/route']);
});

// AI自动回复路由
Route::import(['ai_auto_reply']);

// AI知识库路由
Route::import(['ai/routes/knowledge_base']);

// 默认首页路由
Route::get('/', 'Index/index')->name('home');

// WebSocket 路由
Route::get('customer_service/', 'CustomerServiceChatController/index');
Route::get('user_service/', 'UserServiceChatController/index');
//Route::get('user_service/', 'UserServiceChatController/index')->middleware(\app\api\middleware\CommonConfigInit::class);

// 静态文件路由
Route::get('static/:path', function (string $path) {
    $filename = public_path() . $path;
    return new \think\swoole\response\File($filename);
})->pattern(['path' => '.*\.(js|css|png|jpg|gif|ico|svg)$']);

Route::miss(function() {
    return '404 Not Found!';
});