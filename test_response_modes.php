<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\ai\services\KnowledgeBaseService;

echo "🎨 知识库回复模式测试\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// 测试问题
$testQuestion = "如何进行功能测试？";

echo "📋 测试问题: {$testQuestion}\n\n";

try {
    // 模式1：简洁模式（推荐）
    echo "🎯 模式1: 简洁模式 (simple) - 推荐\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    $kb1 = new KnowledgeBaseService([
        'mode' => 'simple',
        'include_fallback_message' => false,
        'max_content_length' => 200
    ]);
    
    echo "配置: 简洁直接，无多余格式，不显示客服提示\n";
    echo "特点: 直接回答问题，简洁明了\n\n";
    
    // 这里模拟回复，因为需要真实的知识库数据
    echo "模拟回复:\n";
    echo "功能测试步骤：\n";
    echo "1. 登录系统后台\n";
    echo "2. 进入\"系统设置\"页面\n";
    echo "3. 点击\"功能测试\"按钮\n";
    echo "4. 选择要测试的功能模块\n";
    echo "5. 查看测试结果报告\n\n";
    
    // 模式2：正式模式
    echo "🎯 模式2: 正式模式 (formal)\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    $kb2 = new KnowledgeBaseService([
        'mode' => 'formal',
        'include_fallback_message' => true,
        'include_suggestions' => true
    ]);
    
    echo "配置: 礼貌正式，适合客服场景\n";
    echo "特点: 使用正式语言，包含客服建议\n\n";
    
    echo "模拟回复:\n";
    echo "您好，关于功能测试的问题，我为您提供以下操作指导：\n\n";
    echo "根据系统帮助文档，功能测试的具体步骤如下：\n";
    echo "1. 请先登录系统管理后台\n";
    echo "2. 在左侧菜单中找到\"系统设置\"\n";
    echo "3. 点击\"功能测试\"选项\n";
    echo "4. 根据需要选择相应的测试模块\n";
    echo "5. 系统会自动生成测试报告\n\n";
    echo "如果您在操作过程中遇到任何问题，建议联系人工客服为您详细解答。\n\n";
    
    // 模式3：详细模式
    echo "🎯 模式3: 详细模式 (detailed)\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    $kb3 = new KnowledgeBaseService([
        'mode' => 'detailed',
        'max_content_length' => 600
    ]);
    
    echo "配置: 详细全面，包含背景信息和建议\n";
    echo "特点: 提供完整的解决方案和相关信息\n\n";
    
    echo "模拟回复:\n";
    echo "【功能测试完整指南】\n\n";
    echo "功能测试是确保系统正常运行的重要环节。以下是详细的操作说明：\n\n";
    echo "一、测试前准备：\n";
    echo "- 确保您有管理员权限\n";
    echo "- 建议在非高峰时段进行测试\n";
    echo "- 准备测试数据和预期结果\n\n";
    echo "二、具体操作步骤：\n";
    echo "1. 登录系统管理后台（使用管理员账号）\n";
    echo "2. 导航至\"系统设置\" > \"功能测试\"\n";
    echo "3. 选择测试类型（基础功能/高级功能/全面测试）\n";
    echo "4. 配置测试参数（如测试数据量、测试时长等）\n";
    echo "5. 启动测试并等待完成\n";
    echo "6. 下载并查看测试报告\n\n";
    echo "三、注意事项：\n";
    echo "- 测试过程中请勿进行其他系统操作\n";
    echo "- 如发现异常，请及时停止测试\n";
    echo "- 建议定期进行功能测试以确保系统稳定\n\n";
    echo "如需进一步技术支持，请联系我们的技术团队。\n\n";
    
    // 配置对比表
    echo "📊 配置对比表\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $configs = [
        'simple' => $kb1->getResponseConfig(),
        'formal' => $kb2->getResponseConfig(),
        'detailed' => $kb3->getResponseConfig()
    ];
    
    echo sprintf("%-12s %-10s %-15s %-15s\n", "模式", "长度限制", "客服提示", "建议");
    echo str_repeat("-", 50) . "\n";
    
    foreach ($configs as $mode => $config) {
        echo sprintf("%-12s %-10s %-15s %-15s\n", 
            $mode,
            $config['max_content_length'],
            $config['include_fallback_message'] ? '是' : '否',
            $config['include_suggestions'] ? '是' : '否'
        );
    }
    
    echo "\n💡 使用建议:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo "🎯 简洁模式 (simple):\n";
    echo "   - 适用于: 大多数日常问题\n";
    echo "   - 优点: 直接高效，用户体验好\n";
    echo "   - 推荐: ⭐⭐⭐⭐⭐\n\n";
    
    echo "🎯 正式模式 (formal):\n";
    echo "   - 适用于: 正式客服场景\n";
    echo "   - 优点: 礼貌专业，符合客服规范\n";
    echo "   - 推荐: ⭐⭐⭐\n\n";
    
    echo "🎯 详细模式 (detailed):\n";
    echo "   - 适用于: 复杂技术问题\n";
    echo "   - 优点: 信息全面，解决方案完整\n";
    echo "   - 推荐: ⭐⭐⭐⭐\n\n";
    
    echo "🔧 实际应用代码示例:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    echo "// 推荐配置 - 简洁模式\n";
    echo "\$kb = new KnowledgeBaseService([\n";
    echo "    'mode' => 'simple',\n";
    echo "    'include_fallback_message' => false,\n";
    echo "    'max_content_length' => 300\n";
    echo "]);\n\n";
    
    echo "// 动态切换模式\n";
    echo "\$kb->setResponseMode('simple');  // 简洁回复\n";
    echo "\$result = \$kb->ask('简单问题');\n\n";
    
    echo "\$kb->setResponseMode('detailed'); // 详细回复\n";
    echo "\$result = \$kb->ask('复杂问题');\n\n";
    
    echo "🎉 测试完成!\n";
    echo "现在你可以根据需要选择合适的回复模式，\n";
    echo "彻底解决固定格式和多余提示的问题。\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误:\n";
    echo "错误信息: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n📖 详细配置说明请查看: KNOWLEDGE_BASE_RESPONSE_CONFIG.md\n";
