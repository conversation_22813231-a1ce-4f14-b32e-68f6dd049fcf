-- =====================================================
-- AI记忆存储MySQL表结构
-- 基于MySqlMemory类的实际需求创建
-- 只包含AI功能实际使用的3张表
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- AI记忆存储表（MySqlMemory类使用）
-- =====================================================

-- AI聊天会话表
DROP TABLE IF EXISTS `ai_chat_sessions`;
CREATE TABLE `ai_chat_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(255) NOT NULL COMMENT '会话唯一标识',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_activity` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
  `message_count` int(11) DEFAULT 0 COMMENT '消息数量',
  `status` tinyint(1) DEFAULT 1 COMMENT '会话状态：1-活跃，0-已结束',
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `last_activity` (`last_activity`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI聊天会话表';

-- AI聊天消息表
DROP TABLE IF EXISTS `ai_chat_messages`;
CREATE TABLE `ai_chat_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(255) NOT NULL COMMENT '会话ID',
  `message_type` enum('input','output') NOT NULL COMMENT '消息类型：input-用户输入，output-AI输出',
  `content` text NOT NULL COMMENT '消息内容',
  `metadata` json DEFAULT NULL COMMENT '消息元数据（JSON格式）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `token_count` int(11) DEFAULT 0 COMMENT 'Token数量',
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `created_at` (`created_at`),
  KEY `message_type` (`message_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI聊天消息表';

-- AI聊天上下文表
DROP TABLE IF EXISTS `ai_chat_contexts`;
CREATE TABLE `ai_chat_contexts` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(255) NOT NULL COMMENT '会话ID',
  `context_key` varchar(255) NOT NULL COMMENT '上下文键名',
  `context_value` json NOT NULL COMMENT '上下文值（JSON格式）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_context` (`session_id`, `context_key`),
  KEY `session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI聊天上下文表';

SET FOREIGN_KEY_CHECKS = 1;
