<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateTaskTables extends Migrator
{
    /**
     * 创建数据表
     */
    public function up()
    {
        // 创建定时任务表
        $this->table('sys_task')
            ->addColumn('name', 'string', ['limit' => 50, 'null' => false, 'comment' => '任务名称'])
            ->addColumn('command', 'string', ['limit' => 255, 'null' => false, 'comment' => '执行命令'])
            ->addColumn('rule', 'string', ['limit' => 50, 'null' => false, 'comment' => '执行规则'])
            ->addColumn('status', 'boolean', ['null' => false, 'default' => 1, 'comment' => '状态 0:禁用 1:启用'])
            ->addColumn('concurrent', 'boolean', ['null' => false, 'default' => 0, 'comment' => '是否允许并发 0:否 1:是'])
            ->addColumn('retry_times', 'integer', ['null' => false, 'default' => 0, 'comment' => '重试次数'])
            ->addColumn('retry_interval', 'integer', ['null' => false, 'default' => 0, 'comment' => '重试间隔(秒)'])
            ->addColumn('notify_type', 'text', ['null' => true, 'comment' => '通知方式 ["email","webhook","sms"]'])
            ->addColumn('notify_target', 'text', ['null' => true, 'comment' => '通知对象'])
            ->addColumn('description', 'string', ['limit' => 255, 'null' => true, 'comment' => '任务描述'])
            ->addColumn('createtime', 'integer', ['null' => false, 'default' => 0, 'comment' => '创建时间'])
            ->addColumn('updatetime', 'integer', ['null' => false, 'default' => 0, 'comment' => '更新时间'])
            ->addIndex(['status'])
            ->create();

        // 创建任务执行记录表
        $this->table('sys_task_record')
            ->addColumn('task_id', 'integer', ['null' => false, 'comment' => '任务ID'])
            ->addColumn('status', 'integer', ['null' => false, 'default' => 0, 'comment' => '执行状态 0:执行中 1:成功 2:失败'])
            ->addColumn('result', 'text', ['null' => true, 'comment' => '执行结果'])
            ->addColumn('start_time', 'integer', ['null' => false, 'default' => 0, 'comment' => '开始时间'])
            ->addColumn('end_time', 'integer', ['null' => false, 'default' => 0, 'comment' => '结束时间'])
            ->addIndex(['task_id'])
            ->addIndex(['status'])
            ->addIndex(['start_time'])
            ->create();
    }

    /**
     * 删除数据表
     */
    public function down()
    {
        $this->dropSchema('ad_task_record');
        $this->dropSchema('ad_task');
    }
}