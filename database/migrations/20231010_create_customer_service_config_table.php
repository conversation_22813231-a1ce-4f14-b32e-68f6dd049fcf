<?php

use think\facade\Db;
use think\migration\Migrator;

class CreateCustomerServiceConfigTable extends Migrator
{
    public function up()
    {
        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `ad_customer_service_config` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `reception_mode` ENUM('round_robin', 'group', 'intelligent') NOT NULL DEFAULT 'round_robin' COMMENT '接待方式',
    `max_concurrent_customers` INT(11) NOT NULL DEFAULT 10 COMMENT '最大接待数量',
    `auto_assign_wait_time` INT(11) NOT NULL DEFAULT 30 COMMENT '自动分配等待时间（秒）',
    `session_timeout` INT(11) NOT NULL DEFAULT 10 COMMENT '会话超时时间（分钟）',
    `work_time_mode` ENUM('all_day', 'specified') NOT NULL DEFAULT 'all_day' COMMENT '工作时间模式',
    `non_work_time_message` TEXT COMMENT '非工作时间提示',
    `enable_non_work_time_message` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '启用留言功能',
    `max_queue_size` INT(11) NOT NULL DEFAULT 50 COMMENT '最大排队人数',
    `queue_message` TEXT COMMENT '排队提示信息',
    `createtime` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `updatetime` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;

        Db::execute($sql);
    }

    public function down()
    {
        Db::execute('DROP TABLE IF EXISTS `ad_customer_service_config`');
    }
}