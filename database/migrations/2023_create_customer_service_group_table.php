<?php
use think\migration\Migrator;
use think\migration\db\Column;

class CreateCustomerServiceGroupTable extends Migrator
{
    public function up()
    {
        $table = $this->table('customer_service_group', ['comment' => '客服分组表']);
        $table->addColumn('name', 'string', ['limit' => 50, 'comment' => '分组名称'])
            ->addColumn('description', 'string', ['limit' => 255, 'null' => true, 'comment' => '分组描述'])
            ->addColumn('sort', 'integer', ['default' => 0, 'comment' => '排序权重'])
            ->addColumn('enabled', 'boolean', ['default' => true, 'comment' => '是否启用'])
            ->addColumn('max_services', 'integer', ['default' => 0, 'comment' => '最大客服数量'])
            ->addColumn('createtime', 'datetime', ['comment' => '创建时间'])
            ->addColumn('updatetime', 'datetime', ['comment' => '更新时间'])
            ->addColumn('deletetime', 'datetime', ['null' => true, 'comment' => '删除时间'])
            ->addIndex(['name'], ['unique' => true])
            ->create();
    }

    public function down()
    {
        $this->dropSchema('customer_service_group');
    }
}