-- Token安全升级迁移脚本
-- 创建时间: 2024-12-01
-- 描述: 为Token系统添加安全功能支持

-- 1. 创建Token黑名单表
CREATE TABLE IF NOT EXISTS `sys_token_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `token` varchar(255) NOT NULL COMMENT 'Token值',
  `token_type` enum('access','refresh') NOT NULL DEFAULT 'access' COMMENT 'Token类型',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `reason` varchar(500) DEFAULT NULL COMMENT '加入黑名单原因',
  `created_by` varchar(100) DEFAULT NULL COMMENT '操作者',
  `expire_time` int(11) NOT NULL COMMENT '过期时间',
  `createtime` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_token` (`token`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_create_time` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Token黑名单表';

-- 2. 创建Token操作日志表
CREATE TABLE IF NOT EXISTS `sys_token_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `token` varchar(255) DEFAULT NULL COMMENT 'Token值（部分）',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `result` enum('success','failed','error') NOT NULL COMMENT '操作结果',
  `error_code` varchar(50) DEFAULT NULL COMMENT '错误代码',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `extra_data` json DEFAULT NULL COMMENT '额外数据',
  `createtime` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_token` (`token`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_create_time` (`createtime`),
  KEY `idx_result` (`result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Token操作日志表';

-- 3. 创建安全事件表
CREATE TABLE IF NOT EXISTS `sys_security_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型',
  `severity` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium' COMMENT '严重程度',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `description` text NOT NULL COMMENT '事件描述',
  `details` json DEFAULT NULL COMMENT '事件详情',
  `status` enum('new','investigating','resolved','ignored') NOT NULL DEFAULT 'new' COMMENT '处理状态',
  `handled_by` varchar(100) DEFAULT NULL COMMENT '处理人',
  `handled_time` int(11) DEFAULT NULL COMMENT '处理时间',
  `createtime` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全事件表';

-- 4. 为现有Token表添加安全相关字段（如果不存在）
ALTER TABLE `qi_user_token` 
ADD COLUMN IF NOT EXISTS `jwt_id` varchar(64) DEFAULT NULL COMMENT 'JWT ID' AFTER `token`,
ADD COLUMN IF NOT EXISTS `ip_address` varchar(45) DEFAULT NULL COMMENT '创建时IP地址' AFTER `expiretime`,
ADD COLUMN IF NOT EXISTS `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理' AFTER `ip_address`,
ADD COLUMN IF NOT EXISTS `last_used_time` int(11) DEFAULT NULL COMMENT '最后使用时间' AFTER `user_agent`,
ADD COLUMN IF NOT EXISTS `use_count` int(11) DEFAULT 0 COMMENT '使用次数' AFTER `last_used_time`;

-- 为新字段添加索引
ALTER TABLE `qi_user_token` 
ADD INDEX IF NOT EXISTS `idx_jwt_id` (`jwt_id`),
ADD INDEX IF NOT EXISTS `idx_ip_address` (`ip_address`),
ADD INDEX IF NOT EXISTS `idx_last_used_time` (`last_used_time`);

-- 5. 为系统管理员Token表添加相同字段（如果存在）
ALTER TABLE `sys_admin_token` 
ADD COLUMN IF NOT EXISTS `jwt_id` varchar(64) DEFAULT NULL COMMENT 'JWT ID' AFTER `token`,
ADD COLUMN IF NOT EXISTS `ip_address` varchar(45) DEFAULT NULL COMMENT '创建时IP地址' AFTER `expiretime`,
ADD COLUMN IF NOT EXISTS `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理' AFTER `ip_address`,
ADD COLUMN IF NOT EXISTS `last_used_time` int(11) DEFAULT NULL COMMENT '最后使用时间' AFTER `user_agent`,
ADD COLUMN IF NOT EXISTS `use_count` int(11) DEFAULT 0 COMMENT '使用次数' AFTER `last_used_time`;

ALTER TABLE `sys_admin_token` 
ADD INDEX IF NOT EXISTS `idx_jwt_id` (`jwt_id`),
ADD INDEX IF NOT EXISTS `idx_ip_address` (`ip_address`),
ADD INDEX IF NOT EXISTS `idx_last_used_time` (`last_used_time`);

-- 6. 创建清理过期数据的存储过程
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS `CleanupExpiredTokens`(
    IN cleanup_days INT DEFAULT 30
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(64);
    DECLARE cur CURSOR FOR 
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME IN ('qi_user_token', 'sys_admin_token', 'sys_admin_refresh_token');
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    DECLARE cleanup_time INT DEFAULT UNIX_TIMESTAMP() - (cleanup_days * 24 * 3600);
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        SET @sql = CONCAT('DELETE FROM ', table_name, ' WHERE expiretime < ', cleanup_time);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
    END LOOP;
    
    CLOSE cur;
    
    -- 清理过期的黑名单记录
    DELETE FROM `sys_token_blacklist` WHERE expire_time < UNIX_TIMESTAMP();
    
    -- 清理旧的日志记录（保留90天）
    DELETE FROM `sys_token_logs` WHERE create_time < UNIX_TIMESTAMP() - (90 * 24 * 3600);
    
    -- 清理已处理的安全事件（保留180天）
    DELETE FROM `sys_security_events` 
    WHERE status IN ('resolved', 'ignored') 
    AND create_time < UNIX_TIMESTAMP() - (180 * 24 * 3600);
    
END//

DELIMITER ;

-- 7. 创建定时清理事件（可选，需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- 
-- CREATE EVENT IF NOT EXISTS `token_cleanup_event`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL CleanupExpiredTokens(30);

-- 8. 插入一些示例配置数据
INSERT IGNORE INTO `sys_config` (`name`, `value`, `description`, `create_time`) VALUES
('token_security_enabled', '1', 'Token安全功能开关', UNIX_TIMESTAMP()),
('token_encryption_enabled', '1', 'Token数据加密开关', UNIX_TIMESTAMP()),
('token_blacklist_enabled', '1', 'Token黑名单功能开关', UNIX_TIMESTAMP()),
('token_rate_limit_enabled', '1', 'Token速率限制开关', UNIX_TIMESTAMP()),
('token_max_attempts', '5', 'Token验证最大尝试次数', UNIX_TIMESTAMP()),
('token_lockout_duration', '300', 'Token锁定持续时间（秒）', UNIX_TIMESTAMP());

-- 迁移完成提示
SELECT 'Token安全升级迁移完成！' AS message;