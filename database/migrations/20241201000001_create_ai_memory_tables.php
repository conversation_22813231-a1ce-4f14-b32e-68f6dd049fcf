<?php

use think\migration\Migrator;
use think\migration\db\Column;

/**
 * AI记忆系统数据库表迁移
 * 创建AI聊天会话、消息和上下文相关的数据库表
 */
class CreateAiMemoryTables extends Migrator
{
    /**
     * 执行迁移
     */
    public function change()
    {
        // 创建AI聊天会话表
        $this->createSessionsTable();
        
        // 创建AI聊天消息表
        $this->createMessagesTable();
        
        // 创建AI聊天上下文表
        $this->createContextsTable();
    }
    
    /**
     * 创建会话表
     */
    protected function createSessionsTable()
    {
        $table = $this->table('ai_chat_sessions', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => 'AI聊天会话表'
        ]);
        
        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '主键ID'
        ])
        ->addColumn('session_id', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '会话唯一标识'
        ])
        ->addColumn('user_id', 'integer', [
            'signed' => false,
            'null' => true,
            'comment' => '用户ID（可选）'
        ])
        ->addColumn('service_id', 'integer', [
            'signed' => false,
            'null' => true,
            'comment' => '客服ID（可选）'
        ])
        ->addColumn('session_type', 'enum', [
            'values' => ['user_ai', 'service_ai', 'auto_reply', 'general'],
            'default' => 'general',
            'comment' => '会话类型'
        ])
        ->addColumn('status', 'boolean', [
            'default' => true,
            'comment' => '会话状态：1-活跃，0-已结束'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addColumn('last_activity', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '最后活跃时间'
        ])
        ->addColumn('message_count', 'integer', [
            'default' => 0,
            'comment' => '消息数量'
        ])
        ->addColumn('total_tokens', 'integer', [
            'default' => 0,
            'comment' => '总Token数量'
        ])
        ->addColumn('metadata', 'json', [
            'null' => true,
            'comment' => '会话元数据'
        ])
        ->addIndex(['session_id'], ['unique' => true, 'name' => 'uk_session_id'])
        ->addIndex(['user_id'], ['name' => 'idx_user_id'])
        ->addIndex(['service_id'], ['name' => 'idx_service_id'])
        ->addIndex(['session_type'], ['name' => 'idx_session_type'])
        ->addIndex(['last_activity'], ['name' => 'idx_last_activity'])
        ->addIndex(['status'], ['name' => 'idx_status'])
        ->addIndex(['created_at'], ['name' => 'idx_created_at'])
        ->create();
    }
    
    /**
     * 创建消息表
     */
    protected function createMessagesTable()
    {
        $table = $this->table('ai_chat_messages', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => 'AI聊天消息表'
        ]);
        
        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '主键ID'
        ])
        ->addColumn('session_id', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '会话ID'
        ])
        ->addColumn('message_type', 'enum', [
            'values' => ['input', 'output'],
            'null' => false,
            'comment' => '消息类型：input-用户输入，output-AI输出'
        ])
        ->addColumn('content', 'text', [
            'null' => false,
            'comment' => '消息内容'
        ])
        ->addColumn('content_hash', 'string', [
            'limit' => 64,
            'null' => true,
            'comment' => '内容哈希值（用于去重）'
        ])
        ->addColumn('provider', 'string', [
            'limit' => 50,
            'null' => true,
            'comment' => 'AI提供商'
        ])
        ->addColumn('model', 'string', [
            'limit' => 100,
            'null' => true,
            'comment' => 'AI模型'
        ])
        ->addColumn('token_count', 'integer', [
            'default' => 0,
            'comment' => 'Token数量'
        ])
        ->addColumn('processing_time', 'decimal', [
            'precision' => 10,
            'scale' => 3,
            'null' => true,
            'comment' => '处理时间（毫秒）'
        ])
        ->addColumn('metadata', 'json', [
            'null' => true,
            'comment' => '消息元数据（JSON格式）'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('is_cached', 'boolean', [
            'default' => false,
            'comment' => '是否来自缓存'
        ])
        ->addColumn('quality_score', 'decimal', [
            'precision' => 3,
            'scale' => 2,
            'null' => true,
            'comment' => '回复质量评分'
        ])
        ->addIndex(['session_id'], ['name' => 'idx_session_id'])
        ->addIndex(['message_type'], ['name' => 'idx_message_type'])
        ->addIndex(['created_at'], ['name' => 'idx_created_at'])
        ->addIndex(['session_id', 'created_at'], ['name' => 'idx_session_created'])
        ->addIndex(['content_hash'], ['name' => 'idx_content_hash'])
        ->addIndex(['provider', 'model'], ['name' => 'idx_provider_model'])
        ->addIndex(['token_count'], ['name' => 'idx_token_count'])
        ->create();
    }
    
    /**
     * 创建上下文表
     */
    protected function createContextsTable()
    {
        $table = $this->table('ai_chat_contexts', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => 'AI聊天上下文表'
        ]);
        
        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '主键ID'
        ])
        ->addColumn('session_id', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '会话ID'
        ])
        ->addColumn('context_key', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '上下文键名'
        ])
        ->addColumn('context_value', 'json', [
            'null' => false,
            'comment' => '上下文值（JSON格式）'
        ])
        ->addColumn('context_type', 'string', [
            'limit' => 50,
            'default' => 'general',
            'comment' => '上下文类型'
        ])
        ->addColumn('priority', 'integer', [
            'default' => 0,
            'comment' => '优先级'
        ])
        ->addColumn('expires_at', 'timestamp', [
            'null' => true,
            'comment' => '过期时间'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addIndex(['session_id', 'context_key'], ['unique' => true, 'name' => 'uk_session_context'])
        ->addIndex(['session_id'], ['name' => 'idx_session_id'])
        ->addIndex(['context_type'], ['name' => 'idx_context_type'])
        ->addIndex(['expires_at'], ['name' => 'idx_expires_at'])
        ->addIndex(['priority'], ['name' => 'idx_priority'])
        ->create();
    }
}
