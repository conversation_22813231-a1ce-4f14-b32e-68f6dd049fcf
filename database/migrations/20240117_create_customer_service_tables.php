<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateCustomerServiceTables extends Migrator
{
    /**
     * 创建客服系统相关数据表
     */
    public function up()
    {
        // 客服表
        $this->table('ad_customer_service')
            ->addColumn('name', 'string', ['limit' => 50, 'null' => false, 'comment' => '客服名称'])
            ->addColumn('avatar', 'string', ['limit' => 255, 'null' => true, 'comment' => '头像'])
            ->addColumn('status', 'boolean', ['default' => 0, 'comment' => '在线状态：0=离线 1=在线'])
            ->addColumn('max_sessions', 'integer', ['default' => 5, 'comment' => '最大会话数'])
            ->addColumn('auto_reply', 'text', ['null' => true, 'comment' => '自动回复内容'])
            ->addColumn('group_id', 'integer', ['default' => 0, 'comment' => '分组ID'])
            ->addColumn('createtime', 'integer', ['null' => false, 'comment' => '创建时间'])
            ->addColumn('updatetime', 'integer', ['null' => false, 'comment' => '更新时间'])
            ->addIndex(['status'])
            ->addIndex(['group_id'])
            ->create();

        // 会话表
        $this->table('ad_chat_session')
            ->addColumn('user_id', 'integer', ['null' => false, 'comment' => '用户ID'])
            ->addColumn('service_id', 'integer', ['null' => false, 'comment' => '客服ID'])
            ->addColumn('status', 'boolean', ['default' => 1, 'comment' => '会话状态：0=已结束 1=进行中'])
            ->addColumn('start_time', 'integer', ['null' => false, 'comment' => '开始时间'])
            ->addColumn('end_time', 'integer', ['null' => true, 'comment' => '结束时间'])
            ->addColumn('duration', 'integer', ['null' => true, 'comment' => '会话时长(秒)'])
            ->addColumn('satisfaction', 'integer', ['null' => true, 'comment' => '满意度评分：1-5'])
            ->addColumn('source', 'string', ['limit' => 20, 'default' => 'web', 'comment' => '会话来源'])
            ->addColumn('last_message_time', 'integer', ['null' => false, 'comment' => '最后消息时间'])
            ->addColumn('createtime', 'integer', ['null' => false, 'comment' => '创建时间'])
            ->addColumn('updatetime', 'integer', ['null' => false, 'comment' => '更新时间'])
            ->addIndex(['user_id'])
            ->addIndex(['service_id'])
            ->addIndex(['status'])
            ->addIndex(['last_message_time'])
            ->create();

        // 消息表
        $this->table('ad_chat_message')
            ->addColumn('session_id', 'integer', ['null' => false, 'comment' => '会话ID'])
            ->addColumn('from_id', 'integer', ['null' => false, 'comment' => '发送者ID'])
            ->addColumn('from_type', 'string', ['limit' => 20, 'null' => false, 'comment' => '发送者类型：user=用户 service=客服 system=系统'])
            ->addColumn('to_id', 'integer', ['null' => false, 'comment' => '接收者ID'])
            ->addColumn('content', 'text', ['null' => false, 'comment' => '消息内容'])
            ->addColumn('content_type', 'string', ['limit' => 20, 'default' => 'text', 'comment' => '内容类型：text=文本 image=图片 file=文件'])
            ->addColumn('status', 'boolean', ['default' => 1, 'comment' => '消息状态：0=已删除 1=正常'])
            ->addColumn('read_status', 'boolean', ['default' => 0, 'comment' => '读取状态：0=未读 1=已读'])
            ->addColumn('response_time', 'integer', ['null' => true, 'comment' => '响应时间(秒)'])
            ->addColumn('createtime', 'integer', ['null' => false, 'comment' => '创建时间'])
            ->addColumn('updatetime', 'integer', ['null' => false, 'comment' => '更新时间'])
            ->addIndex(['session_id'])
            ->addIndex(['from_id', 'from_type'])
            ->addIndex(['to_id'])
            ->addIndex(['status'])
            ->addIndex(['read_status'])
            ->create();
    }

    /**
     * 删除客服系统相关数据表
     */
    public function down()
    {
        $this->dropSchema('ad_chat_message');
        $this->dropSchema('ad_chat_session');
        $this->dropSchema('ad_customer_service');
    }
}