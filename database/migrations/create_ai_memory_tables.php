<?php

use think\migration\Migrator;
use think\migration\db\Column;

/**
 * AI记忆系统数据库表迁移
 * 创建AI聊天会话、消息和上下文相关的数据库表
 */
class CreateAiMemoryTables extends Migrator
{
    /**
     * 执行迁移
     */
    public function change()
    {
        // 创建AI聊天会话表
        $this->createSessionsTable();
        
        // 创建AI聊天消息表
        $this->createMessagesTable();
        
        // 创建AI聊天上下文表
        $this->createContextsTable();
    }
    
    /**
     * 创建会话表
     */
    protected function createSessionsTable()
    {
        $table = $this->table('ai_chat_sessions', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => 'AI聊天会话表'
        ]);
        
        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '主键ID'
        ])
        ->addColumn('session_id', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '会话唯一标识'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addColumn('last_activity', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '最后活动时间'
        ])
        ->addColumn('message_count', 'integer', [
            'signed' => false,
            'default' => 0,
            'comment' => '消息数量'
        ])
        ->addColumn('status', 'boolean', [
            'default' => true,
            'comment' => '会话状态：1-活跃，0-非活跃'
        ])
        ->addIndex(['session_id'], ['unique' => true, 'name' => 'idx_session_id'])
        ->addIndex(['last_activity'], ['name' => 'idx_last_activity'])
        ->addIndex(['status'], ['name' => 'idx_status'])
        ->addIndex(['created_at'], ['name' => 'idx_created_at'])
        ->create();
    }
    
    /**
     * 创建消息表
     */
    protected function createMessagesTable()
    {
        $table = $this->table('ai_chat_messages', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => 'AI聊天消息表'
        ]);
        
        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '主键ID'
        ])
        ->addColumn('session_id', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '会话ID'
        ])
        ->addColumn('message_type', 'enum', [
            'values' => ['input', 'output'],
            'null' => false,
            'comment' => '消息类型：input-用户输入，output-AI输出'
        ])
        ->addColumn('content', 'text', [
            'null' => false,
            'comment' => '消息内容'
        ])
        ->addColumn('metadata', 'json', [
            'null' => true,
            'comment' => '消息元数据（JSON格式）'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('token_count', 'integer', [
            'signed' => false,
            'default' => 0,
            'comment' => 'Token数量'
        ])
        ->addIndex(['session_id'], ['name' => 'idx_session_id'])
        ->addIndex(['created_at'], ['name' => 'idx_created_at'])
        ->addIndex(['message_type'], ['name' => 'idx_message_type'])
        ->addIndex(['session_id', 'created_at'], ['name' => 'idx_session_created'])
        ->create();
    }
    
    /**
     * 创建上下文表
     */
    protected function createContextsTable()
    {
        $table = $this->table('ai_chat_contexts', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => 'AI聊天上下文表'
        ]);
        
        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '主键ID'
        ])
        ->addColumn('session_id', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '会话ID'
        ])
        ->addColumn('context_key', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '上下文键名'
        ])
        ->addColumn('context_value', 'json', [
            'null' => false,
            'comment' => '上下文值（JSON格式）'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'null' => true,
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addIndex(['session_id'], ['name' => 'idx_session_id'])
        ->addIndex(['context_key'], ['name' => 'idx_context_key'])
        ->addIndex(['session_id', 'context_key'], [
            'unique' => true, 
            'name' => 'idx_session_context_unique'
        ])
        ->addIndex(['created_at'], ['name' => 'idx_created_at'])
        ->create();
    }
}