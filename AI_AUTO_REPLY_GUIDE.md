# AI自动回复功能指南

## 概述

AI自动回复功能是基于现代化AI服务架构构建的智能客服自动回复系统，能够根据用户消息内容智能判断是否需要回复，并提供个性化的AI回复。

## 核心特性

### 🤖 智能触发
- **关键词触发**: 支持 `@AI`、`智能助手`、`机器人` 等关键词
- **模式匹配**: 支持正则表达式模式匹配
- **智能识别**: 自动识别需要AI回复的消息

### 🧠 上下文记忆
- **会话记忆**: 保持用户对话上下文
- **个性化回复**: 基于历史对话提供个性化回复
- **会话管理**: 支持会话清除和管理

### ⚡ 性能优化
- **智能缓存**: 相同问题自动缓存回复
- **频率限制**: 防止频繁回复
- **异步处理**: 不阻塞主要业务流程

### 🛡️ 安全控制
- **内容过滤**: 支持关键词和模式过滤
- **频率控制**: 限制回复频率
- **权限管理**: 支持用户权限控制

## 快速开始

### 1. 环境配置

复制环境变量配置文件：
```bash
cp .env.ai_auto_reply.example .env
```

配置必要的环境变量：
```bash
# 启用AI自动回复
AI_AUTO_REPLY_ENABLED=true

# 配置AI服务
AI_AUTO_REPLY_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_api_key_here

# 启用记忆功能
AI_AUTO_REPLY_USE_MEMORY=true
```

### 2. 基础使用

```php
use app\vchat\auto_reply\AiAutoReply;

// 创建AI自动回复实例
$aiReply = new AiAutoReply();

// 构建消息
$message = [
    'content' => '@AI 你好，请介绍一下你自己',
    'from_id' => 12345,
    'type' => 'text',
    'timestamp' => time(),
];

// 检查是否应该回复
if ($aiReply->shouldReply($message)) {
    $reply = $aiReply->getReply($message);
    echo "AI回复: " . $reply;
}
```

### 3. 集成到现有系统

AI自动回复已经集成到自动回复管理器中，会自动处理符合条件的消息：

```php
use app\vchat\auto_reply\AutoReplyManager;

$manager = new AutoReplyManager();
$reply = $manager->getAutoReply($message);
```

## API接口

### 基础管理接口

#### 获取服务状态
```http
GET /ai-auto-reply/status
```

#### 健康检查
```http
GET /ai-auto-reply/health
```

#### 获取配置
```http
GET /ai-auto-reply/config
```

#### 更新配置
```http
POST /ai-auto-reply/config
Content-Type: application/json

{
    "config": {
        "enabled": true,
        "temperature": 0.8,
        "max_tokens": 600
    }
}
```

### 测试接口

#### 单条消息测试
```http
POST /ai-auto-reply/test
Content-Type: application/json

{
    "content": "@AI 你好，请问今天天气怎么样？",
    "from_id": 12345
}
```

#### 批量消息测试
```http
POST /ai-auto-reply/batch-test
Content-Type: application/json

{
    "messages": [
        "@AI 你好",
        "智能助手：请帮我查询订单",
        "机器人：今天天气如何？"
    ]
}
```

### 会话管理

#### 清除用户会话
```http
DELETE /ai-auto-reply/session/12345
```

## 配置说明

### 触发条件配置

```php
'triggers' => [
    // 触发关键词
    'keywords' => [
        '@AI', '@ai', '智能助手', '机器人', 'AI助手'
    ],
    // 触发模式（正则表达式）
    'patterns' => [
        '/^AI[：:]/i', 
        '/^智能助手[：:]/i'
    ],
    // 是否对所有消息都回复
    'always_reply' => false,
    // 消息长度限制
    'min_length' => 3,
    'max_length' => 1000,
]
```

### 记忆功能配置

```php
'context' => [
    // 是否使用记忆功能
    'use_memory' => true,
    // 会话ID前缀
    'session_prefix' => 'ai_auto_reply_',
    // 最大历史记录数
    'max_history' => 10,
    // 会话清除时间（秒）
    'clear_after' => 3600,
]
```

### 提示词配置

```php
'prompts' => [
    'system' => '你是一个友好的智能客服助手...',
    'welcome' => '你好！我是AI智能助手...',
    'error' => '抱歉，我暂时无法理解您的问题...',
    'busy' => '系统繁忙，请稍后再试。',
]
```

## 使用场景

### 1. 客服自动回复
- 用户咨询时自动提供初步回复
- 减少人工客服工作量
- 提高响应速度

### 2. 智能问答
- 常见问题自动回答
- 产品信息查询
- 服务指导

### 3. 互动娱乐
- 智能聊天机器人
- 用户互动增强
- 社区活跃度提升

## 最佳实践

### 1. 触发条件设置
- 使用明确的触发关键词（如 `@AI`）
- 避免设置 `always_reply=true`
- 合理设置消息长度限制

### 2. 提示词优化
- 设置清晰的系统提示词
- 控制回复长度和语气
- 定期优化提示词效果

### 3. 性能优化
- 启用缓存功能
- 设置合理的频率限制
- 监控响应时间

### 4. 安全控制
- 配置内容过滤规则
- 设置用户权限
- 监控异常使用

## 监控和调试

### 1. 状态监控
```bash
curl http://your-domain/ai-auto-reply/status
```

### 2. 健康检查
```bash
curl http://your-domain/ai-auto-reply/health
```

### 3. 统计信息
```bash
curl http://your-domain/ai-auto-reply/stats
```

### 4. 日志查看
查看应用日志中的AI自动回复相关记录：
```bash
tail -f runtime/log/ai_auto_reply.log
```

## 故障排除

### 常见问题

1. **AI服务不可用**
   - 检查API密钥配置
   - 验证网络连接
   - 查看错误日志

2. **回复质量不佳**
   - 优化系统提示词
   - 调整temperature参数
   - 增加上下文信息

3. **响应速度慢**
   - 启用缓存功能
   - 减少max_tokens
   - 优化网络配置

4. **频繁触发**
   - 调整触发条件
   - 增加频率限制
   - 优化关键词匹配

### 调试模式

启用调试模式获取更多日志信息：
```php
'debug' => true
```

## 版本信息

- 版本: 1.0.0
- 依赖: 现代化AI服务架构 v2.0.0
- 兼容性: PHP 8.0+, ThinkPHP 8.0+

## 许可证

MIT License
