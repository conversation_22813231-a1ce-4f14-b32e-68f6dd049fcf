-- 运费模板表
CREATE TABLE `ad_shipping_fee_template` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '模板名称',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计费方式：1按重量，2按件数',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `createtime` int(10) unsigned NOT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运费模板表';

-- 运费模板区域费用表
CREATE TABLE `ad_shipping_fee_area` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` bigint(20) unsigned NOT NULL COMMENT '运费模板ID',
  `area_ids` text NOT NULL COMMENT '区域ID列表，逗号分隔',
  `area_names` text NOT NULL COMMENT '区域名称列表，逗号分隔',
  `first_unit` decimal(10,2) NOT NULL COMMENT '首重/首件数量',
  `first_fee` decimal(10,2) NOT NULL COMMENT '首重/首件费用',
  `additional_unit` decimal(10,2) NOT NULL COMMENT '续重/续件数量',
  `additional_fee` decimal(10,2) NOT NULL COMMENT '续重/续件费用',
  `createtime` int(10) unsigned NOT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_template` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运费模板区域费用表';

-- 插入示例数据
INSERT INTO `ad_shipping_fee_template` (`name`, `type`, `enabled`, `createtime`, `updatetime`) VALUES
('标准快递', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

INSERT INTO `ad_shipping_fee_area` (`template_id`, `area_ids`, `area_names`, `first_unit`, `first_fee`, `additional_unit`, `additional_fee`, `createtime`, `updatetime`) VALUES
(1, '1,2,3', '北京,天津,河北', 1.00, 8.00, 1.00, 5.00, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '4,5,6', '山西,内蒙古,辽宁', 1.00, 10.00, 1.00, 6.00, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());