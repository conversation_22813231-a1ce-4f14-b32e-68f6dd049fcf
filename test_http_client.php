<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\ai\utils\HttpClient;
use app\ai\utils\Logger;
use app\ai\config\ConfigManager;

echo "🔧 测试增强的HTTP客户端\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // 初始化配置
    ConfigManager::set('debug', true);
    
    // 创建HTTP客户端
    $httpClient = new HttpClient([
        'timeout' => 10,
        'debug' => true,
        'max_retries' => 2,
        'retry_delay' => 1
    ]);
    
    echo "✅ HTTP客户端创建成功\n\n";
    
    // 测试1: 简单的GET请求
    echo "📡 测试1: GET请求到httpbin.org\n";
    try {
        $result = $httpClient->get('https://httpbin.org/get', ['test' => 'value']);
        echo "  ✅ GET请求成功\n";
        echo "  📊 响应时间: " . round($result['execution_time'], 2) . "ms\n";
        echo "  📦 响应大小: " . $result['response_size'] . " bytes\n";
        echo "  🔢 HTTP状态码: " . $result['http_code'] . "\n";
        
        if (isset($result['data']['args']['test'])) {
            echo "  ✅ 参数传递正确: " . $result['data']['args']['test'] . "\n";
        }
    } catch (Exception $e) {
        echo "  ❌ GET请求失败: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 测试2: POST请求
    echo "📡 测试2: POST请求到httpbin.org\n";
    try {
        $postData = [
            'message' => 'Hello from AI HTTP Client',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $result = $httpClient->post('https://httpbin.org/post', $postData);
        echo "  ✅ POST请求成功\n";
        echo "  📊 响应时间: " . round($result['execution_time'], 2) . "ms\n";
        echo "  📦 响应大小: " . $result['response_size'] . " bytes\n";
        echo "  🔢 HTTP状态码: " . $result['http_code'] . "\n";
        
        if (isset($result['data']['json']['message'])) {
            echo "  ✅ JSON数据传递正确: " . $result['data']['json']['message'] . "\n";
        }
    } catch (Exception $e) {
        echo "  ❌ POST请求失败: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 测试3: 带自定义头的请求
    echo "📡 测试3: 带自定义请求头\n";
    try {
        $headers = [
            'User-Agent' => 'AI-Test-Client/1.0',
            'X-Custom-Header' => 'test-value'
        ];
        
        $result = $httpClient->get('https://httpbin.org/headers', [], $headers);
        echo "  ✅ 自定义头请求成功\n";
        echo "  📊 响应时间: " . round($result['execution_time'], 2) . "ms\n";
        
        if (isset($result['data']['headers']['X-Custom-Header'])) {
            echo "  ✅ 自定义头传递正确: " . $result['data']['headers']['X-Custom-Header'] . "\n";
        }
    } catch (Exception $e) {
        echo "  ❌ 自定义头请求失败: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 测试4: 错误处理 - 无效URL
    echo "📡 测试4: 错误处理 - 无效URL\n";
    try {
        $result = $httpClient->get('https://invalid-domain-that-does-not-exist.com');
        echo "  ❌ 应该失败但成功了\n";
    } catch (Exception $e) {
        echo "  ✅ 正确捕获错误: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 测试5: 超时处理
    echo "📡 测试5: 超时处理\n";
    try {
        $shortTimeoutClient = new HttpClient([
            'timeout' => 1, // 1秒超时
            'debug' => false
        ]);
        
        $result = $shortTimeoutClient->get('https://httpbin.org/delay/3'); // 延迟3秒
        echo "  ❌ 应该超时但成功了\n";
    } catch (Exception $e) {
        echo "  ✅ 正确处理超时: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 测试6: JSON解析错误处理
    echo "📡 测试6: JSON解析错误处理\n";
    try {
        // 请求返回HTML而不是JSON的端点
        $result = $httpClient->get('https://httpbin.org/html');
        echo "  ❌ 应该JSON解析失败但成功了\n";
    } catch (Exception $e) {
        echo "  ✅ 正确处理JSON解析错误: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    echo "🎉 HTTP客户端测试完成！\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // 显示一些统计信息
    echo "\n📊 测试统计:\n";
    echo "- 总测试数: 6\n";
    echo "- 预期成功: 3 (GET, POST, 自定义头)\n";
    echo "- 预期失败: 3 (无效URL, 超时, JSON解析)\n";
    echo "- 重试机制: 已启用 (最多2次重试)\n";
    echo "- 调试模式: 已启用\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
