<?php
/**
 * 测试AI服务提供者修复
 */

echo "=== AI服务提供者修复验证 ===\n\n";

try {
    // 测试ServiceContainer
    echo "1. 测试ServiceContainer:\n";
    
    require_once 'app/ai/container/ServiceContainer.php';
    $container = \app\ai\container\ServiceContainer::getInstance();
    echo "  ✓ ServiceContainer 创建成功\n";
    
    // 测试基本绑定
    $container->bind('test.service', function() {
        return 'test_value';
    });
    
    $result = $container->make('test.service');
    if ($result === 'test_value') {
        echo "  ✓ 服务绑定和解析正常\n";
    } else {
        echo "  ❌ 服务绑定和解析失败\n";
    }
    
    // 测试单例
    $container->singleton('test.singleton', function() {
        return new stdClass();
    });
    
    $instance1 = $container->make('test.singleton');
    $instance2 = $container->make('test.singleton');
    
    if ($instance1 === $instance2) {
        echo "  ✓ 单例模式正常\n";
    } else {
        echo "  ❌ 单例模式失败\n";
    }
    
    // 测试别名
    $container->alias('test.alias', 'test.service');
    $aliasResult = $container->make('test.alias');
    
    if ($aliasResult === 'test_value') {
        echo "  ✓ 别名功能正常\n";
    } else {
        echo "  ❌ 别名功能失败\n";
    }
    
    // 测试bound方法
    if ($container->bound('test.service') && $container->bound('test.alias')) {
        echo "  ✓ bound 方法正常\n";
    } else {
        echo "  ❌ bound 方法失败\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ ServiceContainer 测试失败: " . $e->getMessage() . "\n";
}

try {
    // 测试AiServiceProvider
    echo "\n2. 测试AiServiceProvider:\n";
    
    require_once 'app/ai/providers/AiServiceProvider.php';
    $provider = new \app\ai\providers\AiServiceProvider();
    echo "  ✓ AiServiceProvider 创建成功\n";
    
    // 测试register方法（不应该抛出参数错误）
    $provider->register();
    echo "  ✓ register() 方法调用成功\n";
    
    // 测试boot方法
    $provider->boot();
    echo "  ✓ boot() 方法调用成功\n";
    
} catch (Exception $e) {
    echo "  ❌ AiServiceProvider 测试失败: " . $e->getMessage() . "\n";
}

try {
    // 测试AiApplication（如果存在）
    echo "\n3. 测试AiApplication集成:\n";
    
    if (file_exists('app/ai/bootstrap/AiApplication.php')) {
        require_once 'app/ai/bootstrap/AiApplication.php';
        
        // 检查类是否存在
        if (class_exists('\app\ai\bootstrap\AiApplication')) {
            echo "  ✓ AiApplication 类存在\n";
            
            // 测试创建实例（可能需要依赖）
            try {
                $app = new \app\ai\bootstrap\AiApplication();
                echo "  ✓ AiApplication 实例创建成功\n";
                
                // 测试获取容器
                $container = $app->getContainer();
                if ($container instanceof \app\ai\container\ServiceContainer) {
                    echo "  ✓ 容器获取成功\n";
                } else {
                    echo "  ❌ 容器类型不正确\n";
                }
                
            } catch (Exception $e) {
                echo "  ⚠️  AiApplication 实例化需要依赖: " . $e->getMessage() . "\n";
            }
        } else {
            echo "  ❌ AiApplication 类不存在\n";
        }
    } else {
        echo "  ⚠️  AiApplication.php 文件不存在\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ AiApplication 测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 修复验证完成 ===\n";

echo "\n📋 修复总结:\n";
echo "✅ 修复了 AiServiceProvider::register() 方法参数问题\n";
echo "✅ 完善了 ServiceContainer 的功能实现\n";
echo "✅ 添加了单例、别名、绑定检查等功能\n";
echo "✅ 修复了 AiApplication 中的服务提供者注册调用\n";

echo "\n🎯 修复的具体问题:\n";
echo "1. AiServiceProvider::register() 不再要求参数\n";
echo "2. ServiceContainer 支持完整的容器功能\n";
echo "3. AiApplication 正确调用 provider->register()\n";
echo "4. 所有语法错误已修复\n";

echo "\n🚀 现在AI系统应该可以正常启动了！\n";
