<?php

/**
 * 知识库回复格式控制 - 使用示例
 * 展示如何解决固定格式和多余提示的问题
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\ai\services\KnowledgeBaseService;
use app\vchat\auto_reply\HelpReplyFixed;

echo "🎯 知识库回复格式控制 - 实际应用示例\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// 加载配置
$config = include __DIR__ . '/config/knowledge_base_response.php';

echo "📋 问题：原始回复格式过于固定\n";
echo "❌ 原来：根据知识库内容，您提到的\"测试\"相关内容如下：**测试...** 如需进一步帮助...\n";
echo "✅ 现在：可以完全控制回复格式，去除固定前缀和后缀\n\n";

// 示例1：推荐配置 - 简洁模式
echo "🎯 示例1：推荐配置 - 简洁模式\n";
echo "-" . str_repeat("-", 50) . "\n";

$kb1 = new KnowledgeBaseService($config['presets']['simple']);

echo "配置特点：\n";
echo "- 模式：simple（简洁）\n";
echo "- 客服提示：关闭\n";
echo "- 最大长度：200字\n";
echo "- 置信度阈值：0.4\n\n";

echo "模拟问题：\"如何充值？\"\n";
echo "回复效果：\n";
echo "充值方法：\n";
echo "1. 点击右上角\"充值\"按钮\n";
echo "2. 选择充值金额\n";
echo "3. 选择支付方式\n";
echo "4. 完成支付即可\n\n";

// 示例2：客服模式
echo "🎯 示例2：客服模式\n";
echo "-" . str_repeat("-", 50) . "\n";

$kb2 = new KnowledgeBaseService($config['presets']['customer_service']);

echo "配置特点：\n";
echo "- 模式：formal（正式）\n";
echo "- 客服提示：开启\n";
echo "- 最大长度：400字\n";
echo "- 置信度阈值：0.3\n\n";

echo "模拟问题：\"退款流程是什么？\"\n";
echo "回复效果：\n";
echo "您好，关于退款流程，具体步骤如下：\n";
echo "1. 登录账户，进入订单页面\n";
echo "2. 找到需要退款的订单\n";
echo "3. 点击\"申请退款\"按钮\n";
echo "4. 填写退款原因\n";
echo "5. 提交申请，等待审核\n\n";
echo "如果您在操作过程中遇到问题，建议联系人工客服。\n\n";

// 示例3：极简模式
echo "🎯 示例3：极简模式\n";
echo "-" . str_repeat("-", 50) . "\n";

$kb3 = new KnowledgeBaseService($config['presets']['minimal']);

echo "配置特点：\n";
echo "- 模式：simple（简洁）\n";
echo "- 建议：关闭\n";
echo "- 最大长度：150字\n";
echo "- 置信度阈值：0.5\n\n";

echo "模拟问题：\"营业时间？\"\n";
echo "回复效果：\n";
echo "营业时间：周一至周日 9:00-21:00\n\n";

// 示例4：动态模式切换
echo "🎯 示例4：动态模式切换\n";
echo "-" . str_repeat("-", 50) . "\n";

$helpReply = new HelpReplyFixed($config['presets']['simple']);

echo "智能模式切换演示：\n\n";

// 简单问题
$simpleMessage = ['content' => '价格多少？'];
echo "简单问题：\"{$simpleMessage['content']}\"\n";
echo "→ 自动使用简洁模式\n";
echo "→ 回复：\"基础版99元/月，专业版199元/月\"\n\n";

// 复杂问题
$complexMessage = ['content' => '请详细说明如何配置高级功能的完整步骤？'];
echo "复杂问题：\"{$complexMessage['content']}\"\n";
echo "→ 自动切换到详细模式\n";
echo "→ 回复：包含完整步骤、注意事项和相关建议\n\n";

// 示例5：配置对比
echo "🎯 示例5：配置效果对比\n";
echo "-" . str_repeat("-", 50) . "\n";

$testQuestion = "如何设置密码？";

echo "同一问题，不同配置的回复效果：\n\n";

echo "📝 问题：{$testQuestion}\n\n";

echo "🔹 极简模式回复：\n";
echo "\"进入设置 > 账户安全 > 修改密码\"\n\n";

echo "🔹 简洁模式回复：\n";
echo "\"密码设置步骤：\n";
echo "1. 登录账户\n";
echo "2. 进入\"设置\"页面\n";
echo "3. 点击\"账户安全\"\n";
echo "4. 选择\"修改密码\"\n";
echo "5. 输入新密码并确认\"\n\n";

echo "🔹 正式模式回复：\n";
echo "\"您好，关于密码设置，请按以下步骤操作：\n";
echo "1. 请先登录您的账户\n";
echo "2. 在页面右上角找到\"设置\"选项\n";
echo "3. 进入\"账户安全\"设置\n";
echo "4. 点击\"修改密码\"功能\n";
echo "5. 按要求输入新密码\n";
echo "如有疑问，建议联系客服协助。\"\n\n";

// 实际应用代码
echo "🔧 实际应用代码\n";
echo "=" . str_repeat("=", 60) . "\n";

echo "// 1. 基础使用 - 推荐配置\n";
echo "\$kb = new KnowledgeBaseService([\n";
echo "    'mode' => 'simple',\n";
echo "    'include_fallback_message' => false,\n";
echo "    'max_content_length' => 300\n";
echo "]);\n\n";

echo "// 2. 使用预设配置\n";
echo "\$config = include 'config/knowledge_base_response.php';\n";
echo "\$kb = new KnowledgeBaseService(\$config['presets']['simple']);\n\n";

echo "// 3. 动态切换模式\n";
echo "\$kb->setResponseMode('simple');   // 简洁回复\n";
echo "\$kb->setResponseMode('detailed'); // 详细回复\n\n";

echo "// 4. 在自动回复中使用\n";
echo "\$helpReply = new HelpReplyFixed(\$config['presets']['simple']);\n";
echo "\$reply = \$helpReply->generateReply(\$message);\n\n";

echo "// 5. 场景化配置\n";
echo "switch (\$scenario) {\n";
echo "    case 'website_chat':\n";
echo "        \$kb->setResponseMode('simple');\n";
echo "        break;\n";
echo "    case 'customer_service':\n";
echo "        \$kb->setResponseMode('formal');\n";
echo "        break;\n";
echo "    case 'help_center':\n";
echo "        \$kb->setResponseMode('detailed');\n";
echo "        break;\n";
echo "}\n\n";

echo "🎉 总结\n";
echo "=" . str_repeat("=", 60) . "\n";
echo "✅ 问题解决：\n";
echo "- 去除固定的\"根据知识库内容\"前缀\n";
echo "- 去除强制的\"建议联系人工客服\"后缀\n";
echo "- 支持3种回复模式：简洁、正式、详细\n";
echo "- 可以动态调整回复风格\n";
echo "- 完全控制回复格式和内容\n\n";

echo "💡 推荐设置：\n";
echo "- 日常使用：simple模式 + 关闭客服提示\n";
echo "- 客服场景：formal模式 + 开启客服提示\n";
echo "- 帮助文档：detailed模式 + 完整信息\n\n";

echo "📖 详细配置说明：KNOWLEDGE_BASE_RESPONSE_CONFIG.md\n";
echo "🔧 配置文件：config/knowledge_base_response.php\n";
echo "💻 修复后的类：app/vchat/auto_reply/HelpReplyFixed.php\n";
