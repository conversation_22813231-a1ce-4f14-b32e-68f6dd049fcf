<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\ai\services\KnowledgeBaseService;

echo "🔧 置信度计算修复测试\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // 创建知识库服务，禁用缓存避免问题
    $kb = new KnowledgeBaseService([
        'mode' => 'simple',
        'include_fallback_message' => false,
        'confidence_threshold' => 0.3
    ]);
    
    echo "📋 问题分析：\n";
    echo "- calculateConfidence 方法计算不稳定\n";
    echo "- 相关性分数 relevance_score 计算有问题\n";
    echo "- 导致匹配到数据但置信度过低\n\n";
    
    echo "🔧 修复方案：\n";
    echo "1. 改进关键词相关性计算算法\n";
    echo "   - 直接字符串匹配（权重最高）\n";
    echo "   - 标题匹配加分\n";
    echo "   - 关键词匹配\n";
    echo "   - 长度惩罚机制\n\n";
    
    echo "2. 改进置信度计算\n";
    echo "   - 最高分和平均分加权\n";
    echo "   - 数量加成机制\n";
    echo "   - 质量阈值判断\n";
    echo "   - 详细日志记录\n\n";
    
    // 模拟测试数据
    echo "📊 模拟测试不同相关性分数的置信度计算\n";
    echo "-" . str_repeat("-", 40) . "\n\n";
    
    // 测试不同的相关性分数组合
    $testCases = [
        [
            'name' => '高质量匹配',
            'scores' => [0.8, 0.7, 0.6],
            'expected' => '高置信度 (>0.6)'
        ],
        [
            'name' => '中等质量匹配',
            'scores' => [0.5, 0.4, 0.3],
            'expected' => '中等置信度 (0.3-0.6)'
        ],
        [
            'name' => '低质量匹配',
            'scores' => [0.3, 0.2, 0.1],
            'expected' => '低置信度 (<0.3)'
        ],
        [
            'name' => '单个高分匹配',
            'scores' => [0.9],
            'expected' => '高置信度'
        ],
        [
            'name' => '多个低分匹配',
            'scores' => [0.2, 0.2, 0.2, 0.2, 0.2],
            'expected' => '低置信度但有数量加成'
        ]
    ];
    
    foreach ($testCases as $case) {
        echo "🔍 测试案例: {$case['name']}\n";
        echo "   相关性分数: [" . implode(', ', $case['scores']) . "]\n";
        
        // 模拟相关帮助数据
        $relevantHelps = [];
        foreach ($case['scores'] as $i => $score) {
            $relevantHelps[] = [
                'id' => $i + 1,
                'title' => "帮助文档 " . ($i + 1),
                'content' => "这是帮助内容 " . ($i + 1),
                'relevance_score' => $score
            ];
        }
        
        // 使用反射调用私有方法进行测试
        $reflection = new ReflectionClass($kb);
        $method = $reflection->getMethod('calculateConfidence');
        $method->setAccessible(true);
        
        $confidence = $method->invoke($kb, $relevantHelps);
        
        echo "   计算置信度: {$confidence}\n";
        echo "   预期结果: {$case['expected']}\n";
        
        // 判断结果
        if ($confidence >= 0.6) {
            $level = "高 ⭐⭐⭐";
        } elseif ($confidence >= 0.3) {
            $level = "中 ⭐⭐";
        } else {
            $level = "低 ⭐";
        }
        echo "   置信度等级: {$level}\n";
        
        // 分析
        $maxScore = max($case['scores']);
        $avgScore = array_sum($case['scores']) / count($case['scores']);
        echo "   分析: 最高分={$maxScore}, 平均分={$avgScore}, 数量=" . count($case['scores']) . "\n";
        
        echo "\n" . str_repeat("-", 40) . "\n\n";
    }
    
    // 测试关键词相关性计算
    echo "🔍 测试关键词相关性计算改进\n";
    echo "=" . str_repeat("=", 50) . "\n\n";
    
    $testQuestions = [
        '怎么购买',
        '测试功能',
        '如何充值',
        '退款流程'
    ];
    
    $testHelps = [
        [
            'id' => 1,
            'title' => '购买指南',
            'content' => '详细的购买流程说明，包括选择商品、下单、支付等步骤。'
        ],
        [
            'id' => 2,
            'title' => '功能测试',
            'content' => '系统功能测试方法，包括基础测试和高级测试功能。'
        ],
        [
            'id' => 3,
            'title' => '账户充值',
            'content' => '账户充值方法，支持多种支付方式，充值后立即到账。'
        ]
    ];
    
    foreach ($testQuestions as $question) {
        echo "❓ 问题: \"{$question}\"\n";
        
        foreach ($testHelps as $help) {
            // 使用反射调用私有方法
            $method = $reflection->getMethod('calculateKeywordRelevance');
            $method->setAccessible(true);
            
            $relevance = $method->invoke($kb, $question, $help);
            
            echo "   与 \"{$help['title']}\" 的相关性: {$relevance}\n";
        }
        echo "\n";
    }
    
    echo "📊 修复效果预期\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo "✅ 相关性计算更准确\n";
    echo "   - 直接字符串匹配优先\n";
    echo "   - 标题匹配权重更高\n";
    echo "   - 避免过度稀释\n\n";
    
    echo "✅ 置信度计算更稳定\n";
    echo "   - 考虑最高分和平均分\n";
    echo "   - 数量加成机制\n";
    echo "   - 质量阈值保护\n\n";
    
    echo "✅ 预期改善\n";
    echo "   - 减少误判\n";
    echo "   - 提高匹配准确性\n";
    echo "   - 更稳定的置信度\n";
    echo "   - 更好的用户体验\n\n";
    
    echo "💡 建议配置\n";
    echo "   - 置信度阈值: 0.3 (平衡准确性和覆盖率)\n";
    echo "   - 使用简洁模式避免冗余信息\n";
    echo "   - 启用详细日志便于调试\n\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误:\n";
    echo "错误信息: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "🎉 置信度计算修复完成!\n";
echo "现在应该能够更准确地计算相关性和置信度了。\n";
