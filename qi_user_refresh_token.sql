/*
 Navicat Premium Dump SQL

 Source Server         : **************
 Source Server Type    : MySQL
 Source Server Version : 80024 (8.0.24)
 Source Host           : **************:3306
 Source Schema         : cloud_19qi_com

 Target Server Type    : MySQL
 Target Server Version : 80024 (8.0.24)
 File Encoding         : 65001

 Date: 18/04/2025 19:15:12
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for qi_user_refresh_token
-- ----------------------------
DROP TABLE IF EXISTS `qi_user_refresh_token`;
CREATE TABLE `qi_user_refresh_token` (
  `token` varchar(50) NOT NULL COMMENT 'Token',
  `data` text NOT NULL COMMENT '会员ID',
  `createtime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `expiretime` int unsigned NOT NULL DEFAULT '0' COMMENT '过期时间',
  PRIMARY KEY (`token`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=COMPACT COMMENT='会员Token表';

SET FOREIGN_KEY_CHECKS = 1;
