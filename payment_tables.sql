-- 支付订单表
CREATE TABLE `payment_order` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `out_trade_no` varchar(64) NOT NULL COMMENT '商户订单号',
    `payment_type` varchar(32) NOT NULL COMMENT '支付方式：alipay=支付宝，wechat=微信支付',
    `trade_no` varchar(64) NOT NULL DEFAULT '' COMMENT '支付平台交易号',
    `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
    `pay_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
    `trade_status` varchar(32) NOT NULL DEFAULT '' COMMENT '交易状态',
    `buyer_id` varchar(128) NOT NULL DEFAULT '' COMMENT '买家ID/openid',
    `buyer_logon_id` varchar(100) NOT NULL DEFAULT '' COMMENT '买家支付账号',
    `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
    `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
    `notify_data` text COMMENT '通知原始数据',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_out_trade_no` (`out_trade_no`),
    KEY `idx_trade_no` (`trade_no`),
    KEY `idx_trade_status` (`trade_status`),
    KEY `idx_pay_time` (`pay_time`),
    KEY `idx_payment_type` (`payment_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';

-- 退款订单表
CREATE TABLE `payment_refund` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `out_trade_no` varchar(64) NOT NULL COMMENT '商户订单号',
    `out_refund_no` varchar(64) NOT NULL COMMENT '退款单号',
    `payment_type` varchar(32) NOT NULL COMMENT '支付方式：alipay=支付宝，wechat=微信支付',
    `refund_id` varchar(64) NOT NULL DEFAULT '' COMMENT '支付平台退款单号',
    `refund_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
    `refund_status` varchar(32) NOT NULL DEFAULT '' COMMENT '退款状态',
    `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
    `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
    `notify_data` text COMMENT '通知原始数据',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_out_refund_no` (`out_refund_no`),
    KEY `idx_out_trade_no` (`out_trade_no`),
    KEY `idx_refund_id` (`refund_id`),
    KEY `idx_refund_status` (`refund_status`),
    KEY `idx_refund_time` (`refund_time`),
    KEY `idx_payment_type` (`payment_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款订单表';