<?php

namespace apps\install\controller;

use think\facade\View;

class Install
{

    protected $step = 0;
    protected $error = '';
    protected $requirements = [
        'php' => '7.2.5',
        'extensions' => [
            'pdo',
            'pdo_mysql',
            'mbstring',
            'curl',
            'openssl',
            'fileinfo'
        ],
        'writableDirs' => [
            'runtime',
            'public/uploads'
        ]
    ];

    public function __construct()
    {
        // 初始化模板引擎配置
        View::config([
            'type' => 'think',
            'view_path' => root_path() . 'apps/example/views/',
            'view_suffix' => 'html',  // 添加视图文件后缀
            'tpl_begin' => '{',      // 模板标签开始标记
            'tpl_end' => '}',        // 模板标签结束标记
            'taglib_begin' => '{',   // 标签库标签开始标记
            'taglib_end' => '}'      // 标签库标签结束标记
        ]);
    }

    public function index()
    {
        $this->step = input('step', 0, 'intval');
        
        switch ($this->step) {
            case 0:
                return $this->welcome();
            case 1:
                return $this->checkEnvironment();
            case 2:
                return $this->configureDatabase();
            case 3:
                return $this->createAdmin();
            case 4:
                return $this->complete();
            default:
                return $this->welcome();
        }
    }

    protected function welcome()
    {
        return view('install/welcome', [
            'step' => $this->step,
            'title' => '欢迎安装'
        ]);
    }

    protected function checkEnvironment()
    {
        $requirements = $this->checkRequirements();
        $passed = !in_array(false, array_column($requirements, 'passed'));

        return view('install/check', [
            'step' => $this->step,
            'title' => '环境检查',
            'requirements' => $requirements,
            'passed' => $passed
        ]);
    }

    protected function configureDatabase()
    {
        if (request()->isPost()) {
            $config = input('post.');
            // 验证数据库连接
            try {
                $dsn = "mysql:host={$config['hostname']};port={$config['hostport']}";
                new \PDO($dsn, $config['username'], $config['password']);
                
                // 保存配置
                $this->saveConfig($config);
                return json(['code' => 1, 'msg' => '数据库配置成功']);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '数据库连接失败：' . $e->getMessage()]);
            }
        }

        return view('install/database', [
            'step' => $this->step,
            'title' => '数据库配置'
        ]);
    }

    protected function createAdmin()
    {
        if (request()->isPost()) {
            $data = input('post.');
            // 创建管理员账号
            try {
                // 执行安装SQL
                $this->installDatabase();
                // 创建管理员
                $this->createAdminUser($data);
                return json(['code' => 1, 'msg' => '安装成功']);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '安装失败：' . $e->getMessage()]);
            }
        }

        return view('install/admin', [
            'step' => $this->step,
            'title' => '创建管理员'
        ]);
    }

    protected function complete()
    {
        // 生成安装锁定文件
        file_put_contents(root_path() . 'data/install.lock', date('Y-m-d H:i:s'));
        
        return view('install/complete', [
            'step' => $this->step,
            'title' => '安装完成'
        ]);
    }

    protected function checkRequirements()
    {
        $results = [];
        
        // 检查PHP版本
        $results['php'] = [
            'name' => 'PHP版本',
            'required' => '>= ' . $this->requirements['php'],
            'current' => PHP_VERSION,
            'passed' => version_compare(PHP_VERSION, $this->requirements['php'], '>=')
        ];

        // 检查扩展
        foreach ($this->requirements['extensions'] as $extension) {
            $results['ext-' . $extension] = [
                'name' => $extension . '扩展',
                'required' => '已安装',
                'current' => extension_loaded($extension) ? '已安装' : '未安装',
                'passed' => extension_loaded($extension)
            ];
        }

        // 检查目录权限
        foreach ($this->requirements['writableDirs'] as $dir) {
            $path = root_path() . $dir;
            $results['dir-' . $dir] = [
                'name' => $dir . '目录',
                'required' => '可写',
                'current' => is_writable($path) ? '可写' : '不可写',
                'passed' => is_writable($path)
            ];
        }

        return $results;
    }

    protected function saveConfig($config)
    {
        $envContent = file_get_contents(root_path() . '.env');
        
        // 更新数据库配置
        $envMap = [
            'DB_HOST' => $config['hostname'],
            'DB_PORT' => $config['hostport'],
            'DB_NAME' => $config['database'],
            'DB_USER' => $config['username'],
            'DB_PASS' => $config['password'],
            'DB_PREFIX' => $config['prefix']
        ];
        
        foreach ($envMap as $key => $value) {
            $pattern = "/$key\s*=\s*[^\n]*/";
            $replacement = "$key = $value";
            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n$replacement";
            }
        }
        
        file_put_contents(root_path() . '.env', $envContent);
    }

    protected function installDatabase()
    {
        $sql = file_get_contents(root_path() . 'apps/example/install.sql');
        $pdo = \think\facade\Db::getPdo();
        $pdo->execute($sql);
    }

    protected function createAdminUser($data)
    {
        // 验证数据
        if (empty($data['username']) || empty($data['password'])) {
            throw new \Exception('用户名和密码不能为空');
        }
        
        // 检查用户名长度
        if (strlen($data['username']) < 4 || strlen($data['username']) > 20) {
            throw new \Exception('用户名长度必须在4-20个字符之间');
        }
        
        // 检查密码长度
        if (strlen($data['password']) < 6 || strlen($data['password']) > 32) {
            throw new \Exception('密码长度必须在6-32个字符之间');
        }
        
        // 准备管理员数据
        $admin = [
            'username' => $data['username'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'nickname' => $data['nickname'] ?? $data['username'],
            'status' => 1, // 1表示启用
            'is_super' => 1, // 1表示超级管理员
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 插入管理员数据
        return \think\facade\Db::name('admin')->insert($admin);
    }
}