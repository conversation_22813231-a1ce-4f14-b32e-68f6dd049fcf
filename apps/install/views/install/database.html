{layout name="install/layout" /}

<div class="step-list">
    <div class="step-item">
        <span class="step-number">1</span>
        <div class="step-label">欢迎使用</div>
    </div>
    <div class="step-item">
        <span class="step-number">2</span>
        <div class="step-label">环境检查</div>
    </div>
    <div class="step-item active">
        <span class="step-number">3</span>
        <div class="step-label">数据库配置</div>
    </div>
    <div class="step-item">
        <span class="step-number">4</span>
        <div class="step-label">创建管理员</div>
    </div>
    <div class="step-item">
        <span class="step-number">5</span>
        <div class="step-label">安装完成</div>
    </div>
</div>
<div class="form-container">
    <form id="dbForm" method="post" action="/apps/example/install/steps?step=3">
        <div class="form-group">
            <label for="hostname">数据库服务器</label>
            <input type="text" class="form-control" id="hostname" name="hostname" value="localhost" required>
        </div>
        <div class="form-group">
            <label for="hostport">端口</label>
            <input type="number" class="form-control" id="hostport" name="hostport" value="3306" required>
        </div>
        <div class="form-group">
            <label for="database">数据库名</label>
            <input type="text" class="form-control" id="database" name="database" required>
        </div>
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" class="form-control" id="username" name="username" required>
        </div>
        <div class="form-group">
            <label for="password">密码</label>
            <input type="password" class="form-control" id="password" name="password" required>
        </div>
        <div class="form-group">
            <label for="prefix">数据表前缀</label>
            <input type="text" class="form-control" id="prefix" name="prefix" value="">
        </div>
        <div class="btn-container">
            <a href="/apps/example/install/steps?step=1" class="btn btn-secondary">上一步</a>
            <button type="submit" class="btn btn-primary">下一步</button>
        </div>
    </form>
</div>