<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{$title|default="安装向导"}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary-color: #4361ee;
            --text-white: #ffffff;
            --secondary-color: #6c757d;
            --text-color: #2d3748;
            --border-color: #e9ecef;
            --bg-color: #f8faff;
            --success-color: #2dd4bf;
            --warning-color: #fbbf24;
            --error-color: #ef4444;
            --card-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            --gradient-primary: linear-gradient(135deg, #4361ee 0%, #3b82f6 100%);
            --gradient-secondary: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
            --transition: all 0.3s ease;
            --input-focus-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
            --input-hover-border: #a3bffa;
            --step-active-shadow: 0 8px 16px rgba(67, 97, 238, 0.3);
            --step-hover-transform: translateY(-2px);
        }

        body {
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            margin: 0;
            background: var(--gradient-secondary);
            color: var(--text-color);
            min-height: 100vh;
        }

        .layout-container {
            display: flex;
            min-height: 100vh;
            margin: 0 auto;
            position: relative;
        }

        .sidebar {
            width: 300px;
            padding: 20px;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 100;
            transition: var(--transition);
            -webkit-overflow-scrolling: touch;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 40px);
            max-width: calc(100% - 320px);
            margin-left: 340px;
        }

        .content-wrapper {
            background: #fff;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            padding: 24px;
            flex: 1;
            width: 100%;
            box-sizing: border-box;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .welcome-content,
        .check-content {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            text-align: left;
            border-radius: 12px;
        }

        .check-content table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 12px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
        }

        .check-content th,
        .check-content td {
            padding: 16px;
            text-align: left;
            transition: var(--transition);
        }

        .check-content th {
            font-weight: 600;
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
            background: rgba(67, 97, 238, 0.05);
        }

        .check-content td {
            background: #ffffff;
            border-bottom: 1px solid var(--border-color);
        }

        .check-content tr:hover td {
            background: var(--bg-color);
        }

        .check-content td:last-child {
            color: var(--success-color);
            font-weight: 500;
            border-radius: 0 8px 8px 0;
        }

        .check-content td:first-child {
            border-radius: 8px 0 0 8px;
            font-weight: 500;
        }

        .form-container,
        .complete-content {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
            text-align: center;
            padding: 20px;
            box-sizing: border-box;
        }

        .btn-container {
            padding-top: 20px;
            text-align: center;
            width: 100%;
        }

        .step-list {
            display: flex;
            justify-content: space-between;
            margin: 0 0 40px;
            position: relative;
            padding: 0;
            list-style: none;
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            gap: 20px;
            padding: 10px 5px;
        }

        .step-list::before {
            content: '';
            position: absolute;
            top: 24px;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--border-color);
            border-radius: 3px;
            z-index: 1;
            opacity: 0.5;
            filter: blur(1px);
        }

        .step-list::after {
            content: '';
            position: absolute;
            top: 24px;
            left: 0;
            height: 3px;
            width: var(--progress, 0%);
            background: var(--gradient-primary);
            border-radius: 3px;
            z-index: 2;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(67, 97, 238, 0.25);
            animation: glow 1.5s ease-in-out infinite alternate;
        }

        .step-item {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            transition: var(--transition);
            cursor: pointer;
        }

        .step-item:hover {
            transform: var(--step-hover-transform);
        }

        .step-number {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #fff;
            border: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 12px;
            transition: var(--transition);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            font-size: 18px;
            position: relative;
            z-index: 3;
            backdrop-filter: blur(5px);
            background: rgba(255, 255, 255, 0.9);
        }

        .step-item.active .step-number {
            background: var(--gradient-primary);
            border-color: transparent;
            color: #fff;
            transform: scale(1.1);
            box-shadow: var(--step-active-shadow);
            animation: pulse 2s infinite;
            backdrop-filter: none;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
            }
        }

        @keyframes glow {
            from {
                box-shadow: 0 2px 8px rgba(67, 97, 238, 0.25);
            }
            to {
                box-shadow: 0 2px 12px rgba(67, 97, 238, 0.5);
            }
        }

        .step-label {
            font-size: 14px;
            color: var(--text-color);
            transition: var(--transition);
            opacity: 0.8;
        }

        .step-item.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
            opacity: 1;
        }

        .form-control {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            transition: var(--transition);
            font-size: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
            background: #ffffff;
            width: 100%;
            box-sizing: border-box;
        }

        .form-control:hover {
            border-color: var(--input-hover-border);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: var(--input-focus-shadow);
            outline: none;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-color);
            opacity: 0.9;
            display: block;
            transition: var(--transition);
        }

        .form-group:focus-within label {
            color: var(--primary-color);
            opacity: 1;
        }

        .btn {
            padding: 12px 24px;
            font-weight: 500;
            border-radius: 8px;
            transition: var(--transition);
            text-transform: none;
            letter-spacing: 0;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .btn:disabled,
        .btn.disabled {
            background: var(--border-color) !important;
            color: var(--secondary-color) !important;
            border-color: var(--border-color) !important;
            opacity: 0.65;
            cursor: not-allowed;
            pointer-events: none;
            box-shadow: none !important;
            transform: none !important;
        }

        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s ease, height 0.6s ease;
        }

        .btn:active:not(:disabled):not(.disabled)::after {
            width: 200px;
            height: 200px;
            opacity: 0;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            box-shadow: 0 4px 12px rgba(67, 97, 238, 0.25);
            color: var(--text-white);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(67, 97, 238, 0.35);
        }

        .btn-secondary {
            background: #fff;
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }

        .btn-secondary:hover {
            background: var(--bg-color);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .sidebar-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .feature-list li {
            margin-bottom: 15px;
            padding-left: 25px;
            position: relative;
        }

        .feature-list li:before {
            content: '✓';
            color: var(--success-color);
            position: absolute;
            left: 0;
            font-size: 16px;
            font-weight: bold;
        }

        .system-info {
            margin-top: 40px;
            padding: 20px;
            border-radius: 12px;
            background: rgba(67, 97, 238, 0.05);
            font-size: 14px;
            color: var(--secondary-color);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .footer {
            text-align: center;
            padding: 20px 0;
            color: var(--secondary-color);
            font-size: 14px;
            border-top: 1px solid var(--border-color);
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .layout-container {
                flex-direction: column;
                padding: 0;
                overflow-x: hidden;
                width: 100%;
            }

            .form-container {
                padding: 15px;
            }

            .form-group {
                width: 100%;
                margin-bottom: 20px;
            }

            .form-control {
                width: 100%;
                max-width: none;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding: 15px;
                margin-bottom: 15px;
                backdrop-filter: none;
                box-sizing: border-box;
            }

            .main-content {
                padding: 15px;
                margin-left: 0;
                max-width: 100%;
                width: 100%;
                box-sizing: border-box;
            }

            .content-wrapper {
                padding: 16px;
                border-radius: 12px;
                width: 100%;
                box-sizing: border-box;
                overflow-x: hidden;
            }

            .step-list {
                padding: 10px 5px;
                margin-bottom: 20px;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                scroll-padding: 10px;
                gap: 10px;
                width: 100%;
                box-sizing: border-box;
            }

            .step-item {
                min-width: 100px;
                padding: 0 5px;
                flex-shrink: 0;
            }

            .step-label {
                font-size: 12px;
                white-space: nowrap;
            }
            
            .feature-list {
                text-align: left;
                padding: 0 10px;
                width: 100%;
                box-sizing: border-box;
            }

            .system-info {
                width: 100%;
                margin: 20px 0;
                padding: 15px;
                text-align: left;
                box-sizing: border-box;
            }

            .welcome-content,
            .check-content,
            .form-container,
            .complete-content {
                width: 100%;
                padding: 0 10px;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="layout-container">
        <div class="sidebar">
            <h2 class="sidebar-title">系统介绍</h2>
            <p>欢迎使用我们的安装向导，本系统具有以下特点：</p>
            <ul class="feature-list">
                <li>简单易用的界面设计</li>
                <li>完整的功能模块</li>
                <li>灵活的扩展机制</li>
                <li>强大的性能表现</li>
                <li>安全可靠的架构</li>
            </ul>
            <div class="system-info">
                <p>Version: 1.0.0</p>
                <p>© {$year|default="2024"} Example Plugin</p>
            </div>
        </div>
        <div class="main-content">
            <div class="content-wrapper">
                {__CONTENT__}
            </div>
        </div>
    </div>
</body>
</html>
