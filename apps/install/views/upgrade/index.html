<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统升级</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <style>
        .upgrade-box { max-width: 800px; margin: 20px auto; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .version-info { margin: 30px 0; }
        .version-item { margin-bottom: 15px; }
        .version-label { font-weight: bold; margin-right: 10px; }
        .upgrade-status { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .status-checking { background-color: #fff3cd; }
        .status-latest { background-color: #d4edda; }
        .status-need-upgrade { background-color: #cce5ff; }
        .btn-container { text-align: center; margin-top: 30px; }
        .loading { display: none; }
        .loading-text { margin-left: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="upgrade-box">
            <h2 class="text-center">系统升级</h2>

            <div class="version-info">
                <div class="version-item">
                    <span class="version-label">当前版本：</span>
                    <span>{$current_version}</span>
                </div>
                <div class="version-item">
                    <span class="version-label">最新版本：</span>
                    <span>{$latest_version}</span>
                </div>
            </div>

            <div id="upgradeStatus" class="upgrade-status {$need_upgrade ? 'status-need-upgrade' : 'status-latest'}">
                {if condition="$need_upgrade"}
                <p>发现新版本，建议立即升级！</p>
                {else/}
                <p>当前已是最新版本！</p>
                {/if}
            </div>

            <div class="btn-container">
                <button type="button" class="btn btn-primary" onclick="checkVersion()">
                    <span class="loading">
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        <span class="loading-text">检查中...</span>
                    </span>
                    <span class="normal-text">检查更新</span>
                </button>
                
                {if condition="$need_upgrade"}
                <button type="button" class="btn btn-success" onclick="startUpgrade()">
                    <span class="loading">
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        <span class="loading-text">升级中...</span>
                    </span>
                    <span class="normal-text">立即升级</span>
                </button>
                {/if}
            </div>
        </div>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.min.js"></script>
    <script>
        function checkVersion() {
            const btn = $('[onclick="checkVersion()"]');
            btn.prop('disabled', true);
            btn.find('.loading').show();
            btn.find('.normal-text').hide();

            $.ajax({
                url: '/example/upgrade/check',
                type: 'GET',
                dataType: 'json',
                success: function(res) {
                    if (res.code === 1) {
                        const data = res.data;
                        $('.version-info .version-item:eq(1) span:eq(1)').text(data.latest_version);
                        
                        const status = $('#upgradeStatus');
                        if (data.need_upgrade) {
                            status.removeClass('status-latest').addClass('status-need-upgrade')
                                  .html('<p>发现新版本，建议立即升级！</p>');
                            if ($('[onclick="startUpgrade()"]').length === 0) {
                                $('.btn-container').append(`
                                    <button type="button" class="btn btn-success" onclick="startUpgrade()">
                                        <span class="loading">
                                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span class="loading-text">升级中...</span>
                                        </span>
                                        <span class="normal-text">立即升级</span>
                                    </button>
                                `);
                            }
                        } else {
                            status.removeClass('status-need-upgrade').addClass('status-latest')
                                  .html('<p>当前已是最新版本！</p>');
                            $('[onclick="startUpgrade()"]').remove();
                        }
                    } else {
                        alert(res.msg);
                    }
                },
                error: function() {
                    alert('检查更新失败，请稍后重试');
                },
                complete: function() {
                    btn.prop('disabled', false);
                    btn.find('.loading').hide();
                    btn.find('.normal-text').show();
                }
            });
        }

        function startUpgrade() {
            if (!confirm('升级前请确保已备份重要数据，是否继续？')) {
                return;
            }

            const btn = $('[onclick="startUpgrade()"]');
            btn.prop('disabled', true);
            btn.find('.loading').show();
            btn.find('.normal-text').hide();

            $.ajax({
                url: '/example/upgrade/upgrade',
                type: 'POST',
                dataType: 'json',
                success: function(res) {
                    if (res.code === 1) {
                        alert('升级成功！');
                        location.reload();
                    } else {
                        alert(res.msg);
                    }
                },
                error: function() {
                    alert('升级失败，请稍后重试');
                },
                complete: function() {
                    btn.prop('disabled', false);
                    btn.find('.loading').hide();
                    btn.find('.normal-text').show();
                }
            });
        }
    </script>
</body>
</html>