<?php

return [
    // 路由别名
    'aliases' => [
        // 管理员路由组
        'admin' => [
            'prefix' => 'admin',
            'middleware' => []
        ],
        // API路由组
        'api' => [
            'prefix' => 'api',
            'middleware' => []
        ]
    ],
    // 路由分组
    'group' => [
        'prefix' => 'install',
        'middleware' => [],
        'routes' => [
            // API文档
            'steps' => 'Install/index',
        ]
    ],

    // 独立路由
    'routes' => []
];