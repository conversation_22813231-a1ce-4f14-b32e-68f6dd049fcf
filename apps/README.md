# 插件系统使用说明

## 概述

插件系统提供了一个灵活的后端扩展机制，允许开发者通过插件的方式扩展系统功能。每个插件都是独立的功能模块，可以包含自己的配置、路由、钩子和业务逻辑。

## 配置说明

### manifest.json 配置

在插件的 `manifest.json` 文件中，需要配置以下信息：

```json
{
    "name": "plugin_name",        // 插件标识，必须唯一
    "title": "插件标题",          // 插件显示名称
    "description": "插件描述",    // 插件功能描述
    "version": "1.0.0",          // 插件版本号
    "author": "作者名称",         // 插件作者
    "status": 1,                  // 插件状态：1-启用，0-禁用
    "dependencies": {             // 依赖的其他插件
        "example": "^1.0.0"      // 插件名称及版本要求
    },
    "hooks": {                    // 钩子配置
        "hook.name": {           // 钩子标识
            "description": "钩子描述",
            "handler": "\\App\\Hook\\ExampleHook",  // 钩子处理类
            "method": "handle",    // 处理方法名
            "params": {           // 钩子参数说明
                "param1": "参数1说明",
                "param2": "参数2说明"
            }
        }
    },
    "languages": {                // 语言包配置
        "zh-cn": "简体中文",      // 语言标识及名称
        "en-us": "English"       // 支持多语言配置
    },
    "config": {                   // 插件配置项
        "enabled": true,         // 是否启用
        "settings": {            // 基础设置
            "option1": "value1"
        },
        "features": {            // 功能配置
            "enable_cache": true
        }
    },
    "global_startup": {          // 全局启动配置
        "services": [            // 需要启动的服务类
            "\\App\\Service\\ExampleService"
        ],
        "commands": [            // 需要注册的命令
            "\\App\\Command\\ExampleCommand"
        ]
    }
}
```

## 目录结构

插件的标准目录结构如下：

```
apps/your_plugin/
├── App.php           # 插件主类文件
├── composer.json      # Composer 配置文件
├── hooks.php         # 钩子定义和实现
├── route.php         # 路由配置文件
├── manifest.json     # 插件配置文件
├── install.sql       # 安装SQL文件
├── uninstall.sql     # 卸载SQL文件
├── upgrade/          # 升级脚本目录
├── data/             # 数据文件目录
├── lang/             # 语言包目录
│   ├── zh-cn.php     # 简体中文语言包
│   └── en-us.php     # 英文语言包
├── views/            # 视图文件目录
└── src/              # 源码目录
    ├── controller/   # 控制器目录
    ├── model/        # 模型目录
    ├── service/      # 服务类目录
    └── lang/         # 模块语言包目录
```

## 开发流程

### 1. 创建插件

1. 在 `apps` 目录下创建插件目录
2. 创建并配置 `manifest.json`
3. 实现插件主类 `App.php`
4. 根据需要添加路由和钩子

### 2. 插件主类

插件主类需要实现以下方法：

```php
class App
{
    // 插件安装
    public function install()
    {
        return true;
    }

    // 插件卸载
    public function uninstall()
    {
        return true;
    }

    // 插件启用
    public function enable()
    {
        return true;
    }

    // 插件禁用
    public function disable()
    {
        return true;
    }
}
```

### 3. 路由配置

在 `route.php` 中配置插件路由：

```php
<?php

return [
    // 路由分组
    'group' => [
        'prefix' => 'example',
        'middleware' => [],
        'routes' => [
            // 简单路由定义
            'index' => 'Example/index',
            'test' => 'Example/test',
            
            // 详细路由定义
            [
                'method' => 'get',
                'rule' => 'config',
                'controller' => 'Example',
                'action' => 'config',
                'middleware' => ['admin']
            ],
            [
                'method' => 'post',
                'rule' => 'save',
                'controller' => 'Example',
                'action' => 'save',
                'middleware' => ['admin']
            ]
        ]
    ],
    
    // 独立路由
    'routes' => [
        // 简单路由定义
        'api/example/info' => 'Example/info',
        
        // 详细路由定义
        [
            'method' => 'get',
            'rule' => 'api/example/data',
            'controller' => 'Example',
            'action' => 'data',
            'middleware' => []
        ]
    ]
]; 
```

### 4. 钩子使用

在 `hooks.php` 中实现钩子：

```php
namespace App\Hook;

class ExampleHook
{
    public function handle($params)
    {
        // 钩子处理逻辑
        return true;
    }
}
```

### 5. 语言包使用

在 `lang` 目录下创建对应的语言包文件：

```php
// lang/zh-cn.php
return [
    'welcome' => '欢迎使用',
    'hello' => '你好，{name}'
];

// lang/en-us.php
return [
    'welcome' => 'Welcome',
    'hello' => 'Hello, {name}'
];
```

在代码中使用语言包：

```php
// 获取当前语言
$currentLang = $this->getCurrentLanguage();

// 翻译文本
$welcome = $this->trans('welcome');
$hello = $this->trans('hello', ['name' => 'World']);

// 切换语言
$this->setLanguage('en-us');
```

## 路由访问

- 后台管理界面: `/admin/apps/{plugin_name}/...`
- 前台访问接口: `/api/apps/{plugin_name}/...`

## 开发建议

1. 遵循PSR-4自动加载规范
2. 合理使用依赖注入和服务容器
3. 保持代码的清晰和可维护性
4. 编写完整的文档和注释
5. 做好错误处理和异常捕获
6. 遵循系统的编码规范

## 示例

请参考 `apps/example` 目录下的示例插件，了解基本的插件开发方式。该示例展示了：

- 基础配置的编写
- 路由的定义方式
- 钩子的使用方法
- 配置项的管理
- 依赖关系的处理
- 多语言支持的实现