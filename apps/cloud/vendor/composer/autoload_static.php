<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit6cef6cad295d04660ce3eb9e3dea80a5
{
    public static $prefixLengthsPsr4 = array (
        'a' => 
        array (
            'apps\\cloud\\' => 11,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'apps\\cloud\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit6cef6cad295d04660ce3eb9e3dea80a5::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit6cef6cad295d04660ce3eb9e3dea80a5::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit6cef6cad295d04660ce3eb9e3dea80a5::$classMap;

        }, null, ClassLoader::class);
    }
}
