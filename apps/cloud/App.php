<?php

namespace apps\cloud;

use app\plugin\core\BasePlugin;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

/**
 * 高级示例插件 - 展示完整的插件系统功能
 */
class App extends BasePlugin
{
    /**
     * 初始化插件
     */
    protected function init(): void
    {
        parent::init();
        
        // 注册系统初始化钩子
        $this->addHook('system.init', function($params) {
            $this->initializePlugin();
        });
        
        // 注册用户注册事件的监听器
        $this->addHook('user.register', function($params) {
            $this->handleUserRegistration($params);
        });
        
        // 注册订单支付完成的监听器
        $this->addHook('order.paid', function($params) {
            $this->handleOrderPaid($params);
        });
    }
    
    /**
     * 插件初始化逻辑
     */
    protected function initializePlugin(): void
    {
        try {
            // 加载配置
            $config = $this->getConfig();
            
            // 初始化缓存
            if ($config['features']['cache_enabled']) {
                $this->initCache();
            }
            
            // 初始化调试模式
            if ($config['features']['debug_mode']) {
                $this->enableDebugMode();
            }
            
            Log::info('Advanced example plugin initialized successfully');
        } catch (\Exception $e) {
            Log::error('Failed to initialize advanced example plugin: ' . $e->getMessage());
        }
    }
    
    /**
     * 处理用户注册事件
     */
    protected function handleUserRegistration(array $params): void
    {
        try {
            // 记录用户注册数据
            $this->logUserActivity('registration', $params);
            
            // 发送欢迎通知
            if ($this->getConfig('features.enable_notification')) {
                $this->sendWelcomeNotification($params);
            }
        } catch (\Exception $e) {
            Log::error('Failed to handle user registration: ' . $e->getMessage());
        }
    }
    
    /**
     * 处理订单支付完成事件
     */
    protected function handleOrderPaid(array $params): void
    {
        try {
            // 记录订单支付数据
            $this->logOrderActivity('payment_completed', $params);
            
            // 更新统计数据
            $this->updateStatistics($params);
            
            // 发送支付成功通知
            if ($this->getConfig('features.enable_notification')) {
                $this->sendPaymentNotification($params);
            }
        } catch (\Exception $e) {
            Log::error('Failed to handle order payment: ' . $e->getMessage());
        }
    }
    
    /**
     * 记录用户活动
     */
    protected function logUserActivity(string $type, array $data): void
    {
        Db::table('plugin_advanced_example_logs')->insert([
            'type' => $type,
            'data' => json_encode($data),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 记录订单活动
     */
    protected function logOrderActivity(string $type, array $data): void
    {
        Db::table('plugin_advanced_example_logs')->insert([
            'type' => $type,
            'data' => json_encode($data),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 更新统计数据
     */
    protected function updateStatistics(array $params): void
    {
        $cacheKey = 'advanced_example_stats';
        $stats = Cache::get($cacheKey, []);
        
        // 更新统计数据
        $stats['total_orders'] = ($stats['total_orders'] ?? 0) + 1;
        $stats['total_amount'] = ($stats['total_amount'] ?? 0) + $params['amount'];
        
        // 保存统计数据
        Cache::set($cacheKey, $stats, $this->getConfig('features.cache_ttl'));
    }
    
    /**
     * 发送欢迎通知
     */
    protected function sendWelcomeNotification(array $params): void
    {
        $notification = [
            'type' => 'welcome',
            'to_user_id' => $params['user_id'],
            'title' => '欢迎加入',
            'content' => "尊敬的{$params['username']}，欢迎您注册成为我们的会员！"
        ];
        
        $this->sendNotification($notification);
    }
    
    /**
     * 发送支付通知
     */
    protected function sendPaymentNotification(array $params): void
    {
        $notification = [
            'type' => 'payment_success',
            'to_user_id' => $params['user_id'],
            'title' => '支付成功通知',
            'content' => "您的订单 {$params['order_id']} 已支付成功，金额：{$params['amount']}元"
        ];
        
        $this->sendNotification($notification);
    }
    
    /**
     * 发送通知
     */
    protected function sendNotification(array $params): void
    {
        foreach ($this->getConfig('features.notification_channels') as $channel) {
            try {
                // 根据不同渠道发送通知
                switch ($channel) {
                    case 'email':
                        // 实现邮件发送逻辑
                        break;
                    case 'sms':
                        // 实现短信发送逻辑
                        break;
                    case 'wechat':
                        // 实现微信通知逻辑
                        break;
                }
            } catch (\Exception $e) {
                Log::error("Failed to send notification via {$channel}: " . $e->getMessage());
            }
        }
    }
}