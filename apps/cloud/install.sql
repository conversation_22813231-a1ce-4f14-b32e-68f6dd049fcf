-- 创建插件日志表
CREATE TABLE IF NOT EXISTS `plugin_cloud_logs` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `type` varchar(50) NOT NULL COMMENT '日志类型',
    `data` json DEFAULT NULL COMMENT '日志数据',
    `created_at` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='高级示例插件日志表';

-- 创建插件统计表
CREATE TABLE IF NOT EXISTS `plugin_cloud_statistics` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `metric` varchar(50) NOT NULL COMMENT '统计指标',
    `value` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '统计值',
    `period` varchar(20) NOT NULL COMMENT '统计周期',
    `updated_at` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_metric_period` (`metric`, `period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='高级示例插件统计表';

-- 创建插件配置表
CREATE TABLE IF NOT EXISTS `plugin_cloud_settings` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `key` varchar(100) NOT NULL COMMENT '配置键',
    `value` text COMMENT '配置值',
    `updated_at` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='高级示例插件配置表';