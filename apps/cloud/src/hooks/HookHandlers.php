<?php

namespace apps\cloud\src\hooks;

use think\facade\Log;

/**
 * API插件钩子处理类
 */
class HookHandlers
{
    /**
     * 系统初始化钩子处理方法
     */
    public function onSystemInit(): void
    {
        // 在系统初始化时执行的逻辑
        // 例如：初始化API配置、加载路由等
        dump(11);
    }

    /**
     * API错误钩子处理方法
     * @param array $params 钩子参数
     */
    public function onApiError(array $params): void
    {
        // 记录API错误日志
        Log::error('API Error:', [
            'api_path' => $params['api_path'] ?? '',
            'method' => $params['method'] ?? '',
            'params' => $params['params'] ?? [],
            'error' => $params['error'] ?? '',
            'app_id' => $params['app_id'] ?? ''
        ]);
    }
}