<?php

return [
    // 用户注册事件
    'user.register' => [
        'description' => '用户注册后的处理',
        'handler' => function($params) {
            // 处理逻辑在Plugin.php中实现
            return true;
        }
    ],
    
    // 订单支付完成事件
    'order.paid' => [
        'description' => '订单支付完成后的处理',
        'handler' => function($params) {
            // 处理逻辑在Plugin.php中实现
            return true;
        }
    ],
    
    // 系统初始化事件
    'system.init' => [
        'description' => '系统初始化时的处理',
        'handler' => function($params) {
            // 处理逻辑在Plugin.php中实现
            return true;
        }
    ]
];