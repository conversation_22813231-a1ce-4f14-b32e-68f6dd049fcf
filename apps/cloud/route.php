<?php

return [
    // API路由组
    'group' => [
        'prefix' => 'api/plugin/advanced-example',
        'middleware' => [],
        'routes' => [
            // 统计数据接口
            [
                'method' => 'get',
                'rule' => 'statistics',
                'controller' => 'StatisticsController',
                'action' => 'index'
            ],
            [
                'method' => 'get',
                'rule' => 'statistics/:period',
                'controller' => 'StatisticsController',
                'action' => 'getByPeriod'
            ],
        ]
    ],
    
    // 管理页面路由组
    'admin' => [
        'prefix' => 'admin/plugin/advanced-example',
        'middleware' => [],
        'routes' => [
            // 仪表盘页面
            [
                'method' => 'get',
                'rule' => 'dashboard',
                'controller' => 'AdminController',
                'action' => 'dashboard'
            ],
        ]
    ]
];