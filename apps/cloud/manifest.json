{"name": "cloud", "title": "apidisk云计算", "description": "apidisk云计算提供云服务器，虚拟主机，云虚拟主机，ssl证书，edm邮件等功能在线开通", "version": "1.0.0", "author": "Apidisk Team", "status": 1, "dependencies": [], "hooks": {"user.register": {"description": "用户注册后的钩子", "params": {"user_id": "用户ID", "username": "用户名", "register_time": "注册时间"}, "handler": "\\apps\\api\\src\\hooks\\HookHandlers", "method": "onSystemInit"}, "order.paid": {"description": "订单支付完成后的钩子", "params": {"order_id": "订单ID", "user_id": "用户ID", "amount": "支付金额", "payment_method": "支付方式"}, "handler": "\\apps\\api\\src\\hooks\\HookHandlers", "method": "onSystemInit"}, "system.init": {"description": "系统初始化钩子", "params": [], "handler": "\\apps\\api\\src\\hooks\\HookHandlers", "method": "onSystemInit"}}, "config": {"enabled": true, "global_startup": {"title": "全局启动", "description": "是否在系统启动时自动加载插件", "type": "switch", "value": false, "tip": "开启后，插件将在系统启动时自动加载"}, "settings": {"title": "高级示例插件", "tip": "展示完整的插件系统功能和最佳实践", "description": "展示完整的插件系统功能和最佳实践", "type": "text", "value": "Anchor Team"}}}