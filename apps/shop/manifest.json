{"name": "shop", "title": "商城shop系统", "description": "商城shop系统", "version": "1.0.0", "author": "System", "email": "<EMAIL>", "website": "https://www.apidisk.com", "license": "MIT", "dependencies": [], "config": {"enabled": true, "global_startup": {"title": "全局启动", "description": "是否在系统启动时自动加载插件", "type": "switch", "value": false, "tip": "开启后，插件将在系统启动时自动加载"}, "settings": {"title": "示例插件", "type": "text", "value": "", "tip": "七牛云AccessKey，在七牛云控制台查看"}}}