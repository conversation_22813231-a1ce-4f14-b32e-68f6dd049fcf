<?php

namespace apps\shop;

use app\plugin\core\BasePlugin;

class App extends BasePlugin
{
    /**
     * 初始化
     */
    protected function init(): void
    {
        parent::init();

    }

    /**
     * 插件安装方法
     */
    public function install(): bool
    {
        // 调用父类安装方法
        if (!parent::install()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 插件卸载方法
     */
    public function uninstall(): bool
    {
        // 调用父类卸载方法
        if (!parent::uninstall()) {
            return false;
        }
        
        return true;
    }
}