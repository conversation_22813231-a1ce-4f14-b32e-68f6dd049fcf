<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit077ffeb5e4ec019728a11103487e49f3
{
    public static $prefixLengthsPsr4 = array (
        'a' => 
        array (
            'apps\\shop\\' => 10,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'apps\\shop\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit077ffeb5e4ec019728a11103487e49f3::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit077ffeb5e4ec019728a11103487e49f3::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit077ffeb5e4ec019728a11103487e49f3::$classMap;

        }, null, ClassLoader::class);
    }
}
