{"name": "example", "title": "这是一个示例插件", "description": "这是一个示例插件 - 升级到1.1.0版本，增加了新的配置功能", "version": "1.1.0", "author": "System", "status": 1, "dependencies": [], "hooks": {"system.init": {"description": "系统初始化钩子", "params": [], "handler": "\\apps\\api\\src\\hooks\\HookHandlers", "method": "onSystemInit"}}, "config": {"enabled": true, "global_startup": {"title": "全局启动", "description": "是否在系统启动时自动加载插件", "type": "switch", "value": false, "tip": "开启后，插件将在系统启动时自动加载"}, "settings": {"title": "示例插件", "type": "text", "value": "", "tip": "七牛云AccessKey，在七牛云控制台查看"}, "notification": {"title": "通知设置", "description": "配置通知相关的设置", "type": "group", "children": {"notification_enabled": {"title": "启用通知", "description": "是否启用商品评论通知功能", "type": "switch", "value": true}, "notification_template": {"title": "通知模板", "description": "商品评论通知的内容模板", "type": "textarea", "value": "您的商品「{goods_name}」收到了新的评论：{comment_content}"}}}, "version": "1.1.0"}, "upgrade": {"minimum_version": "1.0.0", "backup": true, "source": "local", "scripts": {"pre_upgrade": "upgrade/pre_upgrade.php", "post_upgrade": "upgrade/post_upgrade.php"}}}