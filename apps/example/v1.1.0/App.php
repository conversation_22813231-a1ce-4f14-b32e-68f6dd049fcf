<?php

namespace apps\example;

use app\plugin\core\BasePlugin;
use app\plugin\core\SqlHelper;
use think\facade\Db;

/**
 * 示例插件 - 1.1.0版本
 */
class App extends BasePlugin
{
    /**
     * 初始化
     */
    protected function init(): void
    {
        parent::init();
        
        // 注册系统初始化钩子
        $this->addHook('system.init', function($params) {
            // 系统初始化时的处理逻辑
//            $this->loadSettings();
        });
        
        // 注册商品评论创建事件的监听器
        $this->addHook('comment.created', function($params) {
            // 检查是否启用通知
            if (!$this->getSetting('notification_enabled', true)) {
                return;
            }
            
            // 获取评论相关信息
            $commentId = $params['comment_id'];
            $goodsId = $params['goods_id'];
            $userId = $params['user_id'];
            $content = $params['content'];
            
            // 获取商品信息
            $goods = \app\model\Goods::find($goodsId);
            if (!$goods) {
                return;
            }
            
            // 获取通知模板
            $template = $this->getSetting(
                'notification_template', 
                '您的商品「{goods_name}」收到了新的评论：{comment_content}'
            );
            
            // 替换模板变量
            $content = str_replace(
                ['{goods_name}', '{comment_content}'],
                [$goods->name, $content],
                $template
            );
            
            // 构建通知内容
            $notificationParams = [
                'type' => 'comment_notification',
                'to_user_id' => $goods->user_id, // 通知商品所有者
                'title' => '新的商品评论',
                'content' => $content
            ];
            
            // 触发通知发送钩子
            $this->doHook('notification.send', $notificationParams);
            
            // 记录通知历史
            $this->logNotification($notificationParams);
        });
        
        // 注册通知发送的处理器
        $this->addHook('notification.send', function($params) {
            // 这里实现具体的通知发送逻辑
            // 可以是站内信、邮件、短信等多种方式
        });
    }
    
    /**
     * 记录通知历史
     */
    protected function logNotification(array $params): void
    {
        try {
            Db::table('plugin_example_data')->insert([
                'type' => 'notification',
                'data' => json_encode($params),
                'extra_data' => json_encode([
                    'time' => date('Y-m-d H:i:s'),
                    'version' => $this->version
                ]),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('Failed to log notification: ' . $e->getMessage());
        }
    }
    
    /**
     * 从数据库加载设置
     */
    protected function loadSettings(): void
    {
        try {
            $settings = Db::table('plugin_example_settings')
                ->select()
                ->toArray();
            
            foreach ($settings as $setting) {
                $this->settings[$setting['key']] = $setting['value'];
            }
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('Failed to load settings: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取设置值
     */
    protected function getSetting(string $key, $default = null)
    {
        return $this->settings[$key] ?? $default;
    }
    
    /**
     * 保存设置值
     */
    protected function saveSetting(string $key, $value): bool
    {
        try {
            Db::table('plugin_example_settings')
                ->where('key', $key)
                ->update(['value' => $value]);
            
            $this->settings[$key] = $value;
            return true;
        } catch (\Exception $e) {
            \think\facade\Log::error('Failed to save setting: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 插件安装方法
     */
    public function install(): bool
    {
        // 调用父类安装方法
        if (!parent::install()) {
            return false;
        }
        
        // 创建插件数据表
        $sql = file_get_contents(__DIR__ . '/install.sql');
        SqlHelper::parser($sql);
        return true;
    }
    
    /**
     * 插件卸载方法
     */
    public function uninstall(): bool
    {
        // 调用父类卸载方法
        if (!parent::uninstall()) {
            return false;
        }
        
        // 删除插件数据表
        $sql = file_get_contents(__DIR__ . '/uninstall.sql');
        SqlHelper::parser($sql);
        
        return true;
    }
}