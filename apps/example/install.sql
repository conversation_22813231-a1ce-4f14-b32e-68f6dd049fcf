-- 示例插件安装脚本 (1.1.0版本)

-- 创建插件数据表
CREATE TABLE IF NOT EXISTS `plugin_example_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '数据类型',
  `data` text COMMENT '数据内容',
  `extra_data` text COMMENT '额外数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例插件数据表';

-- 创建插件配置表
CREATE TABLE IF NOT EXISTS `plugin_example_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(50) NOT NULL COMMENT '配置键名',
  `value` text COMMENT '配置值',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例插件配置表';

-- 插入默认配置
INSERT IGNORE INTO `plugin_example_settings` (`key`, `value`) VALUES
('notification_enabled', '1'),
('notification_template', '您的商品「{goods_name}」收到了新的评论：{comment_content}');