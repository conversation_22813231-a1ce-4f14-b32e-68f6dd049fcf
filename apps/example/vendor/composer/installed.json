{"packages": [{"name": "psr/simple-cache", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.0"}, "time": "2021-10-29T13:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "install-path": "../psr/simple-cache"}, {"name": "topthink/think-template", "version": "v3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-template.git", "reference": "0b88bd449f0f7626dd75b05f557c8bc208c08b0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-template/zipball/0b88bd449f0f7626dd75b05f557c8bc208c08b0c", "reference": "0b88bd449f0f7626dd75b05f557c8bc208c08b0c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.0", "psr/simple-cache": ">=1.0"}, "time": "2024-10-16T03:41:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the php template engine", "support": {"issues": "https://github.com/top-think/think-template/issues", "source": "https://github.com/top-think/think-template/tree/v3.0.2"}, "install-path": "../topthink/think-template"}, {"name": "topthink/think-view", "version": "v2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-view.git", "reference": "d2a076011c96d2edd8016703a827fb54b2683c62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-view/zipball/d2a076011c96d2edd8016703a827fb54b2683c62", "reference": "d2a076011c96d2edd8016703a827fb54b2683c62", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.0", "topthink/think-template": "^3.0"}, "time": "2023-02-25T12:18:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\view\\driver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp template driver", "support": {"issues": "https://github.com/top-think/think-view/issues", "source": "https://github.com/top-think/think-view/tree/v2.0.0"}, "install-path": "../topthink/think-view"}], "dev": true, "dev-package-names": []}