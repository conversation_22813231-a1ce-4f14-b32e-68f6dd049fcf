<?php return array(
    'root' => array(
        'name' => 'apidisk/example',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'e50af7a1b3abd790666ad47b20c0b64f0e31f357',
        'type' => 'apidisk-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'apidisk/example' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'e50af7a1b3abd790666ad47b20c0b64f0e31f357',
            'type' => 'apidisk-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-template' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '0b88bd449f0f7626dd75b05f557c8bc208c08b0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-view' => array(
            'pretty_version' => 'v2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'd2a076011c96d2edd8016703a827fb54b2683c62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
