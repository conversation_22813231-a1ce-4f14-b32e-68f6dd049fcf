<?php

namespace apps\example\controller;

class Upgrade
{
    protected $currentVersion;
    protected $latestVersion;
    protected $upgradeFiles = [];
    protected $backupDir;

    public function __construct()
    {
        $this->currentVersion = $this->getCurrentVersion();
        $this->backupDir = root_path() . 'apps/backups/';
    }

    public function index()
    {
        $this->checkLatestVersion();
        
        return view('upgrade/index', [
            'current_version' => $this->currentVersion,
            'latest_version' => $this->latestVersion,
            'need_upgrade' => version_compare($this->latestVersion, $this->currentVersion, '>')
        ]);
    }

    public function check()
    {
        try {
            $this->checkLatestVersion();
            $needUpgrade = version_compare($this->latestVersion, $this->currentVersion, '>');
            
            return json([
                'code' => 1,
                'data' => [
                    'current_version' => $this->currentVersion,
                    'latest_version' => $this->latestVersion,
                    'need_upgrade' => $needUpgrade
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    public function upgrade()
    {
        try {
            // 检查是否需要升级
            if (version_compare($this->latestVersion, $this->currentVersion, '<=')) {
                throw new \Exception('当前已是最新版本');
            }

            // 创建备份
            $backupPath = $this->createBackup();
            
            // 下载并解压升级包
            $this->downloadAndExtractPackage();
            
            // 执行升级脚本
            $this->runUpgradeScripts();
            
            // 更新版本号
            $this->updateVersion();

            return json(['code' => 1, 'msg' => '升级成功']);
        } catch (\Exception $e) {
            // 升级失败时恢复备份
            if (isset($backupPath)) {
                $this->restoreBackup($backupPath);
            }
            return json(['code' => 0, 'msg' => '升级失败：' . $e->getMessage()]);
        }
    }

    protected function getCurrentVersion()
    {
        $manifestFile = root_path() . 'apps/example/manifest.json';
        if (!file_exists($manifestFile)) {
            throw new \Exception('无法获取当前版本信息');
        }
        $manifest = json_decode(file_get_contents($manifestFile), true);
        return $manifest['version'] ?? '1.0.0';
    }

    protected function checkLatestVersion()
    {
        // 这里模拟从远程服务器获取最新版本信息
        // 实际应用中应该从真实的更新服务器获取
        $this->latestVersion = '1.1.0';
        $this->upgradeFiles = [
            'apps/example/src/controller/Examples.php',
            'apps/example/views/example/index.html'
        ];
    }

    protected function createBackup()
    {
        $backupDir = $this->backupDir . 'example_' . $this->currentVersion . '_' . date('YmdHis') . '/';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }

        // 备份需要更新的文件
        foreach ($this->upgradeFiles as $file) {
            $sourcePath = root_path() . $file;
            $targetPath = $backupDir . $file;
            
            if (file_exists($sourcePath)) {
                // 创建目标目录
                $targetDir = dirname($targetPath);
                if (!is_dir($targetDir)) {
                    mkdir($targetDir, 0755, true);
                }
                
                // 复制文件
                copy($sourcePath, $targetPath);
            }
        }

        return $backupDir;
    }

    protected function downloadAndExtractPackage()
    {
        // 这里模拟下载和解压升级包的过程
        // 实际应用中需要实现真实的下载和解压逻辑
        $upgradeDir = root_path() . 'apps/example/upgrade/v' . $this->latestVersion . '/';
        
        if (!is_dir($upgradeDir)) {
            throw new \Exception('升级包不存在');
        }

        // 复制升级文件到对应位置
        foreach ($this->upgradeFiles as $file) {
            $sourcePath = $upgradeDir . $file;
            $targetPath = root_path() . $file;
            
            if (file_exists($sourcePath)) {
                // 创建目标目录
                $targetDir = dirname($targetPath);
                if (!is_dir($targetDir)) {
                    mkdir($targetDir, 0755, true);
                }
                
                // 复制文件
                copy($sourcePath, $targetPath);
            }
        }
    }

    protected function runUpgradeScripts()
    {
        $scriptFile = root_path() . 'apps/example/upgrade/v' . $this->latestVersion . '/upgrade.php';
        if (file_exists($scriptFile)) {
            require_once $scriptFile;
        }
    }

    protected function updateVersion()
    {
        $manifestFile = root_path() . 'apps/example/manifest.json';
        $manifest = json_decode(file_get_contents($manifestFile), true);
        $manifest['version'] = $this->latestVersion;
        file_put_contents($manifestFile, json_encode($manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    protected function restoreBackup($backupPath)
    {
        foreach ($this->upgradeFiles as $file) {
            $sourcePath = $backupPath . $file;
            $targetPath = root_path() . $file;
            
            if (file_exists($sourcePath)) {
                copy($sourcePath, $targetPath);
            }
        }
    }
}