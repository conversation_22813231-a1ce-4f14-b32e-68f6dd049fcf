<?php

namespace apps\example\api;

use <PERSON>iu\Auth;
use think\Request;

// 确保能正确引用 SDK 类

class Url
{
    public function info()
    {
        // 测试 SDK 类是否能正常加载
        $auth = new Auth('access_key', 'secret_key');
        dump($auth);
    }

    /**
     * 上传文件
     */
    public function upload(Request $request)
    {
        $plugin = plugin_instance('qiniu_upload');
        $file = $request->file('file');
        $savePath = $request->param('save_path', 'uploads');
        $options = $request->param('options/a', []);

        $upload = $plugin->uploadService();
        $result = $upload->upload($file, $savePath, $options);

        if (empty($result)) {
            return json(['code' => 1, 'msg' => $upload->getError()]);
        }

        return json(['code' => 0, 'msg' => '上传成功', 'data' => $result]);
    }

    /**
     * 删除文件
     */
    public function delete(Request $request)
    {
        $plugin = plugin_instance('qiniu_upload');
        $filePath = $request->param('file_path');

        $upload = $plugin->uploadService();
        $result = $upload->delete($filePath);

        if (!$result) {
            return json(['code' => 1, 'msg' => $upload->getError()]);
        }

        return json(['code' => 0, 'msg' => '删除成功']);
    }
}
