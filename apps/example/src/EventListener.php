<?php

namespace apps\example;

use app\plugin\core\PluginEventListener;
use think\facade\Log;

class EventListener extends PluginEventListener
{

    public function onBeforeBoot(array $params): bool
    {

        try {
            $pluginName = $params['name'] ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件启动前自定义事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            Log::error("插件启动前自定义事件异常: " . $e->getMessage());
            return false;
        }

    }

}
