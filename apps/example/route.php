<?php

return [
    // 路由别名
    'aliases' => [
        // 管理员路由组
        'admin' => [
            'prefix' => 'admin',
            'middleware' => []
        ],
        // API路由组
        'api' => [
            'prefix' => 'api',
            'middleware' => []
        ]
    ],
    // 路由分组
    'group' => [
        'prefix' => 'example',
        'middleware' => [],
        'routes' => [
            // 简单路由定义
            'index' => 'Example/info',
            'test' => 'Examples/test',
            
            // 详细路由定义
            [
                'method' => 'get',
                'rule' => 'config',
                'controller' => 'Examples',
                'action' => 'config',
                'middleware' => ['admin']
            ],
            [
                'method' => 'post',
                'rule' => 'save',
                'controller' => 'Examples',
                'action' => 'save',
                'middleware' => ['admin']
            ]
        ]
    ],
    // 路由分组
    'group_1' => [
        'prefix' => 'install',
        'middleware' => [],
        'routes' => [
            // API文档
            'steps' => 'Install/index',
        ]
    ],
    
    // 独立路由
    'routes' => [
        // 使用别名路由组
        '@admin' => [
            'settings' => 'Examples/settings',
            'dashboard' => 'Examples/dashboard'
        ],
        '@api' => [
            'status' => 'Examples/status',
            'metrics' => 'Examples/metrics'
        ],
        // 简单路由定义
        'api/example/info' => 'Url/info',
        
        // 详细路由定义
        [
            'method' => 'get',
            'rule' => 'api/example/data',
            'controller' => 'Examples',
            'action' => 'data',
            'middleware' => []
        ]
    ]
];