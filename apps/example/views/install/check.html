{layout name="install/layout" /}
            
            <div class="step-list">
                <div class="step-item">
                    <span class="step-number">1</span>
                    <div>欢迎使用</div>
                </div>
                <div class="step-item active">
                    <span class="step-number">2</span>
                    <div>环境检查</div>
                </div>
                <div class="step-item">
                    <span class="step-number">3</span>
                    <div>数据库配置</div>
                </div>
                <div class="step-item">
                    <span class="step-number">4</span>
                    <div>创建管理员</div>
                </div>
                <div class="step-item">
                    <span class="step-number">5</span>
                    <div>安装完成</div>
                </div>
            </div>

            <div class="check-content">
                <table class="table check-table">
                    <thead>
                        <tr>
                            <th>检查项目</th>
                            <th>所需配置</th>
                            <th>当前配置</th>
                            <th>检查结果</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="requirements" id="item"}
                        <tr>
                            <td>{$item.name}</td>
                            <td>{$item.required}</td>
                            <td>{$item.current}</td>
                            <td class="check-result {$item.passed ? 'check-passed' : 'check-failed'}">
                                {$item.passed ? '通过' : '未通过'}
                            </td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
            </div>

            <div class="btn-container">
                <a href="/apps/example/install/steps" class="btn btn-secondary">上一步</a>
                {if condition="$passed"}
                <a href="/apps/example/install/steps?step=2" class="btn btn-primary">下一步</a>
                {else/}
                <button class="btn btn-primary" disabled>下一步</button>
                {/if}
            </div>
        </div>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.min.js"></script>
</body>
</html>