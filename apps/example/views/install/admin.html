{layout name="install/layout" /}
            
            <div class="step-list">
                <div class="step-item">
                    <span class="step-number">1</span>
                    <div>欢迎使用</div>
                </div>
                <div class="step-item">
                    <span class="step-number">2</span>
                    <div>环境检查</div>
                </div>
                <div class="step-item">
                    <span class="step-number">3</span>
                    <div>数据库配置</div>
                </div>
                <div class="step-item active">
                    <span class="step-number">4</span>
                    <div>创建管理员</div>
                </div>
                <div class="step-item">
                    <span class="step-number">5</span>
                    <div>安装完成</div>
                </div>
            </div>

            <div class="form-container">
                <form id="adminForm" method="post" action="/apps/example/install/steps?step=3" onsubmit="return validateForm()">
                    <div class="form-group">
                        <label for="username">管理员账号</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">管理员密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">确认密码</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <div class="form-group">
                        <label for="email">管理员邮箱</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="btn-container">
                        <a href="/apps/example/install/steps?step=2" class="btn btn-secondary">上一步</a>
                        <button type="submit" class="btn btn-primary">下一步</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.min.js"></script>
    <script>
        function validateForm() {
            if (document.getElementById('password').value !== document.getElementById('confirm_password').value) {
                alert('两次输入的密码不一致');
                return false;
            }
            return true;
        }
    </script>
</body>
</html>