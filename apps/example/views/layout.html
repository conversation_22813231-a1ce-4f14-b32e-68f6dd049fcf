<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{$title|default="Example Plugin"}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #6c757d;
            --text-color: #333;
            --border-color: #ddd;
            --bg-color: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            margin: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        .layout-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: #fff;
            padding: 30px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.05);
        }

        .main-content {
            flex: 1;
            padding: 30px;
        }

        .content-wrapper {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 30px;
        }

        .sidebar-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .feature-list li {
            margin-bottom: 15px;
            padding-left: 25px;
            position: relative;
        }

        .feature-list li:before {
            content: '✓';
            color: var(--primary-color);
            position: absolute;
            left: 0;
        }

        .system-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            font-size: 14px;
            color: var(--secondary-color);
        }

        .footer {
            text-align: center;
            padding: 20px 0;
            color: var(--secondary-color);
            font-size: 14px;
            border-top: 1px solid var(--border-color);
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .layout-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="layout-container">
        <div class="sidebar">
            <h2 class="sidebar-title">系统介绍</h2>
            <p>欢迎使用我们的安装向导，本系统具有以下特点：</p>
            <ul class="feature-list">
                <li>简单易用的界面设计</li>
                <li>完整的功能模块</li>
                <li>灵活的扩展机制</li>
                <li>强大的性能表现</li>
                <li>安全可靠的架构</li>
            </ul>
            <div class="system-info">
                <p>Version: 1.0.0</p>
                <p>© {$year|default="2024"} Example Plugin</p>
            </div>
        </div>
        <div class="main-content">
            <div class="content-wrapper">
                {__CONTENT__}
            </div>
            <div class="footer">
                <p>All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>