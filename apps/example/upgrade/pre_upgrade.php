<?php

namespace apps\example\upgrade;

use think\facade\Db;
use think\facade\Log;

/**
 * 示例插件预升级脚本
 * 在升级过程中，此脚本会在停用插件前执行
 */

// 记录升级开始日志
Log::info('Example plugin pre-upgrade script started');

// 备份现有数据
try {
    $existingData = Db::table('plugin_example_data')->field('id,type,data,created_at')->select()->toArray();
    
    // 将数据保存到临时文件中
    $backupFile = __DIR__ . '/data_backup_' . date('YmdHis') . '.json';
    file_put_contents($backupFile, json_encode($existingData, JSON_PRETTY_PRINT));
    
    Log::info('Example plugin data backup created: ' . $backupFile);
} catch (\Exception $e) {
    Log::error('Failed to backup example plugin data: ' . $e->getMessage());
}

// 预处理一些升级前的数据
try {
    // 这里可以添加一些数据预处理逻辑
    // 例如：清理无效数据、格式化数据等
    
    Log::info('Example plugin pre-upgrade data processing completed');
} catch (\Exception $e) {
    Log::error('Failed to process example plugin data: ' . $e->getMessage());
}

// 预升级脚本执行完成
Log::info('Example plugin pre-upgrade script completed');