<?php

return [
    'plugin' => [
        'name' => '示例插件',
        'description' => '这是一个示例插件，展示了插件系统的基本功能',
        'author' => '作者名称',
        'version' => '版本号',
    ],
    'config' => [
        'enable' => '启用插件',
        'api_key' => 'API密钥',
        'api_secret' => 'API密钥密文',
    ],
    'messages' => [
        'install_success' => '插件安装成功',
        'install_failed' => '插件安装失败',
        'uninstall_success' => '插件卸载成功',
        'uninstall_failed' => '插件卸载失败',
        'upgrade_success' => '插件升级成功',
        'upgrade_failed' => '插件升级失败',
    ],
];