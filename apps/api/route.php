<?php

return [
    // 路由别名
    'aliases' => [
        // 管理员路由组
        'admin' => [
            'prefix' => 'admin',
            'middleware' => []
        ],
        // API路由组
        'api' => [
            'prefix' => 'api',
            'middleware' => []
        ]
    ],
    
    // 路由分组
    'group' => [
        'prefix' => 'open',
        'middleware' => [],
        'routes' => [
            // OAuth2.0授权相关路由
            'authorize' => 'Auth/authorize',
            'token' => 'Auth/token',
            'revoke' => 'Auth/revoke',
            
            // API文档
            'doc' => 'Api/doc',
            
            // 开发者门户
            'developer/index' => 'Developer/index',
            'developer/register' => 'Developer/register',
            'developer/app/create' => 'Developer/createApp',
            'developer/app/detail' => 'Developer/appDetail',
            'developer/app/edit' => 'Developer/editApp',
            'developer/app/reset_secret' => 'Developer/resetAppSecret',
            'developer/api_doc' => 'Developer/apiDoc',
            'developer/sdk' => 'Developer/sdkDownload',
            'developer/download_sdk' => 'Developer/downloadSdk',
            'developer/api_tester' => 'Developer/apiTester'
        ]
    ],
    // 路由分组
    'group_1' => [
        'prefix' => 'opens',
        'middleware' => [],
        'routes' => [
            // API文档
            'doc' => '\apps\qiniu_upload\api\Url/index',
        ]
    ],
    
    // 独立路由
    'routes' => [
        // 简单路由定义
        'open/developer/list' => '\plugins\qiniu_upload\src\api\Url/index',
        '@api' => [
            'api/doc' => 'open.Developer/apiDoc'
        ],
        // 管理员路由
        '@admin' => [
            'open/developer/list' => 'admin.Platform/auditDeveloper',
            'open/developer/audit' => 'admin.Platform/auditDeveloper',
            'open/app/list' => 'admin.Platform/appList',
            'open/app/audit' => 'admin.Platform/auditApp',
            'open/app/reset_secret' => 'admin.Platform/resetAppSecret',
            'open/api/list' => 'admin.Platform/apiList',
            'open/api/add' => 'admin.Platform/addApi',
            'open/api/edit' => 'admin.Platform/editApi',
            'open/api/delete' => 'admin.Platform/deleteApi',
            'open/app/api_permission' => 'admin.Platform/appApiPermission',
            'open/api/stats' => 'admin.Platform/apiStats'
        ]
    ]
];