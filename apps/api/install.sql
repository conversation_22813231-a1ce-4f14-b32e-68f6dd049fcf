-- 开放平台数据库表结构

-- 开发者表
CREATE TABLE IF NOT EXISTS `plugin_api_developer` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '开发者ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '关联的用户ID',
  `developer_name` varchar(50) NOT NULL DEFAULT '' COMMENT '开发者名称',
  `company_name` varchar(100) NOT NULL DEFAULT '' COMMENT '公司名称',
  `contact_name` varchar(50) NOT NULL DEFAULT '' COMMENT '联系人姓名',
  `contact_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `contact_email` varchar(100) NOT NULL DEFAULT '' COMMENT '联系邮箱',
  `website` varchar(255) NOT NULL DEFAULT '' COMMENT '网站地址',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=待审核，1=已通过，2=已拒绝',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开放平台开发者表';

-- 应用表
CREATE TABLE IF NOT EXISTS `plugin_api_app` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '应用ID',
  `developer_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '开发者ID',
  `app_name` varchar(50) NOT NULL DEFAULT '' COMMENT '应用名称',
  `app_key` varchar(32) NOT NULL DEFAULT '' COMMENT '应用Key',
  `app_secret` varchar(64) NOT NULL DEFAULT '' COMMENT '应用Secret',
  `app_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '应用描述',
  `redirect_uri` varchar(255) NOT NULL DEFAULT '' COMMENT '授权回调地址',
  `scope` varchar(255) NOT NULL DEFAULT '' COMMENT '授权范围',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=待审核，1=已通过，2=已拒绝，3=已禁用',
  `app_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '应用类型：1=网站应用，2=移动应用，3=第三方应用',
  `logo` varchar(255) NOT NULL DEFAULT '' COMMENT '应用Logo',
  `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_key` (`app_key`),
  KEY `idx_developer_id` (`developer_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开放平台应用表';

-- API接口表
CREATE TABLE IF NOT EXISTS `plugin_api_api` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '接口ID',
  `api_name` varchar(50) NOT NULL DEFAULT '' COMMENT '接口名称',
  `api_code` varchar(50) NOT NULL DEFAULT '' COMMENT '接口代码',
  `api_category` varchar(50) NOT NULL DEFAULT '' COMMENT '接口分类',
  `request_method` varchar(10) NOT NULL DEFAULT 'GET' COMMENT '请求方法：GET, POST, PUT, DELETE',
  `request_url` varchar(255) NOT NULL DEFAULT '' COMMENT '请求URL',
  `description` text COMMENT '接口描述',
  `params` text COMMENT '请求参数，JSON格式',
  `response` text COMMENT '返回结果，JSON格式',
  `is_auth` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否需要授权：0=否，1=是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_api_code` (`api_code`),
  KEY `idx_category` (`api_category`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开放平台API接口表';

-- 应用API权限表
CREATE TABLE IF NOT EXISTS `plugin_api_app_api` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '应用ID',
  `api_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '接口ID',
  `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_api` (`app_id`,`api_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开放平台应用API权限表';

-- 访问令牌表
CREATE TABLE IF NOT EXISTS `plugin_api_token` (
    `token` varchar(50) NOT NULL COMMENT 'Token',
    `data` text NOT NULL COMMENT 'api会员数据',
    `createtime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `expiretime` int unsigned NOT NULL DEFAULT '0' COMMENT '过期时间',
    `updatetime` int unsigned NOT NULL DEFAULT '0',
     PRIMARY KEY (`token`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=COMPACT COMMENT='开放平台访问令牌表';

-- 刷新访问令牌表
CREATE TABLE IF NOT EXISTS `plugin_api_refresh_token` (
    `token` varchar(50) NOT NULL COMMENT 'Token',
    `data` text NOT NULL COMMENT 'api会员数据',
    `createtime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `expiretime` int unsigned NOT NULL DEFAULT '0' COMMENT '过期时间',
    PRIMARY KEY (`token`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=COMPACT COMMENT='开放平台访问令牌表';

-- API调用日志表
CREATE TABLE IF NOT EXISTS `plugin_api_api_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `app_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '应用ID',
  `api_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '接口ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `request_method` varchar(10) NOT NULL DEFAULT '' COMMENT '请求方法',
  `request_url` varchar(255) NOT NULL DEFAULT '' COMMENT '请求URL',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `status_code` int(11) NOT NULL DEFAULT '200' COMMENT '状态码',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `execute_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '执行时间(毫秒)',
  `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_create_time` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开放平台API调用日志表';

-- 授权码表
CREATE TABLE IF NOT EXISTS `plugin_api_auth_code` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '应用ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `code` varchar(64) NOT NULL DEFAULT '' COMMENT '授权码',
  `scope` varchar(255) NOT NULL DEFAULT '' COMMENT '授权范围',
  `redirect_uri` varchar(255) NOT NULL DEFAULT '' COMMENT '重定向URI',
  `expiretime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '过期时间戳',
  `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_app_user` (`app_id`,`user_id`),
  KEY `idx_expire_time` (`expiretime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开放平台授权码表';

-- 插入默认配置
INSERT IGNORE INTO `plugin_api_config` (`name`, `value`) VALUES
('oauth2_auth_code_expire', '600'),
('oauth2_access_token_expire', '7200'),
('oauth2_refresh_token_expire', '2592000'),
('developer_auto_approve', '0'),
('app_auto_approve', '0');