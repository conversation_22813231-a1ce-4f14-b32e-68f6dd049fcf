<?php

namespace apps\api;

use app\plugin\core\BasePlugin;
use app\plugin\core\SqlHelper;

/**
 * 开放平台API插件
 */
class App extends BasePlugin
{
    /**
     * 初始化
     */
    protected function init(): void
    {
        parent::init();
        // 注册系统初始化钩子
//        $this->addHook('system.init', [new CommonConfigInit(), 'handle']);
        $this->doHook('system.init', request());

    }

    /**
     * 插件安装方法
     */
    public function install(): bool
    {
        // 调用父类安装方法
        if (!parent::install()) {
            return false;
        }
        
        // 创建插件数据表
        $sql = file_get_contents(__DIR__ . '/install.sql');
        SqlHelper::parser($sql);
        return true;
    }
    
    /**
     * 插件卸载方法
     */
    public function uninstall(): bool
    {
        // 调用父类卸载方法
        if (!parent::uninstall()) {
            return false;
        }
        
        // 删除插件数据表
        $sql = file_get_contents(__DIR__ . '/uninstall.sql');
        SqlHelper::parser($sql);
        
        return true;
    }
    
    /**
     * 启用插件
     * @return bool
     */
    public function enable(): bool
    {
        if (!parent::enable()) {
            return false;
        }

        // 这里可以添加插件特定的启用逻辑
        return true;
    }

    /**
     * 禁用插件
     * @return bool
     */
    public function disable(): bool
    {
        if (!parent::disable()) {
            return false;
        }

        // 这里可以添加插件特定的禁用逻辑
        return true;
    }
    
    /**
     * 获取OAuth2服务实例
     * @return \plugins\api\src\service\OAuth2Service
     */
    public function oauth2Service()
    {
        return new \plugins\api\src\service\OAuth2Service($this->getConfig());
    }
    
    /**
     * 获取API服务实例
     * @return \plugins\api\src\service\ApiService
     */
    public function apiService()
    {
        return new \plugins\api\src\service\ApiService($this->getConfig());
    }
}