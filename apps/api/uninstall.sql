-- 开放平台插件卸载脚本

-- 删除授权码表
DROP TABLE IF EXISTS `plugin_api_auth_code`;

-- 删除API调用日志表
DROP TABLE IF EXISTS `plugin_api_api_log`;

-- 删除访问令牌表
DROP TABLE IF EXISTS `plugin_api_token`;
DROP TABLE IF EXISTS `plugin_api_refresh_token`;

-- 删除应用API权限表
DROP TABLE IF EXISTS `plugin_api_app_api`;

-- 删除API接口表
DROP TABLE IF EXISTS `plugin_api_api`;

-- 删除应用表
DROP TABLE IF EXISTS `plugin_api_app`;

-- 删除开发者表
DROP TABLE IF EXISTS `plugin_api_developer`;

-- 删除配置表
DROP TABLE IF EXISTS `plugin_api_config`;