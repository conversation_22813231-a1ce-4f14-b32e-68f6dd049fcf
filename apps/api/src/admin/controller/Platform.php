<?php
namespace apps\api\src\admin\controller;

use app\common\controller\BaseAdminController;
use apps\api\src\model\Api;
use apps\api\src\model\App;
use apps\api\src\model\Developer;
use think\facade\View;

/**
 * 开放平台管理控制器
 */
class Platform extends BaseAdminController
{
    /**
     * 开发者管理
     */
    public function developerList()
    {
        if ($this->request->isAjax()) {
            $param = $this->request->param();
            $where = [];
            
            // 搜索条件
            if (!empty($param['keyword'])) {
                $where[] = ['developer_name|company_name|contact_name', 'like', '%' . $param['keyword'] . '%'];
            }
            
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['status', '=', $param['status']];
            }
            
            $page = isset($param['page']) ? intval($param['page']) : 1;
            $limit = isset($param['limit']) ? intval($param['limit']) : 15;
            
            $result = Developer::getList($where, $page, $limit);
            
            return json([
                'code' => 0,
                'msg' => '获取成功',
                'count' => $result['total'],
                'data' => $result['list']
            ]);
        }
        
        return View::fetch();
    }
    
    /**
     * 开发者审核
     */
    public function auditDeveloper()
    {
        if ($this->request->isPost()) {
            $param = $this->request->param();
            $id = isset($param['id']) ? intval($param['id']) : 0;
            $status = isset($param['status']) ? intval($param['status']) : 0;
            $remark = isset($param['remark']) ? $param['remark'] : '';
            
            if ($id <= 0 || !in_array($status, [1, 2])) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            $result = Developer::audit($id, $status, $remark);
            if ($result) {
                return json(['code' => 0, 'msg' => '审核成功']);
            } else {
                return json(['code' => 1, 'msg' => '审核失败']);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 应用管理
     */
    public function appList()
    {
        if ($this->request->isAjax()) {
            $param = $this->request->param();
            $where = [];
            
            // 搜索条件
            if (!empty($param['keyword'])) {
                $where[] = ['app_name|app_key', 'like', '%' . $param['keyword'] . '%'];
            }
            
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['status', '=', $param['status']];
            }
            
            if (!empty($param['developer_id'])) {
                $where[] = ['developer_id', '=', $param['developer_id']];
            }
            
            $page = isset($param['page']) ? intval($param['page']) : 1;
            $limit = isset($param['limit']) ? intval($param['limit']) : 15;
            
            $result = App::getList($where, $page, $limit);
            
            // 关联开发者信息
            foreach ($result['list'] as &$app) {
                $developer = Developer::getInfo($app['developer_id']);
                $app['developer_name'] = $developer ? $developer['developer_name'] : '未知';
            }
            
            return json([
                'code' => 0,
                'msg' => '获取成功',
                'count' => $result['total'],
                'data' => $result['list']
            ]);
        }
        
        // 获取开发者列表
        $developers = Developer::where('status', 1)->select()->toArray();
        View::assign('developers', $developers);
        
        return View::fetch();
    }
    
    /**
     * 应用审核
     */
    public function auditApp()
    {
        if ($this->request->isPost()) {
            $param = $this->request->param();
            $id = isset($param['id']) ? intval($param['id']) : 0;
            $status = isset($param['status']) ? intval($param['status']) : 0;
            
            if ($id <= 0 || !in_array($status, [1, 2])) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            $result = App::audit($id, $status);
            if ($result) {
                return json(['code' => 0, 'msg' => '审核成功']);
            } else {
                return json(['code' => 1, 'msg' => '审核失败']);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 重置应用密钥
     */
    public function resetAppSecret()
    {
        if ($this->request->isPost()) {
            $param = $this->request->param();
            $id = isset($param['id']) ? intval($param['id']) : 0;
            
            if ($id <= 0) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            $newSecret = App::resetSecret($id);
            if ($newSecret) {
                return json(['code' => 0, 'msg' => '重置成功', 'data' => ['app_secret' => $newSecret]]);
            } else {
                return json(['code' => 1, 'msg' => '重置失败']);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * API接口管理
     */
    public function apiList()
    {
        if ($this->request->isAjax()) {
            $param = $this->request->param();
            $where = [];
            
            // 搜索条件
            if (!empty($param['keyword'])) {
                $where[] = ['api_name|api_code', 'like', '%' . $param['keyword'] . '%'];
            }
            
            if (!empty($param['api_category'])) {
                $where[] = ['api_category', '=', $param['api_category']];
            }
            
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['status', '=', $param['status']];
            }
            
            $page = isset($param['page']) ? intval($param['page']) : 1;
            $limit = isset($param['limit']) ? intval($param['limit']) : 15;
            
            $result = Api::getList($where, $page, $limit);
            
            return json([
                'code' => 0,
                'msg' => '获取成功',
                'count' => $result['total'],
                'data' => $result['list']
            ]);
        }
        
        // 获取API分类列表
        $categories = Api::group('api_category')->column('api_category');
        View::assign('categories', $categories);
        
        return View::fetch();
    }
    
}