<?php
namespace apps\api\src\common;

use app\common\ApiCode;
use app\common\controller\BaseController;
use apps\api\src\model\App;
use apps\api\src\service\ApiService;
use apps\api\src\service\OAuth2Service;
use think\facade\Request;

/**
 * 开放平台API基础控制器
 */
class Api extends BaseController
{
    /**
     * 应用ID
     * @var int
     */
    protected $appId = 0;
    
    /**
     * 用户ID
     * @var int
     */
    protected $userId = 0;
    
    /**
     * API服务
     * @var ApiService
     */
    protected $apiService;
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->apiService = new ApiService();
    }
    
    /**
     * 验证API请求
     * @param string $apiCode API代码
     * @param bool $needAuth 是否需要授权
     * @return bool 是否验证通过
     */
    protected function verifyApiRequest($apiCode, $needAuth = true)
    {
        // 获取应用Key
        $appKey = Request::header('X-App-Key');
        if (empty($appKey)) {
            $this->error('缺少应用Key', [], ApiCode::UNAUTHORIZED);
            return false;
        }
        
        // 验证应用
        $app = App::getByAppKey($appKey);
        if (!$app || $app['status'] != 1) {
            $this->error('应用不存在或未通过审核', [], ApiCode::UNAUTHORIZED);
            return false;
        }
        
        $this->appId = $app['id'];
        
        // 验证API权限
        $api = $this->apiService->verifyApiPermission($apiCode, $app['id']);
        if (!$api) {
            $this->error('没有权限访问该API', [], ApiCode::FORBIDDEN);
            return false;
        }
        
        // 如果需要授权，验证访问令牌
        if ($needAuth && $api['is_auth']) {
            $accessToken = Request::header('X-Access-Token');
            if (empty($accessToken)) {
                $this->error('缺少访问令牌', [], ApiCode::UNAUTHORIZED);
                return false;
            }
            
            $oauth2Service = new OAuth2Service();
            $tokenInfo = $oauth2Service->verifyToken($accessToken);
            
            if (!$tokenInfo) {
                $this->error('访问令牌无效或已过期', [], ApiCode::UNAUTHORIZED);
                return false;
            }
            
            // 验证应用ID是否匹配
            if ($tokenInfo['app_id'] != $app['id']) {
                $this->error('访问令牌与应用不匹配', [], ApiCode::UNAUTHORIZED);
                return false;
            }
            
            $this->userId = $tokenInfo['user_id'];
        }
        
        // 记录API调用开始时间
        $this->request->apiStartTime = microtime(true);
        $this->request->apiId = $api['id'];
        
        return true;
    }
    
    /**
     * 记录API调用日志
     * @param array $responseData 响应数据
     * @param int $statusCode 状态码
     */
    protected function logApiCall($responseData, $statusCode = 200)
    {
        if (!isset($this->request->apiStartTime) || !isset($this->request->apiId)) {
            return;
        }
        
        // 计算执行时间（毫秒）
        $executeTime = round((microtime(true) - $this->request->apiStartTime) * 1000);
        
        // 记录日志
        $this->apiService->logApiCall(
            $this->appId,
            $this->request->apiId,
            $this->userId,
            $this->request->method(),
            $this->request->url(true),
            $this->request->param(),
            $responseData,
            $statusCode,
            $executeTime
        );
    }
    
    /**
     * 成功响应
     * @param string $msg 消息
     * @param array $data 数据
     * @param int $code 状态码
     * @return \think\Response
     */
    protected function success($msg = '', $data = [], $code = ApiCode::SUCCESS)
    {
        $result = [
            'code' => $code,
            'message' => $msg ?: ApiCode::getMessage($code),
            'data' => $data
        ];
        
        // 记录API调用日志
        $this->logApiCall($result, $code);
        
        return json($result);
    }
    
    /**
     * 错误响应
     * @param string $msg 消息
     * @param array $data 数据
     * @param int $code 状态码
     * @return \think\Response
     */
    protected function error($msg = '', $data = [], $code = ApiCode::BAD_REQUEST)
    {
        $result = [
            'code' => $code,
            'message' => $msg ?: ApiCode::getMessage($code),
            'data' => $data
        ];
        
        // 记录API调用日志
        $this->logApiCall($result, $code);
        
        return json($result);
    }
    
    /**
     * API文档
     */
    public function doc()
    {
        // 获取API列表
        $apis = Api::where('status', 1)
            ->order('api_category', 'asc')
            ->order('sort', 'desc')
            ->select()
            ->toArray();
        
        // 按分类分组
        $categoryApis = [];
        foreach ($apis as $api) {
            $category = $api['api_category'];
            if (!isset($categoryApis[$category])) {
                $categoryApis[$category] = [];
            }
            $categoryApis[$category][] = $api;
        }
        
        return view('doc', [
            'categoryApis' => $categoryApis
        ]);
    }
}