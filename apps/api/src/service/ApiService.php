<?php
namespace apps\api\src\service;

use app\common\ApiCode;
use apps\api\src\model\Api;
use apps\api\src\model\ApiLog;
use apps\api\src\model\App;
use think\facade\Cache;

/**
 * 开放平台API服务
 */
class ApiService
{
    /**
     * 验证API请求权限
     * @param string $apiCode API代码
     * @param int $appId 应用ID
     * @return bool|array 成功返回API信息，失败返回false
     */
    public function verifyApiPermission($apiCode, $appId)
    {
        // 获取API信息
        $api = Api::getByCode($apiCode);
        if (!$api || $api['status'] != 1) {
            return false;
        }
        
        // 检查应用是否有权限访问该API
        if ($api['is_auth']) {
            $hasPermission = Api::where([
                'app_id' => $appId,
                'api_id' => $api['id']
            ])->count() > 0;
            
            if (!$hasPermission) {
                return false;
            }
        }
        
        // 检查API调用限制
        if (!$this->checkApiLimit($appId, $api['id'])) {
            return false;
        }
        
        return $api;
    }
    
    /**
     * 检查API调用限制
     * @param int $appId 应用ID
     * @param int $apiId API ID
     * @return bool 是否允许调用
     */
    protected function checkApiLimit($appId, $apiId)
    {
        // 获取应用信息
        $app = App::getInfo($appId);
        if (!$app) {
            return false;
        }
        
        // 获取API信息
        $api = Api::getInfo($apiId);
        if (!$api) {
            return false;
        }
        
        // 检查API调用次数限制
        if ($api['call_limit'] > 0) {
            $cacheKey = "api_call_count_{$appId}_{$apiId}_" . date('Ymd');
            $callCount = Cache::get($cacheKey, 0);
            
            if ($callCount >= $api['call_limit']) {
                return false;
            }
            
            // 增加调用计数
            Cache::inc($cacheKey);
            if ($callCount == 0) {
                Cache::expire($cacheKey, 86400); // 24小时后过期
            }
        }
        
        // 检查调用频率限制（次数/分钟）
        if ($api['rate_limit'] > 0) {
            $cacheKey = "api_rate_limit_{$appId}_{$apiId}_" . time() / 60;
            $rateCount = Cache::get($cacheKey, 0);
            
            if ($rateCount >= $api['rate_limit']) {
                return false;
            }
            
            // 增加频率计数
            Cache::inc($cacheKey);
            if ($rateCount == 0) {
                Cache::expire($cacheKey, 60); // 1分钟后过期
            }
        }
        
        return true;
    }
    
    /**
     * 记录API调用日志
     * @param int $appId 应用ID
     * @param int $apiId API ID
     * @param int $userId 用户ID
     * @param string $method 请求方法
     * @param string $url 请求URL
     * @param array $requestData 请求数据
     * @param array $responseData 响应数据
     * @param int $statusCode 状态码
     * @param int $executeTime 执行时间(毫秒)
     * @return int|bool 成功返回日志ID，失败返回false
     */
    public function logApiCall($appId, $apiId, $userId, $method, $url, $requestData, $responseData, $statusCode, $executeTime)
    {
        $data = [
            'app_id' => $appId,
            'api_id' => $apiId,
            'user_id' => $userId,
            'request_method' => $method,
            'request_url' => $url,
            'request_data' => is_array($requestData) ? json_encode($requestData, JSON_UNESCAPED_UNICODE) : $requestData,
            'response_data' => is_array($responseData) ? json_encode($responseData, JSON_UNESCAPED_UNICODE) : $responseData,
            'status_code' => $statusCode,
            'ip' => request()->ip(),
            'execute_time' => $executeTime,
            'create_time' => time()
        ];
        
        $model = ApiLog::create($data);
        return $model ? $model->id : false;
    }
    
    /**
     * 获取API调用统计
     * @param int $appId 应用ID
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 统计数据
     */
    public function getApiCallStats($appId, $startDate = '', $endDate = '')
    {
        $query = ApiLog::where('app_id', $appId);
        
        if ($startDate) {
            $startTime = strtotime($startDate . ' 00:00:00');
            $query->where('create_time', '>=', $startTime);
        }
        
        if ($endDate) {
            $endTime = strtotime($endDate . ' 23:59:59');
            $query->where('create_time', '<=', $endTime);
        }
        
        // 总调用次数
        $totalCalls = $query->count();
        
        // 成功调用次数
        $successCalls = $query->where('status_code', ApiCode::SUCCESS)->count();
        
        // 失败调用次数
        $failedCalls = $totalCalls - $successCalls;
        
        // 按API分组统计
        $apiStats = $query->group('api_id')
            ->field('api_id, count(*) as call_count')
            ->order('call_count', 'desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        // 获取API名称
        foreach ($apiStats as &$stat) {
            $api = Api::getInfo($stat['api_id']);
            $stat['api_name'] = $api ? $api['api_name'] : '未知API';
        }
        
        return [
            'total_calls' => $totalCalls,
            'success_calls' => $successCalls,
            'failed_calls' => $failedCalls,
            'api_stats' => $apiStats
        ];
    }
    
    /**
     * 获取应用的API权限列表
     * @param int $appId 应用ID
     * @return array API权限列表
     */
    public function getAppApiPermissions($appId)
    {
        // 获取应用已有的API权限
        $appApiIds = Api::where('app_id', $appId)->column('api_id');
        
        // 获取所有可用的API
        $allApis = Api::where('status', 1)->select()->toArray();
        
        // 标记应用是否有权限
        foreach ($allApis as &$api) {
            $api['has_permission'] = in_array($api['id'], $appApiIds);
        }
        
        return $allApis;
    }
    
    /**
     * 更新应用的API权限
     * @param int $appId 应用ID
     * @param array $apiIds API ID数组
     * @return bool 是否成功
     */
    public function updateAppApiPermissions($appId, $apiIds)
    {
        // 删除原有权限
        Api::where('app_id', $appId)->delete();
        
        // 添加新权限
        $data = [];
        $now = time();
        foreach ($apiIds as $apiId) {
            $data[] = [
                'app_id' => $appId,
                'api_id' => $apiId,
                'create_time' => $now
            ];
        }
        
        if (!empty($data)) {
            return Api::insertAll($data) !== false;
        }
        
        return true;
    }
}