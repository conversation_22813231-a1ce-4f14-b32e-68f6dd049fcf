<?php

namespace apps\api\src\service;

use think\Model;
use app\common\Auth;
use app\common\TokenStorage;

class AccessToken extends Model
{
    protected $name = 'plugin_cloud_access_token';
    
    /**
     * 获取有效的访问令牌信息
     * @param string $token 访问令牌
     * @return array|null
     */
    public static function getValidToken($token)
    {
        try {
            return Auth::checkToken($token);
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * 创建访问令牌
     * @param string $appId 应用ID
     * @param int $userId 用户ID
     * @param array $scopes 授权范围
     * @param int $expireIn 过期时间（秒）
     * @return array
     */
    public static function createToken($appId, $userId, $scopes = [], $expireIn = 7200)
    {
        $customData = [
            'app_id' => $appId,
            'scopes' => $scopes
        ];
        
        // 创建访问令牌和刷新令牌
        $tokens = Auth::createToken($userId, $customData, $expireIn, true);
        
        return [
            'access_token' => $tokens['access_token'],
            'refresh_token' => $tokens['refresh_token'],
            'expires_in' => $tokens['expires_in'],
            'token_type' => 'Bearer'
        ];
    }
    
    /**
     * 刷新访问令牌
     * @param string $refreshToken 刷新令牌
     * @param int $expireIn 新访问令牌的过期时间（秒）
     * @return array
     */
    public static function refreshToken($refreshToken, $expireIn = 7200)
    {
        try {
            $result = Auth::refreshToken($refreshToken, $expireIn);
            return [
                'access_token' => $result['access_token'],
                'expires_in' => $result['expires_in'],
                'token_type' => 'Bearer'
            ];
        } catch (\Exception $e) {
            throw new \Exception('刷新令牌失败：' . $e->getMessage());
        }
    }
    
    /**
     * 撤销令牌
     * @param string $token 访问令牌
     * @return bool
     */
    public static function revokeToken($token)
    {
        try {
            return TokenStorage::remove($token);
        } catch (\Exception $e) {
            return false;
        }
    }
}