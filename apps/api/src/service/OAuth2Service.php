<?php

namespace apps\api\src\service;

use apps\api\src\model\App;
use apps\api\src\model\AccessToken;
use apps\api\src\model\AuthCode;

class OAuth2Service
{

    // 授权码有效期（秒）
    const AUTH_CODE_EXPIRE = 600; // 10分钟

    // 访问令牌有效期（秒）
    const ACCESS_TOKEN_EXPIRE = 7200; // 2小时

    // 刷新令牌有效期（秒）
    const REFRESH_TOKEN_EXPIRE = 2592000; // 30天

    protected $config;
    
    public function __construct($config = [])
    {
        $this->config = $config;
    }

    /**
     * 生成授权码
     * @param int $appId 应用ID
     * @param int $userId 用户ID
     * @param string $redirectUri 重定向URI
     * @param string $scope 授权范围
     * @return string|bool 成功返回授权码，失败返回false
     */
    public function generateAuthCode($appId, $userId, $redirectUri, $scope = '')
    {
        // 检查应用是否存在且状态正常
        $app = \plugins\api\src\model\App::getInfo($appId);
        if (!$app || $app['status'] != 1) {
            return false;
        }

        // 生成授权码
        $code = md5(uniqid(mt_rand(), true));
        $expireTime = time() + self::AUTH_CODE_EXPIRE;

        // 保存授权码
        $data = [
            'app_id' => $appId,
            'user_id' => $userId,
            'code' => $code,
            'scope' => $scope,
            'redirect_uri' => $redirectUri,
            'expire_time' => $expireTime,
            'create_time' => time()
        ];

        $result = \plugins\api\src\model\AuthCode::create($data);
        return $result ? $code : false;
    }
    
    /**
     * 获取访问令牌
     * @param string $code 授权码
     * @param string $appId 应用ID
     * @param string $appSecret 应用密钥
     * @param string $redirectUri 重定向URI
     * @return array
     */
    public function getAccessToken($code, $appId, $appSecret, $redirectUri)
    {
        // 验证应用
        $app = \plugins\api\src\model\App::verifySecret($appId, $appSecret);
        if (!$app) {
            return false;
        }

        // 验证授权码
        $authCode = \plugins\api\src\model\AuthCode::where([
            'code' => $code,
            'app_id' => $appId,
            'redirect_uri' => $redirectUri
        ])->where('expire_time', '>', time())->find();

        if (!$authCode) {
            return false;
        }
        // 验证应用信息
        $app = App::where(['app_id' => $appId, 'app_secret' => $appSecret])->find();
        if (!$app) {
            throw new \Exception('无效的应用ID或密钥');
        }
        
        // 验证授权码
        $authCode = AuthCode::where(['code' => $code, 'app_id' => $appId])
            ->where('expire_time', '>', time())
            ->find();
        if (!$authCode) {
            throw new \Exception('无效的授权码');
        }
        
        // 验证重定向URI
        if ($authCode->redirect_uri !== $redirectUri) {
            throw new \Exception('重定向URI不匹配');
        }
        
        // 创建访问令牌
        $token = AccessToken::createToken(
            $appId,
            $authCode->user_id,
            $authCode->scopes,
            $this->config['access_token_expire'] ?? 7200
        );
        
        // 删除已使用的授权码
        $authCode->delete();
        
        return $token;
    }
    
    /**
     * 刷新访问令牌
     * @param string $refreshToken 刷新令牌
     * @param string $appId 应用ID
     * @param string $appSecret 应用密钥
     * @return array
     */
    public function refreshToken($refreshToken, $appId, $appSecret)
    {
        // 验证应用
        $app = App::verifySecret($appId, $appSecret);
        if (!$app) {
            return false;
        }
        // 验证应用信息
        $app = App::where(['app_id' => $appId, 'app_secret' => $appSecret])->find();
        if (!$app) {
            throw new \Exception('无效的应用ID或密钥');
        }
        
        return AccessToken::refreshToken(
            $refreshToken,
            $this->config['access_token_expire'] ?? 7200
        );
    }
    
    /**
     * 验证访问令牌
     * @param string $accessToken 访问令牌
     * @return array|null
     */
    public function verifyToken($accessToken)
    {
        return AccessToken::getValidToken($accessToken);
    }
    
    /**
     * 撤销访问令牌
     * @param string $accessToken 访问令牌
     * @param string $appId 应用ID
     * @return bool
     */
    public function revokeToken($accessToken, $appId)
    {
        // 验证令牌所属应用
        $tokenInfo = $this->verifyToken($accessToken);
        if (!$tokenInfo || $tokenInfo['app_id'] !== $appId) {
            return false;
        }
        
        return AccessToken::revokeToken($accessToken);
    }
}