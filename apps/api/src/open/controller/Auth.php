<?php
namespace apps\api\src\open\controller;

use app\common\ApiCode;
use app\common\controller\BaseController;
use apps\api\src\model\App;
use apps\api\src\model\Developer;
use apps\api\src\service\OAuth2Service;
use think\facade\Validate;

/**
 * 开放平台授权控制器
 */
class Auth extends BaseController
{
    /**
     * 授权页面
     * 用于用户授权第三方应用访问其资源
     */
    public function authorize()
    {
        $param = $this->request->param();
        
        // 验证参数
        $validate = Validate::rule([
            'response_type' => 'require|in:code',
            'client_id' => 'require',
            'redirect_uri' => 'require|url',
            'scope' => 'require',
            'state' => 'require'
        ]);
        
        if (!$validate->check($param)) {
            $this->error('参数错误: ' . $validate->getError(), [], ApiCode::BAD_REQUEST);
        }
        
        // 检查应用是否存在
        $app = App::getByAppKey($param['client_id']);
        if (!$app) {
            $this->error('应用不存在', [], ApiCode::NOT_FOUND);
        }
        
        // 检查应用状态
        if ($app['status'] != 1) {
            $this->error('应用未通过审核或已被禁用', [], ApiCode::FORBIDDEN);
        }
        
        // 检查回调地址是否匹配
        if ($app['redirect_uri'] != $param['redirect_uri']) {
            $this->error('回调地址不匹配', [], ApiCode::BAD_REQUEST);
        }
        
        // 获取开发者信息
        $developer = Developer::getInfo($app['developer_id']);
        
        // 检查用户是否已登录
        $userId = session('user.id');
        if (!$userId) {
            // 未登录，跳转到登录页面，并带上授权参数
            $loginUrl = url('index/login/index', ['redirect' => urlencode(url('api/open.auth/authorize', $param))]);
            $this->ok('获取登入地址', ['login_url' => $loginUrl], ApiCode::SUCCESS);
        }
        
        // 如果是POST请求，表示用户已确认授权
        if ($this->request->isPost()) {
            $oauth2Service = new OAuth2Service();
            $code = $oauth2Service->generateAuthCode(
                $app['id'],
                $userId,
                $param['redirect_uri'],
                $param['scope']
            );
            
            if (!$code) {
                $this->error('授权失败', [], ApiCode::SERVER_ERROR);
            }
            
            // 重定向到回调地址
            $redirectUrl = $param['redirect_uri'] . '?code=' . $code . '&state=' . $param['state'];
            return redirect($redirectUrl);
        }
        
        // 显示授权页面
        $this->ok('authorize', [
            'app' => $app,
            'developer' => $developer,
            'scope' => $param['scope'],
            'state' => $param['state'],
            'redirect_uri' => $param['redirect_uri'],
            'client_id' => $param['client_id']
        ]);
    }
    
    /**
     * 获取访问令牌
     * 用于应用通过授权码获取访问令牌
     */
    public function token()
    {
        $param = $this->request->param();
        
        // 验证参数
        $validate = Validate::rule([
            'grant_type' => 'require|in:authorization_code,refresh_token',
            'client_id' => 'require',
            'client_secret' => 'require'
        ]);
        
        if (!$validate->check($param)) {
            return json(['error' => 'invalid_request', 'error_description' => $validate->getError()]);
        }
        
        $oauth2Service = new OAuth2Service();
        
        // 根据授权类型处理
        if ($param['grant_type'] == 'authorization_code') {
            // 验证授权码
            $validate = Validate::rule([
                'code' => 'require',
                'redirect_uri' => 'require|url'
            ]);
            
            if (!$validate->check($param)) {
                return json(['error' => 'invalid_request', 'error_description' => $validate->getError()]);
            }
            
            // 获取访问令牌
            $tokenInfo = $oauth2Service->getAccessToken(
                $param['code'],
                $param['client_id'],
                $param['client_secret'],
                $param['redirect_uri']
            );
            
            if (!$tokenInfo) {
                return json(['error' => 'invalid_grant', 'error_description' => '授权码无效或已过期']);
            }
            
            return json($tokenInfo);
        } elseif ($param['grant_type'] == 'refresh_token') {
            // 验证刷新令牌
            $validate = Validate::rule([
                'refresh_token' => 'require'
            ]);
            
            if (!$validate->check($param)) {
                return json(['error' => 'invalid_request', 'error_description' => $validate->getError()]);
            }
            
            // 刷新访问令牌
            $tokenInfo = $oauth2Service->refreshToken(
                $param['refresh_token'],
                $param['client_id'],
                $param['client_secret']
            );
            
            if (!$tokenInfo) {
                return json(['error' => 'invalid_grant', 'error_description' => '刷新令牌无效或已过期']);
            }
            
            return json($tokenInfo);
        }
        
        return json(['error' => 'unsupported_grant_type', 'error_description' => '不支持的授权类型']);
    }
    
    /**
     * 撤销令牌
     * 用于应用撤销访问令牌
     */
    public function revoke()
    {
        $param = $this->request->param();
        
        // 验证参数
        $validate = Validate::rule([
            'token' => 'require',
            'client_id' => 'require',
            'client_secret' => 'require'
        ]);
        
        if (!$validate->check($param)) {
            return json(['error' => 'invalid_request', 'error_description' => $validate->getError()]);
        }
        
        // 验证应用
        $app = App::verifySecret($param['client_id'], $param['client_secret']);
        if (!$app) {
            return json(['error' => 'invalid_client', 'error_description' => '应用密钥无效']);
        }
        
        // 撤销令牌
        $oauth2Service = new OAuth2Service();
        $result = $oauth2Service->revokeToken($param['token'], $app['id']);
        
        return json(['result' => $result]);
    }
}