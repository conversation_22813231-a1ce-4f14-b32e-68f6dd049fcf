<?php
namespace apps\api\src\open\controller;

use app\common\ApiCode;
use app\common\controller\BaseController;
use apps\api\src\model\Api;
use apps\api\src\model\App;
use apps\api\src\model\Developer as DeveloperModel;
use apps\api\src\service\ApiService;
use think\facade\Validate;

/**
 * 开放平台开发者门户控制器
 */
class Developer extends BaseController
{
    /**
     * 开发者门户首页
     */
    public function index()
    {
        // 检查用户是否已登录
        $userId = session('user.id');
        if (!$userId) {
            return redirect(url('index/login/index', ['redirect' => urlencode(url('api/open.developer/index'))]));
        }
        
        // 获取开发者信息
        $developer = DeveloperModel::getByUserId($userId);
        
        // 如果不是开发者，显示注册页面
        if (!$developer) {
            return $this->register();
        }
        
        // 如果开发者未审核通过，显示审核中页面
        if ($developer['status'] == 0) {
            return view('pending', [
                'developer' => $developer
            ]);
        } elseif ($developer['status'] == 2) {
            // 如果开发者被拒绝，显示拒绝页面
            return view('rejected', [
                'developer' => $developer
            ]);
        }
        
        // 获取开发者的应用列表
        $apps = App::getDeveloperApps($developer['id']);
        
        // 获取API分类列表
        $apiCategories = Api::getCategoryList();
        
        return view('index', [
            'developer' => $developer,
            'apps' => $apps,
            'apiCategories' => $apiCategories
        ]);
    }
    
    /**
     * 开发者注册
     */
    public function register()
    {
        // 检查用户是否已登录
        $userId = session('user.id');
        if (!$userId) {
            return redirect(url('index/login/index', ['redirect' => urlencode(url('api/open.developer/register'))]));
        }
        
        // 检查是否已经是开发者
        $developer = DeveloperModel::getByUserId($userId);
        if ($developer) {
            return redirect(url('api/open.developer/index'));
        }
        
        if ($this->request->isPost()) {
            $param = $this->request->param();
            
            // 验证数据
            $validate = Validate::rule([
                'developer_name' => 'require|max:50',
                'company_name' => 'require|max:100',
                'contact_name' => 'require|max:50',
                'contact_phone' => 'require|mobile',
                'contact_email' => 'require|email',
                'website' => 'url'
            ]);
            
            if (!$validate->check($param)) {
                return $this->error($validate->getError(), [], ApiCode::BAD_REQUEST);
            }
            
            // 创建开发者
            $data = $param;
            $data['user_id'] = $userId;
            $data['status'] = 0; // 待审核
            
            $result = DeveloperModel::createDeveloper($data);
            if ($result) {
                return $this->success('申请提交成功，请等待审核', ['developer_id' => $result]);
            } else {
                return $this->error('申请提交失败', [], ApiCode::SERVER_ERROR);
            }
        }
        
        return view('register');
    }
    
    /**
     * 创建应用
     */
    public function createApp()
    {
        // 检查用户是否已登录
        $userId = session('user.id');
        if (!$userId) {
            return $this->error('请先登录', [], ApiCode::UNAUTHORIZED);
        }
        
        // 获取开发者信息
        $developer = DeveloperModel::getByUserId($userId);
        if (!$developer) {
            return $this->error('您还不是开发者', [], ApiCode::FORBIDDEN);
        }
        
        // 检查开发者状态
        if ($developer['status'] != 1) {
            return $this->error('开发者账号未审核通过', [], ApiCode::FORBIDDEN);
        }
        
        if ($this->request->isPost()) {
            $param = $this->request->param();
            
            // 验证数据
            $validate = Validate::rule([
                'app_name' => 'require|max:50',
                'app_desc' => 'require|max:255',
                'redirect_uri' => 'require|url',
                'app_type' => 'require|in:1,2,3'
            ]);
            
            if (!$validate->check($param)) {
                return $this->error($validate->getError(), [], ApiCode::BAD_REQUEST);
            }
            
            // 创建应用
            $data = $param;
            $data['developer_id'] = $developer['id'];
            $data['status'] = 0; // 待审核
            $data['scope'] = 'basic'; // 默认授权范围
            
            $result = App::createApp($data);
            if ($result) {
                return $this->success('应用创建成功，请等待审核', ['app_id' => $result]);
            } else {
                return $this->error('应用创建失败', [], ApiCode::SERVER_ERROR);
            }
        }
        
        return view('create_app', [
            'developer' => $developer
        ]);
    }
    
    /**
     * 应用详情
     */
    public function appDetail()
    {
        $appId = $this->request->param('id/d', 0);
        if ($appId <= 0) {
            return $this->error('参数错误', [], ApiCode::BAD_REQUEST);
        }
        
        // 检查用户是否已登录
        $userId = session('user.id');
        if (!$userId) {
            return $this->error('请先登录', [], ApiCode::UNAUTHORIZED);
        }
        
        // 获取开发者信息
        $developer = OpenDeveloper::getByUserId($userId);
        if (!$developer) {
            return $this->error('您还不是开发者', [], ApiCode::FORBIDDEN);
        }
        
        // 获取应用信息
        $app = App::getInfo($appId);
        if (!$app || $app['developer_id'] != $developer['id']) {
            return $this->error('应用不存在或无权限查看', [], ApiCode::FORBIDDEN);
        }
        
        // 获取应用的API权限列表
        $apiService = new ApiService();
        $permissions = $apiService->getAppApiPermissions($appId);
        
        // 按分类分组
        $categoryApis = [];
        foreach ($permissions as $api) {
            if ($api['has_permission']) {
                $category = $api['api_category'];
                if (!isset($categoryApis[$category])) {
                    $categoryApis[$category] = [];
                }
                $categoryApis[$category][] = $api;
            }
        }
        
        // 获取API调用统计
        $startDate = date('Y-m-d', strtotime('-30 days'));
        $endDate = date('Y-m-d');
        $stats = $apiService->getApiCallStats($appId, $startDate, $endDate);
        
        return view('app_detail', [
            'developer' => $developer,
            'app' => $app,
            'categoryApis' => $categoryApis,
            'stats' => $stats
        ]);
    }
    
    /**
     * 编辑应用
     */
    public function editApp()
    {
        $appId = $this->request->param('id/d', 0);
        if ($appId <= 0) {
            return $this->error('参数错误', [], ApiCode::BAD_REQUEST);
        }
        
        // 检查用户是否已登录
        $userId = session('user.id');
        if (!$userId) {
            return $this->error('请先登录', [], ApiCode::UNAUTHORIZED);
        }
        
        // 获取开发者信息
        $developer = OpenDeveloper::getByUserId($userId);
        if (!$developer) {
            return $this->error('您还不是开发者', [], ApiCode::FORBIDDEN);
        }
        
        // 获取应用信息
        $app = App::getInfo($appId);
        if (!$app || $app['developer_id'] != $developer['id']) {
            return $this->error('应用不存在或无权限编辑', [], ApiCode::FORBIDDEN);
        }
        
        if ($this->request->isPost()) {
            $param = $this->request->param();
            
            // 验证数据
            $validate = Validate::rule([
                'app_name' => 'require|max:50',
                'app_desc' => 'require|max:255',
                'redirect_uri' => 'require|url'
            ]);
            
            if (!$validate->check($param)) {
                return $this->error($validate->getError(), [], ApiCode::BAD_REQUEST);
            }
            
            // 更新应用
            $data = [
                'app_name' => $param['app_name'],
                'app_desc' => $param['app_desc'],
                'redirect_uri' => $param['redirect_uri']
            ];
            
            $result = App::updateApp($appId, $data);
            if ($result) {
                return $this->success('应用更新成功');
            } else {
                return $this->error('应用更新失败', [], ApiCode::SERVER_ERROR);
            }
        }
        
        return view('edit_app', [
            'developer' => $developer,
            'app' => $app
        ]);
    }
    
    /**
     * 重置应用密钥
     */
    public function resetAppSecret()
    {
        $appId = $this->request->param('id/d', 0);
        if ($appId <= 0) {
            return $this->error('参数错误', [], ApiCode::BAD_REQUEST);
        }
        
        // 检查用户是否已登录
        $userId = session('user.id');
        if (!$userId) {
            return $this->error('请先登录', [], ApiCode::UNAUTHORIZED);
        }
        
        // 获取开发者信息
        $developer = DeveloperModel::getByUserId($userId);
        if (!$developer) {
            return $this->error('您还不是开发者', [], ApiCode::FORBIDDEN);
        }
        
        // 获取应用信息
        $app = App::getInfo($appId);
        if (!$app || $app['developer_id'] != $developer['id']) {
            return $this->error('应用不存在或无权限操作', [], ApiCode::FORBIDDEN);
        }
        
        // 重置密钥
        $newSecret = App::resetSecret($appId);
        if ($newSecret) {
            return $this->success('密钥重置成功', ['app_secret' => $newSecret]);
        } else {
            return $this->error('密钥重置失败', [], ApiCode::SERVER_ERROR);
        }
    }
    
    /**
     * API文档
     */
    public function apiDoc()
    {
        // 获取API列表
        $apis = Api::where('status', 1)
            ->order('api_category', 'asc')
            ->order('sort', 'desc')
            ->select()
            ->toArray();
        
        // 按分类分组
        $categoryApis = [];
        foreach ($apis as $api) {
            $category = $api['api_category'];
            if (!isset($categoryApis[$category])) {
                $categoryApis[$category] = [];
            }
            $categoryApis[$category][] = $api;
        }
        
        return view('api_doc', [
            'categoryApis' => $categoryApis
        ]);
    }
    
    /**
     * SDK下载
     */
    public function sdkDownload()
    {
        // 获取SDK列表
        $sdks = [
            [
                'name' => 'PHP SDK',
                'version' => 'v1.0.0',
                'description' => '用于PHP应用的开放平台SDK',
                'download_url' => url('api/open.developer/downloadSdk', ['type' => 'php'])
            ],
            [
                'name' => 'Java SDK',
                'version' => 'v1.0.0',
                'description' => '用于Java应用的开放平台SDK',
                'download_url' => url('api/open.developer/downloadSdk', ['type' => 'java'])
            ],
            [
                'name' => 'Python SDK',
                'version' => 'v1.0.0',
                'description' => '用于Python应用的开放平台SDK',
                'download_url' => url('api/open.developer/downloadSdk', ['type' => 'python'])
            ],
            [
                'name' => 'JavaScript SDK',
                'version' => 'v1.0.0',
                'description' => '用于Web前端的开放平台SDK',
                'download_url' => url('api/open.developer/downloadSdk', ['type' => 'js'])
            ]
        ];
        
        return view('sdk_download', [
            'sdks' => $sdks
        ]);
    }
    
    /**
     * 下载SDK
     */
    public function downloadSdk()
    {
        $type = $this->request->param('type', '');
        
        // 根据类型返回不同的SDK包
        switch ($type) {
            case 'php':
                return download('sdk/php_sdk.zip', 'php_sdk.zip');
            case 'java':
                return download('sdk/java_sdk.zip', 'java_sdk.zip');
            case 'python':
                return download('sdk/python_sdk.zip', 'python_sdk.zip');
            case 'js':
                return download('sdk/js_sdk.zip', 'js_sdk.zip');
            default:
                return $this->error('SDK类型不存在', [], ApiCode::NOT_FOUND);
        }
    }
    
    /**
     * API调试工具
     */
    public function apiTester()
    {
        // 获取API列表
        $apis = Api::where('status', 1)
            ->order('api_category', 'asc')
            ->order('sort', 'desc')
            ->select()
            ->toArray();
        
        // 按分类分组
        $categoryApis = [];
        foreach ($apis as $api) {
            $category = $api['api_category'];
            if (!isset($categoryApis[$category])) {
                $categoryApis[$category] = [];
            }
            $categoryApis[$category][] = $api;
        }
        
        return view('api_tester', [
            'categoryApis' => $categoryApis
        ]);
    }
}