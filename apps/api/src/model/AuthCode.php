<?php
namespace apps\api\src\model;

use think\Model;

/**
 * OAuth2授权码模型
 */
class AuthCode extends Model
{
    protected $name = 'plugin_cloud_api_auth_code';
    
    /**
     * 获取有效的授权码
     * @param string $code 授权码
     * @return array|null 授权码信息
     */
    public static function getValidCode($code)
    {
        return self::where([
            'code' => $code,
            'status' => 1
        ])->where('expire_time', '>', time())
          ->find();
    }
    
    /**
     * 创建授权码
     * @param int $appId 应用ID
     * @param int $userId 用户ID
     * @param string $redirectUri 重定向URI
     * @param array $scopes 权限范围
     * @param int $expireIn 有效期（秒）
     * @return array 授权码信息
     */
    public static function createCode($appId, $userId, $redirectUri, $scopes = [], $expireIn = 600)
    {
        $code = md5(uniqid(mt_rand(), true));
        $expireTime = time() + $expireIn;
        
        $data = [
            'app_id' => $appId,
            'user_id' => $userId,
            'code' => $code,
            'redirect_uri' => $redirectUri,
            'scope' => implode(',', $scopes),
            'expire_time' => $expireTime,
            'create_time' => time(),
            'status' => 1
        ];
        
        $model = self::create($data);
        return $model ? $model->toArray() : null;
    }
    
    /**
     * 使授权码失效
     * @param string $code 授权码
     * @return bool 是否成功
     */
    public static function invalidateCode($code)
    {
        return self::where('code', $code)
            ->update(['status' => 0]) !== false;
    }
}