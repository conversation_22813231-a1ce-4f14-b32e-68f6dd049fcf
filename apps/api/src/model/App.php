<?php
namespace apps\api\src\model;

use think\Model;

/**
 * 开放平台应用模型
 */
class App extends Model
{
    protected $name = 'plugin_cloud_api_app';
    
    /**
     * 获取列表
     */
    public static function getList($where = [], $page = 1, $limit = 15)
    {
        $total = self::where($where)->count();
        $list = self::where($where)
            ->page($page, $limit)
            ->order('id', 'desc')
            ->select()
            ->toArray();
            
        return ['total' => $total, 'list' => $list];
    }
    
    /**
     * 获取应用信息
     */
    public static function getInfo($id)
    {
        return self::where('id', $id)->find();
    }
    
    /**
     * 审核应用
     */
    public static function audit($id, $status)
    {
        return self::where('id', $id)->update([
            'status' => $status,
            'update_time' => time()
        ]);
    }
    
    /**
     * 重置应用密钥
     */
    public static function resetSecret($id)
    {
        $newSecret = md5(uniqid(mt_rand(), true));
        $result = self::where('id', $id)->update([
            'app_secret' => $newSecret,
            'update_time' => time()
        ]);
        
        return $result ? $newSecret : false;
    }
}