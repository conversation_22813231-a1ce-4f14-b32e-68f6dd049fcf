<?php
namespace apps\api\src\model;

use think\Model;

/**
 * 开放平台开发者模型
 */
class Developer extends Model
{
    // 设置表名
    protected $name = 'plugin_cloud_api_developer';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    /**
     * 获取开发者信息
     * @param int $id 开发者ID
     * @return array|null 开发者信息
     */
    public static function getInfo($id)
    {
        return self::where('id', $id)->find();
    }
    
    /**
     * 根据用户ID获取开发者信息
     * @param int $userId 用户ID
     * @return array|null 开发者信息
     */
    public static function getByUserId($userId)
    {
        return self::where('user_id', $userId)->find();
    }
    
    /**
     * 创建开发者
     * @param array $data 开发者数据
     * @return int|bool 成功返回开发者ID，失败返回false
     */
    public static function createDeveloper($data)
    {
        $model = new self();
        $model->save($data);
        return $model->id ?: false;
    }
    
    /**
     * 更新开发者信息
     * @param int $id 开发者ID
     * @param array $data 更新数据
     * @return bool 是否成功
     */
    public static function updateDeveloper($id, $data)
    {
        return self::where('id', $id)->update($data) !== false;
    }
    
    /**
     * 审核开发者
     * @param int $id 开发者ID
     * @param int $status 审核状态：1=通过，2=拒绝
     * @param string $remark 备注
     * @return bool 是否成功
     */
    public static function audit($id, $status, $remark = '')
    {
        $data = [
            'status' => $status,
            'remark' => $remark,
            'update_time' => time()
        ];
        return self::where('id', $id)->update($data) !== false;
    }
    
    /**
     * 获取开发者列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array 开发者列表
     */
    public static function getList($where = [], $page = 1, $limit = 15)
    {
        $query = self::where($where);
        $total = $query->count();
        $list = $query->page($page, $limit)->order('id', 'desc')->select()->toArray();
        
        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit
        ];
    }
}