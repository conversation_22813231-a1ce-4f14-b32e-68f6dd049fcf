<?php
namespace apps\api\src\model;

use think\Model;

/**
 * 开放平台API接口模型
 */
class Api extends Model
{
    protected $name = 'plugin_cloud_api_api';
    
    /**
     * 获取列表
     */
    public static function getList($where = [], $page = 1, $limit = 15)
    {
        $total = self::where($where)->count();
        $list = self::where($where)
            ->page($page, $limit)
            ->order('sort', 'asc')
            ->order('id', 'desc')
            ->select()
            ->toArray();
            
        return ['total' => $total, 'list' => $list];
    }
    
    /**
     * 获取API信息
     */
    public static function getInfo($id)
    {
        return self::where('id', $id)->find();
    }
    
    /**
     * 获取应用可用的API列表
     */
    public static function getAppApiList($appId)
    {
        return self::alias('a')
            ->join('plugin_cloud_api_app_api aa', 'a.id = aa.api_id')
            ->where('aa.app_id', $appId)
            ->where('a.status', 1)
            ->field('a.*')
            ->select()
            ->toArray();
    }
}