<?php
namespace apps\api\src\model;

use think\Model;

/**
 * OAuth2访问令牌模型
 */
class AccessToken extends Model
{
    protected $name = 'plugin_cloud_api_access_token';
    
    /**
     * 获取有效的访问令牌
     * @param string $token 访问令牌
     * @return array|null 令牌信息
     */
    public static function getValidToken($token)
    {
        return self::where([
            'token' => $token,
            'status' => 1
        ])->where('expire_time', '>', time())
          ->find();
    }
    
    /**
     * 创建访问令牌
     * @param int $appId 应用ID
     * @param int $userId 用户ID
     * @param array $scopes 权限范围
     * @param int $expireIn 有效期（秒）
     * @return array 令牌信息
     */
    public static function createToken($appId, $userId, $scopes = [], $expireIn = 7200)
    {
        $token = md5(uniqid(mt_rand(), true));
        $refreshToken = md5(uniqid(mt_rand(), true));
        $expireTime = time() + $expireIn;
        
        $data = [
            'app_id' => $appId,
            'user_id' => $userId,
            'token' => $token,
            'refresh_token' => $refreshToken,
            'scope' => implode(',', $scopes),
            'expire_time' => $expireTime,
            'create_time' => time(),
            'status' => 1
        ];
        
        $model = self::create($data);
        return $model ? $model->toArray() : null;
    }
    
    /**
     * 刷新访问令牌
     * @param string $refreshToken 刷新令牌
     * @param int $expireIn 新令牌有效期（秒）
     * @return array|null 新的令牌信息
     */
    public static function refreshToken($refreshToken, $expireIn = 7200)
    {
        $tokenInfo = self::where([
            'refresh_token' => $refreshToken,
            'status' => 1
        ])->find();
        
        if (!$tokenInfo) {
            return null;
        }
        
        // 生成新的访问令牌
        $newToken = md5(uniqid(mt_rand(), true));
        $expireTime = time() + $expireIn;
        
        // 更新令牌信息
        $tokenInfo->token = $newToken;
        $tokenInfo->expire_time = $expireTime;
        $tokenInfo->save();
        
        return $tokenInfo->toArray();
    }
    
    /**
     * 撤销访问令牌
     * @param string $token 访问令牌
     * @return bool 是否成功
     */
    public static function revokeToken($token)
    {
        return self::where('token', $token)
            ->update(['status' => 0]) !== false;
    }
}