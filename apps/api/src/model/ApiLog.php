<?php
namespace apps\api\src\model;

use think\Model;

/**
 * API调用日志模型
 */
class ApiLog extends Model
{
    protected $name = 'plugin_cloud_api_log';
    
    /**
     * 获取日志列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array 日志列表和总数
     */
    public static function getList($where = [], $page = 1, $limit = 15)
    {
        $total = self::where($where)->count();
        $list = self::where($where)
            ->page($page, $limit)
            ->order('id', 'desc')
            ->select()
            ->toArray();
            
        return ['total' => $total, 'list' => $list];
    }
    
    /**
     * 获取日志详情
     * @param int $id 日志ID
     * @return array|null 日志信息
     */
    public static function getInfo($id)
    {
        return self::where('id', $id)->find();
    }
    
    /**
     * 获取应用的API调用统计
     * @param int $appId 应用ID
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 统计数据
     */
    public static function getAppStats($appId, $startDate = '', $endDate = '')
    {
        $query = self::where('app_id', $appId);
        
        if ($startDate) {
            $startTime = strtotime($startDate . ' 00:00:00');
            $query->where('create_time', '>=', $startTime);
        }
        
        if ($endDate) {
            $endTime = strtotime($endDate . ' 23:59:59');
            $query->where('create_time', '<=', $endTime);
        }
        
        // 总调用次数
        $totalCalls = $query->count();
        
        // 成功调用次数
        $successCalls = $query->where('status_code', 200)->count();
        
        // 失败调用次数
        $failedCalls = $totalCalls - $successCalls;
        
        // 按API分组统计
        $apiStats = $query->group('api_id')
            ->field('api_id, count(*) as call_count')
            ->order('call_count', 'desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        return [
            'total_calls' => $totalCalls,
            'success_calls' => $successCalls,
            'failed_calls' => $failedCalls,
            'api_stats' => $apiStats
        ];
    }
    
    /**
     * 清理过期日志
     * @param int $days 保留天数
     * @return bool 是否成功
     */
    public static function cleanExpiredLogs($days = 30)
    {
        $expireTime = time() - ($days * 86400);
        return self::where('create_time', '<', $expireTime)->delete() !== false;
    }
}