<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit8d718e3795c4be8c66479022f91fb6e6
{
    public static $prefixLengthsPsr4 = array (
        'a' => 
        array (
            'apps\\api\\' => 9,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'apps\\api\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit8d718e3795c4be8c66479022f91fb6e6::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit8d718e3795c4be8c66479022f91fb6e6::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit8d718e3795c4be8c66479022f91fb6e6::$classMap;

        }, null, ClassLoader::class);
    }
}
