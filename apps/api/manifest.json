{"name": "api", "title": "开放平台API", "description": "提供开放平台API接口、OAuth2授权、开发者门户等功能", "version": "1.0.0", "author": "System", "status": 1, "dependencies": [], "hooks": {"system.init": {"description": "系统初始化钩子，在系统启动时触发", "params": [], "handler": "\\apps\\api\\src\\hooks\\HookHandlers", "method": "onSystemInit"}, "api.error": {"description": "API调用错误时的钩子", "params": {"api_path": "API路径", "method": "请求方法", "params": "请求参数", "error": "错误信息", "app_id": "调用方应用ID"}, "handler": "\\apps\\api\\src\\hooks\\HookHandlers", "method": "onApiError"}}, "config": {"enabled": false, "global_startup": {"title": "全局启动", "description": "是否在系统启动时自动加载插件", "type": "switch", "value": false, "tip": "开启后，插件将在系统启动时自动加载"}, "oauth": {"title": "OAuth2配置", "description": "OAuth2授权相关配置", "type": "group", "children": {"auth_code_expire": {"title": "授权码有效期", "description": "授权码有效期（秒）", "type": "number", "value": 600, "tip": "授权码的有效期，默认为10分钟"}, "access_token_expire": {"title": "访问令牌有效期", "description": "访问令牌有效期（秒）", "type": "number", "value": 7200, "tip": "访问令牌的有效期，默认为2小时"}, "refresh_token_expire": {"title": "刷新令牌有效期", "description": "刷新令牌有效期（秒）", "type": "number", "value": 2592000, "tip": "刷新令牌的有效期，默认为30天"}}}, "developer": {"title": "开发者配置", "description": "开发者门户相关配置", "type": "group", "children": {"auto_approve": {"title": "自动审核", "description": "是否自动审核开发者申请", "type": "switch", "value": false, "tip": "开启后，开发者申请将自动通过审核"}, "app_auto_approve": {"title": "应用自动审核", "description": "是否自动审核应用申请", "type": "switch", "value": false, "tip": "开启后，应用申请将自动通过审核"}}}}}