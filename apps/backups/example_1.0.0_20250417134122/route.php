<?php

return [
    // 路由分组
    'group' => [
        'prefix' => 'example',
        'middleware' => [],
        'routes' => [
            // 简单路由定义
            'index' => 'Example/index',
            'test' => 'Example/test',
            
            // 详细路由定义
            [
                'method' => 'get',
                'rule' => 'config',
                'controller' => 'Example',
                'action' => 'config',
                'middleware' => ['admin']
            ],
            [
                'method' => 'post',
                'rule' => 'save',
                'controller' => 'Example',
                'action' => 'save',
                'middleware' => ['admin']
            ]
        ]
    ],
    
    // 独立路由
    'routes' => [
        // 简单路由定义
        'api/example/info' => 'Example/info',
        
        // 详细路由定义
        [
            'method' => 'get',
            'rule' => 'api/example/data',
            'controller' => 'Example',
            'action' => 'data',
            'middleware' => []
        ]
    ]
]; 