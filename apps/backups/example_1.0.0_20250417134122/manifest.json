{"name": "example", "title": "这是一个示例插件", "description": "这是一个示例插件", "version": "1.0.0", "author": "System", "status": 1, "dependencies": [], "composer": [], "package": [], "hooks": {"comment.created": {"description": "商品评论创建后的钩子", "params": {"comment_id": "评论ID", "goods_id": "商品ID", "user_id": "用户ID", "content": "评论内容"}}, "notification.send": {"description": "发送通知的钩子", "params": {"type": "通知类型", "to_user_id": "接收用户ID", "title": "通知标题", "content": "通知内容"}}}, "config": {"enabled": false, "version": "1.0.0", "settings": {"title": "示例插件", "description": "这是一个示例插件，用于展示插件系统的功能", "author": "Anchor Team"}, "features": {"enable_cache": true, "cache_time": 3600, "max_items": 100}, "display": {"show_in_menu": true, "menu_position": 10, "menu_icon": "fa fa-cube"}, "api": {"enabled": true, "rate_limit": 100, "allowed_ips": []}, "security": {"require_auth": true, "allowed_roles": ["admin", "editor"], "encrypt_data": false}, "custom": {"option1": "value1", "option2": "value2", "option3": {"sub_option1": "sub_value1", "sub_option2": "sub_value2"}}}, "upgrade": {"minimum_version": "1.0.0", "version": "1.1.0", "backup": true, "source": "local", "scripts": {"pre_upgrade": "upgrade/pre_upgrade.php", "post_upgrade": "upgrade/post_upgrade.php"}}}