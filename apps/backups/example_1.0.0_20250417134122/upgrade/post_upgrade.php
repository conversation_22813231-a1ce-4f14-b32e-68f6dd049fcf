<?php

namespace plugins\example\upgrade;

use think\facade\Db;
use think\facade\Log;

/**
 * 示例插件后升级脚本
 * 在升级过程中，此脚本会在启用插件前执行
 */

// 记录升级开始日志
Log::info('Example plugin post-upgrade script started');

// 迁移旧数据到新结构
try {
    // 获取所有没有extra_data的记录
    $records = Db::table('plugin_example_data')
        ->whereNull('extra_data')
        ->select()
        ->toArray();
    
    // 更新这些记录，添加额外数据
    foreach ($records as $record) {
        Db::table('plugin_example_data')
            ->where('id', $record['id'])
            ->update([
                'extra_data' => json_encode([
                    'migrated' => true,
                    'migration_time' => date('Y-m-d H:i:s'),
                    'version' => '1.1.0'
                ])
            ]);
    }
    
    Log::info('Example plugin data migration completed: ' . count($records) . ' records updated');
} catch (\Exception $e) {
    Log::error('Failed to migrate example plugin data: ' . $e->getMessage());
}

// 更新插件配置
try {
    // 检查新的配置表是否存在记录
    $settingsCount = Db::table('plugin_example_settings')->count();
    Log::info('Example plugin settings count: ' . $settingsCount);
    
    // 如果SQL脚本执行失败，手动创建配置
    if ($settingsCount == 0) {
        Db::table('plugin_example_settings')->insert([
            ['key' => 'notification_enabled', 'value' => '1'],
            ['key' => 'notification_template', 'value' => '您的商品「{goods_name}」收到了新的评论：{comment_content}']
        ]);
        Log::info('Example plugin settings manually created');
    }
} catch (\Exception $e) {
    Log::error('Failed to update example plugin settings: ' . $e->getMessage());
}

// 后升级脚本执行完成
Log::info('Example plugin post-upgrade script completed');