# 示例插件升级包使用说明

本目录包含示例插件的升级包和相关脚本，用于演示如何使用 `UpgradeTrait` 实现插件的版本升级。

## 目录结构

```
upgrade/
├── README.md                 # 本说明文件
├── v1.1.0.zip                # 1.1.0版本升级包
├── 1.0.0_to_1.1.0.sql        # 数据库升级脚本
├── pre_upgrade.php           # 预升级脚本
└── post_upgrade.php          # 后升级脚本
```

## 升级流程说明

1. **升级包内容**：`v1.1.0.zip` 包含了从 1.0.0 升级到 1.1.0 版本所需的所有文件，包括更新后的 `manifest.json`、`Plugin.php` 等核心文件。

2. **数据库升级**：`1.0.0_to_1.1.0.sql` 文件包含了数据库结构的变更，会在升级过程中自动执行。

3. **升级脚本**：
   - `pre_upgrade.php`：在停用插件前执行，主要用于数据备份和预处理
   - `post_upgrade.php`：在更新文件后、启用插件前执行，用于数据迁移和配置更新

## 如何使用

### 方法一：通过插件管理器升级

```php
// 通过插件管理器升级插件
$pluginManager = new \app\plugin\core\PluginManager();
$result = $pluginManager->upgradePlugin('example', '1.1.0');

if ($result) {
    echo "插件升级成功！";
} else {
    echo "插件升级失败！";
}
```

### 方法二：直接调用插件的升级方法

```php
// 获取插件实例
$pluginManager = new \app\plugin\core\PluginManager();
$plugin = $pluginManager->getPlugin('example');

// 直接调用升级方法
if ($plugin) {
    $result = $plugin->upgrade('1.1.0');
    if ($result) {
        echo "插件升级成功！";
    } else {
        echo "插件升级失败！";
    }
} else {
    echo "插件不存在或未安装！";
}
```

## 版本 1.1.0 新特性

1. 新增插件配置表 `plugin_example_settings`
2. 增强通知功能，支持自定义通知模板
3. 新增系统初始化钩子 `system.init`
4. 优化数据存储结构，增加 `extra_data` 字段

## 注意事项

1. 升级前请确保已备份重要数据
2. 升级过程中请勿中断操作
3. 如升级失败，系统会自动回滚到之前的版本