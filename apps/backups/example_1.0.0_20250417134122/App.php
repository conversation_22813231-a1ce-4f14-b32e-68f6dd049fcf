<?php

namespace apps\example;

use app\plugin\core\BasePlugin;

/**
 * 示例插件
 */
class App extends BasePlugin
{
    /**
     * 初始化
     */
    protected function init(): void
    {
        parent::init();
        
        // 注册商品评论创建事件的监听器
        $this->addHook('comment.created', function($params) {
            // 获取评论相关信息
            $commentId = $params['comment_id'];
            $goodsId = $params['goods_id'];
            $userId = $params['user_id'];
            $content = $params['content'];
            
            // 获取商品信息
            $goods = \app\model\Goods::find($goodsId);
            if (!$goods) {
                return;
            }
            
            // 构建通知内容
            $notificationParams = [
                'type' => 'comment_notification',
                'to_user_id' => $goods->user_id, // 通知商品所有者
                'title' => '新的商品评论',
                'content' => sprintf('您的商品「%s」收到了新的评论：%s', $goods->name, $content)
            ];
            
            // 触发通知发送钩子
            $this->doHook('notification.send', $notificationParams);
        });
        
        // 注册通知发送的处理器
        $this->addHook('notification.send', function($params) {
            // 这里实现具体的通知发送逻辑
            // 可以是站内信、邮件、短信等多种方式
            $notification = new \app\common\notification\Notification();
            $notification->send($params['to_user_id'], $params['title'], $params['content'], $params['type']);
        });
    }

    /**
     * 安装插件
     * @return bool
     */
    public function install(): bool
    {
        // 添加插件安装前的监听器
        $this->addHook('plugin.before_install', function($params) {
            \think\facade\Log::info("准备安装插件 {$params['name']}");
            // 在这里可以进行安装前的准备工作
            return true;
        });
        
        if (!parent::install()) {
            return false;
        }

        // 这里可以添加插件特定的安装逻辑
        \think\facade\Log::info("插件 {$this->name} 安装成功");
        return true;
    }

    /**
     * 卸载插件
     * @return bool
     */
    public function uninstall(): bool
    {
        // 添加插件卸载前的监听器
        $this->addHook('plugin.before_uninstall', function($params) {
            \think\facade\Log::info("准备卸载插件 {$params['name']}");
            // 在这里可以进行卸载前的清理工作
            return true;
        });

        if (!parent::uninstall()) {
            return false;
        }

        // 这里可以添加插件特定的卸载逻辑
        \think\facade\Log::info("插件 {$this->name} 卸载成功");
        return true;
    }

    /**
     * 启用插件
     * @return bool
     */
    public function enable(): bool
    {
        // 添加插件启用前的监听器
        $this->addHook('plugin.before_enable', function($params) {
            \think\facade\Log::info("准备启用插件 {$params['name']}");
            // 在这里可以进行启用前的准备工作
            return true;
        });

        if (!parent::enable()) {
            return false;
        }

        // 这里可以添加插件特定的启用逻辑
        \think\facade\Log::info("插件 {$this->name} 启用成功");
        return true;
    }

    /**
     * 禁用插件
     * @return bool
     */
    public function disable(): bool
    {
        // 添加插件禁用前的监听器
        $this->addHook('plugin.before_disable', function($params) {
            \think\facade\Log::info("准备禁用插件 {$params['name']}");
            // 在这里可以进行禁用前的清理工作
            return true;
        });

        if (!parent::disable()) {
            return false;
        }

        // 这里可以添加插件特定的禁用逻辑
        \think\facade\Log::info("插件 {$this->name} 禁用成功");
        return true;
    }
}