{"name": "example", "title": "这是一个示例插件", "description": "这是一个示例插件 - 升级到1.1.0版本，增加了新的配置功能", "version": "1.1.0", "author": "System", "status": 1, "dependencies": {}, "composer": {}, "package": {}, "hooks": {"comment.created": {"description": "商品评论创建后的钩子", "params": {"comment_id": "评论ID", "goods_id": "商品ID", "user_id": "用户ID", "content": "评论内容"}}, "notification.send": {"description": "发送通知的钩子", "params": {"type": "通知类型", "to_user_id": "接收用户ID", "title": "通知标题", "content": "通知内容"}}, "system.init": {"description": "系统初始化钩子", "params": {}}}, "config": {"enabled": false, "settings": {"title": "示例插件", "description": "这是一个示例插件，用于展示插件系统的功能", "author": "Anchor Team"}, "features": {"enable_cache": true, "cache_time": 3600, "max_items": 100}, "display": {"show_in_menu": true, "menu_position": 10, "menu_icon": "fa fa-cube"}, "api": {"enabled": true, "rate_limit": 100, "allowed_ips": []}, "security": {"require_auth": true, "allowed_roles": ["admin", "editor"], "encrypt_data": false}, "custom": {"option1": "value1", "option2": "value2", "option3": {"sub_option1": "sub_value1", "sub_option2": "sub_value2"}}, "notification": {"title": "通知设置", "description": "配置通知相关的设置", "type": "group", "children": {"notification_enabled": {"title": "启用通知", "description": "是否启用商品评论通知功能", "type": "switch", "value": true}, "notification_template": {"title": "通知模板", "description": "商品评论通知的内容模板", "type": "textarea", "value": "您的商品「{goods_name}」收到了新的评论：{comment_content}"}}}}, "upgrade": {"minimum_version": "1.0.0", "backup": true, "source": "local", "scripts": {"pre_upgrade": "upgrade/pre_upgrade.php", "post_upgrade": "upgrade/post_upgrade.php"}}}