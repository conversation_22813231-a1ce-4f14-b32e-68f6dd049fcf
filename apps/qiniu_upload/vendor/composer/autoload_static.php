<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit14334b0d300e389fcd79591806c13601
{
    public static $files = array (
        '841780ea2e1d6545ea3a253239d59c05' => __DIR__ . '/..' . '/qiniu/php-sdk/src/Qiniu/functions.php',
        '5dd19d8a547b7318af0c3a93c8bd6565' => __DIR__ . '/..' . '/qiniu/php-sdk/src/Qiniu/Http/Middleware/Middleware.php',
    );

    public static $prefixLengthsPsr4 = array (
        'a' => 
        array (
            'apps\\qiniu_upload\\' => 18,
        ),
        'Q' => 
        array (
            '<PERSON>iu\\' => 6,
        ),
        'M' => 
        array (
            'MyCLabs\\Enum\\' => 13,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'apps\\qiniu_upload\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
        '<PERSON><PERSON>\\' => 
        array (
            0 => __DIR__ . '/..' . '/qiniu/php-sdk/src/Qiniu',
        ),
        'MyCLabs\\Enum\\' => 
        array (
            0 => __DIR__ . '/..' . '/myclabs/php-enum/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Stringable' => __DIR__ . '/..' . '/myclabs/php-enum/stubs/Stringable.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit14334b0d300e389fcd79591806c13601::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit14334b0d300e389fcd79591806c13601::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit14334b0d300e389fcd79591806c13601::$classMap;

        }, null, ClassLoader::class);
    }
}
