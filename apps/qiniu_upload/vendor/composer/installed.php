<?php return array(
    'root' => array(
        'name' => 'apidisk/qiniu_upload',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'e50af7a1b3abd790666ad47b20c0b64f0e31f357',
        'type' => 'apidisk-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'apidisk/qiniu_upload' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'e50af7a1b3abd790666ad47b20c0b64f0e31f357',
            'type' => 'apidisk-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.8.4',
            'version' => '1.8.4.0',
            'reference' => 'a867478eae49c9f59ece437ae7f9506bfaa27483',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'qiniu/php-sdk' => array(
            'pretty_version' => 'v7.14.0',
            'version' => '7.14.0.0',
            'reference' => 'ee752ffa7263ce99fca0bd7340cf13c486a3516c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qiniu/php-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
