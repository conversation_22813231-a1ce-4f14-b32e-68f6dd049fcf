<?php

namespace apps\qiniu_upload\controller;

use <PERSON><PERSON>\Auth;
use <PERSON>iu\Storage\BucketManager;
use <PERSON>iu\Storage\UploadManager;

/**
 * 七牛云存储上传类
 */
class QiniuOssUpload
{
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'qiniu';
    
    /**
     * 服务提供商
     * @var string
     */
    protected $provider = 'qiniu';
    
    /**
     * 存储空间名称
     * @var string
     */
    protected $bucket;
    
    /**
     * 访问域名
     * @var string
     */
    protected $domain;
    
    /**
     * 七牛云认证对象
     * @var Auth
     */
    protected $auth;
    
    /**
     * 上传管理器
     * @var UploadManager
     */
    protected $uploadManager;
    
    /**
     * 存储空间管理器
     * @var BucketManager
     */
    protected $bucketManager;
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        parent::__construct($config);
        
        // 初始化配置
        $this->bucket = $config['bucket'] ?? '';
        $this->domain = $config['domain'] ?? '';
        
        // 初始化七牛云认证
        $accessKey = $config['access_key'] ?? '';
        $secretKey = $config['secret_key'] ?? '';
        $this->auth = new Auth($accessKey, $secretKey);
        
        // 初始化管理器
        $this->uploadManager = new UploadManager();
        $this->bucketManager = new BucketManager($this->auth);
    }
    
    /**
     * 上传文件
     * @param string $file 文件路径或文件对象
     * @param string $savePath 保存路径
     * @param array $options 上传选项
     * @return array 上传结果
     */
    public function upload($file, $savePath = '', array $options = [])
    {
        try {
            // 生成上传凭证
            $token = $this->auth->uploadToken($this->bucket);
            
            // 处理保存路径
            $key = $savePath ? trim($savePath, '/') : null;
            
            // 执行上传
            list($ret, $err) = $this->uploadManager->putFile($token, $key, $file);
            
            if ($err !== null) {
                $this->setError($err->message());
                return [];
            }
            
            // 返回上传结果
            return [
                'url' => $this->getUrl($ret['key']),
                'path' => $ret['key'],
                'name' => basename($file),
                'size' => filesize($file),
                'mime_type' => mime_content_type($file),
                'extension' => pathinfo($file, PATHINFO_EXTENSION),
            ];
        } catch (\Exception $e) {
            $this->setError($e->getMessage());
            return [];
        }
    }
    
    /**
     * 删除文件
     * @param string $filePath 文件路径
     * @return bool 删除结果
     */
    public function delete($filePath)
    {
        try {
            $err = $this->bucketManager->delete($this->bucket, $filePath);
            return $err === null;
        } catch (\Exception $e) {
            $this->setError($e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取文件访问URL
     * @param string $filePath 文件路径
     * @param int $expires 过期时间（秒）
     * @return string 文件URL
     */
    public function getUrl($filePath, $expires = 0)
    {
        if (empty($this->domain)) {
            return '';
        }
        
        $url = rtrim($this->domain, '/') . '/' . ltrim($filePath, '/');
        
        // 私有空间需要签名访问
        if ($expires > 0) {
            $url = $this->auth->privateDownloadUrl($url, $expires);
        }
        
        return $url;
    }
}