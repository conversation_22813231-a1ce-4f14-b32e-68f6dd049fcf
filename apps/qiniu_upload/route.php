<?php

return [
    // 路由分组
    'group' => [
        'prefix' => 'qiniu',
        'routes' => [
            // 获取上传配置信息
            [
                'method' => 'get',
                'rule' => 'info',
                'controller' => 'Upload',
                'action' => 'info',
                'name' => 'plugin.qiniu.info'
            ],
            // 文件上传
            [
                'method' => 'post',
                'rule' => 'upload',
                'controller' => 'Upload',
                'action' => 'upload',
                'name' => 'plugin.qiniu.upload'
            ],
            // 文件删除
            [
                'method' => 'post',
                'rule' => 'delete',
                'controller' => 'Upload',
                'action' => 'delete',
                'name' => 'plugin.qiniu.delete'
            ]
        ]
    ],
    
    // API路由
    'routes' => [
        // 简单路由定义
        'index' => '\apps\qiniuUpload\api\Url/info',
        // 简单路由定义
        'indexs' => '\apps\qiniuUpload\api\Url@info',
        'test' => function () {
            dump('test');
        },
        // 开放的API接口
        [
            'method' => 'get',
            'rule' => 'api/url',
            'controller' => \apps\qiniuUpload\api\Url::class,
            'action' => 'getUrl',
            'middleware' => [],
            'name' => 'plugin.qiniu.token'
        ]
    ]
];