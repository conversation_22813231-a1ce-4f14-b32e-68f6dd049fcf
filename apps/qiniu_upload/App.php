<?php

namespace apps\qiniu_upload;

use app\plugin\core\BasePlugin;
use apps\qiniu_upload\controller\QiniuOssUpload;

class App extends BasePlugin
{
    protected function init(): void
    {
        parent::init();
    }

    /**
     * 安装插件
     * @return bool
     */
    public function install(): bool
    {
        if (!parent::install()) {
            return false;
        }

        // 这里可以添加插件特定的安装逻辑
        return true;
    }

    /**
     * 卸载插件
     * @return bool
     */
    public function uninstall(): bool
    {
        if (!parent::uninstall()) {
            return false;
        }

        // 这里可以添加插件特定的卸载逻辑
        return true;
    }

    /**
     * 启用插件
     * @return bool
     */
    public function enable(): bool
    {
        if (!parent::enable()) {
            return false;
        }

        // 这里可以添加插件特定的启用逻辑
        return true;
    }

    /**
     * 禁用插件
     * @return bool
     */
    public function disable(): bool
    {
        if (!parent::disable()) {
            return false;
        }

        // 这里可以添加插件特定的禁用逻辑
        return true;
    }

    /**
     * 实现上传服务
     * @param array $config 配置信息
     * @return QiniuOssUpload
     */
    public function uploadService($config = []): QiniuOssUpload
    {
        // 合并配置
        $config = array_merge($this->getConfig(), $config);
        return new QiniuOssUpload($config);
    }
    
}