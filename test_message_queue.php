<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\vchat\services\MessageQueueService;
use think\facade\Cache;

// 初始化 ThinkPHP 应用
$app = new \think\App();
$app->initialize();

// 测试消息队列服务
$messageQueueService = new MessageQueueService();

echo "=== 消息队列功能测试 ===\n";

// 1. 测试推送 Socket.IO 消息
echo "\n1. 测试推送 Socket.IO 消息\n";
$result = $messageQueueService->pushSocketIoMessage('test_user_123', 'queue_update', [
    'message' => '这是一条测试消息',
    'timestamp' => time()
]);
echo "推送结果: " . ($result ? '成功' : '失败') . "\n";

// 2. 测试推送普通消息
echo "\n2. 测试推送普通消息\n";
$result = $messageQueueService->pushMessage([
    'type' => 'notification',
    'user_id' => 'test_user_456',
    'content' => '这是一条通知消息',
    'created_at' => date('Y-m-d H:i:s')
]);
echo "推送结果: " . ($result ? '成功' : '失败') . "\n";

// 3. 检查队列状态
echo "\n3. 检查队列状态\n";
$status = $messageQueueService->getQueueStatus();
echo "待处理消息数: " . $status['pending'] . "\n";
echo "失败消息数: " . $status['failed'] . "\n";

// 4. 查看队列中的消息（仅用于测试）
echo "\n4. 查看队列中的消息\n";
$pendingMessages = Cache::lRange(MessageQueueService::QUEUE_PENDING, 0, -1);
foreach ($pendingMessages as $index => $message) {
    $decoded = json_decode($message, true);
    echo "消息 " . ($index + 1) . ": " . json_encode($decoded, JSON_UNESCAPED_UNICODE) . "\n";
}

echo "\n=== 测试完成 ===\n";