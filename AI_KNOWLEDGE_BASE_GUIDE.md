# AI知识库系统使用指南

## 概述

AI知识库系统是将现有的帮助文档与AI功能深度整合的智能问答系统。它能够理解用户的自然语言问题，从知识库中找到相关内容，并通过AI生成准确、友好的回答。

## 系统架构

### 🏗️ 核心组件

1. **KnowledgeBaseService** - 核心服务类，处理智能问答逻辑
2. **KnowledgeBaseController** - API控制器，提供RESTful接口
3. **前端组件** - 交互式的问答界面
4. **路由配置** - API路由和页面路由

### 🔄 工作流程

```
用户问题 → 搜索相关文档 → 构建AI上下文 → 生成智能回答 → 返回结果
```

## 功能特性

### 🧠 智能问答
- **自然语言理解**: 支持自然语言问题输入
- **上下文记忆**: 保持对话历史，支持多轮对话
- **置信度评估**: 评估回答的可信度
- **来源追溯**: 显示答案来源的帮助文档

### 🔍 智能搜索
- **关键词匹配**: 基于关键词的精确搜索
- **语义相似度**: 基于语义的模糊匹配
- **混合排序**: 综合多种因素的结果排序
- **实时建议**: 输入时的实时搜索建议

### 📚 知识库管理
- **分类浏览**: 按分类查看帮助文档
- **热门问题**: 展示常见和热门问题
- **统计信息**: 知识库使用统计
- **批量处理**: 支持批量问题查询

## API接口

### 基础问答接口

#### 智能问答
```http
POST /ai/kb/ask
Content-Type: application/json

{
    "question": "如何重置密码？",
    "session_id": "user_12345",
    "use_memory": true
}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "answer": "您可以通过以下步骤重置密码：...",
        "sources": [
            {
                "id": 1,
                "title": "密码重置指南",
                "relevance_score": 0.95
            }
        ],
        "session_id": "user_12345",
        "confidence": 0.9,
        "suggestions": [
            {"title": "账户安全设置", "id": 2}
        ]
    }
}
```

#### 批量问答
```http
POST /ai/kb/batch-ask
Content-Type: application/json

{
    "questions": [
        "如何重置密码？",
        "如何修改个人信息？"
    ],
    "session_id": "batch_12345"
}
```

#### 搜索问题
```http
GET /ai/kb/search?keyword=密码&limit=5
```

### 浏览接口

#### 热门问题
```http
GET /ai/kb/popular?limit=10
```

#### 分类问题
```http
GET /ai/kb/category/1?limit=20
```

#### 问题详情
```http
GET /ai/kb/detail/123
```

### 管理接口

#### 统计信息
```http
GET /ai/kb/stats
```

#### 清除会话
```http
DELETE /ai/kb/session/user_12345
```

## 前端集成

### HTML结构
```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="/static/css/ai-knowledge-base.css">
</head>
<body>
    <div id="ai-kb-container"></div>
    
    <script src="/static/js/ai-knowledge-base.js"></script>
</body>
</html>
```

### JavaScript初始化
```javascript
// 自动初始化
const aiKB = new AiKnowledgeBase({
    apiBase: '/ai/kb',
    container: '#ai-kb-container',
    useMemory: true,
    autoSuggestions: true
});

// 手动初始化
const aiKB = new AiKnowledgeBase({
    apiBase: '/ai/kb',
    container: '#my-container',
    sessionId: 'custom_session_123',
    useMemory: false
});
```

### 自定义配置
```javascript
const aiKB = new AiKnowledgeBase({
    apiBase: '/ai/kb',
    container: '#ai-kb-container',
    sessionId: 'user_' + userId,
    useMemory: true,
    autoSuggestions: true,
    // 自定义选项
    maxHistoryLength: 10,
    suggestionDelay: 300,
    confidenceThreshold: 0.6
});
```

## 与现有系统集成

### 1. 自动回复系统集成

AI知识库已经集成到自动回复系统中：

```php
// HelpReply类已更新，支持AI智能回答
$helpReply = new HelpReply();
$reply = $helpReply->getReply($message);
```

### 2. 帮助系统集成

利用现有的Help模型和Category模型：

```php
// 获取帮助文档
$helps = Help::where('enabled', 1)->select();

// 获取分类
$categories = Category::where('enabled', 1)->select();
```

### 3. 用户系统集成

支持用户会话管理：

```php
// 用户特定的会话ID
$sessionId = 'user_' . $userId;

// 清除用户会话
$knowledgeBase->clearUserSession($userId);
```

## 配置说明

### 环境变量配置

在`.env`文件中添加：

```bash
# AI知识库配置
AI_KB_ENABLED=true
AI_KB_CACHE_TTL=300
AI_KB_MAX_RESULTS=10
AI_KB_CONFIDENCE_THRESHOLD=0.6

# AI服务配置
AI_DEFAULT_PROVIDER=deepseek
AI_MEMORY_TYPE=mysql
AI_CACHE_ENABLED=true
```

### 服务配置

```php
// config/ai_knowledge_base.php
return [
    'enabled' => env('AI_KB_ENABLED', true),
    'cache_ttl' => env('AI_KB_CACHE_TTL', 300),
    'max_results' => env('AI_KB_MAX_RESULTS', 10),
    'confidence_threshold' => env('AI_KB_CONFIDENCE_THRESHOLD', 0.6),
    
    'search' => [
        'keyword_weight' => 0.6,
        'semantic_weight' => 0.4,
        'min_similarity' => 0.3,
    ],
    
    'ai' => [
        'temperature' => 0.7,
        'max_tokens' => 800,
        'timeout' => 10,
    ]
];
```

## 使用场景

### 1. 客服自动回复
- 用户咨询时自动提供智能回答
- 减少人工客服工作量
- 提高响应速度和准确性

### 2. 帮助中心升级
- 传统帮助文档的智能化升级
- 支持自然语言搜索
- 提供个性化的问答体验

### 3. 知识管理
- 企业内部知识库
- 员工培训和支持
- 知识共享和传承

### 4. 网站集成
- 嵌入到任何网页中
- 提供即时帮助功能
- 改善用户体验

## 最佳实践

### 1. 内容优化
- **结构化内容**: 确保帮助文档结构清晰
- **关键词丰富**: 在标题和内容中包含相关关键词
- **定期更新**: 保持内容的时效性和准确性

### 2. 用户体验
- **快速响应**: 优化API响应时间
- **友好界面**: 提供直观的用户界面
- **错误处理**: 优雅处理错误情况

### 3. 性能优化
- **缓存策略**: 合理使用缓存减少重复计算
- **索引优化**: 优化数据库查询性能
- **异步处理**: 使用异步处理提高并发能力

### 4. 监控和分析
- **使用统计**: 监控API使用情况
- **质量评估**: 收集用户反馈评估回答质量
- **持续改进**: 基于数据分析持续优化

## 故障排除

### 常见问题

1. **AI服务不可用**
   - 检查AI服务配置
   - 验证API密钥
   - 查看网络连接

2. **搜索结果不准确**
   - 优化关键词提取算法
   - 调整相似度阈值
   - 改进内容质量

3. **响应速度慢**
   - 启用缓存功能
   - 优化数据库查询
   - 使用CDN加速

4. **前端显示异常**
   - 检查JavaScript控制台错误
   - 验证API接口返回
   - 确认CSS样式加载

### 调试模式

启用调试模式获取详细日志：

```php
// 在配置中启用调试
'debug' => true,
'log_level' => 'debug'
```

## 版本信息

- **版本**: 1.0.0
- **依赖**: AI服务架构 v2.0.0
- **兼容性**: PHP 8.0+, ThinkPHP 8.0+
- **浏览器支持**: Chrome 60+, Firefox 55+, Safari 12+

## 许可证

MIT License
