<?php
declare(strict_types=1);

/**
 * VChat 定时任务启动脚本
 * 用于启动会话超时检查、队列清理等定时任务
 */

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../../app/common.php';

use app\vchat\services\ScheduleService;
use app\vchat\utils\Logger;
use think\facade\Config;

// 初始化应用
\think\App::getInstance()->initialize();

// 创建日志记录器
$logger = new Logger();

// 检查是否已有进程在运行
$pidFile = __DIR__ . '/schedule.pid';
if (file_exists($pidFile)) {
    $pid = file_get_contents($pidFile);
    if ($pid && posix_kill((int)$pid, 0)) {
        $logger->warning('定时任务已在运行', ['pid' => $pid]);
        exit("定时任务已在运行，PID: {$pid}\n");
    } else {
        // 清理无效的PID文件
        unlink($pidFile);
    }
}

// 记录当前进程PID
file_put_contents($pidFile, getmypid());

// 注册信号处理器
pcntl_signal(SIGTERM, function() use ($pidFile, $logger) {
    $logger->info('收到SIGTERM信号，正在停止定时任务');
    if (file_exists($pidFile)) {
        unlink($pidFile);
    }
    exit(0);
});

pcntl_signal(SIGINT, function() use ($pidFile, $logger) {
    $logger->info('收到SIGINT信号，正在停止定时任务');
    if (file_exists($pidFile)) {
        unlink($pidFile);
    }
    exit(0);
});

try {
    $logger->info('启动VChat定时任务服务');
    
    // 创建定时任务服务实例
    $scheduleService = new ScheduleService();
    
    // 启动定时任务
    $scheduleService->start();
    
    $logger->info('定时任务服务启动成功', [
        'pid' => getmypid(),
        'status' => $scheduleService->getStatus()
    ]);
    
    // 保持进程运行
    while (true) {
        // 处理信号
        pcntl_signal_dispatch();
        
        // 短暂休眠，避免CPU占用过高
        usleep(100000); // 0.1秒
    }
    
} catch (\Exception $e) {
    $logger->error('定时任务服务启动失败', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    // 清理PID文件
    if (file_exists($pidFile)) {
        unlink($pidFile);
    }
    
    exit("定时任务启动失败: {$e->getMessage()}\n");
}