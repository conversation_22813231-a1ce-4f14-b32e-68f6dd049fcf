<?php

namespace app\vchat\listener;

use app\model\ChatSessionRecord;
use app\vchat\services\SuggestionService;
use app\vchat\utils\Log;
use app\vchat\utils\RequestConvert;
use think\Request;

class SwooleOpenListener
{
    protected $websocket;

    public function handle(Request $request): void
    {


        Log::info("[SwooleOpenListener]: 处理WebSocket连接建立事件");

        $this->websocket = app('think\swoole\Websocket'); // think\swoole\Websocket 实例

        try {
            // 获取当前连接信息
            $fd = $this->websocket->getSender();

            $ipAddress = $request->server('remote_addr', 'unknown');
            $userAgent = $request->header('user-agent', 'unknown');

            // 解析平台和设备信息
            $platform = RequestConvert::platformFromUserAgent($userAgent);
            $deviceInfo = RequestConvert::deviceFromUserAgent($userAgent);
            $browser = RequestConvert::browserFromUserAgent($userAgent);
            $app = RequestConvert::platformAppFromUserAgent($userAgent);

            // 获取用户信息
            $userId = (int)$request->get('user_id', 1);
            $userType = $request->get('user_type', 'user');
            $serviceId = 0;

            // 生成唯一session_id
            $recordSessionId = 'conn_' . uniqid() . '_' . $fd;

            // 保存连接记录
            ChatSessionRecord::createRecord([
                'session_id' => $recordSessionId,
                'user_id' => $userId,
                'service_id' => $serviceId,
                'platform' => $platform,
                'device_info' => $deviceInfo,
                'ip_address' => $ipAddress,
                'browser' => $browser,
                'app' => $app,
                'geo_location' => '',
            ]);

            Log::info("[SwooleOpenListener] 连接记录已保存: " . json_encode([
                    'fd' => $fd,
                    'user_id' => $userId,
                    'ip' => $ipAddress,
                    'platform' => $platform,
                    'device' => $deviceInfo
                ]));

            // 发送建议问题给用户
            if ($userType === 'user' && $userId > 0) {
                app(SuggestionService::class)->sendSuggestionsToUser($this->websocket, $userId, $userType);
            }

        } catch (\Throwable $e) {
            Log::error("处理WebSocket连接建立事件异常" . json_encode([
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]));

            // 尝试关闭连接
            if (isset($fd) && $this->websocket->isEstablished()) {
                $this->websocket->close();
            }
        }
    }
}