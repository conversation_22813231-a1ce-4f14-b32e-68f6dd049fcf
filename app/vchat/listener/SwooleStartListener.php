<?php

namespace app\vchat\listener;

use app\vchat\services\MessageQueueService;
use app\vchat\services\ScheduleService;
use app\vchat\utils\Log;
use Swoole\Coroutine;
use Swoole\WebSocket\Server;

class SwooleStartListener
{

    protected $manager;
    public function handle(Server $server): void
    {
        $this->manager = $server;

        $workerId = $server->getWorkerId() ?? -1;

        Log::info("[Worker {$workerId}] SwooleStartListener开始执行");

        // 只有 Worker ID 为 0 的进程负责定时任务
        if ($workerId !== 0) {
            Log::info("[Worker {$workerId}] 非主定时任务Worker，跳过定时任务初始化");
            return;
        }

        // 使用协程安全的方式获取服务器实例
        Coroutine::create(function () use ($workerId) {

            // 使用Think-Swoole的Websocket工具类
            $websocket = app('think\swoole\Websocket');

            $scheduleService = new ScheduleService($websocket);

            $scheduleService->start();
        });

        Log::info("[ScheduleService:] SwooleStartListener执行完成");
    }

    /**
     * 获取房间ID
     * @param string $type
     * @param int $id
     * @return string
     */
    protected function getRoomId(string $type, int $id): string
    {
        return $type . '_' . $id;
    }
    
    /**
     * 启动消息队列处理器
     */
//    private function startMessageQueueProcessor(Server $server): void
//    {
//        // 启动协程处理消息队列
//        Coroutine::create(function () use ($server) {
//            $messageQueueService = new MessageQueueService();
//            $fdManagerService = new FdManagerService($server);
//
//            Log::info("[MessageQueue:] 消息队列处理器协程启动");
//
//            while (true) {
//                try {
//                    // 每秒处理一次队列消息
//                    $processedCount = $messageQueueService->processQueueMessages($server, $fdManagerService);
//
//                    if ($processedCount > 0) {
//                        Log::info("[MessageQueue:] 处理了 {$processedCount} 条消息");
//                    }
//
//                    // 每10秒输出一次队列状态
//                    static $statusCounter = 0;
//                    $statusCounter++;
//                    if ($statusCounter >= 10) {
//                        $status = $messageQueueService->getQueueStatus();
//                        Log::info("[MessageQueue:] 队列状态", $status);
//                        $statusCounter = 0;
//                    }
//
//                    // 等待1秒
//                    Coroutine::sleep(1);
//                } catch (\Throwable $e) {
//                    Log::error("[MessageQueue:] 消息队列处理异常: " . $e->getMessage());
//                    // 出现异常时等待5秒再继续
//                    Coroutine::sleep(5);
//                }
//            }
//        });
//    }
}