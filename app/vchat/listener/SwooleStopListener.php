<?php

namespace app\vchat\listener;

use app\vchat\services\ScheduleService;
use app\vchat\utils\Log;
use Swoole\WebSocket\Server;
use think\Request;

class SwooleStopListener
{

    protected function handle(Server $server, Request $request): void
    {
        try {
            // 在这里执行清理工作
            Log::info("[ScheduleService:] 正在执行服务停止清理工作...");
            
            // 清理定时器
            // 使用Think-Swoole的Websocket工具类
            $websocket = app('think\swoole\Websocket');

            $scheduleService = new ScheduleService($websocket);

            $scheduleService->stop();

            // 关闭所有连接
            if (method_exists($server, 'close')) {
                foreach ($server->connections as $fd) {
                    $server->close($fd);
                }
            }

            Log::info("[ScheduleService:] 服务停止清理工作完成");
        } catch (\Throwable $e) {
            Log::error("[ScheduleService:] 服务停止清理工作异常: " . $e->getMessage());
        }
    }
}