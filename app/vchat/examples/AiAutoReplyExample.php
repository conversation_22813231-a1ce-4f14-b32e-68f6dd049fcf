<?php

namespace app\vchat\examples;

use app\vchat\auto_reply\AiAutoReply;
use app\vchat\auto_reply\AutoReplyManager;

/**
 * AI自动回复使用示例
 */
class AiAutoReplyExample
{
    /**
     * 运行所有示例
     */
    public function runAll(): void
    {
        echo "=== AI自动回复功能示例 ===\n\n";
        
        $this->basicUsageExample();
        $this->triggerExamples();
        $this->memoryExample();
        $this->batchTestExample();
        $this->managementExample();
        
        echo "=== 示例运行完成 ===\n";
    }

    /**
     * 基础使用示例
     */
    public function basicUsageExample(): void
    {
        echo "1. 基础使用示例:\n";
        
        try {
            $aiReply = new AiAutoReply();
            
            // 测试消息
            $message = [
                'content' => '@AI 你好，请介绍一下你自己',
                'from_id' => 12345,
                'type' => 'text',
                'timestamp' => time(),
            ];
            
            // 检查是否应该回复
            $shouldReply = $aiReply->shouldReply($message);
            echo "是否应该回复: " . ($shouldReply ? 'Yes' : 'No') . "\n";
            
            if ($shouldReply) {
                $reply = $aiReply->getReply($message);
                echo "AI回复: " . $reply . "\n";
            }
            
            echo "\n";
            
        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * 触发条件示例
     */
    public function triggerExamples(): void
    {
        echo "2. 触发条件示例:\n";
        
        $aiReply = new AiAutoReply();
        
        $testMessages = [
            '@AI 今天天气怎么样？',
            '智能助手：请帮我查询订单',
            '机器人：你能做什么？',
            'AI助手，请介绍一下产品',
            '普通消息，不应该触发',
            '@ai 小写也能触发吗？',
            'AI：这是冒号触发',
            '小助手：你好',
        ];
        
        foreach ($testMessages as $index => $content) {
            $message = [
                'content' => $content,
                'from_id' => 10000 + $index,
                'type' => 'text',
                'timestamp' => time(),
            ];
            
            $shouldReply = $aiReply->shouldReply($message);
            echo "消息: \"{$content}\" -> " . ($shouldReply ? '触发' : '不触发') . "\n";
        }
        
        echo "\n";
    }

    /**
     * 记忆功能示例
     */
    public function memoryExample(): void
    {
        echo "3. 记忆功能示例:\n";
        
        try {
            $aiReply = new AiAutoReply();
            $userId = 12345;
            
            // 第一次对话
            $message1 = [
                'content' => '@AI 我的名字是张三',
                'from_id' => $userId,
                'type' => 'text',
                'timestamp' => time(),
            ];
            
            if ($aiReply->shouldReply($message1)) {
                $reply1 = $aiReply->getReply($message1);
                echo "用户: {$message1['content']}\n";
                echo "AI: {$reply1}\n\n";
            }
            
            // 第二次对话（测试记忆）
            $message2 = [
                'content' => '@AI 你还记得我的名字吗？',
                'from_id' => $userId,
                'type' => 'text',
                'timestamp' => time() + 10,
            ];
            
            if ($aiReply->shouldReply($message2)) {
                $reply2 = $aiReply->getReply($message2);
                echo "用户: {$message2['content']}\n";
                echo "AI: {$reply2}\n\n";
            }
            
        } catch (\Exception $e) {
            echo "记忆功能测试错误: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * 批量测试示例
     */
    public function batchTestExample(): void
    {
        echo "4. 批量测试示例:\n";
        
        $aiReply = new AiAutoReply();
        
        $testMessages = [
            '@AI 你好',
            '智能助手：今天天气如何？',
            '机器人：请推荐一本书',
            '@ai 你能帮我做什么？',
            '普通消息',
            'AI：请介绍一下功能',
        ];
        
        $results = [];
        foreach ($testMessages as $index => $content) {
            $message = [
                'content' => $content,
                'from_id' => 20000 + $index,
                'type' => 'text',
                'timestamp' => time(),
            ];
            
            $shouldReply = $aiReply->shouldReply($message);
            $reply = $shouldReply ? $aiReply->getReply($message) : null;
            
            $results[] = [
                'input' => $content,
                'triggered' => $shouldReply,
                'reply' => $reply,
            ];
        }
        
        foreach ($results as $index => $result) {
            echo "测试 " . ($index + 1) . ":\n";
            echo "  输入: {$result['input']}\n";
            echo "  触发: " . ($result['triggered'] ? 'Yes' : 'No') . "\n";
            if ($result['reply']) {
                echo "  回复: {$result['reply']}\n";
            }
            echo "\n";
        }
    }

    /**
     * 管理功能示例
     */
    public function managementExample(): void
    {
        echo "5. 管理功能示例:\n";
        
        try {
            $aiReply = new AiAutoReply();
            
            // 获取状态
            $status = $aiReply->getStatus();
            echo "服务状态:\n";
            foreach ($status as $key => $value) {
                $displayValue = is_bool($value) ? ($value ? 'true' : 'false') : $value;
                echo "  {$key}: {$displayValue}\n";
            }
            echo "\n";
            
            // 清除用户会话
            $testUserId = 12345;
            $aiReply->clearUserSession($testUserId);
            echo "已清除用户 {$testUserId} 的会话\n\n";
            
        } catch (\Exception $e) {
            echo "管理功能测试错误: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * 集成到自动回复管理器的示例
     */
    public function integrationExample(): void
    {
        echo "6. 集成示例:\n";
        
        try {
            // 使用自动回复管理器
            $manager = new AutoReplyManager();
            
            $testMessage = [
                'content' => '@AI 你好，我需要帮助',
                'from_id' => 99999,
                'type' => 'text',
                'timestamp' => time(),
            ];
            
            $reply = $manager->getAutoReply($testMessage);
            
            if ($reply) {
                echo "通过管理器获取的回复: {$reply}\n";
            } else {
                echo "管理器未返回回复\n";
            }
            
            echo "\n";
            
        } catch (\Exception $e) {
            echo "集成测试错误: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * 性能测试示例
     */
    public function performanceExample(): void
    {
        echo "7. 性能测试示例:\n";
        
        $aiReply = new AiAutoReply();
        $testCount = 5;
        $totalTime = 0;
        
        for ($i = 0; $i < $testCount; $i++) {
            $message = [
                'content' => "@AI 这是第 {$i} 条测试消息",
                'from_id' => 30000 + $i,
                'type' => 'text',
                'timestamp' => time(),
            ];
            
            $startTime = microtime(true);
            
            if ($aiReply->shouldReply($message)) {
                $reply = $aiReply->getReply($message);
                $endTime = microtime(true);
                $duration = ($endTime - $startTime) * 1000; // 转换为毫秒
                $totalTime += $duration;
                
                echo "测试 " . ($i + 1) . ": {$duration:.2f}ms\n";
            }
        }
        
        $avgTime = $totalTime / $testCount;
        echo "平均响应时间: {$avgTime:.2f}ms\n\n";
    }
}

// 如果直接运行此文件
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'] ?? '')) {
    $example = new AiAutoReplyExample();
    $example->runAll();
}
