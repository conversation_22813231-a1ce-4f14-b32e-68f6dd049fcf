<?php

namespace app\vchat\examples;

use app\vchat\services\ThirdPartyIntegrationService;
use app\vchat\core\MessageProtocol;
use app\vchat\utils\Logger;

/**
 * VChat第三方平台集成使用示例
 * 展示如何将第三方平台完全集成到VChat系统中
 */
class ThirdPartyIntegrationExample
{
    /**
     * 第三方平台集成服务
     * @var ThirdPartyIntegrationService
     */
    protected ThirdPartyIntegrationService $integrationService;

    /**
     * 日志记录器
     * @var Logger
     */
    protected Logger $logger;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->integrationService = new ThirdPartyIntegrationService();
        $this->logger = new Logger();
    }

    /**
     * 第三方平台消息接收示例
     */
    public function thirdPartyMessageReceiving(): void
    {
        echo "=== 第三方平台消息接收示例 ===\n";

        try {
            // 1. 模拟微信小程序用户发送消息
            $wechatMessage = [
                'MsgType' => 'text',
                'FromUserName' => 'user_openid_123',
                'ToUserName' => 'service_account',
                'Content' => '你好，我想咨询产品价格',
                'CreateTime' => time(),
                'MsgId' => 'msg_' . uniqid()
            ];

            echo "处理微信小程序消息：\n";
            $result = $this->integrationService->handleThirdPartyMessage('wechat.miniprogram', $wechatMessage);
            print_r($result);

            if ($result['success']) {
                echo "✅ 消息已成功集成到VChat系统\n";
                echo "   - 会话ID: {$result['session_id']}\n";
                echo "   - 消息ID: {$result['message_id']}\n";
                echo "   - 已通过WebSocket推送给客服\n\n";
            }

            // 2. 模拟企业微信用户发送消息
            $workMessage = [
                'msgtype' => 'text',
                'from' => 'work_user_456',
                'to' => 'work_service',
                'content' => '需要技术支持，系统出现问题',
                'timestamp' => time(),
                'msgid' => 'work_msg_' . uniqid()
            ];

            echo "处理企业微信消息：\n";
            $result = $this->integrationService->handleThirdPartyMessage('wechat.work', $workMessage);
            print_r($result);

            // 3. 模拟QQ机器人消息
            $qqMessage = [
                'message_id' => 'qq_msg_' . uniqid(),
                'user_id' => 'qq_user_789',
                'self_id' => 'qq_bot_123',
                'message' => '请问你们的服务时间是什么时候？',
                'time' => time()
            ];

            echo "处理QQ机器人消息：\n";
            $result = $this->integrationService->handleThirdPartyMessage('qq.bot', $qqMessage);
            print_r($result);

        } catch (\Exception $e) {
            echo "处理第三方平台消息失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * VChat客服回复示例
     */
    public function vchatCustomerServiceReply(): void
    {
        echo "\n=== VChat客服回复示例 ===\n";

        try {
            // 模拟客服在VChat界面回复消息
            $vchatReplyMessage = [
                'id' => 'vchat_reply_' . uniqid(),
                'content' => '您好！关于产品价格，我们有多种套餐可选，请问您需要了解哪种类型的产品？',
                'content_type' => MessageProtocol::TYPE_TEXT,
                'from_type' => 'service',
                'to_type' => 'user',
                'session_id' => 'demo_session_123',
                'extra' => json_encode([
                    'platform' => 'wechat.miniprogram',
                    'third_party_user_id' => 'user_openid_123'
                ])
            ];

            echo "客服回复消息到微信小程序：\n";
            $result = $this->integrationService->sendMessageToThirdParty('wechat.miniprogram', $vchatReplyMessage);
            print_r($result);

            if ($result['success']) {
                echo "✅ 客服回复已成功发送到微信小程序\n";
                echo "   - 用户将在微信小程序中收到回复\n";
                echo "   - VChat中显示发送状态已更新\n\n";
            }

            // 模拟发送图片消息
            $imageMessage = [
                'id' => 'vchat_image_' . uniqid(),
                'content' => 'media_id_12345',
                'content_type' => MessageProtocol::TYPE_IMAGE,
                'from_type' => 'service',
                'to_type' => 'user',
                'session_id' => 'demo_session_456',
                'extra' => json_encode([
                    'platform' => 'wechat.work',
                    'third_party_user_id' => 'work_user_456'
                ])
            ];

            echo "客服发送图片到企业微信：\n";
            $result = $this->integrationService->sendMessageToThirdParty('wechat.work', $imageMessage);
            print_r($result);

        } catch (\Exception $e) {
            echo "客服回复失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * VChat现有功能集成示例
     */
    public function vchatExistingFeaturesExample(): void
    {
        echo "\n=== VChat现有功能集成示例 ===\n";

        echo "第三方平台消息完全集成到VChat后，将自动享受VChat的所有现有功能：\n\n";

        echo "🤖 自动回复功能：\n";
        echo "   - 使用VChat现有的自动回复配置\n";
        echo "   - 无需为第三方平台单独配置\n";
        echo "   - 统一的回复规则和逻辑\n\n";

        echo "🧠 AI智能客服：\n";
        echo "   - 使用VChat现有的AI配置\n";
        echo "   - 第三方平台消息自动享受AI回复\n";
        echo "   - 统一的AI模型和参数\n\n";

        echo "📊 数据统计分析：\n";
        echo "   - 第三方平台数据自动纳入VChat统计\n";
        echo "   - 统一的报表和分析\n";
        echo "   - 无需单独的统计系统\n\n";

        echo "👥 客服管理：\n";
        echo "   - 使用VChat现有的客服分配逻辑\n";
        echo "   - 统一的工作量管理\n";
        echo "   - 一致的服务质量控制\n\n";

        echo "⚙️ 系统配置：\n";
        echo "   - 在VChat现有配置中管理所有功能\n";
        echo "   - 避免重复配置和管理\n";
        echo "   - 简化系统维护\n\n";

        echo "✅ 这种设计的优势：\n";
        echo "   1. 避免功能重复开发\n";
        echo "   2. 保持系统一致性\n";
        echo "   3. 简化配置和维护\n";
        echo "   4. 充分利用VChat现有投入\n";
        echo "   5. 降低系统复杂度\n";
    }

    /**
     * 统一会话管理示例
     */
    public function unifiedSessionManagement(): void
    {
        echo "\n=== 统一会话管理示例 ===\n";

        try {
            // 展示不同平台的会话如何在VChat中统一管理
            $platforms = [
                'wechat.miniprogram' => '微信小程序',
                'wechat.officialaccount' => '微信公众号',
                'wechat.work' => '企业微信',
                'qq.bot' => 'QQ机器人',
                'dingtalk.bot' => '钉钉机器人',
                'feishu.bot' => '飞书机器人'
            ];

            echo "VChat统一会话管理特性：\n";
            echo "1. 所有平台用户都会自动映射为VChat用户ID\n";
            echo "2. 每个平台用户都会创建独立的VChat会话\n";
            echo "3. 客服可以在同一个界面处理所有平台的咨询\n";
            echo "4. 会话状态、消息历史统一存储和管理\n\n";

            foreach ($platforms as $platform => $name) {
                $enabled = config("vchat.third_party_integration.platforms.{$platform}.enabled", false);
                $status = $enabled ? '✅ 已启用' : '❌ 未启用';
                $color = config("vchat.third_party_integration.platforms.{$platform}.color", '#666666');
                $icon = config("vchat.third_party_integration.platforms.{$platform}.icon", 'default');
                
                echo "{$name} ({$platform}): {$status}\n";
                echo "  - 主题色: {$color}\n";
                echo "  - 图标: {$icon}\n";
                echo "  - Webhook: " . config("vchat.third_party_integration.platforms.{$platform}.webhook_url", 'N/A') . "\n\n";
            }

        } catch (\Exception $e) {
            echo "会话管理示例失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 实时WebSocket推送示例
     */
    public function realTimeWebSocketExample(): void
    {
        echo "\n=== 实时WebSocket推送示例 ===\n";

        echo "VChat第三方平台集成的实时特性：\n\n";

        echo "1. 消息接收流程：\n";
        echo "   第三方平台用户发送消息\n";
        echo "   ↓\n";
        echo "   Webhook推送到VChat\n";
        echo "   ↓\n";
        echo "   消息格式转换和存储\n";
        echo "   ↓\n";
        echo "   WebSocket实时推送给客服\n";
        echo "   ↓\n";
        echo "   客服在VChat界面立即看到消息\n\n";

        echo "2. 消息发送流程：\n";
        echo "   客服在VChat界面回复\n";
        echo "   ↓\n";
        echo "   消息保存到VChat数据库\n";
        echo "   ↓\n";
        echo "   WebSocket推送给其他客服\n";
        echo "   ↓\n";
        echo "   自动发送到对应第三方平台\n";
        echo "   ↓\n";
        echo "   用户在第三方平台收到回复\n\n";

        echo "3. 平台标识显示：\n";
        echo "   - 每条消息都会显示来源平台图标\n";
        echo "   - 不同平台使用不同的主题色\n";
        echo "   - 用户信息包含平台标识\n";
        echo "   - 支持平台特有的消息类型\n\n";

        echo "4. 客服工作台统一体验：\n";
        echo "   - 单一界面处理所有平台咨询\n";
        echo "   - 统一的消息格式和操作\n";
        echo "   - 跨平台的会话转接和管理\n";
        echo "   - 统一的自动回复和AI支持\n\n";
    }

    /**
     * 运行所有示例
     */
    public function runAllExamples(): void
    {
        echo "VChat第三方平台完全集成系统演示\n";
        echo str_repeat('=', 60) . "\n";

        $this->thirdPartyMessageReceiving();
        $this->vchatCustomerServiceReply();
        $this->vchatExistingFeaturesExample();
        $this->unifiedSessionManagement();
        $this->realTimeWebSocketExample();

        echo str_repeat('=', 60) . "\n";
        echo "🎉 VChat第三方平台集成演示完成！\n\n";

        echo "核心特性总结：\n";
        echo "✅ 第三方平台消息完全集成到VChat\n";
        echo "✅ 客服在统一界面处理所有平台咨询\n";
        echo "✅ 实时WebSocket推送，无延迟\n";
        echo "✅ 统一的消息格式和会话管理\n";
        echo "✅ 跨平台自动回复和AI支持\n";
        echo "✅ 平台标识和主题色区分\n";
        echo "✅ 无缝的双向消息同步\n\n";

        echo "配置说明：\n";
        echo "1. 在 config/vchat.php 中配置第三方平台\n";
        echo "2. 在 .env 中设置平台开关和密钥\n";
        echo "3. 配置各平台的Webhook URL\n";
        echo "4. 启动VChat WebSocket服务\n";
        echo "5. 客服登录VChat即可处理所有平台消息\n";
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    try {
        $example = new ThirdPartyIntegrationExample();
        $example->runAllExamples();
    } catch (\Exception $e) {
        echo "示例运行失败：" . $e->getMessage() . "\n";
        echo "错误追踪：\n" . $e->getTraceAsString() . "\n";
    }
}
