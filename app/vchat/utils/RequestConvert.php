<?php

namespace app\vchat\utils;

class RequestConvert
{

    /**
     * 从User-Agent字符串解析平台信息
     * @param string $userAgent
     * @return string
     */
    public static function platformFromUserAgent(string $userAgent): string
    {
        $userAgent = strtolower($userAgent);
        if (str_contains($userAgent, 'android')) {
            return 'Android';
        } elseif (str_contains($userAgent, 'iphone') || str_contains($userAgent, 'ipad') || str_contains($userAgent, 'ipod')) {
            return 'iOS';
        } elseif (str_contains($userAgent, 'windows')) {
            return 'Windows';
        } elseif (str_contains($userAgent, 'macintosh') || str_contains($userAgent, 'mac os x')) {
            return 'macOS';
        } elseif (str_contains($userAgent, 'linux')) {
            return 'Linux';
        } elseif (str_contains($userAgent, 'wechat')) {
            return 'WeChat';
        } elseif (str_contains($userAgent, 'postman')) {
            return 'Postman'; // For testing purposes
        }
        return 'Unknown';
    }

    /**
     * 从User-Agent字符串解析设备信息 (简陋版)
     * @param string $userAgent
     * @return string
     */
    public static function deviceFromUserAgent(string $userAgent): string
    {
        $userAgent = strtolower($userAgent);
        if (str_contains($userAgent, 'mobile')) {
            return 'Mobile';
        } elseif (str_contains($userAgent, 'tablet') || str_contains($userAgent, 'ipad')) {
            return 'Tablet';
        } elseif (str_contains($userAgent, 'windows') || str_contains($userAgent, 'macintosh') || str_contains($userAgent, 'linux')) {
            return 'Desktop';
        } elseif (str_contains($userAgent, 'postman')) {
            return 'Development Tool';
        }
        return 'Unknown';
    }

    /**
     * 获取客户端IP地址
     *
     * @return string
     */
    public static function getClientIp()
    {
        if (function_exists('request')) {
            return request()->ip();
        }

        // 备用方法
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ??
            $_SERVER['HTTP_X_REAL_IP'] ??
            $_SERVER['REMOTE_ADDR'] ??
            'unknown';

        return $ip;
    }

    /**
     * 获取用户代理
     *
     * @return string
     */
    public static function getUserAgent()
    {
        if (function_exists('request')) {
            return request()->header('User-Agent', 'unknown');
        }

        return $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    }

    /**
     * 从User-Agent字符串解析浏览器信息
     * @param string $userAgent
     * @return array [browser_name, browser_version]
     */
    public static function browserFromUserAgent(string $userAgent): array
    {
        $userAgent = strtolower($userAgent);
        
        // 常见浏览器匹配规则
        $browsers = [
            'chrome' => 'Chrome',
            'firefox' => 'Firefox',
            'safari' => 'Safari',
            'opera' => 'Opera',
            'edge' => 'Edge',
            'msie' => 'IE',
            'trident' => 'IE'
        ];

        foreach ($browsers as $pattern => $name) {
            if (str_contains($userAgent, $pattern)) {
                // 提取版本号
                preg_match('/'.preg_quote($pattern).'[\/\s]?([0-9\.]+)/', $userAgent, $matches);
                $version = $matches[1] ?? 'unknown';
                
                // 特殊处理IE浏览器
                if ($pattern === 'trident') {
                    preg_match('/rv:([0-9\.]+)/', $userAgent, $ieMatches);
                    $version = $ieMatches[1] ?? 'unknown';
                }
                
                return [$name, $version];
            }
        }

        return ['Unknown', 'unknown'];
    }

    /**
     * 从User-Agent字符串解析平台信息
     * @param string $userAgent
     * @return string
     */
    public static function platformAppFromUserAgent(string $userAgent): string
    {
        $userAgent = strtolower($userAgent);
        // 先检查常见App
        if (str_contains($userAgent, 'micromessenger')) {
            return 'WeChat';
        } elseif (str_contains($userAgent, 'dingtalk')) {
            return 'DingTalk';
        } elseif (str_contains($userAgent, 'alipay')) {
            return 'Alipay';
        } elseif (str_contains($userAgent, 'weibo')) {
            return 'Weibo';
        } elseif (str_contains($userAgent, 'qq/')) {
            return 'QQ';
        } elseif (str_contains($userAgent, 'toutiao')) {
            return 'Toutiao';
        } elseif (str_contains($userAgent, 'android')) {
            return 'Android';
        }
        return 'Unknown';
    }
}
