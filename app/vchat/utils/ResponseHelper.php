<?php
namespace app\vchat\utils;

class ResponseHelper
{
    public static function ok($data = [], $msg = 'ok', $event = null)
    {
        $res = [
            'code' => 200,
            'msg' => $msg,
            'data' => $data,
        ];
        if ($event) $res['event'] = $event;
        return $res;
    }

    public static function fail($msg = 'error', $code = 0, $event = null, $data = [])
    {
        $res = [
            'code' => $code,
            'msg' => $msg,
            'data' => $data,
        ];
        if ($event) $res['event'] = $event;
        return $res;
    }
}