<?php
declare(strict_types=1);

namespace app\vchat\utils;

use think\facade\Log;

/**
 * 日志工具类
 */
class Logger
{
    /**
     * 记录信息日志
     * @param string $message
     * @param array $context
     */
    public function info(string $message, array $context = []): void
    {
        Log::info($this->formatMessage($message, $context));
    }

    /**
     * 记录错误日志
     * @param string $message
     * @param array $context
     */
    public function error(string $message, array $context = []): void
    {
        Log::error($this->formatMessage($message, $context));
    }

    /**
     * 记录警告日志
     * @param string $message
     * @param array $context
     */
    public function warning(string $message, array $context = []): void
    {
        Log::warning($this->formatMessage($message, $context));
    }

    /**
     * 记录调试日志
     * @param string $message
     * @param array $context
     */
    public function debug(string $message, array $context = []): void
    {
        Log::debug($this->formatMessage($message, $context));
    }

    /**
     * 格式化日志消息
     * @param string $message
     * @param array $context
     * @return string
     */
    protected function formatMessage(string $message, array $context = []): string
    {
        $prefix = '[vchat] ';
        if (!empty($context)) {
            return $prefix . $message . ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }
        return $prefix . $message;
    }
}