<?php
namespace app\vchat\utils;

class Log
{
    const MAX_LINES = 5000; // 单个文件最大行数
    const FILE_PREFIX = 'log_'; // 日志文件名前缀

    /**
     * 写入日志文件
     */
    public static function info(string $message, string $filename = 'vchat_start.log', array $context = []): bool
    {
        $logMessage = '[INFO] ' . $message;
        
        if (!empty($context)) {
            $logMessage .= ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }
        
        return self::write($logMessage, $filename);
    }

    /**
     * 基础写入方法
     */
    private static function write(string $message, string $filename, int $flags = FILE_APPEND): bool
    {
        $logDir = runtime_path() . 'log/';
        
        // 确保日志目录存在
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }

        // 获取实际写入文件路径
        $actualFile = self::checkFileRotation($logDir, $filename);
        
        return file_put_contents(
            $actualFile, 
            date('Y-m-d H:i:s') . ' ' . $message . "\n",
            $flags
        ) !== false;
    }

    /**
     * 检查文件是否需要轮转
     */
    private static function checkFileRotation(string $logDir, string $filename): string
    {
        $filePath = $logDir . $filename;

        // 如果文件不存在直接返回
        if (!file_exists($filePath)) {
            return $filePath;
        }

        // 安全计算当前行数
        try {
            $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $lineCount = $lines ? count($lines) : 0;
        } catch (\Exception $e) {
            // 文件读取失败，返回原路径
            error_log("Failed to read log file for rotation check: {$filePath}, error: " . $e->getMessage());
            return $filePath;
        }

        // 如果未超过最大行数
        if ($lineCount < self::MAX_LINES) {
            return $filePath;
        }

        // 生成新文件名
        $newFilename = self::FILE_PREFIX . date('Ymd_His') . '_' . $filename;
        $newPath = $logDir . $newFilename;

        // 重命名原文件（安全处理）
        try {
            if (file_exists($filePath)) {
                if (!rename($filePath, $newPath)) {
                    // 重命名失败，记录错误但不中断程序
                    error_log("Failed to rename log file: {$filePath} to {$newPath}");
                }
            }
        } catch (\Exception $e) {
            // 重命名异常，记录错误但不中断程序
            error_log("Exception during log file rotation: " . $e->getMessage());
        }

        return $filePath; // 返回原始文件名（新建文件）
    }

    /**
     * 记录错误日志
     * @param string $message 错误信息
     * @param string $filename 日志文件名
     * @param array $context 上下文信息
     * @return bool
     */
    public static function error(string $message, string $filename = 'vchat_error.log', array $context = []): bool
    {
        $logMessage = '[ERROR] ' . $message;
        
        if (!empty($context)) {
            $logMessage .= ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }
        
        return self::info($logMessage, $filename);
    }

    /**
     * 记录错误日志
     * @param string $message 报错提示信息
     * @param string $filename 日志文件名
     * @param array $context 上下文信息
     * @return bool
     */
    public static function warning(string $message, string $filename = 'vchat_warring.log', array $context = []): bool
    {
        $logMessage = '[WARNING] ' . $message;

        if (!empty($context)) {
            $logMessage .= ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }

        return self::info($logMessage, $filename);
    }
}