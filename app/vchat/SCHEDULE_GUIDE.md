# VChat 定时任务使用指南

## 概述

VChat 定时任务系统提供了自动化的会话管理和队列维护功能，确保系统的稳定运行和良好的用户体验。

> **重要更新**: 定时任务现已集成到 Swoole 服务中，无需单独启动。详见 [Swoole 集成指南](SWOOLE_INTEGRATION_GUIDE.md)。

## 功能特性

### 1. 会话超时检查
- **功能**: 自动检查所有活跃会话的超时状态
- **默认间隔**: 60秒
- **处理内容**:
  - 检测用户无响应超时
  - 检测客服无响应超时
  - 检测会话最大持续时间超时
  - 发送超时警告和通知
  - 自动结束超时会话

### 2. 队列清理
- **功能**: 清理过期的排队记录和无效数据
- **默认间隔**: 300秒（5分钟）
- **处理内容**:
  - 清理超过最大等待时间的排队用户
  - 清理无效的队列数据
  - 释放Redis存储空间

### 3. 队列状态更新
- **功能**: 更新所有排队用户的等待时间
- **默认间隔**: 30秒
- **处理内容**:
  - 重新计算每个用户的预估等待时间
  - 更新队列统计信息
  - 优化队列性能

## 启动和管理

### 推荐方式：Swoole 集成启动

```bash
# 启动 Swoole 服务（自动包含定时任务）
php think swoole

# 后台启动
nohup php think swoole > /dev/null 2>&1 &
```

### 传统方式：独立脚本启动（已废弃）

> **注意**: 以下方式已废弃，建议使用 Swoole 集成方式。

```bash
# 进入项目目录
cd /Users/<USER>/workspace/php/anchor

# 启动定时任务（前台运行）
php app/vchat/schedule_start.php

# 后台运行
nohup php app/vchat/schedule_start.php > /dev/null 2>&1 &
```

### 停止定时任务

**Swoole 集成方式**:
```bash
# 停止 Swoole 服务（同时停止定时任务）
killall -TERM php
# 或使用 systemd
sudo systemctl stop swoole-vchat
```

**传统方式**:
```bash
# 优雅停止
php app/vchat/schedule_stop.php
```

### 查看运行状态

**Swoole 集成方式**:
```bash
# 查看 Swoole 进程
ps aux | grep "php think swoole"

# 查看日志
tail -f runtime/swoole.log
tail -f runtime/vchat/schedule.log
```

**传统方式**:
```bash
# 检查状态
php app/vchat/schedule_status.php
```

## 配置说明

定时任务的配置位于 `config/vchat.php` 文件中：

```php
return [
    // 会话超时配置
    'session_timeout' => [
        'check_interval' => 60,        // 检查间隔（秒）
        'user_inactive_timeout' => 600, // 用户无响应超时（秒）
        'service_inactive_timeout' => 300, // 客服无响应超时（秒）
        'max_duration' => 3600,        // 最大会话时长（秒）
        'warning_before_timeout' => 120, // 超时前警告时间（秒）
    ],
    
    // 队列配置
    'queue' => [
        'cleanup_interval' => 300,      // 清理间隔（秒）
        'status_update_interval' => 30, // 状态更新间隔（秒）
        'max_wait_time' => 1800,        // 最大等待时间（秒）
    ]
];
```

## 系统要求

### PHP扩展
- **pcntl**: 用于信号处理
- **posix**: 用于进程管理
- **swoole**: 用于定时器功能

### 权限要求
- 读写 `app/vchat/` 目录的权限（用于PID文件）
- 读写 `runtime/log/` 目录的权限（用于日志）

## 监控和日志

### 日志文件
定时任务的日志记录在以下位置：
- 路径: `runtime/log/vchat_*.log`
- 格式: 按日期分割的日志文件
- 内容: 任务执行状态、错误信息、性能统计

### 监控指标
- **会话超时处理数量**: 每次检查处理的超时会话数
- **队列清理数量**: 每次清理的过期用户数
- **任务执行时间**: 各个任务的执行耗时
- **错误率**: 任务执行失败的比例

## 故障排除

### 常见问题

#### 1. 定时任务无法启动
**症状**: 执行启动脚本后立即退出
**可能原因**:
- PHP扩展缺失（pcntl、posix）
- 权限不足
- 配置文件错误

**解决方案**:
```bash
# 检查PHP扩展
php -m | grep -E "pcntl|posix|swoole"

# 检查权限
ls -la app/vchat/

# 检查配置
php -c config/vchat.php
```

#### 2. 进程意外退出
**症状**: 定时任务运行一段时间后停止
**可能原因**:
- 内存不足
- 数据库连接问题
- Redis连接问题

**解决方案**:
```bash
# 查看系统资源
free -h
df -h

# 检查错误日志
tail -f runtime/log/vchat_$(date +%Y%m%d).log

# 重启相关服务
sudo systemctl restart redis
sudo systemctl restart mysql
```

#### 3. PID文件问题
**症状**: 提示已有进程运行，但实际没有
**解决方案**:
```bash
# 清理PID文件
rm -f app/vchat/schedule.pid

# 或使用状态检查脚本清理
php app/vchat/schedule_status.php
```

### 性能优化

#### 1. 调整检查间隔
根据系统负载调整各任务的执行间隔：
- 高负载系统：增加间隔时间
- 低负载系统：减少间隔时间以提高响应速度

#### 2. 数据库优化
- 为相关表添加适当的索引
- 定期清理历史数据
- 优化查询语句

#### 3. Redis优化
- 设置合适的内存限制
- 配置数据过期策略
- 监控内存使用情况

## 部署建议

### 生产环境（推荐：Swoole 集成）

1. **使用 systemd 管理**:
   ```bash
   # 创建服务文件
   sudo tee /etc/systemd/system/swoole-vchat.service > /dev/null <<EOF
   [Unit]
   Description=Swoole VChat Service
   After=network.target
   
   [Service]
   Type=simple
   User=www-data
   Group=www-data
   WorkingDirectory=/path/to/anchor
   ExecStart=/usr/bin/php think swoole
   Restart=always
   RestartSec=5
   
   [Install]
   WantedBy=multi-user.target
   EOF
   
   # 启用并启动服务
   sudo systemctl enable swoole-vchat
   sudo systemctl start swoole-vchat
   ```

### 传统部署方式（已废弃）

1. **使用进程管理器**:
   ```bash
   # 使用supervisor管理
   sudo apt-get install supervisor
   
   # 配置文件 /etc/supervisor/conf.d/vchat-schedule.conf
   [program:vchat-schedule]
   command=php /path/to/anchor/app/vchat/schedule_start.php
   directory=/path/to/anchor
   autostart=true
   autorestart=true
   user=www-data
   stdout_logfile=/var/log/vchat-schedule.log
   stderr_logfile=/var/log/vchat-schedule-error.log
   ```

2. **设置日志轮转**:
   ```bash
   # 配置logrotate
   sudo vim /etc/logrotate.d/vchat
   
   /path/to/anchor/runtime/log/vchat_*.log {
       daily
       rotate 30
       compress
       delaycompress
       missingok
       notifempty
       create 644 www-data www-data
   }
   ```

3. **监控告警**:
   - 设置进程监控
   - 配置日志告警
   - 监控系统资源使用

### 开发环境
- 可以直接使用提供的脚本进行管理
- 建议在开发时关闭不必要的任务
- 使用较短的检查间隔便于测试

## API接口

定时任务服务还提供了一些管理接口：

```php
// 获取任务状态
$scheduleService = new ScheduleService();
$status = $scheduleService->getStatus();

// 手动触发会话超时检查
$timeoutService = new SessionTimeoutService();
$timeoutService->checkAllSessionsTimeout();

// 手动清理队列
$queueService = new QueueService();
$queueService->cleanupInvalidData();
```

## 更新和维护

### 版本更新
1. 停止定时任务
2. 更新代码
3. 检查配置文件变更
4. 重启定时任务

### 定期维护
- 清理旧日志文件
- 检查系统资源使用
- 更新配置参数
- 备份重要数据

---

如有问题，请查看日志文件或联系技术支持。