# Redis FD 管理服务重构指南

## 概述

`FdManagerService` 已从使用 Swoole Table 重构为使用 Redis 进行 FD 映射存储。这一改进提供了更好的跨进程数据共享能力和更高的可扩展性。

## 主要变化

### 1. 存储方式变更

**之前 (Swoole Table):**
- 使用 Swoole Table 在内存中存储 FD 映射
- 数据仅在单个进程内可见
- 需要通过构造函数注入 ThinkTable 实例

**现在 (Redis):**
- 使用 Redis 存储 FD 映射数据
- 支持跨进程数据共享
- 通过服务提供者自动注入 SwooleServer 依赖

### 2. 数据结构优化

#### Redis 键结构
```
vchat:fd:info:{fd}           # FD 信息映射 (fd -> 用户信息)
vchat:fd:user:{userId}       # 用户 FD 映射 (userId -> fd)
vchat:fd:service:{serviceId} # 客服 FD 映射 (serviceId -> fd)
```

#### 数据格式
```php
// FD 信息
[
    'fd' => '2.1',
    'type' => 'user_123',
    'user_id' => 123,
    'user_type' => 'user',
    'created_at' => 1638360000
]
```

### 3. 性能优化

- **直接查询**: 通过 Redis 键直接获取 FD，避免遍历
- **反向映射**: 用户ID 到 FD 的直接映射，提高查询效率
- **自动过期**: 设置 1 小时过期时间，自动清理无效数据

## 配置要求

### Redis 配置

确保 `config/cache.php` 中的 Redis 配置正确：

```php
'redis' => [
    'type'       => 'redis',
    'host'       => env('redis.host', '127.0.0.1'),
    'port'       => env('redis.port', 6379),
    'password'   => env('redis.password', ''),
    'select'     => env('redis.database', 0),
    'timeout'    => 0,
    'prefix'     => env('redis.prefix', 'goee:'),
    'persistent' => false,
],
```

### 环境变量

在 `.env` 文件中配置 Redis 连接信息：

```env
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_PREFIX=goee:
```

## 使用方式

### 服务注入

通过容器自动注入（推荐）：

```php
// 手动实例化
$swooleServer = app()->make(\Swoole\WebSocket\Server::class);
$fdManager = new \app\vchat\services\FdManagerService($swooleServer);

// 或在构造函数中注入
public function __construct(FdManagerService $fdManager)
{
    $this->fdManager = $fdManager;
}
```

### API 使用

```php
// 设置 FD 映射
$fdManager->setFd('2.1', 123, 'user');

// 获取用户信息
$userInfo = $fdManager->getUserByFd('2.1');
// 返回: ['userId' => 123, 'userType' => 'user']

// 获取用户的 FD
$fd = $fdManager->getFdByUserId(123, 'user');
// 返回: '2.1'

// 移除 FD 映射
$fdManager->removeFd('2.1');

// 检查 FD 是否有效
$isValid = $fdManager->isFdValid('2.1');
```

## 兼容性说明

### 构造函数变更

**之前:**
```php
public function __construct(SwooleServer $swooleServer, ThinkTable $table)
```

**现在:**
```php
public function __construct(SwooleServer $swooleServer)
```

### 服务提供者

新增 `VChatServiceProvider` 来管理服务绑定，确保依赖正确注入。

## 迁移步骤

1. **更新代码**: 所有直接实例化 `FdManagerService` 的地方改为使用容器注入
2. **配置 Redis**: 确保 Redis 服务正常运行且配置正确
3. **清理旧数据**: 重启服务后，Swoole Table 数据会自动清空
4. **测试验证**: 验证 FD 映射功能正常工作

## 故障排除

### 常见问题

1. **Redis 连接失败**
   - 检查 Redis 服务是否运行
   - 验证连接配置是否正确
   - 确认网络连通性

2. **FD 映射丢失**
   - 检查 Redis 数据是否过期
   - 验证键名前缀配置
   - 查看 Redis 内存使用情况

3. **性能问题**
   - 监控 Redis 响应时间
   - 检查网络延迟
   - 考虑使用 Redis 连接池

### 调试命令

```bash
# 查看 FD 相关的 Redis 键
redis-cli --scan --pattern "*vchat:fd:*"

# 查看特定 FD 的信息
redis-cli get "goee:vchat:fd:info:2.1"

# 查看用户的 FD 映射
redis-cli get "goee:vchat:fd:user:123"
```

## 性能监控

建议监控以下指标：

- Redis 连接数
- Redis 内存使用量
- FD 映射操作响应时间
- 过期键清理频率

## 最佳实践

1. **合理设置过期时间**: 根据业务需求调整 FD 映射的过期时间
2. **监控 Redis 健康状态**: 定期检查 Redis 服务状态
3. **使用连接池**: 在高并发场景下使用 Redis 连接池
4. **数据备份**: 定期备份重要的 FD 映射数据
5. **日志记录**: 保持详细的操作日志用于问题排查