<?php
namespace app\vchat\auto_reply;

class RobotReply extends AbstractAutoReply
{
    public function shouldReply(array $message): bool
    {
        // Validate message format
        if (!$this->validateMessage($message, 'type', 'string')) {
            return false;
        }

        // Only auto-reply to text messages
        return $message['type'] === 'text';
    }

    public function getReply(array $message): ?string
    {
        // Validate message content
        if (!$this->validateMessage($message, 'content', 'string')) {
            return null;
        }

        // This can be integrated with AI/FAQ
        if (strpos($message['content'], '营业时间') !== false) {
            return '我们的营业时间是9:00-18:00。';
        }
        // ...can be extended
        return null;
    }
}