<?php

namespace app\vchat\auto_reply;

use app\ai\bootstrap\AiApplication;
use app\ai\services\UnifiedAiService;
use app\ai\config\ConfigManager;
use app\vchat\utils\Logger;
use think\facade\Config;
use think\facade\Cache;

/**
 * AI自动回复策略
 * 使用现代化AI服务提供智能自动回复
 */
class AiAutoReply extends AbstractAutoReply
{
    /**
     * AI应用程序
     * @var AiApplication
     */
    protected AiApplication $aiApp;

    /**
     * 统一AI服务
     * @var UnifiedAiService
     */
    protected UnifiedAiService $aiService;

    /**
     * 日志记录器
     * @var Logger
     */
    protected Logger $logger;

    /**
     * 配置缓存
     * @var array
     */
    protected array $config;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logger = new Logger();
        $this->loadConfig();
        $this->initializeAiService();
    }

    /**
     * 加载配置
     */
    protected function loadConfig(): void
    {
        $this->config = Config::get('auto_reply.ai', [
            'enabled' => false,
            'provider' => 'deepseek',
            'model' => 'deepseek-chat',
            'temperature' => 0.7,
            'max_tokens' => 500,
            'timeout' => 10,
            'cache_ttl' => 300, // 5分钟缓存
            'triggers' => [
                'keywords' => ['@AI', '@ai', '智能助手', '机器人', 'AI助手'],
                'patterns' => ['/^AI[：:]/i', '/^智能助手[：:]/i'],
                'always_reply' => false, // 是否对所有消息都回复
                'min_length' => 3, // 最小消息长度
                'max_length' => 1000, // 最大消息长度
            ],
            'context' => [
                'use_memory' => true,
                'session_prefix' => 'ai_auto_reply_',
                'max_history' => 10,
                'clear_after' => 3600, // 1小时后清除会话
            ],
            'prompts' => [
                'system' => '你是一个友好的智能客服助手，请用简洁、专业的语言回复用户的问题。回复长度控制在100字以内。',
                'welcome' => '你好！我是AI智能助手，有什么可以帮助您的吗？',
                'error' => '抱歉，我暂时无法理解您的问题，请稍后再试或联系人工客服。',
                'busy' => '系统繁忙，请稍后再试。',
            ],
            'filters' => [
                'blocked_keywords' => ['敏感词1', '敏感词2'],
                'blocked_patterns' => ['/spam/i'],
                'min_interval' => 5, // 同一用户最小回复间隔（秒）
            ]
        ]);
    }

    /**
     * 初始化AI服务
     */
    protected function initializeAiService(): void
    {
        try {
            if (!$this->config['enabled']) {
                return;
            }

            $this->aiApp = new AiApplication();
            $this->aiApp->boot();
            $this->aiService = $this->aiApp->make('ai.unified');

            // 设置AI配置
            ConfigManager::set('default_provider', $this->config['provider']);
            ConfigManager::loadFromEnv();

            $this->logger->info('AI自动回复服务初始化成功');

        } catch (\Exception $e) {
            $this->logger->error('AI自动回复服务初始化失败: ' . $e->getMessage(), [
                'exception' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 判断是否应该回复
     * @param array $message
     * @return bool
     */
    public function shouldReply(array $message): bool
    {
        $this->logger->info('判断是否应该回复', $message);
        // 检查基础配置
        if (!$this->config['enabled'] || !isset($this->aiService)) {
            return false;
        }

        // 验证消息格式
        if (!$this->validateMessage($message, 'content') || 
            !$this->validateMessage($message, 'from_id', 'int')) {
            return false;
        }

        $content = trim($message['content']);
        $fromId = $message['from_id'];

        // 检查消息长度
        $contentLength = mb_strlen($content);
        if ($contentLength < $this->config['triggers']['min_length'] || 
            $contentLength > $this->config['triggers']['max_length']) {
            return false;
        }

        // 检查频率限制
        if (!$this->checkRateLimit($fromId)) {
            return false;
        }

        // 检查内容过滤
        if (!$this->passContentFilter($content)) {
            return false;
        }

        // 检查触发条件
        return $this->checkTriggers($content);
    }

    /**
     * 获取AI回复
     * @param array $message
     * @return string|null
     */
    public function getReply(array $message): ?string
    {
        $this->logger->info('获取AI回复', $message);
        try {
            $content = trim($message['content']);
            $fromId = $message['from_id'];
            $sessionId = $this->getSessionId($fromId);

            // 检查缓存
            $cacheKey = $this->getCacheKey($content);
            $cachedReply = Cache::get($cacheKey);
            if ($cachedReply) {
                $this->logger->info('使用缓存的AI回复', ['content' => $content, 'reply' => $cachedReply]);
                return $cachedReply;
            }

            // 准备AI请求选项
            $options = [
                'provider' => $this->config['provider'],
                'model' => $this->config['model'],
                'temperature' => $this->config['temperature'],
                'max_tokens' => $this->config['max_tokens'],
                'timeout' => $this->config['timeout'],
                'cache' => true,
            ];

            // 如果启用记忆功能
            if ($this->config['context']['use_memory']) {
                $options['session_id'] = $sessionId;
                $options['type'] = 'memory';
            }

            // 构建完整的输入内容
            $input = $this->buildAiInput($content);

            // 调用AI服务
            $response = $this->aiService->process($input, $options);
            
            if ($response && $response->isSuccess()) {
                $reply = $this->processAiResponse($response->content);
                
                // 缓存回复
                Cache::set($cacheKey, $reply, $this->config['cache_ttl']);
                
                // 记录回复间隔
                $this->recordReplyTime($fromId);
                
                $this->logger->info('AI自动回复成功', [
                    'input' => $content,
                    'reply' => $reply,
                    'service' => $response->service,
                    'duration' => $response->duration
                ]);
                
                return $reply;
            }

        } catch (\Exception $e) {
            $this->logger->error('AI自动回复失败: ' . $e->getMessage(), [
                'message' => $message,
                'exception' => $e->getTraceAsString()
            ]);
        }

        // 返回默认错误回复
        return $this->config['prompts']['error'];
    }

    /**
     * 检查频率限制
     * @param int $fromId
     * @return bool
     */
    protected function checkRateLimit(int $fromId): bool
    {
        $lastReplyKey = "ai_auto_reply_last_{$fromId}";
        $lastReplyTime = Cache::get($lastReplyKey, 0);
        $now = time();
        
        if ($now - $lastReplyTime < $this->config['filters']['min_interval']) {
            return false;
        }
        
        return true;
    }

    /**
     * 记录回复时间
     * @param int $fromId
     */
    protected function recordReplyTime(int $fromId): void
    {
        $lastReplyKey = "ai_auto_reply_last_{$fromId}";
        Cache::set($lastReplyKey, time(), 3600);
    }

    /**
     * 内容过滤检查
     * @param string $content
     * @return bool
     */
    protected function passContentFilter(string $content): bool
    {
        // 检查屏蔽关键词
        foreach ($this->config['filters']['blocked_keywords'] as $keyword) {
            if (stripos($content, $keyword) !== false) {
                return false;
            }
        }

        // 检查屏蔽模式
        foreach ($this->config['filters']['blocked_patterns'] as $pattern) {
            if (preg_match($pattern, $content)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查触发条件
     * @param string $content
     * @return bool
     */
    protected function checkTriggers(string $content): bool
    {
        $triggers = $this->config['triggers'];

        // 如果设置为总是回复
        if ($triggers['always_reply']) {
            return true;
        }

        // 检查关键词触发
        foreach ($triggers['keywords'] as $keyword) {
            if (stripos($content, $keyword) !== false) {
                return true;
            }
        }

        // 检查模式触发
        foreach ($triggers['patterns'] as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 构建AI输入内容
     * @param string $content
     * @return string
     */
    protected function buildAiInput(string $content): string
    {
        $systemPrompt = $this->config['prompts']['system'];
        
        // 移除触发关键词
        $cleanContent = $content;
        foreach ($this->config['triggers']['keywords'] as $keyword) {
            $cleanContent = str_ireplace($keyword, '', $cleanContent);
        }
        
        // 移除触发模式
        foreach ($this->config['triggers']['patterns'] as $pattern) {
            $cleanContent = preg_replace($pattern, '', $cleanContent);
        }
        
        $cleanContent = trim($cleanContent, ' ：:');
        
        return $systemPrompt . "\n\n用户问题：" . $cleanContent;
    }

    /**
     * 处理AI响应
     * @param string $response
     * @return string
     */
    protected function processAiResponse(string $response): string
    {
        // 清理响应内容
        $response = trim($response);
        
        // 限制长度
        if (mb_strlen($response) > 200) {
            $response = mb_substr($response, 0, 197) . '...';
        }
        
        // 移除可能的系统提示
        $response = preg_replace('/^(系统|助手|AI)[：:]/', '', $response);
        
        return $response ?: $this->config['prompts']['error'];
    }

    /**
     * 获取会话ID
     * @param int $fromId
     * @return string
     */
    protected function getSessionId(int $fromId): string
    {
        return $this->config['context']['session_prefix'] . $fromId;
    }

    /**
     * 获取缓存键
     * @param string $content
     * @return string
     */
    protected function getCacheKey(string $content): string
    {
        return 'ai_auto_reply_' . md5($content);
    }

    /**
     * 清除用户会话
     * @param int $fromId
     */
    public function clearUserSession(int $fromId): void
    {
        try {
            if (isset($this->aiService) && $this->config['context']['use_memory']) {
                $sessionId = $this->getSessionId($fromId);
                // 这里可以调用AI服务的清除记忆方法
                // $this->aiService->clearMemory($sessionId);
            }
        } catch (\Exception $e) {
            $this->logger->error('清除用户AI会话失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取服务状态
     * @return array
     */
    public function getStatus(): array
    {
        return [
            'enabled' => $this->config['enabled'],
            'ai_service_available' => isset($this->aiService),
            'provider' => $this->config['provider'],
            'model' => $this->config['model'],
            'memory_enabled' => $this->config['context']['use_memory'],
            'cache_ttl' => $this->config['cache_ttl'],
        ];
    }
}
