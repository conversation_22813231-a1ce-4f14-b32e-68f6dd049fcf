<?php
namespace app\vchat\auto_reply;

class WelcomeReply extends AbstractAutoReply
{
    public function shouldReply(array $message): bool
    {
        // Validate message format
        if (!$this->validateMessage($message, 'message_type', 'string')) {
            return false;
        }

        // Only reply when a new session starts
        return $message['message_type'] === 'new_session';
    }

    public function getReply(array $message): ?string
    {
        return '您好，欢迎咨询！';
    }
}