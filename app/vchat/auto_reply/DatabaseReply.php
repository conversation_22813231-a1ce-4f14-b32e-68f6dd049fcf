<?php
namespace app\vchat\auto_reply;

use app\model\AutoReplyRule;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

class DatabaseReply extends AbstractAutoReply
{

    private $cacheKey = 'vchat_database_reply_cache';

    private $rules = [];

    private $message = [];

    public function __construct()
    {
    }

    public function shouldReply(array $message): bool
    {
        // Validate message format
        if (!$this->validateMessage($message, 'content', 'string')) {
            return false;
        }

        $this->message = $message;

        return true;
    }

    protected function initRules()
    {
        // 从缓存获取帮助内容
        $rules = Cache::get($this->cacheKey);
        if (!$rules) {
            $serviceId = $this->message['to_id'] ?? 0;
            // 一次性加载所有激活规则到内存
            $rules = Db::name('ad_chat_auto_reply_rules')
                ->where('enabled', 1)
                ->where('user_id', $serviceId)
                ->field('keyword, reply_content, match_type, priority')
                ->order('priority', 'desc')
                ->select();
            // 缓存10分钟
            Cache::set($this->cacheKey, $rules, 600);
        }

        foreach ($rules as $rule) {
            $this->rules[] = [
                'keyword' => $rule['keyword'],
                'reply' => $rule['reply_content'],
                'match_type' => $rule['match_type'],
                'priority' => $rule['priority']
            ];
        }
    }

    protected function matchKeyword(string $content, string $keyword, int $matchType): bool
    {
        switch ($matchType) {
            case AutoReplyRule::MATCH_TYPE_EXACT:
                return $content === $keyword;
            case AutoReplyRule::MATCH_TYPE_FUZZY:
                return strpos($content, $keyword) !== false;
            case AutoReplyRule::MATCH_TYPE_REGEX:
                return preg_match("/{$keyword}/", $content) === 1;
            default:
                return false;
        }
    }

    public function getReply(array $message): ?string
    {

        $this->initRules();

        foreach ($this->rules as $rule) {
            if ($this->matchKeyword($message['content'], $rule['keyword'], $rule['match_type'])) {
                return $rule['reply'];
            }
        }

        return null;
    }
}