<?php
namespace app\vchat\auto_reply;

use app\vchat\utils\Logger;
use think\facade\Config;

class AutoReplyManager
{
    protected $strategies = [];
    private $logger;

    public function __construct()
    {
        $this->logger = new Logger();

        // 从配置文件中加载策略类
        $enabled = Config::get('auto_reply.enabled', false);
        if (!$enabled) {
            return;
        }

        $strategyClasses = Config::get('auto_reply.strategies', []);
        foreach ($strategyClasses as $class) {
            if (class_exists($class) && is_subclass_of($class, AutoReplyInterface::class)) {
                $this->strategies[] = new $class();
            } else {
                $this->logger->warning("Invalid auto-reply strategy: {$class}");
            }
        }
    }

    public function getAutoReply(array $message): ?string
    {
        // Basic message validation
        if (!isset($message['type']) || !is_string($message['type'])) {
            $this->logger->warning('Invalid message format: missing or invalid "type" field', $message);
            return null;
        }

        foreach ($this->strategies as $strategy) {
            try {
                if ($strategy->shouldReply($message)) {
                    $reply = $strategy->getReply($message);
                    if ($reply) {
                        $this->logger->info("Triggering auto reply", ['message' => $message, 'reply' => $reply]);
                        return $reply;
                    }
                }
            } catch (\Exception $e) {
                $this->logger->error("AutoReply strategy failed: " . $e->getMessage(), ['exception' => $e]);
            }
        }
        return null;
    }
}