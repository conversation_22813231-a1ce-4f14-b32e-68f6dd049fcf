<?php
namespace app\vchat\auto_reply;

use app\ai\utils\Logger;
use app\model\Help;
use app\ai\services\KnowledgeBaseService;
use think\facade\Cache;

class HelpReply extends AbstractAutoReply
{
    private $cacheKey = 'vchat_help_reply_cache';

    /**
     * AI知识库服务
     * @var KnowledgeBaseService
     */
    private $knowledgeBaseService;

    /**
     * 回复配置
     * @var array
     */
    protected array $config;

    public function __construct(array $config = [])
    {
        // 默认配置 - 推荐设置
        $defaultConfig = [
            'mode' => 'formal',                    // 简洁模式
            'include_fallback_message' => true,   // 不显示客服提示
            'include_suggestions' => true,         // 显示相关建议
            'max_content_length' => 300,          // 最大300字
            'confidence_threshold' => 0.15,        // 置信度阈值
            'ai_priority' => true                  // AI优先模式
        ];

        $this->config = array_merge($defaultConfig, $config);

        // 创建知识库服务，使用新的回复格式配置
        $this->knowledgeBaseService = new KnowledgeBaseService($this->config);

        Logger::info('HelpReply initialized with enhanced features', [
            'mode' => $this->config['mode'],
            'fallback_message' => $this->config['include_fallback_message'],
            'ai_priority' => $this->config['ai_priority']
        ]);
    }

    public function shouldReply(array $message): bool
    {
        // 验证消息格式
        if (!$this->validateMessage($message, 'content', 'string')) {
            return false;
        }

        $content = $message['content'] ?? '';

        // 检查是否包含帮助相关关键词
        $helpKeywords = [
            '帮助', '怎么', '如何', '怎样', '教程', '说明',
            '操作', '使用', '功能', '设置', '配置', '问题',
            '不会', '不懂', '不知道', '求助', '指导'
        ];

        foreach ($helpKeywords as $keyword) {
            if (strpos($content, $keyword) !== false) {
                Logger::info('Help keyword detected', [
                    'keyword' => $keyword,
                    'content' => $content
                ]);
                return true;
            }
        }

        // 如果启用AI优先模式，对所有消息都尝试回复
        if ($this->config['ai_priority']) {
            return true;
        }

        return false;
    }

    /**
     * 获取自动回复内容
     * @param array $message
     * @return string|null
     */
    public function getReply(array $message): ?string
    {
        try {
            $content = $message['content'] ?? '';
            $sessionId = $message['session_id'] ?? 'help_' . ($message['from_id'] ?? uniqid());

            Logger::info('Generating help reply', [
                'content' => $content,
                'session_id' => $sessionId,
                'mode' => $this->config['mode']
            ]);

            // 根据问题长度和复杂度动态调整模式
            $this->adjustModeByQuestion($content);

            // 首先尝试AI智能回答
            $aiResult = $this->getAiReply($content, $sessionId);
            if ($aiResult && $aiResult['confidence'] >= $this->config['confidence_threshold']) {
                return $this->formatAiReply($aiResult);
            }

            // 如果AI回答置信度不高，生成低置信度回复
            if ($aiResult && $aiResult['confidence'] > 0.1) {
                return $this->generateLowConfidenceReply($content, $aiResult);
            }

            // 最后回退到传统匹配
            $traditionalReply = $this->getTraditionalReply($content);
            if ($traditionalReply) {
                return $traditionalReply;
            }

            // 如果都没有匹配，返回友好的提示
            return $this->generateFallbackReply($content);

        } catch (\Exception $e) {
            Logger::error('Failed to generate help reply', [
                'content' => $content ?? '',
                'error' => $e->getMessage()
            ]);

            return $this->generateErrorReply();
        }
    }

    /**
     * 获取AI智能回复
     * @param string $content
     * @param mixed $fromId
     * @return array|null
     */
    protected function getAiReply(string $content, mixed $sessionId): ?array
    {
        try {
            $result = $this->knowledgeBaseService->ask($content, [
                'session_id' => $sessionId,
                'use_memory' => true
            ]);

            Logger::info('AI help reply: ', $result);

            if ($result['success']) {
                return [
                    'answer' => $result['content'],
                    'confidence' => $result['confidence'],
                    'sources' => $result['sources']
                ];
            }
        } catch (\Exception $e) {
            // AI服务异常时记录日志但不影响传统回复
            error_log('AI help reply failed: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * 获取传统匹配回复
     * @param string $content
     * @return string|null
     */
    protected function getTraditionalReply(string $content): ?string
    {
        $helps = $this->getCachedHelps();

        foreach ($helps as $help) {
            if ($this->matchHelp($content, $help['title'])) {
                return $this->formatHelpContent($help);
            }
        }

        return null;
    }

    protected function getCachedHelps()
    {
        // 从缓存获取帮助内容
        $helps = Cache::get($this->cacheKey);

        if (!$helps) {
            $helps = Help::where('enabled', 1)
                ->field('title, content')
                ->order('sort', 'desc')
                ->select()
                ->toArray();

            // 缓存10分钟
            Cache::set($this->cacheKey, $helps, 600);
        }

        return $helps;
    }

    protected function matchHelp(string $content, string $title): bool
    {
        return strpos($content, $title) !== false;
    }

    /**
     * 格式化帮助内容
     * @param array $help
     * @return string
     */
    protected function formatHelpContent(array $help): string
    {
        $content = strip_tags($help['content']);

        // 限制长度
        if (mb_strlen($content) > 200) {
            $content = mb_substr($content, 0, 200) . '...';
        }

        return "📋 {$help['title']}\n\n{$content}\n\n如需更多帮助，请输入 @AI 获取智能回答。";
    }

    /**
     * 根据问题动态调整回复模式
     * @param string $question
     */
    protected function adjustModeByQuestion(string $question): void
    {
        $length = mb_strlen($question);

        // 复杂问题关键词
        $complexKeywords = ['详细', '具体', '完整', '全面', '步骤', '教程', '指南'];
        $isComplex = false;

        foreach ($complexKeywords as $keyword) {
            if (strpos($question, $keyword) !== false) {
                $isComplex = true;
                break;
            }
        }

        // 动态调整模式
        if ($isComplex || $length > 50) {
            $this->knowledgeBaseService->setResponseMode('detailed');
            Logger::info('Switched to detailed mode', ['reason' => 'complex_question']);
        } elseif ($length > 20) {
            $this->knowledgeBaseService->setResponseMode('formal');
            Logger::info('Switched to formal mode', ['reason' => 'medium_question']);
        } else {
            $this->knowledgeBaseService->setResponseMode('simple');
            Logger::info('Using simple mode', ['reason' => 'simple_question']);
        }
    }

    /**
     * 格式化AI回复内容
     * @param array $result
     * @return string
     */
    protected function formatAiReply(array $result): string
    {
        $content = $result['answer'];

        // 限制内容长度
        if (strlen($content) > $this->config['max_content_length']) {
            $content = mb_substr($content, 0, $this->config['max_content_length']) . '...';
        }

        // 添加相关建议（如果启用）
        if ($this->config['include_suggestions'] && !empty($result['suggestions'])) {
            $suggestions = array_slice($result['suggestions'], 0, 3);

            if (!empty($suggestions)) {
                $suggestionText = "\n\n相关帮助：";
                foreach ($suggestions as $suggestion) {
                    $suggestionText .= "\n• " . ($suggestion['title'] ?? '相关内容');
                }
                $content .= $suggestionText;
            }
        }

        return $content;
    }

    /**
     * 生成低置信度回复
     * @param string $question
     * @param array $result
     * @return string
     */
    protected function generateLowConfidenceReply(string $question, array $result): string
    {
        $content = "抱歉，我对您的问题不太确定。";

        // 如果有部分相关内容，提供参考
        if (!empty($result['sources'])) {
            $content .= "您可以参考以下相关信息：\n\n";
            foreach (array_slice($result['sources'], 0, 2) as $source) {
                $content .= "• " . ($source['title'] ?? '相关内容') . "\n";
            }
        }

        if ($this->config['include_fallback_message']) {
            $content .= "\n建议您联系人工客服获得更准确的帮助。";
        }

        return $content;
    }

    /**
     * 生成备用回复
     * @param string $question
     * @return string
     */
    protected function generateFallbackReply(string $question): string
    {
        $fallbackMessages = [
            '抱歉，我暂时无法回答您的问题。',
            '很抱歉，我没有找到相关的帮助信息。',
            '对不起，这个问题超出了我的知识范围。'
        ];

        $message = $fallbackMessages[array_rand($fallbackMessages)];

        if ($this->config['include_fallback_message']) {
            $message .= '建议您查看帮助文档或联系客服。';
        } else {
            $message .= '您可以尝试换个方式提问。';
        }

        return $message;
    }

    /**
     * 生成错误回复
     * @return string
     */
    protected function generateErrorReply(): string
    {
        return '系统暂时繁忙，请稍后再试。';
    }

    /**
     * 设置回复模式
     * @param string $mode
     * @return self
     */
    public function setMode(string $mode): self
    {
        $this->config['mode'] = $mode;
        $this->knowledgeBaseService->setResponseMode($mode);
        return $this;
    }

    /**
     * 设置配置
     * @param array $config
     * @return self
     */
    public function setConfig(array $config): self
    {
        $this->config = array_merge($this->config, $config);
        $this->knowledgeBaseService->setResponseConfig($this->config);
        return $this;
    }

    /**
     * 获取当前配置
     * @return array
     */
    public function getConfig(): array
    {
        return $this->config;
    }
}