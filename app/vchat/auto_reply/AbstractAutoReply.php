<?php
namespace app\vchat\auto_reply;

use app\vchat\utils\Logger;

abstract class AbstractAutoReply implements AutoReplyInterface
{
    /**
     * Validate message structure
     * @param array $message
     * @param string $requiredField
     * @param string $fieldType
     * @return bool
     */
    protected function validateMessage(array $message, string $requiredField, string $fieldType = 'string'): bool
    {
        if (!isset($message[$requiredField])) {
            (new Logger())->warning("Missing required field in message: {$requiredField}", $message);
            return false;
        }

        $validTypes = [
            'string' => 'is_string',
            'int' => 'is_int',
            'array' => 'is_array',
            'bool' => 'is_bool'
        ];

        if (isset($validTypes[$fieldType]) && !$validTypes[$fieldType]($message[$requiredField])) {
            (new Logger())->warning("Invalid type for field: {$requiredField}, expected {$fieldType}", $message);
            return false;
        }

        return true;
    }
}