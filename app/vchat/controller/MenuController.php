<?php

namespace app\vchat\controller;

use app\vchat\models\Menu;
use app\vchat\services\MenuService;
use think\Controller;
use think\Request;
use think\Response;

/**
 * 菜单管理控制器
 */
class MenuController extends Controller
{
    /**
     * @var MenuService
     */
    protected $menuService;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->menuService = new MenuService();
    }
    
    /**
     * 获取菜单列表
     * 
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $parentId = $request->param('parent_id', 0);
            $status = $request->param('status', '');
            $type = $request->param('type', '');
            
            $query = Menu::order('sort', 'asc')->order('id', 'asc');
            
            if ($parentId !== '') {
                $query->where('parent_id', $parentId);
            }
            
            if ($status !== '') {
                $query->where('status', $status);
            }
            
            if ($type !== '') {
                $query->where('type', $type);
            }
            
            $menus = $query->select()->toArray();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $menus
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取菜单列表失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 获取菜单树
     * 
     * @param Request $request
     * @return Response
     */
    public function tree(Request $request): Response
    {
        try {
            $parentId = $request->param('parent_id', 0);
            $maxLevel = $request->param('max_level', 3);
            
            $tree = Menu::getMenuTree($parentId, $maxLevel);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $tree
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取菜单树失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 获取菜单详情
     * 
     * @param Request $request
     * @return Response
     */
    public function read(Request $request): Response
    {
        try {
            $menuId = $request->param('menu_id', '');
            
            if (empty($menuId)) {
                return json([
                    'code' => 400,
                    'message' => '菜单ID不能为空',
                    'data' => null
                ]);
            }
            
            $menu = Menu::getMenuById($menuId);
            
            if (!$menu) {
                return json([
                    'code' => 404,
                    'message' => '菜单不存在',
                    'data' => null
                ]);
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $menu
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取菜单详情失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 创建菜单
     * 
     * @param Request $request
     * @return Response
     */
    public function save(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证数据
            $validation = Menu::validateMenuData($data);
            if (!$validation['valid']) {
                return json([
                    'code' => 400,
                    'message' => '数据验证失败',
                    'data' => $validation['errors']
                ]);
            }
            
            // 处理参数JSON
            if (isset($data['params']) && is_array($data['params'])) {
                $data['params'] = json_encode($data['params']);
            }
            
            $result = Menu::createMenu($data);
            
            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '创建成功',
                    'data' => $data
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '创建失败',
                    'data' => null
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建菜单失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 更新菜单
     * 
     * @param Request $request
     * @return Response
     */
    public function update(Request $request): Response
    {
        try {
            $menuId = $request->param('menu_id', '');
            $data = $request->put();
            
            if (empty($menuId)) {
                return json([
                    'code' => 400,
                    'message' => '菜单ID不能为空',
                    'data' => null
                ]);
            }
            
            // 处理参数JSON
            if (isset($data['params']) && is_array($data['params'])) {
                $data['params'] = json_encode($data['params']);
            }
            
            $result = Menu::updateMenu($menuId, $data);
            
            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '更新成功',
                    'data' => $data
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '更新失败',
                    'data' => null
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新菜单失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 删除菜单
     * 
     * @param Request $request
     * @return Response
     */
    public function delete(Request $request): Response
    {
        try {
            $menuId = $request->param('menu_id', '');
            
            if (empty($menuId)) {
                return json([
                    'code' => 400,
                    'message' => '菜单ID不能为空',
                    'data' => null
                ]);
            }
            
            $result = Menu::deleteMenu($menuId);
            
            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '删除成功',
                    'data' => null
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '删除失败',
                    'data' => null
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除菜单失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 设置菜单状态
     * 
     * @param Request $request
     * @return Response
     */
    public function status(Request $request): Response
    {
        try {
            $menuId = $request->param('menu_id', '');
            $status = $request->param('status', 1);
            
            if (empty($menuId)) {
                return json([
                    'code' => 400,
                    'message' => '菜单ID不能为空',
                    'data' => null
                ]);
            }
            
            $result = Menu::setMenuStatus($menuId, $status);
            
            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '状态更新成功',
                    'data' => ['status' => $status]
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '状态更新失败',
                    'data' => null
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新状态失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 批量导入菜单
     * 
     * @param Request $request
     * @return Response
     */
    public function import(Request $request): Response
    {
        try {
            $menus = $request->param('menus', []);
            $parentId = $request->param('parent_id', 0);
            
            if (empty($menus)) {
                return json([
                    'code' => 400,
                    'message' => '菜单数据不能为空',
                    'data' => null
                ]);
            }
            
            $result = Menu::importMenus($menus, $parentId);
            
            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '导入成功',
                    'data' => ['count' => count($menus)]
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '导入失败',
                    'data' => null
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '导入菜单失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取菜单统计信息
     * 
     * @return Response
     */
    public function stats(): Response
    {
        try {
            $stats = Menu::getMenuStats();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取统计信息失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 清除菜单缓存
     * 
     * @return Response
     */
    public function clearCache(): Response
    {
        try {
            Menu::clearMenuCache();
            $this->menuService->clearMenuCache();
            
            return json([
                'code' => 200,
                'message' => '缓存清除成功',
                'data' => null
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '清除缓存失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取菜单类型列表
     * 
     * @return Response
     */
    public function types(): Response
    {
        try {
            $types = Menu::getMenuTypes();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $types
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取菜单类型失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 预览菜单
     * 
     * @param Request $request
     * @return Response
     */
    public function preview(Request $request): Response
    {
        try {
            $userId = $request->param('user_id', 'preview_user');
            $menuId = $request->param('menu_id', '');
            
            $result = $this->menuService->getUserMenu($userId, $menuId);
            
            return json([
                'code' => 200,
                'message' => '预览成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '预览菜单失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
}