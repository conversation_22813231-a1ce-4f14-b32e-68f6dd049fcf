<?php

namespace app\vchat\controller;

use app\vchat\auto_reply\AiAutoReply;
use app\vchat\utils\Logger;
use think\Request;
use think\Response;
use think\facade\Config;
use think\facade\Cache;
use think\facade\Validate;

/**
 * AI自动回复管理控制器
 */
class AiAutoReplyController
{
    /**
     * AI自动回复实例
     * @var AiAutoReply
     */
    protected AiAutoReply $aiAutoReply;

    /**
     * 日志记录器
     * @var Logger
     */
    protected Logger $logger;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->aiAutoReply = new AiAutoReply();
        $this->logger = new Logger();
    }

    /**
     * 获取AI自动回复状态
     * @return Response
     */
    public function status(): Response
    {
        try {
            $status = $this->aiAutoReply->getStatus();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $status
            ]);

        } catch (\Exception $e) {
            $this->logger->error('获取AI自动回复状态失败: ' . $e->getMessage());
            
            return json([
                'code' => 500,
                'message' => '获取状态失败',
                'data' => null
            ]);
        }
    }

    /**
     * 测试AI自动回复
     * @param Request $request
     * @return Response
     */
    public function test(Request $request): Response
    {
        try {
            // 验证请求参数
            $validate = Validate::rule([
                'content' => 'require|string|max:1000',
                'from_id' => 'integer|gt:0',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $content = $request->param('content');
            $fromId = $request->param('from_id', 999999); // 测试用户ID

            // 构建测试消息
            $message = [
                'content' => $content,
                'from_id' => $fromId,
                'type' => 'text',
                'timestamp' => time(),
            ];

            // 检查是否应该回复
            $shouldReply = $this->aiAutoReply->shouldReply($message);
            
            $result = [
                'should_reply' => $shouldReply,
                'message' => $message,
                'reply' => null,
                'test_time' => date('Y-m-d H:i:s'),
            ];

            // 如果应该回复，获取回复内容
            if ($shouldReply) {
                $reply = $this->aiAutoReply->getReply($message);
                $result['reply'] = $reply;
            }

            return json([
                'code' => 200,
                'message' => '测试完成',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            $this->logger->error('AI自动回复测试失败: ' . $e->getMessage());
            
            return json([
                'code' => 500,
                'message' => '测试失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 清除用户会话
     * @param Request $request
     * @return Response
     */
    public function clearSession(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'from_id' => 'require|integer|gt:0',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $fromId = $request->param('from_id');
            $this->aiAutoReply->clearUserSession($fromId);

            return json([
                'code' => 200,
                'message' => '会话清除成功',
                'data' => ['from_id' => $fromId]
            ]);

        } catch (\Exception $e) {
            $this->logger->error('清除用户会话失败: ' . $e->getMessage());
            
            return json([
                'code' => 500,
                'message' => '清除会话失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取配置信息
     * @return Response
     */
    public function getConfig(): Response
    {
        try {
            $config = Config::get('auto_reply.ai', []);
            
            // 隐藏敏感信息
            unset($config['api_key']);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $config
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取配置失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新配置
     * @param Request $request
     * @return Response
     */
    public function updateConfig(Request $request): Response
    {
        try {
            $config = $request->param('config', []);
            
            // 验证配置格式
            $allowedKeys = [
                'enabled', 'provider', 'model', 'temperature', 'max_tokens',
                'timeout', 'cache_ttl', 'triggers', 'context', 'prompts', 'filters'
            ];
            
            $filteredConfig = [];
            foreach ($allowedKeys as $key) {
                if (isset($config[$key])) {
                    $filteredConfig[$key] = $config[$key];
                }
            }
            
            if (empty($filteredConfig)) {
                return json([
                    'code' => 400,
                    'message' => '无效的配置参数',
                    'data' => null
                ]);
            }

            // 这里可以添加配置验证逻辑
            // 注意：实际生产环境中应该将配置保存到数据库或配置文件
            
            return json([
                'code' => 200,
                'message' => '配置更新成功',
                'data' => $filteredConfig
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新配置失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取统计信息
     * @return Response
     */
    public function getStats(): Response
    {
        try {
            // 从缓存中获取统计信息
            $stats = Cache::get('ai_auto_reply_stats', [
                'total_replies' => 0,
                'success_replies' => 0,
                'failed_replies' => 0,
                'avg_response_time' => 0,
                'last_24h_replies' => 0,
                'active_sessions' => 0,
            ]);

            // 添加实时状态
            $stats['service_status'] = $this->aiAutoReply->getStatus();
            $stats['last_updated'] = date('Y-m-d H:i:s');

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取统计失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 批量测试
     * @param Request $request
     * @return Response
     */
    public function batchTest(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'messages' => 'require|array',
                'messages.*' => 'string|max:1000',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $messages = $request->param('messages');
            $results = [];

            foreach ($messages as $index => $content) {
                $message = [
                    'content' => $content,
                    'from_id' => 999999 + $index, // 测试用户ID
                    'type' => 'text',
                    'timestamp' => time(),
                ];

                $shouldReply = $this->aiAutoReply->shouldReply($message);
                $reply = $shouldReply ? $this->aiAutoReply->getReply($message) : null;

                $results[] = [
                    'index' => $index,
                    'input' => $content,
                    'should_reply' => $shouldReply,
                    'reply' => $reply,
                ];
            }

            return json([
                'code' => 200,
                'message' => '批量测试完成',
                'data' => [
                    'total' => count($messages),
                    'replied' => count(array_filter($results, fn($r) => $r['should_reply'])),
                    'results' => $results,
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '批量测试失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 健康检查
     * @return Response
     */
    public function health(): Response
    {
        try {
            $status = $this->aiAutoReply->getStatus();
            $isHealthy = $status['enabled'] && $status['ai_service_available'];

            return json([
                'code' => $isHealthy ? 200 : 503,
                'message' => $isHealthy ? '服务正常' : '服务异常',
                'data' => [
                    'healthy' => $isHealthy,
                    'status' => $status,
                    'timestamp' => time(),
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 503,
                'message' => '健康检查失败：' . $e->getMessage(),
                'data' => ['healthy' => false]
            ]);
        }
    }
}
