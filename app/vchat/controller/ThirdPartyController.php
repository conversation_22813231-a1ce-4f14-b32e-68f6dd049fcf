<?php

namespace app\vchat\controller;

use app\vchat\handlers\ThirdPartyMessageHandler;
use app\vchat\services\ThirdPartyIntegrationService;
use app\vchat\utils\Logger;
use think\Request;
use think\Response;

/**
 * VChat第三方平台集成控制器
 * 处理第三方平台的Webhook和API请求
 */
class ThirdPartyController
{
    /**
     * 第三方平台消息处理器
     * @var ThirdPartyMessageHandler
     */
    protected ThirdPartyMessageHandler $messageHandler;

    /**
     * 第三方平台集成服务
     * @var ThirdPartyIntegrationService
     */
    protected ThirdPartyIntegrationService $integrationService;

    /**
     * 日志记录器
     * @var Logger
     */
    protected Logger $logger;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->messageHandler = new ThirdPartyMessageHandler();
        $this->integrationService = new ThirdPartyIntegrationService();
        $this->logger = new Logger();
    }

    /**
     * 微信小程序Webhook
     * @param Request $request
     * @return Response
     */
    public function wechatMiniprogram(Request $request): Response
    {
        return $this->messageHandler->handleWebhook('wechat.miniprogram', $request);
    }

    /**
     * 微信公众号Webhook
     * @param Request $request
     * @return Response
     */
    public function wechatOfficialAccount(Request $request): Response
    {
        return $this->messageHandler->handleWebhook('wechat.officialaccount', $request);
    }

    /**
     * 企业微信Webhook
     * @param Request $request
     * @return Response
     */
    public function wechatWork(Request $request): Response
    {
        return $this->messageHandler->handleWebhook('wechat.work', $request);
    }

    /**
     * QQ机器人Webhook
     * @param Request $request
     * @return Response
     */
    public function qqBot(Request $request): Response
    {
        return $this->messageHandler->handleWebhook('qq.bot', $request);
    }

    /**
     * 钉钉机器人Webhook
     * @param Request $request
     * @return Response
     */
    public function dingtalkBot(Request $request): Response
    {
        return $this->messageHandler->handleWebhook('dingtalk.bot', $request);
    }

    /**
     * 飞书机器人Webhook
     * @param Request $request
     * @return Response
     */
    public function feishuBot(Request $request): Response
    {
        return $this->messageHandler->handleWebhook('feishu.bot', $request);
    }

    /**
     * 发送消息到第三方平台
     * @param Request $request
     * @return Response
     */
    public function sendMessage(Request $request): Response
    {
        try {
            $platform = $request->param('platform', '');
            $messageId = $request->param('message_id', '');

            if (empty($platform) || empty($messageId)) {
                return $this->errorResponse('缺少必要参数');
            }

            // 获取VChat消息
            $message = \app\model\ChatMessage::find($messageId);
            if (!$message) {
                return $this->errorResponse('消息不存在');
            }

            // 发送到第三方平台
            $result = $this->integrationService->sendMessageToThirdParty($platform, $message->toArray());

            return $this->successResponse('消息发送成功', $result);

        } catch (\Exception $e) {
            $this->logger->error('发送消息到第三方平台失败', [
                'error' => $e->getMessage(),
                'params' => $request->param()
            ]);

            return $this->errorResponse('发送消息失败：' . $e->getMessage());
        }
    }

    /**
     * 获取第三方平台状态
     * @param Request $request
     * @return Response
     */
    public function getPlatformStatus(Request $request): Response
    {
        try {
            $platforms = [
                'wechat.miniprogram' => '微信小程序',
                'wechat.officialaccount' => '微信公众号',
                'wechat.work' => '企业微信',
                'qq.bot' => 'QQ机器人',
                'dingtalk.bot' => '钉钉机器人',
                'feishu.bot' => '飞书机器人'
            ];

            $status = [];
            foreach ($platforms as $platform => $name) {
                $status[$platform] = [
                    'name' => $name,
                    'enabled' => config("vchat.third_party_integration.platforms.{$platform}.enabled", false),
                    'connected' => $this->checkPlatformConnection($platform)
                ];
            }

            return $this->successResponse('获取平台状态成功', $status);

        } catch (\Exception $e) {
            $this->logger->error('获取平台状态失败', [
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse('获取平台状态失败：' . $e->getMessage());
        }
    }

    /**
     * 获取第三方平台会话统计
     * @param Request $request
     * @return Response
     */
    public function getSessionStats(Request $request): Response
    {
        try {
            $startDate = $request->param('start_date', date('Y-m-d', strtotime('-7 days')));
            $endDate = $request->param('end_date', date('Y-m-d'));

            $startTimestamp = strtotime($startDate);
            $endTimestamp = strtotime($endDate . ' 23:59:59');

            // 统计各平台会话数据
            $stats = [];
            $platforms = ['wechat.miniprogram', 'wechat.officialaccount', 'wechat.work', 'qq.bot', 'dingtalk.bot', 'feishu.bot'];

            foreach ($platforms as $platform) {
                $sessionCount = \app\model\ChatSession::where([
                    ['source', '=', $platform],
                    ['start_time', 'between', [$startTimestamp, $endTimestamp]]
                ])->count();

                $messageCount = \app\model\ChatMessage::alias('m')
                    ->join('chat_session s', 'm.session_id = s.id')
                    ->where([
                        ['s.source', '=', $platform],
                        ['m.createtime', 'between', [$startTimestamp, $endTimestamp]]
                    ])->count();

                $stats[$platform] = [
                    'name' => config("vchat.third_party_integration.platforms.{$platform}.name", $platform),
                    'sessions' => $sessionCount,
                    'messages' => $messageCount,
                    'avg_messages_per_session' => $sessionCount > 0 ? round($messageCount / $sessionCount, 2) : 0
                ];
            }

            return $this->successResponse('获取会话统计成功', [
                'period' => ['start_date' => $startDate, 'end_date' => $endDate],
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            $this->logger->error('获取会话统计失败', [
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse('获取会话统计失败：' . $e->getMessage());
        }
    }

    /**
     * 测试第三方平台连接
     * @param Request $request
     * @return Response
     */
    public function testConnection(Request $request): Response
    {
        try {
            $platform = $request->param('platform', '');

            if (empty($platform)) {
                return $this->errorResponse('平台参数不能为空');
            }

            $connected = $this->checkPlatformConnection($platform);

            return $this->successResponse('连接测试完成', [
                'platform' => $platform,
                'connected' => $connected,
                'timestamp' => time()
            ]);

        } catch (\Exception $e) {
            $this->logger->error('测试平台连接失败', [
                'platform' => $request->param('platform'),
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse('测试连接失败：' . $e->getMessage());
        }
    }

    /**
     * 获取第三方平台配置
     * @param Request $request
     * @return Response
     */
    public function getConfig(Request $request): Response
    {
        try {
            $platform = $request->param('platform');

            if ($platform) {
                // 获取单个平台配置
                $config = config("vchat.third_party_integration.platforms.{$platform}");
                if (!$config) {
                    return $this->errorResponse('平台配置不存在');
                }

                $result = [
                    'platform' => $platform,
                    'config' => $config
                ];
            } else {
                // 获取所有平台配置
                $result = config('vchat.third_party_integration.platforms', []);
            }

            return $this->successResponse('获取配置成功', $result);

        } catch (\Exception $e) {
            $this->logger->error('获取平台配置失败', [
                'platform' => $request->param('platform'),
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse('获取配置失败：' . $e->getMessage());
        }
    }

    /**
     * 检查平台连接状态
     * @param string $platform 平台标识
     * @return bool 连接状态
     */
    protected function checkPlatformConnection(string $platform): bool
    {
        try {
            // 检查平台是否启用
            $enabled = config("vchat.third_party_integration.platforms.{$platform}.enabled", false);
            if (!$enabled) {
                return false;
            }

            // 检查第三方平台配置
            $platformConfig = config("third_official_link.platforms.{$platform}");
            if (!$platformConfig) {
                return false;
            }

            // 检查必要的配置项
            $requiredFields = ['app_id', 'secret'];
            if ($platform === 'wechat.work') {
                $requiredFields = ['corp_id', 'agent_id', 'secret'];
            }

            foreach ($requiredFields as $field) {
                if (empty($platformConfig[$field])) {
                    return false;
                }
            }

            return true;

        } catch (\Exception $e) {
            $this->logger->error('检查平台连接失败', [
                'platform' => $platform,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 成功响应
     * @param string $message 消息
     * @param array $data 数据
     * @return Response
     */
    protected function successResponse(string $message = 'success', array $data = []): Response
    {
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => time()
        ];

        if (!empty($data)) {
            $response['data'] = $data;
        }

        return json($response);
    }

    /**
     * 错误响应
     * @param string $message 错误消息
     * @param int $code HTTP状态码
     * @return Response
     */
    protected function errorResponse(string $message = 'error', int $code = 400): Response
    {
        $response = [
            'success' => false,
            'error' => $message,
            'timestamp' => time()
        ];

        return json($response, $code);
    }
}
