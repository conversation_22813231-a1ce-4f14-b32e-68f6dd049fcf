<?php

namespace app\vchat\menu\handlers;

use app\vchat\utils\Logger;
use think\swoole\Websocket;

/**
 * 菜单处理器抽象基类
 */
abstract class AbstractMenuHandler
{
    /**
     * @var Logger
     */
    protected $logger;
    
    /**
     * @var array 菜单配置
     */
    protected $menuConfig;
    
    /**
     * @var array 处理器参数
     */
    protected $params;
    
    /**
     * @var string 用户ID
     */
    protected $userId;
    
    /**
     * @var Websocket WebSocket连接
     */
    protected $websocket;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logger = new Logger();
    }
    
    /**
     * 设置处理器参数
     * 
     * @param array $menuConfig 菜单配置
     * @param array $params 处理器参数
     * @param string $userId 用户ID
     * @param Websocket $websocket WebSocket连接
     * @return $this
     */
    public function setParams(array $menuConfig, array $params, string $userId, Websocket $websocket)
    {
        $this->menuConfig = $menuConfig;
        $this->params = $params;
        $this->userId = $userId;
        $this->websocket = $websocket;
        
        return $this;
    }
    
    /**
     * 处理菜单点击事件
     * 
     * @return array 处理结果
     */
    abstract public function handle(): array;
    
    /**
     * 验证处理器参数
     * 
     * @return bool
     */
    protected function validateParams(): bool
    {
        return true;
    }
    
    /**
     * 获取处理器参数
     * 
     * @param string $key 参数键名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function getParam(string $key, $default = null)
    {
        return $this->params[$key] ?? $default;
    }
    
    /**
     * 发送消息给用户
     * 
     * @param array $message 消息内容
     * @return bool
     */
    protected function sendMessage(array $message): bool
    {
        try {
            $this->websocket->to($this->userId)->emit('message', $message);
            return true;
        } catch (\Exception $e) {
            $this->logger->error('发送消息失败', [
                'user_id' => $this->userId,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 发送文本消息
     * 
     * @param string $text 文本内容
     * @param array $extra 额外参数
     * @return bool
     */
    protected function sendTextMessage(string $text, array $extra = []): bool
    {
        $message = array_merge([
            'type' => 'text',
            'content' => $text,
            'timestamp' => time(),
            'from' => 'system'
        ], $extra);
        
        return $this->sendMessage($message);
    }
    
    /**
     * 发送图文消息
     * 
     * @param array $articles 图文列表
     * @param array $extra 额外参数
     * @return bool
     */
    protected function sendNewsMessage(array $articles, array $extra = []): bool
    {
        $message = array_merge([
            'type' => 'news',
            'articles' => $articles,
            'timestamp' => time(),
            'from' => 'system'
        ], $extra);
        
        return $this->sendMessage($message);
    }
    
    /**
     * 发送媒体消息
     * 
     * @param string $mediaType 媒体类型 (image|video|audio)
     * @param string $mediaUrl 媒体URL
     * @param array $extra 额外参数
     * @return bool
     */
    protected function sendMediaMessage(string $mediaType, string $mediaUrl, array $extra = []): bool
    {
        $message = array_merge([
            'type' => 'media',
            'media_type' => $mediaType,
            'media_url' => $mediaUrl,
            'timestamp' => time(),
            'from' => 'system'
        ], $extra);
        
        return $this->sendMessage($message);
    }
    
    /**
     * 记录处理日志
     * 
     * @param string $action 操作名称
     * @param array $data 日志数据
     * @return void
     */
    protected function logAction(string $action, array $data = []): void
    {
        $this->logger->info('菜单处理器执行', [
            'handler' => static::class,
            'action' => $action,
            'user_id' => $this->userId,
            'menu_config' => $this->menuConfig,
            'params' => $this->params,
            'data' => $data
        ]);
    }
    
    /**
     * 记录错误日志
     * 
     * @param string $message 错误消息
     * @param array $context 上下文数据
     * @return void
     */
    protected function logError(string $message, array $context = []): void
    {
        $this->logger->error($message, array_merge([
            'handler' => static::class,
            'user_id' => $this->userId,
            'menu_config' => $this->menuConfig,
            'params' => $this->params
        ], $context));
    }
    
    /**
     * 构建成功响应
     * 
     * @param array $data 响应数据
     * @param string $message 响应消息
     * @return array
     */
    protected function success(array $data = [], string $message = '处理成功'): array
    {
        return [
            'success' => true,
            'message' => $message,
            'data' => $data
        ];
    }
    
    /**
     * 构建失败响应
     * 
     * @param string $message 错误消息
     * @param array $data 响应数据
     * @return array
     */
    protected function error(string $message = '处理失败', array $data = []): array
    {
        return [
            'success' => false,
            'message' => $message,
            'data' => $data
        ];
    }
}