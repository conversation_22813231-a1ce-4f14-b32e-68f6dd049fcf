<?php

namespace app\vchat\menu\handlers;

/**
 * 联系我们处理器
 */
class ContactHandler extends AbstractMenuHandler
{
    /**
     * 联系方式配置
     * @var array
     */
    protected $contactConfig = [
        'phone' => '************',
        'email' => '<EMAIL>',
        'wechat' => 'company_wechat',
        'qq' => '*********',
        'address' => '北京市朝阳区某某大厦1001室',
        'website' => 'https://www.example.com',
        'working_hours' => '周一至周五 9:00-18:00'
    ];
    
    /**
     * 处理菜单点击事件
     * 
     * @return array
     */
    public function handle(): array
    {
        $action = $this->getParam('action', 'show_contact');
        
        $this->logAction('联系我们处理', ['action' => $action]);
        
        switch ($action) {
            case 'show_contact':
                return $this->showContactInfo();
                
            case 'submit_feedback':
                return $this->submitFeedback();
                
            case 'show_feedback_form':
                return $this->showFeedbackForm();
                
            case 'emergency_contact':
                return $this->emergencyContact();
                
            default:
                return $this->error('不支持的操作: ' . $action);
        }
    }
    
    /**
     * 显示联系信息
     * 
     * @return array
     */
    protected function showContactInfo(): array
    {
        try {
            $message = "📞 联系我们\n\n" .
                      "🏢 公司信息\n" .
                      "地址：{$this->contactConfig['address']}\n" .
                      "网站：{$this->contactConfig['website']}\n\n" .
                      "📱 联系方式\n" .
                      "客服热线：{$this->contactConfig['phone']}\n" .
                      "邮箱：{$this->contactConfig['email']}\n" .
                      "微信：{$this->contactConfig['wechat']}\n" .
                      "QQ：{$this->contactConfig['qq']}\n\n" .
                      "⏰ 服务时间\n" .
                      "{$this->contactConfig['working_hours']}\n\n" .
                      "💬 您也可以直接在此对话框留言\n" .
                      "我们会尽快回复您的消息";
            
            $this->sendTextMessage($message);
            
            // 发送联系方式卡片
            $this->sendContactCard();
            
            return $this->success($this->contactConfig, '联系信息已发送');
            
        } catch (\Exception $e) {
            $this->logError('显示联系信息失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('获取联系信息失败，请稍后重试。');
            return $this->error('显示联系信息失败');
        }
    }
    
    /**
     * 发送联系方式卡片
     * 
     * @return bool
     */
    protected function sendContactCard(): bool
    {
        $articles = [
            [
                'title' => '客服热线',
                'description' => $this->contactConfig['phone'],
                'url' => 'tel:' . $this->contactConfig['phone'],
                'pic_url' => '/static/images/phone.png'
            ],
            [
                'title' => '邮箱联系',
                'description' => $this->contactConfig['email'],
                'url' => 'mailto:' . $this->contactConfig['email'],
                'pic_url' => '/static/images/email.png'
            ],
            [
                'title' => '官方网站',
                'description' => '访问我们的官方网站',
                'url' => $this->contactConfig['website'],
                'pic_url' => '/static/images/website.png'
            ]
        ];
        
        return $this->sendNewsMessage($articles);
    }
    
    /**
     * 显示反馈表单
     * 
     * @return array
     */
    protected function showFeedbackForm(): array
    {
        try {
            $message = "💬 意见反馈\n\n" .
                      "感谢您使用我们的服务！\n" .
                      "您的意见对我们非常重要。\n\n" .
                      "请按以下格式发送反馈：\n" .
                      "反馈类型：[建议/投诉/咨询/其他]\n" .
                      "详细内容：[请详细描述您的问题或建议]\n" .
                      "联系方式：[可选，便于我们回复]\n\n" .
                      "示例：\n" .
                      "反馈类型：建议\n" .
                      "详细内容：希望增加夜间客服服务\n" .
                      "联系方式：13800138000";
            
            $this->sendTextMessage($message);
            
            return $this->success([], '反馈表单已发送');
            
        } catch (\Exception $e) {
            $this->logError('显示反馈表单失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('获取反馈表单失败，请稍后重试。');
            return $this->error('显示反馈表单失败');
        }
    }
    
    /**
     * 提交反馈
     * 
     * @return array
     */
    protected function submitFeedback(): array
    {
        try {
            $feedbackType = $this->getParam('feedback_type', '');
            $content = $this->getParam('content', '');
            $contact = $this->getParam('contact', '');
            
            if (empty($content)) {
                $this->sendTextMessage('反馈内容不能为空，请重新填写。');
                return $this->error('反馈内容为空');
            }
            
            // 保存反馈信息
            $feedbackData = [
                'user_id' => $this->userId,
                'type' => $feedbackType,
                'content' => $content,
                'contact' => $contact,
                'submit_time' => date('Y-m-d H:i:s'),
                'status' => 'pending'
            ];
            
            // TODO: 这里应该保存到数据库
            $this->saveFeedback($feedbackData);
            
            $this->sendTextMessage(
                "✅ 反馈提交成功！\n\n" .
                "我们已收到您的反馈：\n" .
                "类型：{$feedbackType}\n" .
                "内容：{$content}\n\n" .
                "我们会认真处理您的反馈，\n" .
                "并在3个工作日内给您回复。\n\n" .
                "感谢您的支持！"
            );
            
            return $this->success($feedbackData, '反馈提交成功');
            
        } catch (\Exception $e) {
            $this->logError('提交反馈失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('提交反馈失败，请稍后重试。');
            return $this->error('提交反馈失败');
        }
    }
    
    /**
     * 紧急联系
     * 
     * @return array
     */
    protected function emergencyContact(): array
    {
        try {
            $message = "🚨 紧急联系方式\n\n" .
                      "如遇紧急情况，请立即联系：\n\n" .
                      "📞 紧急热线：{$this->contactConfig['phone']}\n" .
                      "（24小时服务）\n\n" .
                      "📧 紧急邮箱：<EMAIL>\n\n" .
                      "⚠️ 请注意：\n" .
                      "• 仅限紧急情况使用\n" .
                      "• 一般问题请使用常规客服\n" .
                      "• 我们会优先处理紧急事务";
            
            $this->sendTextMessage($message);
            
            // 记录紧急联系请求
            $this->logAction('紧急联系请求', [
                'user_id' => $this->userId,
                'timestamp' => time()
            ]);
            
            return $this->success([], '紧急联系信息已发送');
            
        } catch (\Exception $e) {
            $this->logError('紧急联系失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('获取紧急联系信息失败，请稍后重试。');
            return $this->error('紧急联系失败');
        }
    }
    
    /**
     * 保存反馈信息
     * 
     * @param array $feedbackData 反馈数据
     * @return bool
     */
    protected function saveFeedback(array $feedbackData): bool
    {
        try {
            // TODO: 实现数据库保存逻辑
            // 这里可以使用ThinkPHP的模型来保存数据
            
            $this->logAction('保存反馈', $feedbackData);
            
            return true;
            
        } catch (\Exception $e) {
            $this->logError('保存反馈失败', [
                'feedback_data' => $feedbackData,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * 解析反馈内容
     * 
     * @param string $content 用户输入的反馈内容
     * @return array
     */
    public function parseFeedbackContent(string $content): array
    {
        $result = [
            'type' => '',
            'content' => '',
            'contact' => ''
        ];
        
        // 解析反馈类型
        if (preg_match('/反馈类型[：:](.*?)\n/u', $content, $matches)) {
            $result['type'] = trim($matches[1]);
        }
        
        // 解析详细内容
        if (preg_match('/详细内容[：:](.*?)(?=\n联系方式|$)/us', $content, $matches)) {
            $result['content'] = trim($matches[1]);
        }
        
        // 解析联系方式
        if (preg_match('/联系方式[：:](.*?)$/us', $content, $matches)) {
            $result['contact'] = trim($matches[1]);
        }
        
        // 如果没有按格式输入，将整个内容作为反馈内容
        if (empty($result['content'])) {
            $result['content'] = $content;
            $result['type'] = '其他';
        }
        
        return $result;
    }
    
    /**
     * 获取联系配置
     * 
     * @param string $key 配置键名
     * @return mixed
     */
    public function getContactConfig(string $key = '')
    {
        if (empty($key)) {
            return $this->contactConfig;
        }
        
        return $this->contactConfig[$key] ?? null;
    }
    
    /**
     * 设置联系配置
     * 
     * @param array $config 配置数组
     * @return $this
     */
    public function setContactConfig(array $config)
    {
        $this->contactConfig = array_merge($this->contactConfig, $config);
        return $this;
    }
    
    /**
     * 验证处理器参数
     * 
     * @return bool
     */
    protected function validateParams(): bool
    {
        $action = $this->getParam('action');
        
        if (empty($action)) {
            return false;
        }
        
        $allowedActions = ['show_contact', 'submit_feedback', 'show_feedback_form', 'emergency_contact'];
        
        return in_array($action, $allowedActions);
    }
}