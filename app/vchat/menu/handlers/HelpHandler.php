<?php

namespace app\vchat\menu\handlers;

/**
 * 帮助中心处理器
 */
class HelpHandler extends AbstractMenuHandler
{
    /**
     * 常见问题数据
     * @var array
     */
    protected $faqData = [
        [
            'id' => 'faq_1',
            'question' => '如何联系客服？',
            'answer' => '您可以通过以下方式联系客服：\n1. 点击菜单中的"在线客服"\n2. 拨打客服热线：400-123-4567\n3. 发送邮件至：<EMAIL>'
        ],
        [
            'id' => 'faq_2',
            'question' => '客服服务时间是什么？',
            'answer' => '我们的客服服务时间：\n周一至周五：9:00-18:00\n周六至周日：10:00-17:00\n节假日可能会有调整，请以实际为准。'
        ],
        [
            'id' => 'faq_3',
            'question' => '如何查看订单状态？',
            'answer' => '查看订单状态的方法：\n1. 登录您的账户\n2. 进入"我的订单"页面\n3. 查看订单详情和物流信息\n如有疑问，请联系客服协助处理。'
        ],
        [
            'id' => 'faq_4',
            'question' => '支持哪些支付方式？',
            'answer' => '我们支持以下支付方式：\n1. 微信支付\n2. 支付宝\n3. 银行卡支付\n4. 信用卡支付\n所有支付都经过加密处理，请放心使用。'
        ],
        [
            'id' => 'faq_5',
            'question' => '如何申请退款？',
            'answer' => '申请退款流程：\n1. 登录账户，找到相关订单\n2. 点击"申请退款"\n3. 填写退款原因\n4. 提交申请\n我们会在3-5个工作日内处理您的申请。'
        ]
    ];
    
    /**
     * 处理菜单点击事件
     * 
     * @return array
     */
    public function handle(): array
    {
        $action = $this->getParam('action', 'show_faq');
        
        $this->logAction('帮助中心处理', ['action' => $action]);
        
        switch ($action) {
            case 'show_faq':
                return $this->showFAQ();
                
            case 'search_faq':
                return $this->searchFAQ();
                
            case 'show_faq_detail':
                return $this->showFAQDetail();
                
            case 'show_help_menu':
                return $this->showHelpMenu();
                
            default:
                return $this->error('不支持的操作: ' . $action);
        }
    }
    
    /**
     * 显示常见问题列表
     * 
     * @return array
     */
    protected function showFAQ(): array
    {
        try {
            $faqList = [];
            foreach ($this->faqData as $index => $faq) {
                $faqList[] = ($index + 1) . '. ' . $faq['question'];
            }
            
            $message = "📋 常见问题\n\n" . implode("\n", $faqList) . "\n\n" .
                      "💡 回复问题编号查看详细答案\n" .
                      "🔍 回复关键词搜索相关问题";
            
            $this->sendTextMessage($message);
            
            return $this->success(['faq_count' => count($this->faqData)], '常见问题列表已发送');
            
        } catch (\Exception $e) {
            $this->logError('显示常见问题失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('获取常见问题失败，请稍后重试。');
            return $this->error('显示常见问题失败');
        }
    }
    
    /**
     * 搜索常见问题
     * 
     * @return array
     */
    protected function searchFAQ(): array
    {
        try {
            $keyword = $this->getParam('keyword', '');
            
            if (empty($keyword)) {
                $this->sendTextMessage('请提供搜索关键词。');
                return $this->error('搜索关键词为空');
            }
            
            $results = [];
            foreach ($this->faqData as $index => $faq) {
                if (strpos($faq['question'], $keyword) !== false || 
                    strpos($faq['answer'], $keyword) !== false) {
                    $results[] = [
                        'index' => $index + 1,
                        'question' => $faq['question'],
                        'id' => $faq['id']
                    ];
                }
            }
            
            if (empty($results)) {
                $this->sendTextMessage("🔍 搜索结果\n\n未找到包含 \"{$keyword}\" 的相关问题。\n\n您可以：\n1. 尝试其他关键词\n2. 联系在线客服获取帮助");
            } else {
                $resultList = [];
                foreach ($results as $result) {
                    $resultList[] = $result['index'] . '. ' . $result['question'];
                }
                
                $message = "🔍 搜索结果 (关键词: {$keyword})\n\n" . 
                          implode("\n", $resultList) . "\n\n" .
                          "💡 回复问题编号查看详细答案";
                
                $this->sendTextMessage($message);
            }
            
            return $this->success([
                'keyword' => $keyword,
                'results' => $results,
                'count' => count($results)
            ], '搜索完成');
            
        } catch (\Exception $e) {
            $this->logError('搜索常见问题失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('搜索失败，请稍后重试。');
            return $this->error('搜索失败');
        }
    }
    
    /**
     * 显示常见问题详情
     * 
     * @return array
     */
    protected function showFAQDetail(): array
    {
        try {
            $faqId = $this->getParam('faq_id', '');
            $faqIndex = $this->getParam('faq_index', 0);
            
            $faq = null;
            
            // 根据ID或索引查找FAQ
            if (!empty($faqId)) {
                foreach ($this->faqData as $item) {
                    if ($item['id'] === $faqId) {
                        $faq = $item;
                        break;
                    }
                }
            } elseif ($faqIndex > 0 && $faqIndex <= count($this->faqData)) {
                $faq = $this->faqData[$faqIndex - 1];
            }
            
            if (empty($faq)) {
                $this->sendTextMessage('未找到相关问题，请检查问题编号。');
                return $this->error('FAQ不存在');
            }
            
            $message = "❓ {$faq['question']}\n\n" .
                      "💬 {$faq['answer']}\n\n" .
                      "📞 如需更多帮助，请联系在线客服";
            
            $this->sendTextMessage($message);
            
            return $this->success(['faq' => $faq], 'FAQ详情已发送');
            
        } catch (\Exception $e) {
            $this->logError('显示FAQ详情失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('获取问题详情失败，请稍后重试。');
            return $this->error('显示FAQ详情失败');
        }
    }
    
    /**
     * 显示帮助菜单
     * 
     * @return array
     */
    protected function showHelpMenu(): array
    {
        try {
            $helpMenu = [
                [
                    'id' => 'faq',
                    'name' => '📋 常见问题',
                    'type' => 'click'
                ],
                [
                    'id' => 'contact',
                    'name' => '📞 联系客服',
                    'type' => 'click'
                ],
                [
                    'id' => 'feedback',
                    'name' => '💬 意见反馈',
                    'type' => 'click'
                ],
                [
                    'id' => 'guide',
                    'name' => '📖 使用指南',
                    'type' => 'view'
                ]
            ];
            
            $message = [
                'type' => 'menu',
                'menu_data' => $helpMenu,
                'title' => '帮助中心',
                'timestamp' => time(),
                'from' => 'system'
            ];
            
            $this->websocket->to($this->userId)->emit('message', $message);
            
            return $this->success(['menu' => $helpMenu], '帮助菜单已发送');
            
        } catch (\Exception $e) {
            $this->logError('显示帮助菜单失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('获取帮助菜单失败，请稍后重试。');
            return $this->error('显示帮助菜单失败');
        }
    }
    
    /**
     * 根据用户输入获取FAQ
     * 
     * @param string $input 用户输入
     * @return array|null
     */
    public function getFAQByInput(string $input): ?array
    {
        // 检查是否为数字（问题编号）
        if (is_numeric($input)) {
            $index = intval($input);
            if ($index > 0 && $index <= count($this->faqData)) {
                return $this->faqData[$index - 1];
            }
        }
        
        // 关键词搜索
        foreach ($this->faqData as $faq) {
            if (strpos($faq['question'], $input) !== false || 
                strpos($faq['answer'], $input) !== false) {
                return $faq;
            }
        }
        
        return null;
    }
    
    /**
     * 获取所有FAQ数据
     * 
     * @return array
     */
    public function getAllFAQ(): array
    {
        return $this->faqData;
    }
    
    /**
     * 添加新的FAQ
     * 
     * @param string $question 问题
     * @param string $answer 答案
     * @return bool
     */
    public function addFAQ(string $question, string $answer): bool
    {
        try {
            $newFAQ = [
                'id' => 'faq_' . (count($this->faqData) + 1),
                'question' => $question,
                'answer' => $answer
            ];
            
            $this->faqData[] = $newFAQ;
            
            $this->logAction('添加FAQ', ['faq' => $newFAQ]);
            
            return true;
            
        } catch (\Exception $e) {
            $this->logError('添加FAQ失败', [
                'question' => $question,
                'answer' => $answer,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * 验证处理器参数
     * 
     * @return bool
     */
    protected function validateParams(): bool
    {
        $action = $this->getParam('action');
        
        if (empty($action)) {
            return false;
        }
        
        $allowedActions = ['show_faq', 'search_faq', 'show_faq_detail', 'show_help_menu'];
        
        return in_array($action, $allowedActions);
    }
}