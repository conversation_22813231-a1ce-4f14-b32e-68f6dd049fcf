<?php

namespace app\vchat\menu\handlers;

/**
 * URL跳转处理器
 */
class UrlHandler extends AbstractMenuHandler
{
    /**
     * 允许的域名白名单
     * @var array
     */
    protected $allowedDomains = [
        'example.com',
        'www.example.com',
        'help.example.com',
        'support.example.com'
    ];
    
    /**
     * 处理菜单点击事件
     * 
     * @return array
     */
    public function handle(): array
    {
        $url = $this->getParam('url', '');
        $title = $this->getParam('title', '链接跳转');
        $description = $this->getParam('description', '');
        
        $this->logAction('URL跳转处理', [
            'url' => $url,
            'title' => $title
        ]);
        
        if (empty($url)) {
            $this->sendTextMessage('跳转链接不能为空。');
            return $this->error('URL参数为空');
        }
        
        // 验证URL安全性
        if (!$this->validateUrl($url)) {
            $this->sendTextMessage('不安全的链接，无法跳转。');
            return $this->error('URL验证失败');
        }
        
        return $this->sendUrlMessage($url, $title, $description);
    }
    
    /**
     * 发送URL消息
     * 
     * @param string $url 跳转链接
     * @param string $title 标题
     * @param string $description 描述
     * @return array
     */
    protected function sendUrlMessage(string $url, string $title, string $description = ''): array
    {
        try {
            // 发送链接卡片消息
            $articles = [[
                'title' => $title,
                'description' => $description ?: '点击访问链接',
                'url' => $url,
                'pic_url' => $this->getUrlPreviewImage($url)
            ]];
            
            $this->sendNewsMessage($articles);
            
            // 同时发送文本提示
            $message = "🔗 {$title}\n\n";
            if (!empty($description)) {
                $message .= "{$description}\n\n";
            }
            $message .= "链接：{$url}\n\n";
            $message .= "💡 点击上方卡片或复制链接到浏览器访问";
            
            $this->sendTextMessage($message);
            
            return $this->success([
                'url' => $url,
                'title' => $title,
                'description' => $description
            ], 'URL消息已发送');
            
        } catch (\Exception $e) {
            $this->logError('发送URL消息失败', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            
            $this->sendTextMessage('链接发送失败，请稍后重试。');
            return $this->error('发送URL消息失败');
        }
    }
    
    /**
     * 验证URL安全性
     * 
     * @param string $url 待验证的URL
     * @return bool
     */
    protected function validateUrl(string $url): bool
    {
        // 基本URL格式验证
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // 解析URL
        $parsedUrl = parse_url($url);
        if (!$parsedUrl || !isset($parsedUrl['scheme']) || !isset($parsedUrl['host'])) {
            return false;
        }
        
        // 只允许HTTP和HTTPS协议
        if (!in_array(strtolower($parsedUrl['scheme']), ['http', 'https'])) {
            return false;
        }
        
        // 检查域名白名单（如果配置了）
        if (!empty($this->allowedDomains)) {
            $host = strtolower($parsedUrl['host']);
            $allowed = false;
            
            foreach ($this->allowedDomains as $domain) {
                if ($host === strtolower($domain) || 
                    str_ends_with($host, '.' . strtolower($domain))) {
                    $allowed = true;
                    break;
                }
            }
            
            if (!$allowed) {
                $this->logAction('URL域名验证失败', [
                    'url' => $url,
                    'host' => $host,
                    'allowed_domains' => $this->allowedDomains
                ]);
                return false;
            }
        }
        
        // 检查恶意URL模式
        $maliciousPatterns = [
            '/javascript:/i',
            '/data:/i',
            '/vbscript:/i',
            '/file:/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取URL预览图片
     * 
     * @param string $url 链接地址
     * @return string
     */
    protected function getUrlPreviewImage(string $url): string
    {
        // 根据URL类型返回不同的预览图
        $parsedUrl = parse_url($url);
        $host = $parsedUrl['host'] ?? '';
        
        // 根据域名返回对应图标
        $iconMap = [
            'github.com' => '/static/images/github.png',
            'docs.qq.com' => '/static/images/docs.png',
            'help.example.com' => '/static/images/help.png',
            'support.example.com' => '/static/images/support.png'
        ];
        
        foreach ($iconMap as $domain => $icon) {
            if (str_contains($host, $domain)) {
                return $icon;
            }
        }
        
        // 默认链接图标
        return '/static/images/link.png';
    }
    
    /**
     * 生成短链接
     * 
     * @param string $url 原始链接
     * @return string
     */
    protected function generateShortUrl(string $url): string
    {
        // TODO: 实现短链接生成逻辑
        // 这里可以集成第三方短链接服务或自建短链接系统
        
        return $url;
    }
    
    /**
     * 记录链接点击统计
     * 
     * @param string $url 链接地址
     * @param string $userId 用户ID
     * @return void
     */
    protected function recordClickStats(string $url, string $userId): void
    {
        try {
            // TODO: 实现点击统计逻辑
            // 可以记录到数据库用于分析
            
            $this->logAction('链接点击统计', [
                'url' => $url,
                'user_id' => $userId,
                'click_time' => date('Y-m-d H:i:s'),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            
        } catch (\Exception $e) {
            $this->logError('记录点击统计失败', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取链接元信息
     * 
     * @param string $url 链接地址
     * @return array
     */
    protected function getUrlMetadata(string $url): array
    {
        $metadata = [
            'title' => '',
            'description' => '',
            'image' => '',
            'site_name' => ''
        ];
        
        try {
            // 设置超时时间
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'user_agent' => 'Mozilla/5.0 (compatible; VChat Bot)'
                ]
            ]);
            
            $html = file_get_contents($url, false, $context);
            
            if ($html) {
                // 解析title
                if (preg_match('/<title[^>]*>([^<]+)</title>/i', $html, $matches)) {
                    $metadata['title'] = trim($matches[1]);
                }
                
                // 解析meta description
                if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\'>]+)["\'][^>]*>/i', $html, $matches)) {
                    $metadata['description'] = trim($matches[1]);
                }
                
                // 解析og:image
                if (preg_match('/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"\'>]+)["\'][^>]*>/i', $html, $matches)) {
                    $metadata['image'] = trim($matches[1]);
                }
                
                // 解析og:site_name
                if (preg_match('/<meta[^>]*property=["\']og:site_name["\'][^>]*content=["\']([^"\'>]+)["\'][^>]*>/i', $html, $matches)) {
                    $metadata['site_name'] = trim($matches[1]);
                }
            }
            
        } catch (\Exception $e) {
            $this->logError('获取URL元信息失败', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
        }
        
        return $metadata;
    }
    
    /**
     * 设置允许的域名白名单
     * 
     * @param array $domains 域名列表
     * @return $this
     */
    public function setAllowedDomains(array $domains)
    {
        $this->allowedDomains = $domains;
        return $this;
    }
    
    /**
     * 添加允许的域名
     * 
     * @param string $domain 域名
     * @return $this
     */
    public function addAllowedDomain(string $domain)
    {
        if (!in_array($domain, $this->allowedDomains)) {
            $this->allowedDomains[] = $domain;
        }
        return $this;
    }
    
    /**
     * 获取允许的域名列表
     * 
     * @return array
     */
    public function getAllowedDomains(): array
    {
        return $this->allowedDomains;
    }
    
    /**
     * 验证处理器参数
     * 
     * @return bool
     */
    protected function validateParams(): bool
    {
        $url = $this->getParam('url');
        
        return !empty($url) && $this->validateUrl($url);
    }
}