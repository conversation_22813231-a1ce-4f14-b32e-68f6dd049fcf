<?php

namespace app\vchat\menu\handlers;

use app\vchat\services\QueueService;
use app\vchat\services\CustomerService;
use app\vchat\services\SessionService;

/**
 * 客服服务处理器
 */
class CustomerServiceHandler extends AbstractMenuHandler
{
    /**
     * @var QueueService
     */
    protected $queueService;
    
    /**
     * @var CustomerService
     */
    protected $customerService;
    
    /**
     * @var SessionService
     */
    protected $sessionService;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        $this->queueService = new QueueService();
        $this->customerService = new CustomerService();
        $this->sessionService = new SessionService();
    }
    
    /**
     * 处理菜单点击事件
     * 
     * @return array
     */
    public function handle(): array
    {
        $action = $this->getParam('action', 'start_service');
        
        $this->logAction('客服服务处理', ['action' => $action]);
        
        switch ($action) {
            case 'start_service':
                return $this->startCustomerService();
                
            case 'queue_status':
                return $this->getQueueStatus();
                
            case 'end_service':
                return $this->endCustomerService();
                
            case 'transfer_service':
                return $this->transferService();
                
            default:
                return $this->error('不支持的操作: ' . $action);
        }
    }
    
    /**
     * 开始客服服务
     * 
     * @return array
     */
    protected function startCustomerService(): array
    {
        try {
            // 检查用户是否已在服务中
            $currentSession = $this->sessionService->getUserSession($this->userId);
            if (!empty($currentSession) && $currentSession['status'] === 'active') {
                $this->sendTextMessage('您已经在客服服务中，无需重复申请。');
                return $this->success([], '用户已在服务中');
            }
            
            // 检查是否有可用客服
            $availableAgent = $this->customerService->getAvailableAgent();
            
            if ($availableAgent) {
                // 直接分配客服
                $sessionResult = $this->customerService->createSession($this->userId, $availableAgent['id']);
                
                if ($sessionResult['success']) {
                    $this->sendTextMessage(
                        "欢迎使用客服服务！\n" .
                        "客服 {$availableAgent['name']} 为您服务\n" .
                        "如需结束服务，请发送 '结束服务'"
                    );
                    
                    return $this->success([
                        'session_id' => $sessionResult['session_id'],
                        'agent_id' => $availableAgent['id'],
                        'agent_name' => $availableAgent['name']
                    ], '客服分配成功');
                } else {
                    $this->sendTextMessage('客服分配失败，请稍后重试。');
                    return $this->error('客服分配失败');
                }
            } else {
                // 加入排队
                $queueResult = $this->queueService->addToQueue($this->userId);
                
                if ($queueResult['success']) {
                    $waitTime = $this->formatWaitTime($queueResult['estimated_wait_time']);
                    
                    $this->sendTextMessage(
                        "当前客服繁忙，您已加入排队\n" .
                        "排队位置：第 {$queueResult['position']} 位\n" .
                        "预计等待时间：{$waitTime}\n" .
                        "我们会尽快为您安排客服"
                    );
                    
                    return $this->success([
                        'queue_position' => $queueResult['position'],
                        'estimated_wait_time' => $queueResult['estimated_wait_time']
                    ], '加入排队成功');
                } else {
                    $this->sendTextMessage('排队失败，请稍后重试。');
                    return $this->error('排队失败');
                }
            }
            
        } catch (\Exception $e) {
            $this->logError('开始客服服务失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('服务异常，请稍后重试。');
            return $this->error('服务异常');
        }
    }
    
    /**
     * 获取排队状态
     * 
     * @return array
     */
    protected function getQueueStatus(): array
    {
        try {
            if (!$this->queueService->isUserInQueue($this->userId)) {
                $this->sendTextMessage('您当前不在排队中。');
                return $this->success([], '用户不在排队中');
            }
            
            $queueInfo = $this->queueService->getUserQueueInfo($this->userId);
            $waitTime = $this->formatWaitTime($queueInfo['estimated_wait_time']);
            
            $this->sendTextMessage(
                "排队状态信息：\n" .
                "当前位置：第 {$queueInfo['position']} 位\n" .
                "预计等待时间：{$waitTime}\n" .
                "请耐心等待"
            );
            
            return $this->success($queueInfo, '获取排队状态成功');
            
        } catch (\Exception $e) {
            $this->logError('获取排队状态失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('获取排队状态失败，请稍后重试。');
            return $this->error('获取排队状态失败');
        }
    }
    
    /**
     * 结束客服服务
     * 
     * @return array
     */
    protected function endCustomerService(): array
    {
        try {
            $session = $this->sessionService->getUserSession($this->userId);
            
            if (empty($session)) {
                $this->sendTextMessage('您当前没有进行中的客服服务。');
                return $this->success([], '无进行中的服务');
            }
            
            $endResult = $this->customerService->endSession($session['id'], $this->userId);
            
            if ($endResult['success']) {
                $this->sendTextMessage(
                    "客服服务已结束\n" .
                    "感谢您的使用！\n" .
                    "如需再次使用客服服务，请点击菜单重新申请"
                );
                
                return $this->success([], '客服服务结束成功');
            } else {
                $this->sendTextMessage('结束服务失败，请稍后重试。');
                return $this->error('结束服务失败');
            }
            
        } catch (\Exception $e) {
            $this->logError('结束客服服务失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('结束服务异常，请稍后重试。');
            return $this->error('结束服务异常');
        }
    }
    
    /**
     * 转接服务
     * 
     * @return array
     */
    protected function transferService(): array
    {
        try {
            $session = $this->sessionService->getUserSession($this->userId);
            
            if (empty($session)) {
                $this->sendTextMessage('您当前没有进行中的客服服务。');
                return $this->error('无进行中的服务');
            }
            
            $targetAgentId = $this->getParam('target_agent_id');
            $reason = $this->getParam('reason', '用户申请转接');
            
            $transferResult = $this->customerService->transferSession(
                $session['id'], 
                $session['agent_id'], 
                $targetAgentId, 
                $reason
            );
            
            if ($transferResult['success']) {
                $this->sendTextMessage(
                    "服务转接成功\n" .
                    "新客服将为您继续服务\n" .
                    "请稍等片刻"
                );
                
                return $this->success($transferResult['data'], '服务转接成功');
            } else {
                $this->sendTextMessage('服务转接失败，请稍后重试。');
                return $this->error('服务转接失败');
            }
            
        } catch (\Exception $e) {
            $this->logError('转接服务失败', ['error' => $e->getMessage()]);
            $this->sendTextMessage('转接服务异常，请稍后重试。');
            return $this->error('转接服务异常');
        }
    }
    
    /**
     * 格式化等待时间
     * 
     * @param int $seconds 秒数
     * @return string
     */
    protected function formatWaitTime(int $seconds): string
    {
        if ($seconds < 60) {
            return $seconds . '秒';
        } elseif ($seconds < 3600) {
            $minutes = intval($seconds / 60);
            return $minutes . '分钟';
        } else {
            $hours = intval($seconds / 3600);
            $minutes = intval(($seconds % 3600) / 60);
            return $hours . '小时' . ($minutes > 0 ? $minutes . '分钟' : '');
        }
    }
    
    /**
     * 验证处理器参数
     * 
     * @return bool
     */
    protected function validateParams(): bool
    {
        $action = $this->getParam('action');
        
        if (empty($action)) {
            return false;
        }
        
        $allowedActions = ['start_service', 'queue_status', 'end_service', 'transfer_service'];
        
        return in_array($action, $allowedActions);
    }
}