# VChat 聊天系统功能说明

## 概述

VChat 聊天系统现已支持以下核心功能：
- 自动分配等待时间
- 会话超时管理
- 最大排队人数控制
- 智能客服分配
- 实时监控管理

## 功能特性

### 1. 队列管理系统 (QueueService)

#### 主要功能
- 用户排队管理
- 自动计算等待时间
- 最大排队人数限制
- 队列状态实时更新

#### 使用示例
```php
// 初始化队列服务
$queueService = new QueueService();

// 用户加入队列
$result = $queueService->addToQueue($userId);
if ($result['success']) {
    echo "排队成功，当前位置：" . $result['position'];
    echo "预计等待时间：" . $result['estimated_wait_time'] . "秒";
}

// 获取队列信息
$queueInfo = $queueService->getQueueInfo();
echo "当前排队人数：" . $queueInfo['length'];

// 检查用户是否在队列中
if ($queueService->isUserInQueue($userId)) {
    echo "用户正在排队中";
}
```

### 2. 会话超时管理 (SessionTimeoutService)

#### 主要功能
- 自动检测会话超时
- 发送超时警告
- 自动结束超时会话
- 活动记录管理

#### 使用示例
```php
// 初始化超时服务
$timeoutService = new SessionTimeoutService();

// 记录用户活动
$timeoutService->recordActivity($userId, $serviceId, 'user');

// 检查会话是否超时
if ($timeoutService->isSessionTimeout($userId, $serviceId)) {
    echo "会话已超时";
}

// 批量检查所有会话超时
$timeoutSessions = $timeoutService->checkAllSessionTimeouts();
foreach ($timeoutSessions as $session) {
    echo "超时会话：用户{$session['user_id']} - 客服{$session['service_id']}";
}
```

### 3. 用户处理器增强 (UserHandler)

#### 新增功能
- 智能客服分配
- 排队状态管理
- 离开队列处理

#### 使用示例
```php
// 用户请求客服
$handler = new UserHandler();
$result = $handler->handleServiceRequest($websocket, [
    'user_id' => $userId,
    'type' => 'request_service'
]);

// 用户离开队列
$handler->handleLeaveQueue($websocket, [
    'user_id' => $userId,
    'type' => 'leave_queue'
]);

// 获取排队状态
$status = $handler->handleGetQueueStatus($websocket, [
    'user_id' => $userId,
    'type' => 'get_queue_status'
]);
```

### 4. 客服处理器增强 (CustomerServiceHandler)

#### 新增功能
- 自动分配排队用户
- 会话结束后自动处理下一个用户
- 活动记录集成

#### 使用示例
```php
// 客服结束会话时自动分配下一个用户
$handler = new CustomerServiceHandler();
$handler->handleEndSession($websocket, [
    'session_id' => $sessionId,
    'from_id' => $serviceId,
    'to_id' => $userId
]);
// 系统会自动为该客服分配下一个排队用户
```

### 5. 定时任务服务 (ScheduleService)

#### 主要功能
- 定期检查会话超时
- 清理过期队列数据
- 更新队列状态

#### 使用示例
```php
// 启动定时任务
$scheduleService = new ScheduleService();
$scheduleService->start();

// 获取任务状态
$status = $scheduleService->getStatus();
print_r($status);
```

### 6. 管理控制台 (AdminService)

#### 主要功能
- 系统概览监控
- 队列详细管理
- 会话监控
- 客服状态管理

#### 使用示例
```php
// 获取系统概览
$adminService = new AdminService();
$overview = $adminService->getSystemOverview();
echo "当前排队人数：" . $overview['queue']['length'];
echo "活跃会话数：" . $overview['sessions']['active_count'];

// 手动分配用户
$result = $adminService->manualAssignUser($userId, $serviceId);
if ($result) {
    echo "手动分配成功";
}

// 强制结束会话
$adminService->forceEndSession($sessionId);

// 清空队列
$adminService->clearQueue();
```

## 配置说明

### 配置文件位置
`/config/vchat.php`

### 主要配置项

```php
return [
    // 队列配置
    'queue' => [
        'max_queue_size' => 100,           // 最大排队人数
        'base_wait_time' => 300,           // 基础等待时间(秒)
        'wait_time_per_user' => 60,        // 每个用户增加的等待时间(秒)
        'cleanup_interval' => 300,         // 队列清理间隔(秒)
        'status_update_interval' => 30,    // 状态更新间隔(秒)
        'max_wait_time' => 1800,           // 最大等待时间(秒)
        'avg_service_time' => 300,         // 平均服务时间(秒)
    ],
    
    // 超时配置
    'timeout' => [
        'session_timeout' => 1800,         // 会话超时时间(秒)
        'warning_time' => 300,             // 超时警告提前时间(秒)
        'check_interval' => 60,            // 超时检查间隔(秒)
        'cleanup_interval' => 3600,        // 清理间隔(秒)
    ],
    
    // 客服分配配置
    'assignment' => [
        'strategy' => 'load_balance',      // 分配策略
        'max_retry' => 3,                  // 最大重试次数
        'retry_interval' => 5,             // 重试间隔(秒)
    ]
];
```

## 数据库要求

### 客服表字段
确保 `customer_service` 表包含以下字段：
- `max_sessions`: 最大会话数
- `current_sessions`: 当前会话数
- `status`: 客服状态

### 会话表字段
确保 `chat_session` 表包含以下字段：
- `user_id`: 用户ID
- `service_id`: 客服ID
- `status`: 会话状态
- `start_time`: 开始时间
- `end_time`: 结束时间

## Redis 要求

系统使用 Redis 存储以下数据：
- 队列信息：`vchat:queue:*`
- 活动记录：`vchat:activity:*`
- 缓存数据：`vchat:cache:*`

## 部署建议

### 1. 启动定时任务
在系统启动时添加：
```php
$scheduleService = new ScheduleService();
$scheduleService->start();
```

### 2. 监控配置
建议配置以下监控指标：
- 队列长度
- 平均等待时间
- 会话超时率
- 客服利用率

### 3. 性能优化
- 合理设置 Redis 过期时间
- 定期清理无效数据
- 监控系统资源使用

## 故障排除

### 常见问题

1. **队列不工作**
   - 检查 Redis 连接
   - 确认配置文件正确
   - 查看日志文件

2. **会话超时不生效**
   - 确认定时任务已启动
   - 检查超时配置
   - 验证活动记录是否正常

3. **客服分配失败**
   - 检查客服状态
   - 确认最大会话数配置
   - 查看分配策略设置

### 日志查看
系统日志位置：`/runtime/log/vchat/`

主要日志文件：
- `queue.log`: 队列相关日志
- `timeout.log`: 超时相关日志
- `assignment.log`: 分配相关日志
- `error.log`: 错误日志

## API 接口

### WebSocket 消息类型

#### 用户端
- `request_service`: 请求客服
- `leave_queue`: 离开队列
- `get_queue_status`: 获取排队状态

#### 客服端
- `end_session`: 结束会话
- `transfer_session`: 转接会话

#### 系统消息
- `service_assigned`: 客服已分配
- `queue_position`: 排队位置更新
- `session_timeout_warning`: 会话超时警告
- `session_timeout`: 会话超时

## 扩展开发

### 自定义分配策略
```php
class CustomAssignmentStrategy
{
    public function assignService($userId, $availableServices)
    {
        // 自定义分配逻辑
        return $selectedServiceId;
    }
}
```

### 自定义通知方式
```php
class CustomNotificationService
{
    public function sendQueueNotification($userId, $message)
    {
        // 自定义通知逻辑
    }
}
```

## 版本更新

### v1.0.0
- 基础队列管理
- 会话超时检测
- 自动客服分配
- 管理控制台

### 后续计划
- 智能分配算法优化
- 多渠道支持
- 数据分析报表
- 移动端适配