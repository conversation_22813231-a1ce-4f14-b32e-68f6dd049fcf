<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\vchat\core\MessageProtocol;
use app\vchat\utils\Log;
use think\swoole\Websocket;

class SuggestionService
{

    protected $messageService;

    public function __construct(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }

    /**
     * 获取建议问题列表
     * 实际应用中，这里可以根据用户、会话状态等动态获取
     * @return array 格式: [['text' => '问题文本', 'value' => '实际发送值'], ...]
     */
    public function getSuggestedQuestions(): array
    {
        // 这里硬编码一些示例问题
        // 实际应用中可以从数据库、配置文件或其他地方加载
        return [
            ["text" => "常见问题1", "value" => "常见问题1"],
            ["text" => "常见问题2", "value" => "常见问题2"],
            ["text" => "常见问题3", "value" => "常见问题3"],
            ["text" => "请问代理佣金怎么领取?", "value" => "请问代理佣金怎么领取?"],
            ["text" => "提款问题", "value" => "提款问题"],
        ];
    }

    /**
     * 构建建议问题消息体
     * @param array $questions
     * @param string $title 消息标题
     * @return array 消息体
     */
    public function buildSuggestionsMessage(array $questions, string $title = "我猜你想问的:"): array
    {
        return [
            "message_type" => MessageProtocol::MESSAGE_CHAT_TYPE,
            "type" => MessageProtocol::MESSAGE_TYPE_SUGGESTIONS,
            "suggestions" => [
                "title" => $title,
                "questions" => $questions,
            ],
            'content' => '',
            "time" => time(), // 添加时间戳
        ];
    }

    /**
     * 发送建议问题消息给指定用户
     * @param Websocket $websocket Think-Swoole Websocket 实例
     * @param int $userId 目标用户ID
     * @param string $userType 'user' or 'service' 房间类型
     */
    public function sendSuggestionsToUser(Websocket $websocket, int $userId, string $userType = 'user'): void
    {
        try {
            $questions = $this->getSuggestedQuestions();
            if (empty($questions)) {
                Log::info("[SuggestionService] 没有可用的建议问题");
                return;
            }

            $message = $this->buildSuggestionsMessage($questions);

            // 使用 Websocket 工具类发送消息到指定房间 (用户或客服的房间)
            $message['to_type'] = $userType;
            $message['to_id'] = $userId;
            Log::info("[SuggestionService] 发送建议问题消息到用户: " . $userId . " (" . $userType . ")");
            Log::info("[SuggestionService Message] " . json_encode($message, JSON_UNESCAPED_UNICODE) . "\n");

            // 使用 Swoole 的 push 方法发送原始消息，或者使用 Think-Swoole 的 emit 方法
            // 如果前端是 Socket.IO 协议，使用 emit 更方便
            // 这里假设前端使用 Socket.IO，监听 'message' 事件
            $websocket->emit(MessageProtocol::MESSAGE_TYPE, $message);

            Log::info("[SuggestionService] 建议问题消息已发送");

        } catch (\Throwable $e) {
            Log::error("[SuggestionService] 发送建议问题消息失败: " . $e->getMessage());
        }
    }
}