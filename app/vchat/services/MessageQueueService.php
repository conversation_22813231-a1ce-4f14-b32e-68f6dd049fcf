<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\vchat\core\MessageProtocol;
use app\vchat\utils\Logger;
use think\swoole\Websocket;
use think\facade\Cache;
use think\facade\Log;

/**
 * 消息队列服务类
 * 用于跨进程消息发送，定时任务可以通过此服务发送消息到 Redis 队列
 * 主进程的 Worker 会处理队列中的消息并发送给客户端
 */
class MessageQueueService
{
    /**
     * Redis 队列前缀
     */
    const REDIS_QUEUE_PREFIX = 'vchat:message_queue:';
    
    /**
     * 待发送消息队列
     */
    const PENDING_QUEUE = 'pending';
    
    /**
     * 失败消息队列
     */
    const FAILED_QUEUE = 'failed';
    
    /**
     * 消息状态存储前缀
     */
    const MESSAGE_STATUS_PREFIX = 'message_status:';
    
    /**
     * 消息详情存储前缀
     */
    const MESSAGE_DETAIL_PREFIX = 'message_detail:';

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->logger = new Logger();
    }

    /**
     * 将 Socket.IO 事件消息加入队列
     * @param int $userId 用户ID
     * @param string $event 事件名称
     * @param string $type 用户类型 (user/service)
     * @param array $data 事件数据
     * @return bool
     */
    public function pushSocketIoMessage(int $userId, string $event, string $type = 'user', array $data = []): bool
    {
        try {
            $messageData = [
                'id' => uniqid('msg_', true),
                'type' => 'socketio_event',
                'user_id' => $userId,
                'user_type' => $type,
                'event' => $event,
                'data' => $data,
                'retry_times' => 0,
                'max_retry' => 3,
                'status' => 'pending',
                'created_at' => time()
            ];

            // 将消息加入 Redis 队列
            $queueKey = self::REDIS_QUEUE_PREFIX . self::PENDING_QUEUE;
            $result = Cache::lPush($queueKey, json_encode($messageData));
            
            if ($result) {
                Log::info('[消息队列] Socket.IO 事件消息已加入队列', [
                    'message_id' => $messageData['id'],
                    'user_id' => $userId,
                    'event' => $event,
                    'type' => $type
                ]);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('[消息队列] Socket.IO 事件消息入队失败', [
                'user_id' => $userId,
                'event' => $event,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 将消息加入队列
     * @param array $message 消息数据
     * @return bool
     */
    public function pushMessage(array $message): bool
    {
        try {
            // 验证消息格式
            if (!MessageProtocol::validate($message)) {
                throw new \Exception('消息格式无效');
            }

            // 将消息加入队列
            $queueData = [
                'id' => uniqid('msg_', true),
                'message' => $message,
                'retry_times' => 0,
                'max_retry' => 3,
                'status' => 'pending',
                'created_at' => time()
            ];

            // 将消息加入 Redis 队列
            $queueKey = self::REDIS_QUEUE_PREFIX . self::PENDING_QUEUE;
            $result = Cache::lPush($queueKey, json_encode($queueData));
            
            if ($result) {
                Log::info('[消息队列] 消息已加入队列', [
                    'message_id' => $queueData['id']
                ]);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('[消息队列] 消息入队失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 处理队列中的消息
     * @param \Swoole\WebSocket\Server $server Swoole Server 实例
     * @param FdManagerService $fdManager FD 管理服务
     * @return int 处理的消息数量
     */
    public function processQueueMessages($server, FdManagerService $fdManager): int
    {
        $processedCount = 0;
        $queueKey = self::REDIS_QUEUE_PREFIX . self::PENDING_QUEUE;
        
        try {
            // 批量处理消息，每次最多处理 10 条
            for ($i = 0; $i < 10; $i++) {
                // 从队列右侧弹出消息（FIFO）
                $messageJson = Cache::rPop($queueKey);
                
                if (!$messageJson) {
                    break; // 队列为空
                }
                
                $messageData = json_decode($messageJson, true);
                if (!$messageData) {
                    Log::error('[消息队列] 消息格式错误', ['message' => $messageJson]);
                    continue;
                }
                
                $success = $this->processMessage($server, $fdManager, $messageData);
                
                if ($success) {
                    $processedCount++;
                    Log::info('[消息队列] 消息处理成功', [
                        'message_id' => $messageData['id'] ?? 'unknown'
                    ]);
                } else {
                    // 处理失败，检查重试次数
                    $this->handleFailedMessage($messageData);
                }
            }
        } catch (\Exception $e) {
            Log::error('[消息队列] 处理队列消息异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        return $processedCount;
    }
    
    /**
     * 处理单条消息
     * @param \Swoole\WebSocket\Server $server
     * @param FdManagerService $fdManager
     * @param array $messageData
     * @return bool
     */
    protected function processMessage($server, FdManagerService $fdManager, array $messageData): bool
    {
        try {
            if ($messageData['type'] === 'socketio_event') {
                return $this->processSocketIoMessage($server, $fdManager, $messageData);
            } else {
                // 处理其他类型的消息
                return $this->processRegularMessage($server, $fdManager, $messageData);
            }
        } catch (\Exception $e) {
            Log::error('[消息队列] 处理消息异常', [
                'message_id' => $messageData['id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 处理 Socket.IO 事件消息
     * @param \Swoole\WebSocket\Server $server
     * @param FdManagerService $fdManager
     * @param array $messageData
     * @return bool
     */
    protected function processSocketIoMessage($server, FdManagerService $fdManager, array $messageData): bool
    {
        $userId = $messageData['user_id'];
        $userType = $messageData['user_type'] ?? 'user';
        $event = $messageData['event'];
        $data = $messageData['data'] ?? [];
        
        // 查找用户对应的 FD
        $fd = $fdManager->getFdByUserId($userId, $userType);
        
        if (!$fd) {
            Log::info('[消息队列] 未找到用户FD', [
                'user_id' => $userId,
                'user_type' => $userType,
                'event' => $event
            ]);
            return false;
        }
        
        // 检查 FD 是否有效
        if (!$fdManager->isFdValid($fd)) {
            Log::info('[消息队列] FD无效', [
                'user_id' => $userId,
                'fd' => $fd,
                'event' => $event
            ]);
            return false;
        }
        
        try {
            // 构建 Socket.IO 事件包
            $socketIoPacket = '42' . json_encode([$event, $data]);
            
            // 发送消息
            $result = $server->push($fd, $socketIoPacket);
            
            if ($result) {
                Log::info('[消息队列] Socket.IO 事件消息发送成功', [
                    'user_id' => $userId,
                    'fd' => $fd,
                    'event' => $event,
                    'packet' => $socketIoPacket
                ]);
                return true;
            } else {
                Log::error('[消息队列] Socket.IO 事件消息发送失败', [
                    'user_id' => $userId,
                    'fd' => $fd,
                    'event' => $event
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('[消息队列] Socket.IO 事件消息发送异常', [
                'user_id' => $userId,
                'fd' => $fd,
                'event' => $event,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 处理常规消息
     * @param \Swoole\WebSocket\Server $server
     * @param FdManagerService $fdManager
     * @param array $messageData
     * @return bool
     */
    protected function processRegularMessage($server, FdManagerService $fdManager, array $messageData): bool
    {
        try {
            $message = $messageData['message'] ?? [];
            $messageId = $messageData['id'] ?? 'unknown';
            
            // 更新消息状态为处理中
            $this->updateMessageStatus($messageId, 'processing');
            
            // 验证消息格式
            if (!MessageProtocol::validate($message)) {
                Log::error('[消息队列] 常规消息格式无效', [
                    'message_id' => $messageId,
                    'message' => $message
                ]);
                $this->updateMessageStatus($messageId, 'failed');
                return false;
            }
            
            $userId = $message['to_user_id'] ?? null;
            $userType = $message['to_user_type'] ?? 'user';
            
            if (!$userId) {
                Log::error('[消息队列] 常规消息缺少接收用户ID', [
                    'message_id' => $messageId,
                    'message' => $message
                ]);
                $this->updateMessageStatus($messageId, 'failed');
                return false;
            }
            
            // 查找用户对应的 FD
            $fd = $fdManager->getFdByUserId($userId, $userType);
            
            if (!$fd) {
                Log::info('[消息队列] 常规消息未找到用户FD', [
                    'message_id' => $messageId,
                    'user_id' => $userId,
                    'user_type' => $userType
                ]);
                $this->updateMessageStatus($messageId, 'user_offline');
                return false;
            }
            
            // 检查 FD 是否有效
            if (!$fdManager->isFdValid($fd)) {
                Log::info('[消息队列] 常规消息FD无效', [
                    'message_id' => $messageId,
                    'user_id' => $userId,
                    'fd' => $fd
                ]);
                $this->updateMessageStatus($messageId, 'fd_invalid');
                return false;
            }
            
            // 发送消息
            $result = $server->push($fd, json_encode($message));
            
            if ($result) {
                Log::info('[消息队列] 常规消息发送成功', [
                    'message_id' => $messageId,
                    'user_id' => $userId,
                    'fd' => $fd
                ]);
                $this->updateMessageStatus($messageId, 'sent');
                return true;
            } else {
                Log::error('[消息队列] 常规消息发送失败', [
                    'message_id' => $messageId,
                    'user_id' => $userId,
                    'fd' => $fd
                ]);
                $this->updateMessageStatus($messageId, 'send_failed');
                return false;
            }
        } catch (\Exception $e) {
            Log::error('[消息队列] 常规消息处理异常', [
                'message_id' => $messageData['id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->updateMessageStatus($messageData['id'] ?? 'unknown', 'exception');
            return false;
        }
    }

    /**
     * 处理失败的消息
     * @param array $messageData 消息数据
     * @return void
     */
    protected function handleFailedMessage(array $messageData): void
    {
        try {
            $retryTimes = $messageData['retry_times'] ?? 0;
            $maxRetry = $messageData['max_retry'] ?? 3;
            
            if ($retryTimes < $maxRetry) {
                // 增加重试次数
                $messageData['retry_times'] = $retryTimes + 1;
                $messageData['status'] = 'retry';
                $messageData['retry_at'] = time();
                
                // 重新加入队列
                $queueKey = self::REDIS_QUEUE_PREFIX . self::PENDING_QUEUE;
                Cache::lPush($queueKey, json_encode($messageData));
                
                Log::info('[消息队列] 消息重新加入队列', [
                    'message_id' => $messageData['id'] ?? 'unknown',
                    'retry_times' => $messageData['retry_times']
                ]);
            } else {
                // 超过最大重试次数，移入失败队列
                $messageData['status'] = 'failed';
                $messageData['failed_at'] = time();
                
                $failedQueueKey = self::REDIS_QUEUE_PREFIX . self::FAILED_QUEUE;
                Cache::lPush($failedQueueKey, json_encode($messageData));
                
                Log::error('[消息队列] 消息处理失败，已移入失败队列', [
                    'message_id' => $messageData['id'] ?? 'unknown',
                    'retry_times' => $retryTimes
                ]);
            }
        } catch (\Exception $e) {
            Log::error('[消息队列] 处理失败消息异常', [
                'message_id' => $messageData['id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 重试失败的消息
     * @param string $messageId 消息ID
     * @return bool
     */
    public function retryFailedMessage(string $messageId): bool
    {
        try {
            $failedQueueKey = self::REDIS_QUEUE_PREFIX . self::FAILED_QUEUE;
            $queueLength = Cache::lLen($failedQueueKey);
            
            // 遍历失败队列查找指定消息
            for ($i = 0; $i < $queueLength; $i++) {
                $messageJson = Cache::lIndex($failedQueueKey, $i);
                if (!$messageJson) continue;
                
                $messageData = json_decode($messageJson, true);
                if (!$messageData || ($messageData['id'] ?? '') !== $messageId) {
                    continue;
                }
                
                // 找到消息，从失败队列中移除
                Cache::lRem($failedQueueKey, $messageJson, 1);
                
                // 重置重试次数并重新加入队列
                $messageData['retry_times'] = 0;
                $messageData['status'] = 'pending';
                $messageData['retry_at'] = time();
                
                $queueKey = self::REDIS_QUEUE_PREFIX . self::PENDING_QUEUE;
                Cache::lPush($queueKey, json_encode($messageData));
                
                Log::info('[消息队列] 失败消息重新加入队列', [
                    'message_id' => $messageId
                ]);
                
                return true;
            }
            
            Log::warning('[消息队列] 未找到指定的失败消息', [
                'message_id' => $messageId
            ]);
            
            return false;
        } catch (\Exception $e) {
            Log::error('[消息队列] 重试失败消息异常', [
                'message_id' => $messageId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 获取队列状态
     * @return array
     */
    public function getQueueStatus(): array
    {
        try {
            $pendingKey = self::REDIS_QUEUE_PREFIX . self::PENDING_QUEUE;
            $failedKey = self::REDIS_QUEUE_PREFIX . self::FAILED_QUEUE;
            
            return [
                'pending_count' => Cache::lLen($pendingKey) ?: 0,
                'failed_count' => Cache::lLen($failedKey) ?: 0,
                'timestamp' => time()
            ];
        } catch (\Exception $e) {
            Log::error('[消息队列] 获取队列状态异常', [
                'error' => $e->getMessage()
            ]);
            return [
                'pending_count' => 0,
                'failed_count' => 0,
                'timestamp' => time(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取消息状态
     * @param string $messageId 消息ID
     * @return string|null
     */
    public function getMessageStatus(string $messageId): ?string
    {
        try {
            // 从Redis中获取消息状态
            $statusKey = self::REDIS_QUEUE_PREFIX . self::MESSAGE_STATUS_PREFIX . $messageId;
            $status = Cache::get($statusKey);
            
            if ($status) {
                return $status;
            }
            
            // 如果状态缓存不存在，尝试从队列中查找
            $status = $this->searchMessageInQueues($messageId);
            
            if ($status) {
                // 更新状态缓存，设置1小时过期
                Cache::set($statusKey, $status, 3600);
                return $status;
            }
            
            Log::warning('[消息队列] 未找到消息状态', ['message_id' => $messageId]);
            return null;
        } catch (\Exception $e) {
            $this->logger->error('获取消息状态失败', [
                'message_id' => $messageId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 清理过期消息
     * @param int $expireTime 过期时间（秒）
     * @return bool
     */
    public function cleanExpiredMessages(int $expireTime = 86400): bool
    {
        try {
            $cleanedCount = 0;
            $currentTime = time();
            
            // 清理失败队列中的过期消息
            $cleanedCount += $this->cleanExpiredMessagesFromQueue(self::FAILED_QUEUE, $expireTime, $currentTime);
            
            // 清理待处理队列中的过期消息
            $cleanedCount += $this->cleanExpiredMessagesFromQueue(self::PENDING_QUEUE, $expireTime, $currentTime);
            
            // 清理过期的消息状态缓存
            $this->cleanExpiredMessageStatus($expireTime, $currentTime);
            
            Log::info('[消息队列] 清理过期消息完成', [
                'cleaned_count' => $cleanedCount,
                'expire_time' => $expireTime
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('清理过期消息失败', [
                'expire_time' => $expireTime,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 更新消息状态
     * @param string $messageId 消息ID
     * @param string $status 状态
     * @return bool
     */
    protected function updateMessageStatus(string $messageId, string $status): bool
    {
        try {
            $statusKey = self::REDIS_QUEUE_PREFIX . self::MESSAGE_STATUS_PREFIX . $messageId;
            // 设置状态，1小时过期
            return Cache::set($statusKey, $status, 3600);
        } catch (\Exception $e) {
            Log::error('[消息队列] 更新消息状态失败', [
                'message_id' => $messageId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 在队列中搜索消息状态
     * @param string $messageId 消息ID
     * @return string|null
     */
    protected function searchMessageInQueues(string $messageId): ?string
    {
        try {
            // 搜索待处理队列
            $pendingKey = self::REDIS_QUEUE_PREFIX . self::PENDING_QUEUE;
            $status = $this->searchMessageInQueue($pendingKey, $messageId);
            if ($status) {
                return $status;
            }
            
            // 搜索失败队列
            $failedKey = self::REDIS_QUEUE_PREFIX . self::FAILED_QUEUE;
            $status = $this->searchMessageInQueue($failedKey, $messageId);
            if ($status) {
                return $status;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('[消息队列] 搜索消息状态异常', [
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 在指定队列中搜索消息
     * @param string $queueKey 队列键
     * @param string $messageId 消息ID
     * @return string|null
     */
    protected function searchMessageInQueue(string $queueKey, string $messageId): ?string
    {
        try {
            $queueLength = Cache::lLen($queueKey);
            
            for ($i = 0; $i < $queueLength; $i++) {
                $messageJson = Cache::lIndex($queueKey, $i);
                if (!$messageJson) continue;
                
                $messageData = json_decode($messageJson, true);
                if (!$messageData || ($messageData['id'] ?? '') !== $messageId) {
                    continue;
                }
                
                return $messageData['status'] ?? 'unknown';
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('[消息队列] 队列搜索异常', [
                'queue_key' => $queueKey,
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 清理指定队列中的过期消息
     * @param string $queueName 队列名称
     * @param int $expireTime 过期时间（秒）
     * @param int $currentTime 当前时间戳
     * @return int 清理的消息数量
     */
    protected function cleanExpiredMessagesFromQueue(string $queueName, int $expireTime, int $currentTime): int
    {
        try {
            $queueKey = self::REDIS_QUEUE_PREFIX . $queueName;
            $queueLength = Cache::lLen($queueKey);
            $cleanedCount = 0;
            
            // 从队列尾部开始遍历，避免索引变化问题
            for ($i = $queueLength - 1; $i >= 0; $i--) {
                $messageJson = Cache::lIndex($queueKey, $i);
                if (!$messageJson) continue;
                
                $messageData = json_decode($messageJson, true);
                if (!$messageData) {
                    // 无效消息，直接删除
                    Cache::lRem($queueKey, $messageJson, 1);
                    $cleanedCount++;
                    continue;
                }
                
                $createdAt = $messageData['created_at'] ?? $currentTime;
                $messageAge = $currentTime - $createdAt;
                
                if ($messageAge > $expireTime) {
                    // 消息过期，删除
                    Cache::lRem($queueKey, $messageJson, 1);
                    $cleanedCount++;
                    
                    Log::info('[消息队列] 清理过期消息', [
                        'queue' => $queueName,
                        'message_id' => $messageData['id'] ?? 'unknown',
                        'age' => $messageAge,
                        'expire_time' => $expireTime
                    ]);
                }
            }
            
            return $cleanedCount;
        } catch (\Exception $e) {
            Log::error('[消息队列] 清理队列过期消息异常', [
                'queue' => $queueName,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * 清理过期的消息状态缓存
     * @param int $expireTime 过期时间（秒）
     * @param int $currentTime 当前时间戳
     * @return void
     */
    protected function cleanExpiredMessageStatus(int $expireTime, int $currentTime): void
    {
        try {
            // Redis会自动清理过期的键，这里主要是记录日志
            Log::info('[消息队列] 消息状态缓存将由Redis自动清理', [
                'expire_time' => $expireTime,
                'current_time' => $currentTime
            ]);
        } catch (\Exception $e) {
            Log::error('[消息队列] 清理消息状态缓存异常', [
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取消息详情
     * @param string $messageId 消息ID
     * @return array|null
     */
    public function getMessageDetail(string $messageId): ?array
    {
        try {
            // 先从状态缓存获取基本信息
            $status = $this->getMessageStatus($messageId);
            
            if (!$status) {
                return null;
            }
            
            // 尝试从队列中获取完整消息信息
            $messageData = $this->searchMessageDetailInQueues($messageId);
            
            if ($messageData) {
                return [
                    'id' => $messageId,
                    'status' => $status,
                    'data' => $messageData,
                    'timestamp' => time()
                ];
            }
            
            return [
                'id' => $messageId,
                'status' => $status,
                'timestamp' => time()
            ];
        } catch (\Exception $e) {
            Log::error('[消息队列] 获取消息详情失败', [
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 在队列中搜索消息详情
     * @param string $messageId 消息ID
     * @return array|null
     */
    protected function searchMessageDetailInQueues(string $messageId): ?array
    {
        try {
            // 搜索待处理队列
            $pendingKey = self::REDIS_QUEUE_PREFIX . self::PENDING_QUEUE;
            $messageData = $this->searchMessageDetailInQueue($pendingKey, $messageId);
            if ($messageData) {
                return $messageData;
            }
            
            // 搜索失败队列
            $failedKey = self::REDIS_QUEUE_PREFIX . self::FAILED_QUEUE;
            $messageData = $this->searchMessageDetailInQueue($failedKey, $messageId);
            if ($messageData) {
                return $messageData;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('[消息队列] 搜索消息详情异常', [
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 在指定队列中搜索消息详情
     * @param string $queueKey 队列键
     * @param string $messageId 消息ID
     * @return array|null
     */
    protected function searchMessageDetailInQueue(string $queueKey, string $messageId): ?array
    {
        try {
            $queueLength = Cache::lLen($queueKey);
            
            for ($i = 0; $i < $queueLength; $i++) {
                $messageJson = Cache::lIndex($queueKey, $i);
                if (!$messageJson) continue;
                
                $messageData = json_decode($messageJson, true);
                if (!$messageData || ($messageData['id'] ?? '') !== $messageId) {
                    continue;
                }
                
                return $messageData;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('[消息队列] 队列详情搜索异常', [
                'queue_key' => $queueKey,
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
}