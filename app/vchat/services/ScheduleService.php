<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\vchat\core\MessageProtocol;
use app\vchat\utils\Log;
use app\vchat\utils\Logger;
use think\facade\Config;
use think\swoole\Websocket;
/**
 * 定时任务服务
 * 用于处理会话超时检查、队列清理等定时任务
 */
class ScheduleService
{
    /**
     * @var SessionTimeoutService
     */
    protected $timeoutService;

    /**
     * @var QueueService
     */
    protected $queueService;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var array 配置信息
     */
    protected $config;

    /**
     * @var bool 运行状态
     */
    protected $isRunning = false;

    /**
     * @var array 定时器ID
     */
    protected $timers = [];

    /**
     * @var Websocket Swoole Server 实例
     */
    protected $swooleServer;

    /**
     * 构造方法
     * @param Websocket|null $server Swoole Server 实例
     */
    public function __construct(Websocket $server)
    {
        $this->timeoutService = new SessionTimeoutService();
        $this->queueService = new QueueService();
        $this->logger = new Logger();
        $this->config = Config::get('vchat', []);
        
        // 如果没有传入实例，则从容器获取
        $this->swooleServer = $server;
    }

    /**
     * 启动定时任务
     */
    public function start(): bool
    {
        if ($this->isRunning) {
            return false;
        }

        Log::info('定时任务服务启动');

        // 启动会话超时检查任务
        $this->startSessionTimeoutCheck();

        // 启动队列清理任务
        $this->startQueueCleanup();

        // 启动队列状态更新任务
        $this->startQueueStatusUpdate();

        // 启动自动分配排队用户任务
        $this->startAutoAssignQueuedUsers();

        // 启动会话时间检测任务
        $this->startSessionTimeCheck();

        $this->isRunning = true;
        return true;

    }

    /**
     * 启动会话超时检查任务
     */
    protected function startSessionTimeoutCheck(): void
    {
        $interval = $this->config['session_timeout']['check_interval'] ?? 60;

        $timerId = \Swoole\Timer::tick($interval * 1000, function() {
            try {
                $this->checkSessionTimeouts();
            } catch (\Exception $e) {
                Log::info('[会话超时检查失败] :' . json_encode([
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]));
            }
        });
        
        $this->timers['session_timeout'] = $timerId;
        Log::info('[会话超时检查任务已启动] :' . json_encode(['interval' => $interval, 'timer_id' => $timerId]));
    }

    /**
     * 启动队列清理任务
     */
    protected function startQueueCleanup(): void
    {
        $interval = $this->config['queue']['cleanup_interval'] ?? 300; // 默认5分钟清理一次

        $timerId = \Swoole\Timer::tick($interval * 1000, function() {
            try {
                $this->cleanupQueue();
            } catch (\Exception $e) {
                Log::info('[队列清理失败] :' . json_encode([
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]));
            }
        });
        
        $this->timers['queue_cleanup'] = $timerId;
        Log::info('[队列清理任务已启动] :' . json_encode(['interval' => $interval, 'timer_id' => $timerId]));
    }

    /**
     * 启动队列状态更新任务
     */
    protected function startQueueStatusUpdate(): void
    {
        $interval = $this->config['queue']['status_update_interval'] ?? 30; // 默认30秒更新一次

        $timerId = \Swoole\Timer::tick($interval * 1000, function() {
            try {
                $this->updateQueueStatus();
            } catch (\Exception $e) {
                Log::info('[队列状态更新失败] :' . json_encode([
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]));
            }
        });

        $this->timers['queue_status'] = $timerId;
        Log::info('[队列状态更新任务已启动] :' . json_encode(['interval' => $interval, 'timer_id' => $timerId]));
    }

    /**
     * 启动自动分配排队用户任务
     */
    protected function startAutoAssignQueuedUsers(): void
    {
        $interval = $this->config['queue']['auto_assign_interval'] ?? 30; // 默认30秒检查一次

        $timerId = \Swoole\Timer::tick($interval * 1000, function() {
            try {
                $this->autoAssignQueuedUsers();
            } catch (\Exception $e) {
                Log::info('[自动分配排队用户失败] :' . json_encode([
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]));
            }
        });

        $this->timers['auto_assign'] = $timerId;
        Log::info('[自动分配排队用户任务已启动] :' . json_encode(['interval' => $interval, 'timer_id' => $timerId]));
    }

    /**
     * 检查会话超时
     */
    protected function checkSessionTimeouts(): void
    {
        $this->timeoutService->checkAllSessionsTimeout();
    }

    /**
     * 处理超时会话
     * @param array $session
     */
    protected function handleTimeoutSession(array $session): void
    {
        try {
            // 发送超时通知
            $this->timeoutService->sendTimeoutNotification($session['user_id'], $session['service_id']);
            
            // 处理会话超时
            $this->timeoutService->handleSessionTimeout($session['user_id'], $session['service_id']);
            
            Log::info('[处理超时会话完成] :' . json_encode([
                'user_id' => $session['user_id'],
                'service_id' => $session['service_id']
            ]));
        } catch (\Exception $e) {
            Log::error('[处理超时会话失败] :' . json_encode([
                'session' => $session,
                'error' => $e->getMessage()
            ]));
        }
    }

    /**
     * 清理队列
     */
    protected function cleanupQueue(): void
    {
        // 清理过期的队列记录
        $maxWaitTime = $this->config['queue']['max_wait_time'] ?? 1800; // 默认30分钟
        $expiredUsers = $this->queueService->getExpiredUsers($maxWaitTime);

        $queueModel = new \app\model\QueueModel();

        $userIdMap = [];
        
        foreach ($expiredUsers as $userId) {
            $this->queueService->removeFromQueue($userId);
            $this->sendSocketIoEventMessage($userId,
                MessageProtocol::MESSAGE_QUEUE_TIMEOUT,
                'user',
                [
                    'success' => true,
                    'user_id' => $userId,
                    'message' => '您的排队已超时，请重新排队',
                    'maxWaitTime' => $maxWaitTime
                ]
            );
            $userIdMap[] = $userId;
            Log::info('[清理过期排队用户] :' . json_encode(['user_id' => $userId]));
        }
        
        // 清理无效的队列数据
        $this->queueService->cleanupInvalidData();

        $queueModel->checkTimeout($userIdMap);

        Log::info('[队列清理完成] :' . json_encode([
            'expired_users' => count($expiredUsers)
        ]));
    }

    /**
     * 更新队列状态
     */
    protected function updateQueueStatus(): void
    {
        // 更新所有排队用户的等待时间
        $queueInfo = $this->queueService->getQueueInfo();
        
        foreach ($queueInfo['users'] as $user) {
            $waitTime = $this->queueService->calculateWaitTime($user['service_id'], $user['priority']);
            $this->queueService->updateUserWaitTime($user['user_id'], $waitTime);
        }

        $this->queueService->updateQueueStatus();

        Log::info('[队列状态更新完成] :' . json_encode([
            'queue_length' => $queueInfo['length'],
            'estimated_wait' => $queueInfo['estimated_wait_time']
        ]));
    }

    /**
     * 自动分配排队用户
     */
    protected function autoAssignQueuedUsers(): void
    {
        try {
            // 获取所有在线且空闲的客服
            $customerServiceModel = new \app\model\CustomerService();
            $availableServices = $customerServiceModel->getAvailableServices();
            
            if (empty($availableServices)) {
                return;
            }

            $autoAssignService = new AutoAssignService();
            // 从容器中获取 Swoole Server 实例，并创建 FdManagerService 实例
            $server = $this->swooleServer;

            // 为每个空闲客服调用 AutoAssignService 的新方法
            foreach ($availableServices as $service) {
                if ($customerServiceModel->canAcceptNewSession($service['id'])) {
                    $autoAssignService->tryAssignOnServiceAvailable($server, $service['id']);
                }
            }

            Log::info('[自动分配排队用户处理完成] :' . json_encode([
                'available_services' => count($availableServices)
            ]));
        } catch (\Exception $e) {
            Log::error('[自动分配排队用户处理失败] :' . json_encode([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]));
        }
    }

    /**
     * 发送 Socket.IO 事件消息给指定用户
     * @param int $userId 目标用户ID
     * @param string $event Socket.IO 事件名称
     * @param string $type fd type类型 [user,service]
     * @param array $data 事件携带的数据
     */
    protected function sendSocketIoEventMessage(int $userId, string $event, string $type = '', array $data = []): void
    {
        try {
            // 确保类型不为空，默认为 'user'
            $userType = !empty($type) ? $type : 'user';
            
            Log::info('[准备发送 Socket.IO 事件消息] :' . json_encode([
                'user_id' => $userId,
                'event' => $event,
                'type' => $userType,
                'data' => $data
            ]));

            // 使用Think-Swoole的Websocket工具类
            $websocket = $this->swooleServer;
            $room = app('think\swoole\websocket\Room');

            // 获取用户所在房间的所有客户端
            $roomId = $this->getRoomId($userType, $userId);
            $fds = $room->getClients($roomId);

            if (empty($fds)) {
                Log::info('[Socket.IO 事件消息发送跳过] :' . json_encode([
                    'user_id' => $userId,
                    'room_id' => $roomId,
                    'event' => $event,
                    'type' => $userType,
                    'reason' => '用户不在任何房间中'
                ]));
                return;
            }

            $data['message_type'] = $event;

            foreach ($fds as $fd) {
                try {
                    if ($websocket->setSender($fd)->isEstablished()) {
                        $websocket->to($fd)->emit(MessageProtocol::MESSAGE_NOTICE, $data);
                        Log::info('[Socket.IO 事件消息发送成功] :' . json_encode([
                            'user_id' => $userId,
                            'fd' => $fd,
                            'event' => $event,
                            'room_id' => $roomId
                        ]));
                    }
                } catch (\Throwable $e) {
                    // 在定时任务进程中，无法访问 Server 是正常的
                    if (strpos($e->getMessage(), 'server is not running') !== false) {
                        Log::info('[定时任务进程中跳过消息发送] :' . json_encode([
                            'user_id' => $userId,
                            'fd' => $fd,
                            'event' => $event,
                            'reason' => '定时任务进程无法访问主进程Server'
                        ]));
                    } else {
                        Log::error('[Socket.IO 事件消息发送异常] :' . json_encode([
                            'user_id' => $userId,
                            'fd' => $fd,
                            'event' => $event,
                            'error' => $e->getMessage()
                        ]));
                    }
                }
            }
        } catch (\Throwable $e) {
            Log::error('[Socket.IO 事件消息发送异常] :' . json_encode([
                'user_id' => $userId,
                'event' => $event,
                'type' => $userType ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]));
        }
    }

    /**
     * 获取房间ID
     * @param string $type
     * @param int $id
     * @return string
     */
    protected function getRoomId(string $type, int $id): string
    {
        return $type . '_' . $id;
    }

    /**
     * 停止定时任务
     */
    public function stop(): void
    {
        if (!$this->isRunning) {
            return;
        }
        
        $this->isRunning = false;
        
        // 清理所有定时器
        foreach ($this->timers as $name => $timerId) {
            if (swoole_timer_exists($timerId)) {
                swoole_timer_clear($timerId);
                Log::info('[定时器已清理] :' . json_encode(['name' => $name, 'timer_id' => $timerId]));
            }
        }
        
        $this->timers = [];
        Log::info('定时任务服务已停止');
    }

    /**
     * 启动会话时间检测任务
     */
    protected function startSessionTimeCheck(): void
    {
        $interval = $this->config['session_timeout']['time_check_interval'] ?? 30; // 默认30秒检查一次

        $timerId = \Swoole\Timer::tick($interval * 1000, function() {
            try {
                $this->checkSessionTime();
            } catch (\Exception $e) {
                Log::info('[会话时间检测失败] :' . json_encode([
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]));
            }
        });
        
        $this->timers['session_time_check'] = $timerId;
        Log::info('[会话时间检测任务已启动] :' . json_encode(['interval' => $interval, 'timer_id' => $timerId]));
    }

    /**
     * 检测会话时间并关闭超时连接
     */
    protected function checkSessionTime(): void
    {
        try {
            // 获取所有活跃会话
            $sessionModel = new \app\model\ChatSession();
            $activeSessions = $sessionModel
                ->where('status', 1) // 1表示进行中
                ->field('id,user_id,service_id')
                ->select()
                ->toArray();
            
            $closedCount = 0;

            foreach ($activeSessions as $session) {
                $timeInfo = $this->timeoutService->getSessionTimeInfo($session['id']);
                
                // 检查是否有任何一个时间为0
                if ($timeInfo['active'] && (
                    $timeInfo['remaining_duration'] === 0 ||
                    $timeInfo['user_inactive_time'] >= $this->config['session_timeout']['user_inactive_timeout'] ||
                    $timeInfo['service_inactive_time'] >= $this->config['session_timeout']['service_inactive_timeout']
                )) {
                    // 获取用户的fd

                    try {
                        $this->swooleServer->close();
                        Log::info('[关闭超时用户连接] :' . json_encode([
                                'session_id' => $session['id'],
                                'user_id' => $session['user_id'],
                                'fd' => $this->swooleServer->getSender(),
                                'reason' => '会话时间检测超时'
                            ]));
                        $closedCount++;
                    } catch (\Throwable $e) {
                        Log::error('[关闭超时用户连接失败] :' . json_encode([
                                'session_id' => $session['id'],
                                'user_id' => $session['user_id'],
                                'fd' => $this->swooleServer->getSender(),
                                'error' => $e->getMessage()
                            ]));
                    }
                    
                    // 处理会话超时
                    $reason = 'time_check_timeout';
                    if ($timeInfo['remaining_duration'] === 0) {
                        $reason = 'max_duration_exceeded';
                    } elseif ($timeInfo['user_inactive_time'] >= $this->config['session_timeout']['user_inactive_timeout']) {
                        $reason = 'user_inactive_timeout';
                    } elseif ($timeInfo['service_inactive_time'] >= $this->config['session_timeout']['service_inactive_timeout']) {
                        $reason = 'service_inactive_timeout';
                    }
                    
                    $this->timeoutService->handleSessionTimeout($session['id'], $reason);
                }
            }
            
            Log::info('[会话时间检测完成] :' . json_encode([
                'total_sessions' => count($activeSessions),
                'closed_connections' => $closedCount
            ]));
        } catch (\Exception $e) {
            Log::error('[会话时间检测异常] :' . json_encode([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]));
        }
    }

    /**
     * 获取任务状态
     * @return array
     */
    public function getStatus(): array
    {
        return [
            'session_timeout_check' => [
                'enabled' => true,
                'interval' => $this->config['session_timeout']['check_interval'] ?? 60
            ],
            'queue_cleanup' => [
                'enabled' => true,
                'interval' => $this->config['queue']['cleanup_interval'] ?? 300
            ],
            'queue_status_update' => [
                'enabled' => true,
                'interval' => $this->config['queue']['status_update_interval'] ?? 30
            ],
            'auto_assign_queued_users' => [
                'enabled' => true,
                'interval' => $this->config['queue']['auto_assign_interval'] ?? 30
            ],
            'session_time_check' => [
                'enabled' => true,
                'interval' => $this->config['session_timeout']['time_check_interval'] ?? 30
            ]
        ];
    }
}