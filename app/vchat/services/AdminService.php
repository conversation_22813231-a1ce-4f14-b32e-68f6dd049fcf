<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\vchat\utils\Logger;
use think\facade\Config;
use think\facade\Cache;

/**
 * 管理控制台服务
 * 提供队列管理、会话监控、系统统计等功能
 */
class AdminService
{
    /**
     * @var QueueService
     */
    protected $queueService;

    /**
     * @var SessionTimeoutService
     */
    protected $timeoutService;

    /**
     * @var SessionService
     */
    protected $sessionService;

    /**
     * @var CustomerService
     */
    protected $customerService;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var array 配置信息
     */
    protected $config;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->queueService = new QueueService();
        $this->timeoutService = new SessionTimeoutService();
        $this->sessionService = new SessionService();
        $this->customerService = new CustomerService();
        $this->logger = new Logger();
        $this->config = Config::get('vchat', []);
    }

    /**
     * 获取系统概览
     * @return array
     */
    public function getSystemOverview(): array
    {
        try {
            $queueInfo = $this->queueService->getQueueInfo();
            $activeSessions = $this->getActiveSessionsCount();
            $onlineServices = $this->getOnlineServicesCount();
            //今日排队失败量
            $queueFailedCount = $this->getTodayQueueFailedCount();
            //今日实际接入率
            $queueAccessRate = $this->getTodayQueueAccessRate();
            //今日排队总数
            $queueTotal = $this->getTodayQueueTotal();
            //今日平均排队时长(秒)
            $averageQueueTime = $this->getTodayAverageQueueTime();
            
            return [
                'queue' => [
                    'length' => $queueInfo['length'],
                    'queue_total' => $queueTotal,
                    'estimated_wait_time' => $queueInfo['estimated_wait_time'],
                    'failed_count' => $queueFailedCount,
                    'access_rate' => $queueAccessRate,
                    'average_queue_time' => $averageQueueTime,
                    'max_queue_size' => $this->config['queue']['max_queue_size'] ?? 100
                ],
                'sessions' => [
                    'active_count' => $activeSessions,
                    'timeout_threshold' => $this->config['timeout']['session_timeout'] ?? 1800
                ],
                'services' => [
                    'online_count' => $onlineServices,
                    'total_capacity' => $this->getTotalServiceCapacity()
                ],
                'system' => [
                    'uptime' => $this->getSystemUptime(),
                    'last_update' => date('Y-m-d H:i:s')
                ]
            ];
        } catch (\Exception $e) {
            $this->logger->error('获取系统概览失败', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取队列详细信息
     * @return array
     */
    public function getQueueDetails(): array
    {
        try {
            $queueInfo = $this->queueService->getQueueInfo();
            $users = [];
            
            foreach ($queueInfo['users'] as $user) {
                if (!isset($user['position'])) {
                    continue;
                }
                $waitTime = $this->queueService->calculateWaitTime($user['user_id'], $user['position']);
                $users[] = [
                    'user_id' => $user['user_id'],
                    'service_id' => $user['service_id'],
                    'join_time' => $user['join_time'],
                    'wait_time' => $waitTime,
                    'position' => $user['position'] ?? 0,
                    'estimated_service_time' => $this->calculateEstimatedServiceTime($user['position'] ?? 0)
                ];
            }
            
            return [
                'total_count' => $queueInfo['length'],
                'users' => $users,
                'average_wait_time' => $this->calculateAverageWaitTime(),
                'peak_queue_size_today' => $this->getPeakQueueSizeToday()
            ];
        } catch (\Exception $e) {
            $this->logger->error('获取队列详细信息失败', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取会话监控信息
     * @return array
     */
    public function getSessionMonitoring(array $params = []): array
    {
        try {
            $activeSessions = $this->getActiveSessionsList($params);
            $timeoutSessions = $this->getTimeoutSessionsList();
            
            return [
                'active_sessions' => $activeSessions,
                'timeout_sessions' => $timeoutSessions,
                'session_statistics' => [
                    'total_today' => $this->getTodaySessionsCount(),
                    'average_duration' => $this->getAverageSessionDuration(),
                    'timeout_rate' => $this->getTimeoutRate(),
                    'average_response_time' => $this->getAverageResponseTime(),
                ]
            ];
        } catch (\Exception $e) {
            $this->logger->error('获取会话监控信息失败', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取客服状态
     * @return array
     */
    public function getServiceStatus(): array
    {
        try {
            $customerModel = $this->customerService->getCustomerServiceModel();
            $onlineServices = $customerModel->getOnlineServices();
            
            if (empty($onlineServices)) {
                return [
                    'services' => [],
                    'summary' => [
                        'total_online' => 0,
                        'total_capacity' => 0,
                        'current_load' => 0,
                        'average_utilization' => 0
                    ]
                ];
            }
            
            $serviceIds = array_column($onlineServices, 'id');
            
            // 批量获取所有客服的当前会话数量
            $sessionCounts = $this->getBatchServiceSessionCounts($serviceIds);
            
            // 批量获取所有客服的最后活动时间
            $lastActivities = $this->getBatchServiceLastActivities($serviceIds);

            $services = [];
            foreach ($onlineServices as $service) {
                $serviceId = $service['id'];
                $currentSessions = $sessionCounts[$serviceId] ?? 0;
                
                $services[] = [
                    'id' => $serviceId,
                    'name' => $service['name'] ?? '客服' . $serviceId,
                    'status' => $service['status'],
                    'current_sessions' => $currentSessions,
                    'max_sessions' => $service['max_sessions'],
                    'utilization_rate' => $service['max_sessions'] > 0 ?
                        round(($currentSessions / $service['max_sessions']) * 100, 2) : 0,
                    'last_activity' => $lastActivities[$serviceId] ?? ''
                ];
            }

            return [
                'services' => $services,
                'summary' => [
                    'total_online' => count($onlineServices),
                    'total_capacity' => array_sum(array_column($onlineServices, 'max_sessions')),
                    'current_load' => array_sum(array_column($services, 'current_sessions')),
                    'average_utilization' => $this->calculateAverageUtilization($services)
                ]
            ];
        } catch (\Exception $e) {
            $this->logger->error('获取客服状态失败', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 手动分配用户到客服
     * @param int $userId
     * @param int $serviceId
     * @return bool
     */
    public function manualAssignUser(int $userId, int $serviceId): bool
    {
        try {
            // 检查用户是否在队列中
            if (!$this->queueService->isUserInQueue($userId)) {
                throw new \Exception('用户不在排队队列中');
            }
            
            // 检查客服是否可以接待
            $customerModel = $this->customerService->getCustomerServiceModel();
            if (!$customerModel->canAcceptNewSession($serviceId)) {
                throw new \Exception('客服无法接待新会话');
            }
            
            // 从队列中移除用户
            $this->queueService->removeFromQueue($userId);
            
            // 创建会话
            $sessionData = [
                'user_id' => $userId,
                'service_id' => $serviceId,
                'status' => 1,
                'start_time' => date('Y-m-d H:i:s')
            ];
            
            $session = $this->sessionService->createSession($sessionData['user_id'], $sessionData['service_id']);
            if ($session) {
                $customerModel->increaseCurrentSessions($serviceId);
                
                $this->logger->info('手动分配用户成功', [
                    'user_id' => $userId,
                    'service_id' => $serviceId,
                    'session_id' => $session['id']
                ]);
                
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            $this->logger->error('手动分配用户失败', [
                'user_id' => $userId,
                'service_id' => $serviceId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 强制结束会话
     * @param int $sessionId
     * @return bool
     */
    public function forceEndSession(int $sessionId): bool
    {
        try {
            $session = $this->sessionService->getSessionDetail($sessionId);
            if (!$session) {
                throw new \Exception('会话不存在');
            }
            
            if ($this->sessionService->endSession($sessionId)) {
                // 清理超时记录
                $this->timeoutService->clearActivity($session['user_id'], $session['service_id']);
                
                $this->logger->info('强制结束会话成功', [
                    'session_id' => $sessionId,
                    'user_id' => $session['user_id'],
                    'service_id' => $session['service_id']
                ]);
                
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            $this->logger->error('强制结束会话失败', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 清空队列
     * @return bool
     */
    public function clearQueue(): bool
    {
        try {
            $queueInfo = $this->queueService->getQueueInfo();
            $clearedCount = $queueInfo['length'];
            
            foreach ($queueInfo['users'] as $user) {
                $this->queueService->removeFromQueue($user['user_id']);
            }
            
            $this->logger->info('清空队列成功', [
                'cleared_count' => $clearedCount
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('清空队列失败', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取活跃会话数量
     * @return int
     */
    protected function getActiveSessionsCount(): int
    {
        try {
            $sessionModel = new \app\model\ChatSession();
            return $sessionModel->where('status', 1)->count();
        } catch (\Exception $e) {
            $this->logger->error('获取活跃会话数量失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * 获取在线客服数量
     * @return int
     */
    protected function getOnlineServicesCount(): int
    {
        $customerModel = $this->customerService->getCustomerServiceModel();
        $onlineServices = $customerModel->getOnlineServiceTotal();
        return $onlineServices;
    }

    /**
     * 获取总客服容量
     * @return int
     */
    protected function getTotalServiceCapacity(): int
    {
        $customerModel = $this->customerService->getCustomerServiceModel();
        $onlineServices = $customerModel->getOnlineServices();
        return array_sum(array_column($onlineServices, 'max_sessions'));
    }

    /**
     * 获取系统运行时间
     * @return string
     */
    protected function getSystemUptime(): string
    {
        $startTime = Cache::get('vchat_start_time', time());
        $uptime = time() - $startTime;
        
        $hours = floor($uptime / 3600);
        $minutes = floor(($uptime % 3600) / 60);
        
        return sprintf('%d小时%d分钟', $hours, $minutes);
    }

    /**
     * 计算预计服务时间
     * @param int $position
     * @return int
     */
    protected function calculateEstimatedServiceTime(int $position): int
    {
        $avgServiceTime = $this->config['queue']['avg_service_time'] ?? 300; // 默认5分钟
        return $position * $avgServiceTime;
    }

    /**
     * 计算平均等待时间
     * @return int
     */
    protected function calculateAverageWaitTime(): int
    {
        try {
            $sessionModel = new \app\model\ChatSession();
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');
            
            // 计算今日已完成会话的平均等待时间
            $sessions = $sessionModel->where([
                ['status', '=', 0],
                ['createtime', 'between', [$startTime, $endTime]]
            ])->field('start_time, createtime')->select();
            
            if ($sessions->isEmpty()) {
                return 0;
            }
            
            $totalWaitTime = 0;
            foreach ($sessions as $session) {
                $waitTime = $session['start_time'] - $session['createtime'];
                $totalWaitTime += max(0, $waitTime);
            }
            
            return (int)($totalWaitTime / count($sessions));
        } catch (\Exception $e) {
            $this->logger->error('计算平均等待时间失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * 获取今日队列峰值
     * @return int
     */
    protected function getPeakQueueSizeToday(): int
    {
        $cacheKey = 'vchat_peak_queue_' . date('Y-m-d');
        return (int)Cache::get($cacheKey, 0);
    }

    /**
     * 获取今日客服统计列表
     * @return array
     */
    protected function getActiveSessionsList(array $params): array
    {
        try {
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');
            
            $sessionModel = new \app\model\ChatSession();
            $messageModel = new \app\model\ChatMessage();
            $customerServiceModel = new \app\model\CustomerService();
            
            // 使用关联查询一次性获取所有需要的数据
            $query = $customerServiceModel->alias('cs')
                ->join('ad_chat_session s', 's.service_id = cs.id')
                ->where([
                    ['s.createtime', 'between', [$startTime, $endTime]]
                ]);

            if (isset($params['name']) && $params['name']) {
                $query->where('cs.name', '=', $params['name']);
            }

            if (isset($params['status']) && $params['status']) {
                $query->where('cs.status', '=', $params['status']);
            }

            $services = $query->field('
                cs.id as service_id,
                cs.name as service_name,
                cs.avatar as service_avatar,
                cs.work_id,
                cs.status as service_status,
                cs.max_sessions,
                COUNT(DISTINCT s.id) as today_total_sessions,
                SUM(CASE WHEN s.status = 1 THEN 1 ELSE 0 END) as current_active_sessions
            ')
            ->group('cs.id')
            ->select();

            if (empty($services)) {
                return [];
            }

            // 获取每个客服的消息统计
            $messageStats = $messageModel->alias('m')
                ->join('ad_chat_session s', 's.id = m.session_id')
                ->where([
                    ['m.from_type', '=', 'service'],
                    ['m.createtime', 'between', [$startTime, $endTime]]
                ])
                ->field('
                    s.service_id,
                    COUNT(m.id) as today_message_count,
                    AVG(m.response_time) as avg_response_time
                ')
                ->group('s.service_id')
                ->select()
                ->column(null, 'service_id');

            // 获取每个客服的会话时长统计
            $durationStats = $sessionModel->alias('s')
                ->where([
                    ['s.status', '=', 0],
                    ['s.duration', '>', 0],
                    ['s.createtime', 'between', [$startTime, $endTime]]
                ])
                ->field('
                    service_id,
                    AVG(duration) as avg_duration
                ')
                ->group('service_id')
                ->select()
                ->column(null, 'service_id');

            $result = [];
            foreach ($services as $service) {
                $serviceId = $service['service_id'];
                $result[] = [
                    'service_id' => $serviceId,
                    'service_name' => $service['service_name'] ?? '客服' . $serviceId,
                    'service_avatar' => $service['service_avatar'] ?? '',
                    'work_id' => $service['work_id'] ?? '',
                    'service_status' => $service['service_status'] ?? 0,
                    'max_sessions' => $service['max_sessions'] ?? 0,
                    'today_total_sessions' => $service['today_total_sessions'],
                    'current_active_sessions' => $service['current_active_sessions'],
                    'today_message_count' => $messageStats[$serviceId]['today_message_count'] ?? 0,
                    // 'avg_session_duration' => $durationStats[$serviceId]['avg_duration'] ? round($durationStats[$serviceId]['avg_duration'], 2) : 0,
                    'avg_first_response_time' => $this->calculateAvgFirstResponseTime($serviceId, $startTime, $endTime),
                    'avg_session_duration' => is_numeric($durationStats[$serviceId]['avg_duration']) 
    ? round((float)$durationStats[$serviceId]['avg_duration'], 2) 
    : 0,
'avg_response_time' => is_numeric($messageStats[$serviceId]['avg_response_time']) 
    ? round((float)$messageStats[$serviceId]['avg_response_time'], 2) 
    : 0,
                    'last_activity' => $this->getServiceLastActivity($serviceId)
                ];
            }

            // 按今日总会话数排序
            usort($result, function($a, $b) {
                return $b['today_total_sessions'] - $a['today_total_sessions'];
            });
            
            return $result;
        } catch (\Exception $e) {
            $this->logger->error('获取客服统计列表失败', ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 计算平均首次响应时间
     * @param int $serviceId 客服ID
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     * @return float
     */
    protected function calculateAvgFirstResponseTime(int $serviceId, int $startTime, int $endTime): float
    {
        try {
            $sessionModel = new \app\model\ChatSession();
            $messageModel = new \app\model\ChatMessage();
            
            // 获取今日该客服的所有会话
            $sessions = $sessionModel->where([
                ['service_id', '=', $serviceId],
                ['createtime', 'between', [$startTime, $endTime]]
            ])->column('id');
            
            if (empty($sessions)) {
                return 0;
            }
            
            $totalFirstResponseTime = 0;
            $validSessionCount = 0;
            
            foreach ($sessions as $sessionId) {
                // 获取用户在该会话中的第一条消息
                $firstUserMessage = $messageModel->where([
                    ['session_id', '=', $sessionId],
                    ['from_type', '=', 'user']
                ])->order('id', 'asc')->find();
                
                if (!$firstUserMessage) {
                    continue;
                }
                
                // 获取客服在该会话中的第一条回复消息
                $firstServiceMessage = $messageModel->where([
                    ['session_id', '=', $sessionId],
                    ['from_id', '=', $serviceId],
                    ['from_type', '=', 'service'],
                    ['id', '>', $firstUserMessage['id']]
                ])->order('id', 'asc')->find();
                
                if ($firstServiceMessage) {
                    $userMessageTime = is_numeric($firstUserMessage['createtime']) 
                        ? $firstUserMessage['createtime'] 
                        : strtotime($firstUserMessage['createtime']);
                    $serviceMessageTime = is_numeric($firstServiceMessage['createtime']) 
                        ? $firstServiceMessage['createtime'] 
                        : strtotime($firstServiceMessage['createtime']);
                    
                    $responseTime = $serviceMessageTime - $userMessageTime;
                    if ($responseTime > 0) {
                        $totalFirstResponseTime += $responseTime;
                        $validSessionCount++;
                    }
                }
            }
            
            return $validSessionCount > 0 ? round($totalFirstResponseTime / $validSessionCount, 2) : 0;
        } catch (\Exception $e) {
            $this->logger->error('计算平均首次响应时间失败', [
                'service_id' => $serviceId,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 获取超时会话列表
     * @return array
     */
    protected function getTimeoutSessionsList(): array
    {
        return $this->timeoutService->checkAllSessionsTimeout();
    }

    /**
     * 获取今日会话数量
     * @return int
     */
    protected function getTodaySessionsCount(): int
    {
        try {
            $sessionModel = new \app\model\ChatSession();
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');
            
            return $sessionModel->where([
                ['createtime', 'between', [$startTime, $endTime]]
            ])->count();
        } catch (\Exception $e) {
            $this->logger->error('获取今日会话数量失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * 获取平均会话时长
     * @return int
     */
    protected function getAverageSessionDuration(): int
    {
        try {
            $sessionModel = new \app\model\ChatSession();
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');
            
            $avgDuration = $sessionModel->where([
                ['status', '=', 0], // 已结束的会话
                ['duration', '>', 0],
                ['createtime', 'between', [$startTime, $endTime]]
            ])->avg('duration');
            
            return (int)($avgDuration ?? 0);
        } catch (\Exception $e) {
            $this->logger->error('获取平均会话时长失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * 获取今日会话的平均响应时长(秒)
     * @return int
     */
    protected function getAverageResponseTime(): int
    {
        try {
            $messageModel = new \app\model\ChatMessage();
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');

            // 获取所有客服回复消息及其响应时间
            $responseTimes = $messageModel->where([
                ['from_type', '=', 'service'],
                ['createtime', 'between', [$startTime, $endTime]],
                ['response_time', '>', 0]
            ])->column('response_time');

            if (empty($responseTimes)) {
                return 0;
            }

            // 计算平均响应时间(秒)
            $average = array_sum($responseTimes) / count($responseTimes);

            return (int)round($average);
        } catch (\Exception $e) {
            $this->logger->error('计算平均响应时间失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }

// ... existing code ...

    /**
     * 获取超时率
     * @return float
     */
    protected function getTimeoutRate(): float
    {
        try {
            $sessionModel = new \app\model\ChatSession();
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');
            
            $totalSessions = $sessionModel->where([
                ['createtime', 'between', [$startTime, $endTime]]
            ])->count();
            
            if ($totalSessions == 0) {
                return 0.0;
            }
            
            // 计算超时会话数（假设超过30分钟无消息为超时）
            $timeoutThreshold = $this->config['timeout']['session_timeout'] ?? 1800;
            $currentTime = time();
            
            $timeoutSessions = $sessionModel->where([
                ['status', '=', 1], // 活跃会话
                ['last_message_time', '<', $currentTime - $timeoutThreshold],
                ['createtime', 'between', [$startTime, $endTime]]
            ])->count();
            
            return round(($timeoutSessions / $totalSessions) * 100, 2);
        } catch (\Exception $e) {
            $this->logger->error('获取超时率失败', ['error' => $e->getMessage()]);
            return 0.0;
        }
    }

    /**
     * 批量获取客服当前会话数量
     * @param array $serviceIds
     * @return array
     */
    protected function getBatchServiceSessionCounts(array $serviceIds): array
    {
        try {
            if (empty($serviceIds)) {
                return [];
            }
            
            $sessionModel = new \app\model\ChatSession();
            
            // 批量查询所有客服的活跃会话数量
            $sessionCounts = $sessionModel->where([
                ['service_id', 'in', $serviceIds],
                ['status', '=', 1] // 活跃状态
            ])->field('service_id, COUNT(*) as session_count')
              ->group('service_id')
              ->select()
              ->column('session_count', 'service_id');
            
            // 确保所有客服ID都有对应的数量（没有会话的客服返回0）
            $result = [];
            foreach ($serviceIds as $serviceId) {
                $result[$serviceId] = (int)($sessionCounts[$serviceId] ?? 0);
            }
            
            return $result;
        } catch (\Exception $e) {
            $this->logger->error('批量获取客服会话数量失败', [
                'service_ids' => $serviceIds,
                'error' => $e->getMessage()
            ]);
            return array_fill_keys($serviceIds, 0);
        }
    }
    
    /**
     * 批量获取客服最后活动时间
     * @param array $serviceIds
     * @return array
     */
    protected function getBatchServiceLastActivities(array $serviceIds): array
    {
        try {
            if (empty($serviceIds)) {
                return [];
            }
            
            $messageModel = new \app\model\ChatMessage();
            
            // 批量获取每个客服最后发送的消息时间
            $lastMessages = $messageModel->where([
                ['from_id', 'in', $serviceIds],
                ['from_type', '=', 'service']
            ])->field('from_id as service_id, MAX(createtime) as last_message_time')
              ->group('from_id')
              ->select()
              ->column('last_message_time', 'service_id');
            
            $result = [];
            
            foreach ($serviceIds as $serviceId) {
                if (isset($lastMessages[$serviceId]) && $lastMessages[$serviceId]) {
                    $createTime = is_numeric($lastMessages[$serviceId]) 
                        ? $lastMessages[$serviceId] 
                        : strtotime($lastMessages[$serviceId]);
                    $result[$serviceId] = date('Y-m-d H:i:s', $createTime);
                } else {
                    // 如果没有消息记录，尝试获取客服的更新时间
                    $result[$serviceId] = $this->getServiceUpdateTime($serviceId);
                }
            }
            
            return $result;
        } catch (\Exception $e) {
            $this->logger->error('批量获取客服最后活动时间失败', [
                'service_ids' => $serviceIds,
                'error' => $e->getMessage()
            ]);
            return array_fill_keys($serviceIds, '');
        }
    }
    
    /**
     * 获取客服更新时间
     * @param int $serviceId
     * @return string
     */
    protected function getServiceUpdateTime(int $serviceId): string
    {
        try {
            $serviceModel = new \app\model\CustomerService();
            $service = $serviceModel->find($serviceId);
            
            if ($service && isset($service['updatetime'])) {
                $updateTime = is_numeric($service['updatetime']) 
                    ? $service['updatetime'] 
                    : strtotime($service['updatetime']);
                return date('Y-m-d H:i:s', $updateTime);
            }
            
            return '';
        } catch (\Exception $e) {
            $this->logger->error('获取客服更新时间失败', [
                'service_id' => $serviceId,
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * 获取客服最后活动时间（保留原方法以兼容其他调用）
     * @param int $serviceId
     * @return string
     */
    protected function getServiceLastActivity(int $serviceId): string
    {
        $activities = $this->getBatchServiceLastActivities([$serviceId]);
        return $activities[$serviceId] ?? '';
    }

    /**
     * 计算平均利用率
     * @param array $services
     * @return float
     */
    protected function calculateAverageUtilization(array $services): float
    {
        if (empty($services)) {
            return 0.0;
        }
        
        $totalUtilization = array_sum(array_column($services, 'utilization_rate'));
        return round($totalUtilization / count($services), 2);
    }

    /**
     * 获取当天按小时分组的统计数据
     * @param string $date 日期，格式：Y-m-d，默认为今天
     * @return array
     */
    public function getHourlyStats(string $date = ''): array
    {
        try {
            if (empty($date)) {
                $date = date('Y-m-d');
            }
            
            $startTime = strtotime($date . ' 00:00:00');
            $endTime = strtotime($date . ' 23:59:59');
            
            // 初始化按统计指标分组的数据结构
            $result = [
                'total_visits' => [
                    'name' => '总来访量',
                    'data' => array_fill(0, 24, 0)
                ],
                'queue_count' => [
                    'name' => '排队量',
                    'data' => array_fill(0, 24, 0)
                ],
                'total_sessions' => [
                    'name' => '总会话量',
                    'data' => array_fill(0, 24, 0)
                ],
                'connected_sessions' => [
                    'name' => '已接入会话量',
                    'data' => array_fill(0, 24, 0)
                ],
                'unconnected_sessions' => [
                    'name' => '未接入会话量',
                    'data' => array_fill(0, 24, 0)
                ],
                'service_initiated_sessions' => [
                    'name' => '客服发起会话量',
                    'data' => array_fill(0, 24, 0)
                ]
            ];
            
            // 获取会话统计数据
            $this->getHourlySessionStatsGrouped($result, $startTime, $endTime);
            
            // 获取队列统计数据
            $this->getHourlyQueueStatsGrouped($result, $startTime, $endTime);
            
            // 获取来访统计数据
            $this->getHourlyVisitStatsGrouped($result, $startTime, $endTime);
            
            return $result;
        } catch (\Exception $e) {
            $this->logger->error('获取按小时统计数据失败', [
                'date' => $date,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取按小时分组的会话统计（按统计指标分组）
     * @param array &$result 结果数组引用
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     */
    protected function getHourlySessionStatsGrouped(array &$result, int $startTime, int $endTime): void
    {
        try {
            $sessionModel = new \app\model\ChatSession();
            $messageModel = new \app\model\ChatMessage();
            
            // 获取当天所有会话
            $sessions = $sessionModel->where([
                ['createtime', 'between', [$startTime, $endTime]]
            ])->field('id,user_id,service_id,status,start_time,createtime,source')->select();
            
            foreach ($sessions as $session) {
                $createTime = is_numeric($session['createtime']) 
                    ? $session['createtime'] 
                    : strtotime($session['createtime']);
                    
                $hour = (int)date('H', $createTime);
                
                // 总会话量
                $result['total_sessions']['data'][$hour]++;
                
                // 已接入会话量（状态为1的活跃会话或状态为0的已结束会话）
                if ($session['status'] == 1 || $session['status'] == 0) {
                    $result['connected_sessions']['data'][$hour]++;
                } else {
                    // 未接入会话量
                    $result['unconnected_sessions']['data'][$hour]++;
                }
                
                // 检查是否为客服发起的会话
                $firstMessage = $messageModel->where([
                    ['session_id', '=', $session['id']]
                ])->order('id', 'asc')->find();
                
                if ($firstMessage && $firstMessage['from_type'] == 'service') {
                    $result['service_initiated_sessions']['data'][$hour]++;
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('获取按小时会话统计失败', ['error' => $e->getMessage()]);
        }
    }
    
    /**
     * 获取按小时分组的队列统计（按统计指标分组）
     * @param array &$result 结果数组引用
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     */
    protected function getHourlyQueueStatsGrouped(array &$result, int $startTime, int $endTime): void
    {
        try {
            $queueModel = new \app\model\QueueModel();
            
            // 统计今日各小时的排队数据
            $queueRecords = $queueModel->where([
                ['join_time', 'between', [$startTime, $endTime]]
            ])->field('join_time, status, assign_time, timeout_time')->select();
            
            // 初始化统计数据
            $hourlyQueueStats = [];
            $hourlyFailedStats = [];
            $hourlySuccessStats = [];
            
            for ($i = 0; $i < 24; $i++) {
                $hourlyQueueStats[$i] = 0;
                $hourlyFailedStats[$i] = 0;
                $hourlySuccessStats[$i] = 0;
            }
            
            foreach ($queueRecords as $record) {
                $joinHour = (int)date('H', $record['join_time']);
                
                // 统计加入队列数量
                $hourlyQueueStats[$joinHour]++;
                
                // 统计排队失败量（超时或取消）
                if (in_array($record['status'], [3, 4])) { // 3-已超时, 4-已取消
                    $hourlyFailedStats[$joinHour]++;
                }
                
                // 统计成功分配量
                if ($record['status'] == 2) { // 2-已分配
                    $hourlySuccessStats[$joinHour]++;
                }
            }
            
            // 计算实际接入率
            $hourlyAccessRate = [];
            for ($i = 0; $i < 24; $i++) {
                if ($hourlyQueueStats[$i] > 0) {
                    $hourlyAccessRate[$i] = round(($hourlySuccessStats[$i] / $hourlyQueueStats[$i]) * 100, 2);
                } else {
                    $hourlyAccessRate[$i] = 0;
                }
            }
            
            // 填充结果数组
            $result['queue_count']['data'] = array_values($hourlyQueueStats);
            
            // 添加新的统计指标
            if (!isset($result['queue_failed_count'])) {
                $result['queue_failed_count'] = [
                    'name' => '排队失败量',
                    'data' => array_values($hourlyFailedStats)
                ];
            }
            
            if (!isset($result['queue_access_rate'])) {
                $result['queue_access_rate'] = [
                    'name' => '实际接入率(%)',
                    'data' => array_values($hourlyAccessRate)
                ];
            }
            
        } catch (\Exception $e) {
            $this->logger->error('获取按小时队列统计失败', ['error' => $e->getMessage()]);
        }
    }
    
    /**
     * 获取按小时分组的来访统计（按统计指标分组）
     * @param array &$result 结果数组引用
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     */
    protected function getHourlyVisitStatsGrouped(array &$result, int $startTime, int $endTime): void
    {
        try {
            // 来访量可以从用户访问日志、会话创建记录或其他访问统计表中获取
            // 这里以会话创建作为来访的指标
            
            $sessionModel = new \app\model\ChatSession();
            
            // 按小时统计独立用户访问量
            $visits = $sessionModel->where([
                ['createtime', 'between', [$startTime, $endTime]]
            ])->field('user_id,createtime')
              ->group('user_id,HOUR(FROM_UNIXTIME(createtime))')
              ->select();
            
            foreach ($visits as $visit) {
                $createTime = is_numeric($visit['createtime']) 
                    ? $visit['createtime'] 
                    : strtotime($visit['createtime']);
                    
                $hour = (int)date('H', $createTime);
                $result['total_visits']['data'][$hour]++;
            }
        } catch (\Exception $e) {
            $this->logger->error('获取按小时来访统计失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 获取今日排队失败量
     * @return int
     */
    public function getTodayQueueFailedCount(): int
    {
        try {
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');
            
            $queueModel = new \app\model\QueueModel();
            
            // 统计今日排队失败的数量（超时或取消）
            $failedCount = $queueModel->where([
                ['join_time', 'between', [$startTime, $endTime]],
                ['status', 'in', [3, 4]] // 3-已超时, 4-已取消
            ])->count();
            
            return $failedCount;
        } catch (\Exception $e) {
            $this->logger->error('获取今日排队失败量失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * 获取今日实际接入率
     * @return float
     */
    public function getTodayQueueAccessRate(): float
    {
        try {
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');
            
            $queueModel = new \app\model\QueueModel();
            
            // 统计今日总排队数量
            $totalQueueCount = $queueModel->where([
                ['join_time', 'between', [$startTime, $endTime]]
            ])->count();
            
            if ($totalQueueCount == 0) {
                return 0.0;
            }
            
            // 统计今日成功分配的数量
            $successCount = $queueModel->where([
                ['assign_time', 'between', [$startTime, $endTime]],
                ['status', '=', 2] // 2-已分配
            ])->count();
            
            // 计算接入率
            $accessRate = ($successCount / $totalQueueCount) * 100;
            
            return round($accessRate, 2);
        } catch (\Exception $e) {
            $this->logger->error('获取今日实际接入率失败', ['error' => $e->getMessage()]);
            return 0.0;
        }
    }
    
    /**
     * 获取今日平均排队时长(秒)
     * @return int
     */
    protected function getTodayAverageQueueTime(): int
    {
        try {
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');
            
            $queueModel = new \app\model\QueueModel();
            $queues = $queueModel->where([
                ['status', 'in', [$queueModel::STATUS_ASSIGNED, $queueModel::STATUS_CANCELED, $queueModel::STATUS_TIMEOUT]],
                ['assign_time', 'between', [$startTime, $endTime]]
            ])->select();
            
            if ($queues->isEmpty()) {
                return 0;
            }
            
            $totalTime = 0;
            foreach ($queues as $queue) {
                $totalTime += ($queue['assign_time'] - $queue['join_time']);
            }
            
            return (int)($totalTime / count($queues));
        } catch (\Exception $e) {
            $this->logger->error('获取今日平均排队时长失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * 获取今日排队总数
     * @return int
     */
    protected function getTodayQueueTotal(): int
    {
        try {
            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');
            
            $queueModel = new \app\model\QueueModel();
            return $queueModel->where([
                ['join_time', 'between', [$startTime, $endTime]]
            ])->count();
        } catch (\Exception $e) {
            $this->logger->error('获取今日排队总数失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * 获取今日平均响应时长
     * @param array $serviceIds 客服ID数组
     * @return float 平均响应时长（秒）
     */
    protected function getTodayAverageResponseTime(array $serviceIds): float
    {
        try {
            if (empty($serviceIds)) {
                return 0.0;
            }

            $today = date('Y-m-d');
            $startTime = strtotime($today . ' 00:00:00');
            $endTime = strtotime($today . ' 23:59:59');

            $sessionModel = new \app\model\ChatSession();
            $messageModel = new \app\model\ChatMessage();

            // 获取今日所有客服的会话
            $sessions = $sessionModel->where([
                ['service_id', 'in', $serviceIds],
                ['createtime', 'between', [$startTime, $endTime]]
            ])->column('id');

            if (empty($sessions)) {
                return 0.0;
            }

            $totalResponseTime = 0;
            $validResponseCount = 0;

            // 批量获取每个会话的第一条用户消息和第一条客服回复
            foreach ($sessions as $sessionId) {
                // 获取用户在该会话中的第一条消息
                $firstUserMessage = $messageModel->where([
                    ['session_id', '=', $sessionId],
                    ['from_type', '=', 'user']
                ])->order('id', 'asc')->find();

                if (!$firstUserMessage) {
                    continue;
                }

                // 获取客服在该会话中的第一条回复消息
                $firstServiceMessage = $messageModel->where([
                    ['session_id', '=', $sessionId],
                    ['from_type', '=', 'service'],
                    ['id', '>', $firstUserMessage['id']]
                ])->order('id', 'asc')->find();

                if (!$firstServiceMessage) {
                    continue;
                }

                // 计算响应时间
                $userMessageTime = is_numeric($firstUserMessage['createtime'])
                    ? $firstUserMessage['createtime']
                    : strtotime($firstUserMessage['createtime']);

                $serviceMessageTime = is_numeric($firstServiceMessage['createtime'])
                    ? $firstServiceMessage['createtime']
                    : strtotime($firstServiceMessage['createtime']);

                $responseTime = $serviceMessageTime - $userMessageTime;

                if ($responseTime > 0 && $responseTime < 3600) { // 响应时间在1小时内才算有效
                    $totalResponseTime += $responseTime;
                    $validResponseCount++;
                }
            }

            return $validResponseCount > 0 ? round($totalResponseTime / $validResponseCount, 2) : 0.0;
        } catch (\Exception $e) {
            $this->logger->error('获取今日平均响应时长失败', [
                'service_ids' => $serviceIds,
                'error' => $e->getMessage()
            ]);
            return 0.0;
        }
    }
}