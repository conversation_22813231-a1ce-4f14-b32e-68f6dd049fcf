<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\vchat\utils\Log;
use app\vchat\utils\Logger;
use app\vchat\core\MessageProtocol;
use Swoole\WebSocket\Server;
use think\swoole\Websocket;

/**
 * 自动分配服务
 * 处理客服状态变化时的自动分配逻辑
 */
class AutoAssignService
{
    /**
     * @var QueueService
     */
    protected $queueService;

    /**
     * @var SessionService
     */
    protected $sessionService;

    /**
     * @var SessionTimeoutService
     */
    protected $timeoutService;

    /**
     * @var MessageService
     */
    protected $messageService;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->queueService = new QueueService();
        $this->sessionService = new SessionService();
        $this->timeoutService = new SessionTimeoutService();
        $this->messageService = new MessageService();
        $this->logger = new Logger();
    }

    /**
     * 当客服状态变为可用时，尝试自动分配排队用户
     * @param \think\swoole\Websocket $websocket
     * @param int $serviceId 客服ID
     */
    public function tryAssignOnServiceAvailable($websocket, int $serviceId): void
    {
        try {
            $customerServiceModel = new \app\model\CustomerService();
            
            // 检查客服是否可以接待新会话
            if (!$customerServiceModel->canAcceptNewSession($serviceId)) {
                return;
            }

            // 获取下一个排队用户
            $nextUser = $this->queueService->getNextUser($serviceId);
            if (!$nextUser) {
                return;
            }

            // 创建新会话
            $session = $this->sessionService->createSession($nextUser['user_id'], $serviceId);
            if ($session) {
                // 更新客服会话数
                $customerServiceModel->increaseCurrentSessions($serviceId);

                // 记录用户和客服活动
                $this->timeoutService->recordUserActivity($nextUser['user_id'], $serviceId);
                $this->timeoutService->recordServiceActivity($nextUser['service_id'], $serviceId);

                $service = $customerServiceModel->getServiceInfo($serviceId);

                // 通知用户分配成功
                $this->notifyUserAssigned($websocket, $nextUser['user_id'], $service, $session['id']);

                // 通知客服新会话
                $this->notifyServiceNewSession($websocket, $serviceId, $nextUser['user_id'], $session['id']);

                $queueModel = new \app\model\QueueModel();

                $queueModel->checkTimeout($nextUser['user_id']);

                $this->logger->info('定时任务自动分配排队用户成功', [
                    'user_id' => $nextUser['user_id'],
                    'service_id' => $serviceId,
                    'session_id' => $session['id']
                ]);

                $this->logger->info('客服状态变化触发自动分配成功', [
                    'user_id' => $nextUser['user_id'],
                    'service_id' => $serviceId,
                    'session_id' => $session['id']
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('客服状态变化自动分配失败', [
                'service_id' => $serviceId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 通知用户分配成功
     * @param $websocket
     * @param int $userId
     * @param array $service
     * @param int $sessionId
     */
    protected function notifyUserAssigned($websocket, int $userId, array $service, int $sessionId): void
    {
        try {
            $message = [
                'type' => 'text',
                'message_type' => MessageProtocol::MESSAGE_SERVICE_ASSIGNED,
                'from_id' => $service['id'],
                'from_type' => 'service',
                'to_id' => $userId,
                'to_type' => 'user',
                'session_id' => $sessionId,
                'content' => '客服已为您分配，开始为您服务',
                'status' => 1
            ];

            $savedMessage = $this->messageService->saveMessage($message);
            if ($savedMessage) {
                $message['id'] = $savedMessage['id'];
                $message['service'] = $service;
                $this->messageService->sendMessage($websocket, $message);
            }
        } catch (\Exception $e) {
            $this->logger->error('通知用户分配失败', [
                'user_id' => $userId,
                'service_id' => $service['id'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 通知客服新会话
     * @param $websocket
     * @param int $serviceId
     * @param int $userId
     * @param int $sessionId
     */
    protected function notifyServiceNewSession($websocket, int $serviceId, int $userId, int $sessionId): void
    {
        try {
            $message = [
                'type' => 'text',
                'message_type' => MessageProtocol::MESSAGE_NEW_SESSION_TYPE,
                'from_id' => $userId,
                'from_type' => 'user',
                'to_id' => $serviceId,
                'to_type' => 'service',
                'session_id' => $sessionId,
                'content' => '新用户已分配给您',
                'status' => 1
            ];

            $savedMessage = $this->messageService->saveMessage($message);
            if ($savedMessage) {
                $message['id'] = $savedMessage['id'];
                $this->messageService->sendMessage($websocket, $message);
            }
        } catch (\Exception $e) {
            $this->logger->error('通知客服新会话失败', [
                'service_id' => $serviceId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 为其他空闲客服分配排队用户
     * @param \think\swoole\Websocket $websocket
     */
    public function assignQueuedUsersToIdleServices($websocket): void
    {
        try {
            $customerServiceModel = new \app\model\CustomerService();
            
            // 获取所有可用的客服
            $availableServices = $customerServiceModel->getAvailableServices();
            
            if (empty($availableServices)) {
                $this->logger->info('没有可用的客服进行自动分配');
                return;
            }

            $assignedCount = 0;
            
            // 为每个可用客服尝试分配排队用户
            foreach ($availableServices as $service) {
                $serviceId = $service['id'];

                $this->tryAssignOnServiceAvailable($websocket, $serviceId);

                $assignedCount++;

            }
            
            if ($assignedCount > 0) {
                $this->logger->info('批量自动分配完成', [
                    'assigned_count' => $assignedCount,
                    'available_services_count' => count($availableServices)
                ]);
            }
            
        } catch (\Exception $e) {
            $this->logger->error('为空闲客服分配排队用户失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}