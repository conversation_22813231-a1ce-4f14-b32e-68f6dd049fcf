<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\model\ChatSession;
use app\model\CustomerService;
use app\vchat\utils\Logger;
use think\swoole\Websocket;

/**
 * 会话服务类
 */
class SessionService
{
    /**
     * @var ChatSession
     */
    protected $sessionModel;

    /**
     * @var CustomerService
     */
    protected $customerServiceModel;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->sessionModel = new ChatSession();
        $this->customerServiceModel = new CustomerService();
        $this->logger = new Logger();
    }

    public function getSessionModel()
    {
        return $this->sessionModel;
    }

    public function getCustomerServiceModel()
    {
        return $this->customerServiceModel;

    }

    /**
     * 创建会话
     * @param int $userId 用户ID
     * @param int $serviceId 客服ID
     * @param string $platform 平台
     * @return array|null
     */
    public function createSession(int $userId, int $serviceId, string $platform = 'web'): ?array
    {
        try {
            $session = $this->sessionModel->createSession($userId, $serviceId, $platform);
            if (!$session) {
                throw new \Exception('创建会话失败');
            }
            return $session;
        } catch (\Exception $e) {
            $this->logger->error('创建会话失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 获取会话列表
     * @param int $userId 用户ID
     * @param string $type 类型(user/service)
     * @return array
     */
    public function getSessionList(int $userId, string $type = 'user'): array
    {
        try {
            $where = $type === 'user' ? ['user_id' => $userId] : ['service_id' => $userId];
            return $this->sessionModel->where($where)
                ->where('status', 1)
                ->order('updatetime', 'desc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            $this->logger->error('获取会话列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * 获取会话详情
     * @param int $sessionId 会话ID
     * @return array|null
     */
    public function getSessionDetail(int $sessionId): ?array
    {
        try {
            $session = $this->sessionModel->where('id', $sessionId)->find();
            return $session ? $session->toArray() : null;
        } catch (\Exception $e) {
            $this->logger->error('获取会话详情失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 结束会话
     * @param int $sessionId 会话ID
     * @return bool
     */
    public function endSession(int $sessionId): bool
    {
        $this->sessionModel->startTrans();
        try {
            $serviceId = $this->sessionModel->endSession($sessionId);
            if ($serviceId === false) {
                $this->sessionModel->rollback();
                return false;
            }
            $this->customerServiceModel->decCurrentSession($serviceId);
            $this->sessionModel->commit();

            return true;
        } catch (\Exception $e) {
            $this->logger->error('结束会话失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 转接会话
     * @param int $sessionId 会话ID
     * @param int $newServiceId 新客服ID
     * @return bool
     */
    public function transferSession(int $sessionId, int $newServiceId): bool
    {
        try {
            return $this->sessionModel->transferSession($sessionId, $newServiceId);
        } catch (\Exception $e) {
            $this->logger->error('转接会话失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 更新会话满意度
     * @param int $sessionId 会话ID
     * @param int $rating 评分
     * @param string $feedback 反馈
     * @return bool
     */
    public function updateSatisfaction(int $sessionId, int $rating, string $feedback): bool
    {
        try {
            return $this->sessionModel->updateSatisfaction($sessionId, $rating, $feedback);
        } catch (\Exception $e) {
            $this->logger->error('更新会话满意度失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 获取客服当前会话数
     * @param int $serviceId 客服ID
     * @return int
     */
    public function getServiceCurrentSessions(int $serviceId): int
    {
        try {
            return $this->sessionModel->where([
                ['service_id', '=', $serviceId],
                ['status', '=', 1]
            ])->count();
        } catch (\Exception $e) {
            $this->logger->error('获取客服当前会话数失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 0;
        }
    }
}