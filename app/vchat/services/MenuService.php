<?php

namespace app\vchat\services;

use app\vchat\utils\Logger;
use think\facade\Cache;
use think\facade\Config;
use think\swoole\Websocket;

/**
 * 菜单服务类
 */
class MenuService
{
    /**
     * @var Logger
     */
    protected $logger;
    
    /**
     * @var array 菜单配置
     */
    protected $config;
    
    /**
     * @var array 处理器映射
     */
    protected $handlers;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logger = new Logger();
        $this->config = Config::get('vchat.menu', []);
        $this->handlers = $this->config['handlers'] ?? [];
    }
    
    /**
     * 获取用户菜单
     * 
     * @param string $userId 用户ID
     * @param string $menuId 菜单ID，为空则获取主菜单
     * @return array
     */
    public function getUserMenu(string $userId, string $menuId = ''): array
    {
        try {
            // 检查菜单功能是否启用
            if (!($this->config['menu']['enabled'] ?? true)) {
                return $this->error('菜单功能未启用');
            }
            
            // 获取菜单数据
            $menuData = $this->getMenuData($menuId);
            if (empty($menuData)) {
                return $this->error('菜单不存在');
            }
            
            // 权限验证
            if (!$this->checkPermission($userId, $menuData)) {
                return $this->error('无权限访问此菜单');
            }
            
            // 格式化菜单数据
            $formattedMenu = $this->formatMenuData($menuData);
            
            $this->logger->info('获取用户菜单', [
                'user_id' => $userId,
                'menu_id' => $menuId,
                'menu_data' => $formattedMenu
            ]);
            
            return $this->success($formattedMenu);
            
        } catch (\Exception $e) {
            $this->logger->error('获取用户菜单失败', [
                'user_id' => $userId,
                'menu_id' => $menuId,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('获取菜单失败');
        }
    }
    
    /**
     * 处理菜单点击事件
     * 
     * @param string $userId 用户ID
     * @param string $menuId 菜单ID
     * @param Websocket $websocket WebSocket连接
     * @return array
     */
    public function handleMenuClick(string $userId, string $menuId, Websocket $websocket): array
    {
        try {
            // 获取菜单配置
            $menuConfig = $this->getMenuData($menuId);
            if (empty($menuConfig)) {
                return $this->error('菜单不存在');
            }
            
            // 权限验证
            if (!$this->checkPermission($userId, $menuConfig)) {
                return $this->error('无权限访问此菜单');
            }
            
            // 根据菜单类型处理
            $result = $this->processMenuAction($menuConfig, $userId, $websocket);
            
            $this->logger->info('处理菜单点击', [
                'user_id' => $userId,
                'menu_id' => $menuId,
                'menu_type' => $menuConfig['type'] ?? '',
                'result' => $result
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error('处理菜单点击失败', [
                'user_id' => $userId,
                'menu_id' => $menuId,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('菜单处理失败');
        }
    }
    
    /**
     * 获取菜单数据
     * 
     * @param string $menuId 菜单ID
     * @return array
     */
    protected function getMenuData(string $menuId = ''): array
    {
        // 优先从缓存获取
        $cacheKey = $this->getCacheKey($menuId);
        $menuData = Cache::get($cacheKey);
        
        if ($menuData !== null) {
            return $menuData;
        }
        
        // 从配置文件获取
        $defaultMenus = $this->config['default_menus'] ?? [];
        
        if (empty($menuId)) {
            // 获取主菜单
            $menuData = $defaultMenus[0] ?? [];
        } else {
            // 查找指定菜单
            $menuData = $this->findMenuById($defaultMenus, $menuId);
        }
        
        // 缓存菜单数据
        if (!empty($menuData)) {
            $cacheTime = $this->config['menu']['cache_time'] ?? 3600;
            Cache::set($cacheKey, $menuData, $cacheTime);
        }
        
        return $menuData;
    }
    
    /**
     * 递归查找菜单
     * 
     * @param array $menus 菜单数组
     * @param string $menuId 菜单ID
     * @return array
     */
    protected function findMenuById(array $menus, string $menuId): array
    {
        foreach ($menus as $menu) {
            if (($menu['id'] ?? '') === $menuId) {
                return $menu;
            }
            
            if (!empty($menu['children'])) {
                $found = $this->findMenuById($menu['children'], $menuId);
                if (!empty($found)) {
                    return $found;
                }
            }
        }
        
        return [];
    }
    
    /**
     * 处理菜单动作
     * 
     * @param array $menuConfig 菜单配置
     * @param string $userId 用户ID
     * @param Websocket $websocket WebSocket连接
     * @return array
     */
    protected function processMenuAction(array $menuConfig, string $userId, Websocket $websocket): array
    {
        $menuType = $menuConfig['type'] ?? '';
        
        switch ($menuType) {
            case 'click':
                return $this->handleClickMenu($menuConfig, $userId, $websocket);
                
            case 'view':
                return $this->handleViewMenu($menuConfig, $userId, $websocket);
                
            case 'button':
                return $this->handleButtonMenu($menuConfig, $userId, $websocket);
                
            case 'media':
                return $this->handleMediaMenu($menuConfig, $userId, $websocket);
                
            case 'text':
                return $this->handleTextMenu($menuConfig, $userId, $websocket);
                
            default:
                return $this->error('不支持的菜单类型: ' . $menuType);
        }
    }
    
    /**
     * 处理点击类型菜单
     * 
     * @param array $menuConfig 菜单配置
     * @param string $userId 用户ID
     * @param Websocket $websocket WebSocket连接
     * @return array
     */
    protected function handleClickMenu(array $menuConfig, string $userId, Websocket $websocket): array
    {
        $handlerName = $menuConfig['handler'] ?? '';
        if (empty($handlerName)) {
            return $this->error('未配置处理器');
        }
        
        $handlerClass = $this->handlers[$handlerName] ?? '';
        if (empty($handlerClass) || !class_exists($handlerClass)) {
            return $this->error('处理器不存在: ' . $handlerName);
        }
        
        try {
            $handler = new $handlerClass();
            $params = $menuConfig['params'] ?? [];
            
            $handler->setParams($menuConfig, $params, $userId, $websocket);
            return $handler->handle();
            
        } catch (\Exception $e) {
            $this->logger->error('处理器执行失败', [
                'handler' => $handlerName,
                'error' => $e->getMessage()
            ]);
            
            return $this->error('处理器执行失败');
        }
    }
    
    /**
     * 处理跳转类型菜单
     * 
     * @param array $menuConfig 菜单配置
     * @param string $userId 用户ID
     * @param Websocket $websocket WebSocket连接
     * @return array
     */
    protected function handleViewMenu(array $menuConfig, string $userId, Websocket $websocket): array
    {
        $url = $menuConfig['params']['url'] ?? '';
        if (empty($url)) {
            return $this->error('未配置跳转链接');
        }
        
        // 发送跳转消息
        $message = [
            'type' => 'view',
            'url' => $url,
            'title' => $menuConfig['name'] ?? '跳转链接',
            'timestamp' => time(),
            'from' => 'system'
        ];
        
        try {
            $websocket->to($userId)->emit('message', $message);
            return $this->success(['url' => $url], '跳转链接已发送');
        } catch (\Exception $e) {
            return $this->error('发送跳转链接失败');
        }
    }
    
    /**
     * 处理按钮类型菜单（子菜单）
     * 
     * @param array $menuConfig 菜单配置
     * @param string $userId 用户ID
     * @param Websocket $websocket WebSocket连接
     * @return array
     */
    protected function handleButtonMenu(array $menuConfig, string $userId, Websocket $websocket): array
    {
        $children = $menuConfig['children'] ?? [];
        if (empty($children)) {
            return $this->error('子菜单为空');
        }
        
        // 格式化子菜单
        $formattedChildren = $this->formatMenuData(['children' => $children]);
        
        // 发送子菜单
        $message = [
            'type' => 'menu',
            'menu_data' => $formattedChildren['children'],
            'title' => $menuConfig['name'] ?? '子菜单',
            'timestamp' => time(),
            'from' => 'system'
        ];
        
        try {
            $websocket->to($userId)->emit('message', $message);
            return $this->success($formattedChildren, '子菜单已发送');
        } catch (\Exception $e) {
            return $this->error('发送子菜单失败');
        }
    }
    
    /**
     * 处理媒体类型菜单
     * 
     * @param array $menuConfig 菜单配置
     * @param string $userId 用户ID
     * @param Websocket $websocket WebSocket连接
     * @return array
     */
    protected function handleMediaMenu(array $menuConfig, string $userId, Websocket $websocket): array
    {
        $params = $menuConfig['params'] ?? [];
        $mediaType = $params['media_type'] ?? 'image';
        $mediaUrl = $params['media_url'] ?? '';
        
        if (empty($mediaUrl)) {
            return $this->error('未配置媒体链接');
        }
        
        $message = [
            'type' => 'media',
            'media_type' => $mediaType,
            'media_url' => $mediaUrl,
            'title' => $menuConfig['name'] ?? '媒体消息',
            'timestamp' => time(),
            'from' => 'system'
        ];
        
        try {
            $websocket->to($userId)->emit('message', $message);
            return $this->success(['media_url' => $mediaUrl], '媒体消息已发送');
        } catch (\Exception $e) {
            return $this->error('发送媒体消息失败');
        }
    }
    
    /**
     * 处理文本类型菜单
     * 
     * @param array $menuConfig 菜单配置
     * @param string $userId 用户ID
     * @param Websocket $websocket WebSocket连接
     * @return array
     */
    protected function handleTextMenu(array $menuConfig, string $userId, Websocket $websocket): array
    {
        $text = $menuConfig['params']['text'] ?? '';
        if (empty($text)) {
            return $this->error('未配置文本内容');
        }
        
        $message = [
            'type' => 'text',
            'content' => $text,
            'timestamp' => time(),
            'from' => 'system'
        ];
        
        try {
            $websocket->to($userId)->emit('message', $message);
            return $this->success(['text' => $text], '文本消息已发送');
        } catch (\Exception $e) {
            return $this->error('发送文本消息失败');
        }
    }
    
    /**
     * 格式化菜单数据
     * 
     * @param array $menuData 原始菜单数据
     * @return array
     */
    protected function formatMenuData(array $menuData): array
    {
        $formatted = [
            'id' => $menuData['id'] ?? '',
            'name' => $menuData['name'] ?? '',
            'type' => $menuData['type'] ?? '',
            'level' => $menuData['level'] ?? 1,
            'sort' => $menuData['sort'] ?? 0
        ];
        
        // 处理子菜单
        if (!empty($menuData['children'])) {
            $formatted['children'] = [];
            foreach ($menuData['children'] as $child) {
                if (($child['status'] ?? 1) == 1) {
                    $formatted['children'][] = $this->formatMenuData($child);
                }
            }
            
            // 按sort字段排序
            usort($formatted['children'], function($a, $b) {
                return ($a['sort'] ?? 0) <=> ($b['sort'] ?? 0);
            });
        }
        
        return $formatted;
    }
    
    /**
     * 检查用户权限
     * 
     * @param string $userId 用户ID
     * @param array $menuData 菜单数据
     * @return bool
     */
    protected function checkPermission(string $userId, array $menuData): bool
    {
        // 如果未启用权限验证，直接返回true
        if (!($this->config['permissions']['enabled'] ?? false)) {
            return true;
        }
        
        // TODO: 实现具体的权限验证逻辑
        // 这里可以根据用户ID查询用户权限级别
        // 然后与菜单要求的权限级别进行比较
        
        return true;
    }
    
    /**
     * 获取缓存键名
     * 
     * @param string $menuId 菜单ID
     * @return string
     */
    protected function getCacheKey(string $menuId = ''): string
    {
        $prefix = $this->config['menu']['cache_prefix'] ?? 'vchat:menu:';
        return $prefix . ($menuId ?: 'main');
    }
    
    /**
     * 清除菜单缓存
     * 
     * @param string $menuId 菜单ID，为空则清除所有菜单缓存
     * @return bool
     */
    public function clearMenuCache(string $menuId = ''): bool
    {
        try {
            if (empty($menuId)) {
                // 清除所有菜单缓存
                $prefix = $this->config['menu']['cache_prefix'] ?? 'vchat:menu:';
                Cache::clear($prefix . '*');
            } else {
                // 清除指定菜单缓存
                $cacheKey = $this->getCacheKey($menuId);
                Cache::delete($cacheKey);
            }
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('清除菜单缓存失败', [
                'menu_id' => $menuId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * 构建成功响应
     * 
     * @param array $data 响应数据
     * @param string $message 响应消息
     * @return array
     */
    protected function success(array $data = [], string $message = '操作成功'): array
    {
        return [
            'success' => true,
            'message' => $message,
            'data' => $data
        ];
    }
    
    /**
     * 构建失败响应
     * 
     * @param string $message 错误消息
     * @param array $data 响应数据
     * @return array
     */
    protected function error(string $message = '操作失败', array $data = []): array
    {
        return [
            'success' => false,
            'message' => $message,
            'data' => $data
        ];
    }
}