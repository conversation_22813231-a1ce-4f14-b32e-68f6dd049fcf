<?php

namespace app\vchat\services;

use app\vchat\utils\Log;
use Swoole\WebSocket\Server as SwooleServer;
use think\facade\Cache;

/**
 * FD 管理服务 (使用 Redis 实现)
 * 通过 type 字段区分客户端和用户端：
 * - type = 'user_[id]' 表示用户端
 * - type = 'service_[id]' 表示客服端
 */
class FdManagerService
{
    /**
     * Redis前缀
     */
    const REDIS_PREFIX = 'vchat:fd:';
    const FD_INFO_PREFIX = 'info:';
    const USER_FD_PREFIX = 'user:';
    const SERVICE_FD_PREFIX = 'service:';

    /**
     * @var SwooleServer Swoole Server 实例
     */
    protected $swooleServer;

    /**
     * 构造方法
     * @param SwooleServer $swooleServer Swoole Server 实例
     */
    public function __construct(SwooleServer $swooleServer)
    {
        $this->swooleServer = $swooleServer;
        Log::info('[FdManagerService initialized with Redis storage.]');
    }

    /**
     * 设置 FD 与用户ID 的映射
     * @param string $fd WebSocket 连接的 FD
     * @param int $userId 用户ID
     * @param string $userType 用户类型 ('user' 或 'service')
     */
    public function setFd(string $fd, int $userId, string $userType): void
    {
        // 验证用户类型
        if (!in_array($userType, ['user', 'service'])) {
            Log::error("[FdManagerService: Invalid user type] :" . json_encode(['userType' => $userType]));
            return;
        }

        // 构建类型标识
        $type = "{$userType}_{$userId}";

        // 查找并清理旧的映射
        $this->cleanupOldMapping($type, $fd);

        // 设置FD信息映射 (fd -> 用户信息)
        $fdInfoKey = self::REDIS_PREFIX . self::FD_INFO_PREFIX . $fd;
        $fdInfo = [
            'fd' => $fd,
            'type' => $type,
            'user_id' => $userId,
            'user_type' => $userType,
            'created_at' => time()
        ];
        Cache::set($fdInfoKey, $fdInfo, 3600); // 1小时过期

        // 设置用户到FD的反向映射 (user_type_id -> fd)
        $userFdKey = self::REDIS_PREFIX . 
            ($userType === 'user' ? self::USER_FD_PREFIX : self::SERVICE_FD_PREFIX) . 
            $userId;
        Cache::set($userFdKey, $fd, 3600); // 1小时过期

        Log::info("[FdManagerService: Set FD mapping] : " . json_encode([
                'fd' => $fd,
                'userId' => $userId,
                'userType' => $userType,
                'type' => $type,
                'fdInfoKey' => $fdInfoKey,
                'userFdKey' => $userFdKey
            ]));

        return;
    }

    /**
     * 根据 FD 获取用户信息
     * @param string $fd WebSocket 连接的 FD
     * @return array|null 包含 userId 和 userType 的关联数组，或 null
     */
    public function getUserByFd(string $fd): ?array
    {
        $fdInfoKey = self::REDIS_PREFIX . self::FD_INFO_PREFIX . $fd;
        $info = Cache::get($fdInfoKey);
        
        if (!$info) {
            Log::warning("[FdManagerService: FD info not found] :" . json_encode(['fd' => $fd, 'key' => $fdInfoKey]));
            return null;
        }

        return [
            'userId' => (int)$info['user_id'],
            'userType' => $info['user_type']
        ];
    }

    /**
     * 根据用户ID和类型获取 FD
     * @param int $userId 用户ID
     * @param string $userType 用户类型 ('user' 或 'service')
     * @return string|null WebSocket 连接的 FD，或 null
     */
    public function getFdByUserId(int $userId, string $userType): ?string
    {
        // 验证用户类型
        if (!in_array($userType, ['user', 'service'])) {
            Log::error("[FdManagerService: Invalid user type] :" . json_encode(['userType' => $userType]));
            return null;
        }

        // 构建用户FD映射键
        $userFdKey = self::REDIS_PREFIX . 
            ($userType === 'user' ? self::USER_FD_PREFIX : self::SERVICE_FD_PREFIX) . 
            $userId;
        
        // 从Redis获取FD
        $fd = Cache::get($userFdKey);
        
        if (!$fd) {
            Log::warning("[FdManagerService: No FD found for user] :" . json_encode([
                'userId' => $userId,
                'userType' => $userType,
                'userFdKey' => $userFdKey
            ]));
            return null;
        }

        Log::info("[FdManagerService: Found FD for user] :" . json_encode([
            'userId' => $userId,
            'userType' => $userType,
            'fd' => $fd
        ]));

        // 验证 FD 是否仍然有效
        if ($this->isFdValidSafe($fd)) {
            Log::info("[FdManagerService: FD is valid] :" . json_encode([
                'fd' => $fd,
                'userId' => $userId,
                'userType' => $userType
            ]));
            return $fd;
        } else {
            // FD 无效，清理映射
            $this->removeFd($fd);
            Log::error("[FdManagerService: Found stale FD, cleaned up] :" . json_encode([
                'fd' => $fd,
                'userId' => $userId,
                'userType' => $userType
            ]));
            return null;
        }
    }

    /**
     * 移除 FD 映射
     * @param string $fd WebSocket 连接的 FD
     */
    public function removeFd(string $fd): void
    {
        // 获取FD信息
        $fdInfoKey = self::REDIS_PREFIX . self::FD_INFO_PREFIX . $fd;
        $info = Cache::get($fdInfoKey);
        
        if ($info) {
            // 删除FD信息映射
            Cache::delete($fdInfoKey);
            
            // 删除用户到FD的反向映射
            $userFdKey = self::REDIS_PREFIX . 
                ($info['user_type'] === 'user' ? self::USER_FD_PREFIX : self::SERVICE_FD_PREFIX) . 
                $info['user_id'];
            Cache::delete($userFdKey);
            
            Log::info("[FdManagerService: Removed FD mapping] :" . json_encode([
                'fd' => $fd,
                'userId' => $info['user_id'],
                'userType' => $info['user_type'],
                'fdInfoKey' => $fdInfoKey,
                'userFdKey' => $userFdKey
            ]));
        } else {
            Log::warning("[FdManagerService: FD info not found for removal] :" . json_encode([
                'fd' => $fd,
                'fdInfoKey' => $fdInfoKey
            ]));
        }
    }

    /**
     * 检查 FD 是否有效且已建立连接
     * @param string $fd WebSocket 连接的 FD
     * @return bool
     */
    public function isFdValid(string $fd): bool
    {
        return $this->swooleServer->isEstablished($fd);
    }

    /**
     * 安全地检查 FD 是否有效且已建立连接
     * 使用 try-catch 处理多进程环境下的 "server is not running" 错误
     * @param string $fd WebSocket 连接的 FD
     * @return bool
     */
    protected function isFdValidSafe(string $fd): bool
    {
        try {
            // 首先检查服务器是否正在运行
            if (!$this->isServerRunning()) {
                Log::warning("[FdManagerService: Server not running, skipping FD validation] :" . json_encode(['fd' => $fd]));
                return false;
            }
            
            // 检查 FD 是否存在且连接已建立
            return $this->swooleServer->exist($fd) && $this->swooleServer->isEstablished($fd);
        } catch (\Throwable $e) {
            Log::error("[FdManagerService: FD validation failed] :" . json_encode([
                'fd' => $fd,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]));
            return false;
        }
    }

    /**
     * 检查 Swoole 服务器是否正在运行
     * 在多进程环境下，子进程无法访问主进程的 Server 实例
     * @return bool
     */
    protected function isServerRunning(): bool
    {
        try {
            // 尝试获取服务器统计信息来判断服务器是否运行
            $stats = $this->swooleServer->stats();
            return is_array($stats) && !empty($stats);
        } catch (\Throwable $e) {
            Log::warning("[FdManagerService: Server status check failed] :" . json_encode([
                'error' => $e->getMessage()
            ]));
            return false;
        }
    }

    /**
     * 清理旧的映射
     * @param string $type 类型标识 (格式: user_123 或 service_456)
     * @param string $newFd 新的 FD
     */
    protected function cleanupOldMapping(string $type, string $newFd): void
    {
        // 解析类型和用户ID
        list($userType, $userId) = explode('_', $type);
        
        // 构建用户FD映射键
        $userFdKey = self::REDIS_PREFIX . 
            ($userType === 'user' ? self::USER_FD_PREFIX : self::SERVICE_FD_PREFIX) . 
            $userId;
        
        // 获取旧的FD
        $oldFd = Cache::get($userFdKey);
        
        if ($oldFd && $oldFd !== $newFd) {
            Log::info("[FdManagerService: Found old FD for cleanup] :" . json_encode([
                'type' => $type,
                'oldFd' => $oldFd,
                'newFd' => $newFd
            ]));
            
            // 关闭旧连接 - 使用安全的检查方式
            if ($this->isFdValidSafe($oldFd)) {
                try {
                    $this->swooleServer->close($oldFd);
                    Log::info("[FdManagerService: Closed old connection for {$type}] :" . json_encode(['oldFd' => $oldFd]));
                } catch (\Throwable $e) {
                    Log::error("[FdManagerService: Failed to close old connection] :" . json_encode([
                        'oldFd' => $oldFd,
                        'type' => $type,
                        'error' => $e->getMessage()
                    ]));
                }
            }
            
            // 移除旧映射
            $this->removeFd($oldFd);
            Log::info("[FdManagerService: Removed old mapping for {$type}] : " . json_encode(['oldFd' => $oldFd]));
        }
    }
}