<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\vchat\core\MessageProtocol;
use app\model\ChatMessage;
use app\vchat\utils\Logger;
use think\swoole\Websocket;

/**
 * 消息服务类
 */
class MessageService
{
    /**
     * @var ChatMessage
     */
    protected $messageModel;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->messageModel = new ChatMessage();
        $this->logger = new Logger();
    }

    public function getMessageModel()
    {
        return $this->messageModel;
    }

    /**
     * 保存消息
     * @param array $data
     * @return array|null
     */
    public function saveMessage(array $data): ?array
    {
        try {
            // 格式化消息
            $message = MessageProtocol::format($data);

            $this->logger->info('保存消息', $message);

            // 保存到数据库
            $result = $this->messageModel->createMessage([
                'id' => $message['id'],
                'session_id' => $message['session_id'],
                'from_id' => $message['from_id'],
                'from_type' => $message['from_type'],
                'to_id' => $message['to_id'],
                'to_type' => $message['to_type'],
                'content' => $message['content'],
                'content_type' => $message['type'],
                'message_type' => $message['message_type'],
                'status' => $message['status'],
                'extra' => json_encode($message['extra']),
                'createtime' => $message['timestamp']
            ]);

            if ($result === false) {
                throw new \Exception('保存消息失败');
            }

            return $message;
        } catch (\Exception $e) {
            $this->logger->error('保存消息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 批量保存消息
     * @param array $messages
     * @return array|null
     */
    public function saveMessages(array $messages): ?array
    {
        try {
            $formattedMessages = [];
            $dbMessages = [];
            
            // 格式化所有消息
            foreach ($messages as $data) {
                $message = MessageProtocol::format($data);
                $formattedMessages[] = $message;
                
                $dbMessages[] = [
                    'id' => $message['id'],
                    'session_id' => $message['session_id'],
                    'from_id' => $message['from_id'],
                    'from_type' => $message['from_type'],
                    'to_id' => $message['to_id'],
                    'to_type' => $message['to_type'],
                    'content' => $message['content'],
                    'content_type' => $message['type'],
                    'message_type' => $message['message_type'],
                    'status' => $message['status'],
                    'extra' => json_encode($message['extra']),
                    'createtime' => $message['timestamp']
                ];
            }

            $this->logger->info('批量保存消息', ['count' => count($dbMessages)]);

            // 批量插入到数据库
            $result = $this->messageModel->createMessages($dbMessages);

            if ($result === false) {
                throw new \Exception('批量保存消息失败');
            }

            return $formattedMessages;
        } catch (\Exception $e) {
            $this->logger->error('批量保存消息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 发送消息
     * @param Websocket $websocket
     * @param array $message
     */
    public function sendMessage(Websocket $websocket, array $message, string $type = MessageProtocol::MESSAGE_TYPE): void
    {
        try {
            // 发送给接收者
            $websocket->to($this->getRoomId($message['to_type'], $message['to_id']))
                ->emit($type, $message);

            // 发送已送达状态
            $this->sendDeliveredStatus($websocket, $message);

            $this->logger->info('发送消息成功', [
                'message_id' => $message['id'] ?? null,
                'message_type' => $message['message_type'],
                'type' => $message['type'] ?? null
            ]);
        } catch (\Exception $e) {
            $this->logger->error('发送消息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 发送已送达状态
     * @param Websocket $websocket
     * @param array $message
     */
    protected function sendDeliveredStatus(Websocket $websocket, array $message): void
    {
        try {
            $status = MessageProtocol::createSystemMessage('消息已送达', [
                'message_id' => $message['id'] ?? '',
                'status' => MessageProtocol::STATUS_DELIVERED,
                'local_id' => $message['local_id'] ?? ''
            ]);

            $websocket->to($this->getRoomId($message['from_type'], $message['from_id']))
                ->emit(MessageProtocol::MESSAGE_SEND_SUCCESS, $status);

            if (!isset($message['id'])) {
                return;
            }
            // 更新消息状态
            $this->messageModel->where('id', $message['id'])
                ->update(['status' => MessageProtocol::STATUS_DELIVERED]);
        } catch (\Exception $e) {
            $this->logger->error('发送已送达状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 发送输入状态
     * @param Websocket $websocket
     * @param array $data
     */
    public function sendTypingStatus(Websocket $websocket, array $data): void
    {
        try {
            $status = MessageProtocol::createTypingMessage($data['content'] === 'typing');

            $websocket->to($this->getRoomId($data['to_type'], $data['to_id']))
                ->emit(MessageProtocol::MESSAGE_TYPING, $status);
        } catch (\Exception $e) {
            $this->logger->error('发送输入状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 更新消息已读状态
     * @param array $messageId
     */
    public function updateMessageReadStatus(array $messageId): void
    {
        try {
            // 更新消息状态
            $this->messageModel->whereIn('id', $messageId)
                ->update(['status' => MessageProtocol::STATUS_READ]);

            // 获取消息信息
            $message = $this->messageModel->whereIn('id', $messageId)->find();
            if (!$message) {
                return;
            }

            // 发送已读状态
            $status = MessageProtocol::createReadMessage($messageId);

            app()->make(Websocket::class)
                ->to($this->getRoomId($message['from_type'], $message['from_id']))
                ->emit(MessageProtocol::MESSAGE_NOTICE, $status);
        } catch (\Exception $e) {
            $this->logger->error('更新消息已读状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 撤回消息
     * @param string $messageId
     */
    public function recallMessage(string $messageId): void
    {
        try {
            // 获取消息信息
            $message = $this->messageModel->where('id', $messageId)->find();
            if (!$message) {
                return;
            }

            // 更新消息状态
            $this->messageModel->where('id', $messageId)
                ->update(['status' => MessageProtocol::STATUS_RECALLED]);

            // 发送撤回通知
            $recall = MessageProtocol::createSystemMessage('消息已撤回', [
                'message_id' => $messageId,
                'status' => MessageProtocol::STATUS_RECALLED
            ]);

            // 发送给发送者和接收者
            $websocket = app()->make(Websocket::class);
            $websocket->to($this->getRoomId($message['from_type'], $message['from_id']))
                ->emit(MessageProtocol::MESSAGE_RECALL, $recall);
            $websocket->to($this->getRoomId($message['to_type'], $message['to_id']))
                ->emit(MessageProtocol::MESSAGE_RECALL, $recall);
        } catch (\Exception $e) {
            $this->logger->error('撤回消息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 获取房间ID
     * @param string $type
     * @param int $id
     * @return string
     */
    protected function getRoomId(string $type, int $id): string
    {
        return $type . '_' . $id;
    }
}