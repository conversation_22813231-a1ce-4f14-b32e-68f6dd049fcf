<?php

namespace app\vchat\services;

use app\vchat\utils\Logger;

class CustomerService
{

    /**
     * @var Logger
     */
    protected $logger;

    protected $customerServiceModel;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->customerServiceModel = new \app\model\CustomerService();
        $this->logger = new Logger();
    }

    public function getCustomerServiceModel()
    {
        return $this->customerServiceModel;
    }

}
