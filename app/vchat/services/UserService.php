<?php

namespace app\vchat\services;

use app\vchat\utils\Logger;
use app\model\system\User;

class UserService
{

    /**
     * @var Logger
     */
    protected $logger;

    protected $userModel;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->userModel = new User();
        $this->logger = new Logger();
    }

    public function getUserModel()
    {
        return $this->userModel;
    }

    /**
     * 根据ID获取用户信息
     * @param int $id
     * @return User|null
     */
    public function getUserById($id, $where = [])
    {
        try {
            return $this->userModel->where($where)->find($id);
        } catch (\Exception $e) {
            $this->logger->error('获取用户信息失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 根据用户名获取用户信息
     * @param string $username
     * @return array|null
     */
    public function getUserByUsername($username)
    {
        try {
            return $this->userModel->where('username', $username)->find();
        } catch (\Exception $e) {
            $this->logger->error('根据用户名获取用户信息失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 创建新用户
     * @param array $data
     * @return bool
     */
    public function createUser($data)
    {
        try {
            $result = $this->userModel->save($data);
            if ($result) {
                $this->logger->info('用户创建成功: ' . json_encode($data));
                return true;
            }
            return false;
        } catch (\Exception $e) {
            $this->logger->error('创建用户失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新用户信息
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateUser($id, $data)
    {
        try {
            $result = $this->userModel->where('id', $id)->update($data);
            if ($result) {
                $this->logger->info('用户信息更新成功: ID=' . $id);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            $this->logger->error('更新用户信息失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除用户
     * @param int $id
     * @return bool
     */
    public function deleteUser($id)
    {
        try {
            $result = $this->userModel->destroy($id);
            if ($result) {
                $this->logger->info('用户删除成功: ID=' . $id);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            $this->logger->error('删除用户失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取用户列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getUserList($where = [], $page = 1, $limit = 10)
    {
        try {
            $query = $this->userModel;
            if (!empty($where)) {
                $query = $query->where($where);
            }
            return $query->page($page, $limit)->select();
        } catch (\Exception $e) {
            $this->logger->error('获取用户列表失败: ' . $e->getMessage());
            return [];
        }
    }

}