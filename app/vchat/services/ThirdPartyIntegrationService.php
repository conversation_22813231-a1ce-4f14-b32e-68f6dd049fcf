<?php

namespace app\vchat\services;

use app\vchat\core\MessageProtocol;
use app\vchat\services\MessageService;
use app\vchat\services\SessionService;

use app\vchat\utils\Logger;
use app\model\ChatSession;
use app\model\ChatMessage;
use app\model\CustomerService;
use think\swoole\Websocket;
use think\facade\Cache;
use think\facade\Config;

/**
 * 第三方平台集成服务
 * 将第三方平台消息无缝集成到VChat系统中
 */
class ThirdPartyIntegrationService
{
    /**
     * 消息服务
     * @var MessageService
     */
    protected MessageService $messageService;

    /**
     * 会话服务
     * @var SessionService
     */
    protected SessionService $sessionService;



    /**
     * 日志记录器
     * @var Logger
     */
    protected Logger $logger;

    /**
     * 支持的第三方平台配置
     * @var array
     */
    protected array $platformConfig = [
        'wechat.miniprogram' => [
            'name' => '微信小程序',
            'icon' => 'wechat',
            'color' => '#07C160',
            'user_id_prefix' => 'wx_mini_'
        ],
        'wechat.officialaccount' => [
            'name' => '微信公众号',
            'icon' => 'wechat',
            'color' => '#07C160',
            'user_id_prefix' => 'wx_mp_'
        ],
        'wechat.work' => [
            'name' => '企业微信',
            'icon' => 'work-wechat',
            'color' => '#1485EE',
            'user_id_prefix' => 'wx_work_'
        ],
        'qq.bot' => [
            'name' => 'QQ机器人',
            'icon' => 'qq',
            'color' => '#12B7F5',
            'user_id_prefix' => 'qq_'
        ],
        'dingtalk.bot' => [
            'name' => '钉钉机器人',
            'icon' => 'dingtalk',
            'color' => '#2EABFF',
            'user_id_prefix' => 'dt_'
        ],
        'feishu.bot' => [
            'name' => '飞书机器人',
            'icon' => 'feishu',
            'color' => '#00D6B9',
            'user_id_prefix' => 'fs_'
        ]
    ];

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->messageService = new MessageService();
        $this->sessionService = new SessionService();

        $this->logger = new Logger();
    }

    /**
     * 处理第三方平台Webhook消息
     * @param string $platform 平台标识
     * @param array $webhookData Webhook原始数据
     * @return array 处理结果
     */
    public function handleThirdPartyMessage(string $platform, array $webhookData): array
    {
        try {
            $this->logger->info('接收第三方平台消息', [
                'platform' => $platform,
                'data_keys' => array_keys($webhookData)
            ]);

            // 1. 解析第三方平台消息
            $parsedMessage = $this->parseThirdPartyMessage($platform, $webhookData);
            if (!$parsedMessage) {
                return ['success' => true, 'message' => '无需处理的消息类型'];
            }

            // 2. 转换为VChat消息格式
            $vchatMessage = $this->convertToVchatMessage($platform, $parsedMessage);

            // 3. 获取或创建会话
            $session = $this->getOrCreateSession($platform, $vchatMessage);

            // 4. 保存消息到VChat数据库
            $savedMessage = $this->saveMessageToVchat($vchatMessage, $session);

            // 5. 通过WebSocket推送给客服
            $this->pushMessageToCustomerService($savedMessage, $session);

            // 6. VChat系统会自动处理自动回复和AI功能，无需额外处理

            return [
                'success' => true,
                'message_id' => $savedMessage['id'],
                'session_id' => $session['id'],
                'platform' => $platform
            ];

        } catch (\Exception $e) {
            $this->logger->error('处理第三方平台消息失败', [
                'platform' => $platform,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => $platform
            ];
        }
    }

    /**
     * 发送消息到第三方平台
     * @param string $platform 平台标识
     * @param array $vchatMessage VChat消息数据
     * @return array 发送结果
     */
    public function sendMessageToThirdParty(string $platform, array $vchatMessage): array
    {
        try {
            // 获取第三方平台管理器
            $thirdPartyManager = $this->getThirdPartyManager();
            
            // 从VChat消息中提取第三方平台用户ID
            $extra = json_decode($vchatMessage['extra'] ?? '{}', true);
            $originalUserId = $extra['third_party_user_id'] ?? '';
            
            if (empty($originalUserId)) {
                throw new \Exception('无法获取第三方平台用户ID');
            }

            // 转换消息内容
            $content = $vchatMessage['content'];
            $type = $this->mapVchatTypeToThirdParty($vchatMessage['content_type']);

            // 发送到第三方平台
            $result = $thirdPartyManager->sendMessage($platform, $originalUserId, $content, $type);

            // 更新VChat消息状态
            if ($result['success']) {
                ChatMessage::where('id', $vchatMessage['id'])
                    ->update(['status' => MessageProtocol::STATUS_SENT]);
            }

            return $result;

        } catch (\Exception $e) {
            $this->logger->error('发送消息到第三方平台失败', [
                'platform' => $platform,
                'message_id' => $vchatMessage['id'] ?? null,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => $platform
            ];
        }
    }

    /**
     * 解析第三方平台消息
     * @param string $platform 平台标识
     * @param array $webhookData Webhook数据
     * @return array|null 解析后的消息
     */
    protected function parseThirdPartyMessage(string $platform, array $webhookData): ?array
    {
        switch ($platform) {
            case 'wechat.miniprogram':
            case 'wechat.officialaccount':
            case 'wechat.work':
                return $this->parseWechatMessage($webhookData);
            
            case 'qq.bot':
                return $this->parseQQMessage($webhookData);
            
            case 'dingtalk.bot':
                return $this->parseDingtalkMessage($webhookData);
            
            case 'feishu.bot':
                return $this->parseFeishuMessage($webhookData);
            
            default:
                return $this->parseGenericMessage($webhookData);
        }
    }

    /**
     * 解析微信消息
     * @param array $data 微信Webhook数据
     * @return array|null 解析后的消息
     */
    protected function parseWechatMessage(array $data): ?array
    {
        // 只处理消息类型，忽略事件类型
        if (($data['MsgType'] ?? '') === 'event') {
            return null;
        }

        $msgType = strtolower($data['MsgType'] ?? 'text');
        $content = '';

        // 根据消息类型提取内容
        switch ($msgType) {
            case 'text':
                $content = $data['Content'] ?? '';
                break;
            case 'image':
            case 'voice':
            case 'video':
                $content = $data['MediaId'] ?? '';
                break;
            default:
                $content = $data['Content'] ?? $data['MediaId'] ?? '';
        }

        return [
            'id' => $data['MsgId'] ?? uniqid('msg_'),
            'from_user_id' => $data['FromUserName'] ?? '',
            'to_user_id' => $data['ToUserName'] ?? '',
            'content' => $content,
            'type' => $msgType,
            'timestamp' => $data['CreateTime'] ?? time(),
            'raw_data' => $data
        ];
    }

    /**
     * 解析QQ消息
     * @param array $data QQ Webhook数据
     * @return array|null 解析后的消息
     */
    protected function parseQQMessage(array $data): ?array
    {
        return [
            'id' => $data['message_id'] ?? uniqid('qq_msg_'),
            'from_user_id' => $data['user_id'] ?? '',
            'to_user_id' => $data['self_id'] ?? '',
            'content' => $data['message'] ?? '',
            'type' => 'text',
            'timestamp' => $data['time'] ?? time(),
            'raw_data' => $data
        ];
    }

    /**
     * 解析钉钉消息
     * @param array $data 钉钉Webhook数据
     * @return array|null 解析后的消息
     */
    protected function parseDingtalkMessage(array $data): ?array
    {
        return [
            'id' => $data['msgId'] ?? uniqid('dt_msg_'),
            'from_user_id' => $data['senderId'] ?? '',
            'to_user_id' => $data['robotCode'] ?? '',
            'content' => $data['text']['content'] ?? '',
            'type' => 'text',
            'timestamp' => $data['createAt'] ?? time(),
            'raw_data' => $data
        ];
    }

    /**
     * 解析飞书消息
     * @param array $data 飞书Webhook数据
     * @return array|null 解析后的消息
     */
    protected function parseFeishuMessage(array $data): ?array
    {
        return [
            'id' => $data['message_id'] ?? uniqid('fs_msg_'),
            'from_user_id' => $data['sender']['sender_id']['user_id'] ?? '',
            'to_user_id' => $data['app_id'] ?? '',
            'content' => $data['message']['content'] ?? '',
            'type' => 'text',
            'timestamp' => $data['message']['create_time'] ?? time(),
            'raw_data' => $data
        ];
    }

    /**
     * 解析通用消息
     * @param array $data 通用Webhook数据
     * @return array|null 解析后的消息
     */
    protected function parseGenericMessage(array $data): ?array
    {
        return [
            'id' => $data['id'] ?? uniqid('msg_'),
            'from_user_id' => $data['from_user_id'] ?? $data['user_id'] ?? '',
            'to_user_id' => $data['to_user_id'] ?? $data['bot_id'] ?? '',
            'content' => $data['content'] ?? $data['message'] ?? '',
            'type' => $data['type'] ?? 'text',
            'timestamp' => $data['timestamp'] ?? time(),
            'raw_data' => $data
        ];
    }

    /**
     * 转换为VChat消息格式
     * @param string $platform 平台标识
     * @param array $parsedMessage 解析后的消息
     * @return array VChat格式消息
     */
    protected function convertToVchatMessage(string $platform, array $parsedMessage): array
    {
        $platformInfo = $this->platformConfig[$platform] ?? [];
        
        // 生成VChat用户ID
        $vchatUserId = $this->generateVchatUserId($platform, $parsedMessage['from_user_id']);

        return MessageProtocol::format([
            'id' => $parsedMessage['id'],
            'type' => $this->mapThirdPartyTypeToVchat($parsedMessage['type']),
            'content' => $parsedMessage['content'],
            'from_id' => $vchatUserId,
            'from_type' => 'user',
            'to_id' => 0, // 将由系统分配客服
            'to_type' => 'service',
            'message_type' => MessageProtocol::MESSAGE_CHAT_TYPE,
            'status' => MessageProtocol::STATUS_SENT,
            'timestamp' => $parsedMessage['timestamp'],
            'extra' => [
                'platform' => $platform,
                'platform_name' => $platformInfo['name'] ?? $platform,
                'platform_icon' => $platformInfo['icon'] ?? 'default',
                'platform_color' => $platformInfo['color'] ?? '#666666',
                'third_party_user_id' => $parsedMessage['from_user_id'],
                'third_party_message_id' => $parsedMessage['id'],
                'raw_data' => $parsedMessage['raw_data'] ?? []
            ]
        ]);
    }

    /**
     * 获取或创建会话
     * @param string $platform 平台标识
     * @param array $vchatMessage VChat消息
     * @return array 会话信息
     */
    protected function getOrCreateSession(string $platform, array $vchatMessage): array
    {
        $userId = $vchatMessage['from_id'];
        
        // 查找现有活跃会话
        $session = ChatSession::where([
            ['user_id', '=', $userId],
            ['status', '=', 1], // 活跃状态
            ['source', '=', $platform]
        ])->find();

        if (!$session) {
            // 使用VChat现有的客服分配逻辑
            $serviceId = $this->assignCustomerService();

            if (!$serviceId) {
                // 如果没有可用客服，创建一个待分配的会话
                $serviceId = 0;
            }

            // 创建新会话
            $sessionData = [
                'user_id' => $userId,
                'service_id' => $serviceId,
                'status' => 1,
                'start_time' => time(),
                'source' => $platform,
                'last_message_time' => time()
            ];

            $session = ChatSession::create($sessionData);

            // 如果分配了客服，增加客服当前会话数
            if ($serviceId > 0) {
                CustomerService::where('id', $serviceId)->inc('current_sessions')->save();
            }

            $this->logger->info('创建新的第三方平台会话', [
                'session_id' => $session['id'],
                'platform' => $platform,
                'user_id' => $userId,
                'service_id' => $serviceId
            ]);
        }

        return $session->toArray();
    }

    /**
     * 保存消息到VChat数据库
     * @param array $vchatMessage VChat消息
     * @param array $session 会话信息
     * @return array|null 保存的消息
     */
    protected function saveMessageToVchat(array $vchatMessage, array $session): ?array
    {
        $vchatMessage['session_id'] = $session['id'];
        $vchatMessage['to_id'] = $session['service_id'];

        return $this->messageService->saveMessage($vchatMessage);
    }

    /**
     * 通过WebSocket推送消息给客服
     * @param array $message 消息数据
     * @param array $session 会话信息
     */
    protected function pushMessageToCustomerService(array $message, array $session): void
    {
        try {
            if ($session['service_id'] <= 0) {
                // 没有分配客服，推送到队列或管理员
                $this->pushToWaitingQueue($message, $session);
                return;
            }

            $websocket = app()->make(Websocket::class);
            
            // 推送给指定客服
            $this->messageService->sendMessage($websocket, $message, MessageProtocol::MESSAGE_CHAT_TYPE);

            // 发送新会话通知（如果是新会话的第一条消息）
            $messageCount = ChatMessage::where('session_id', $session['id'])->count();
            if ($messageCount === 1) {
                $this->sendNewSessionNotification($websocket, $session, $message);
            }

            $this->logger->info('推送第三方平台消息到客服', [
                'message_id' => $message['id'],
                'session_id' => $session['id'],
                'service_id' => $session['service_id'],
                'platform' => $message['extra']['platform'] ?? 'unknown'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('推送消息到客服失败', [
                'message_id' => $message['id'] ?? null,
                'session_id' => $session['id'] ?? null,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 推送到等待队列
     * @param array $message 消息数据
     * @param array $session 会话信息
     */
    protected function pushToWaitingQueue(array $message, array $session): void
    {
        try {
            $websocket = app()->make(Websocket::class);
            
            // 发送到管理员或队列管理系统
            $queueMessage = MessageProtocol::createSystemMessage(
                '新的第三方平台用户等待客服接待',
                [
                    'type' => 'waiting_queue',
                    'session_id' => $session['id'],
                    'platform' => $message['extra']['platform'] ?? 'unknown',
                    'platform_name' => $message['extra']['platform_name'] ?? 'Unknown Platform',
                    'user_message' => $message['content']
                ]
            );

            // 广播给所有在线客服
            $websocket->emit(MessageProtocol::MESSAGE_QUEUE_NOTIFICATION, $queueMessage);

            $this->logger->info('推送第三方平台消息到等待队列', [
                'session_id' => $session['id'],
                'platform' => $message['extra']['platform'] ?? 'unknown'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('推送到等待队列失败', [
                'session_id' => $session['id'] ?? null,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送新会话通知
     * @param Websocket $websocket WebSocket实例
     * @param array $session 会话信息
     * @param array $message 消息数据
     */
    protected function sendNewSessionNotification(Websocket $websocket, array $session, array $message): void
    {
        $extra = $message['extra'];
        
        $notification = MessageProtocol::createSystemMessage(
            "新的{$extra['platform_name']}用户开始咨询",
            [
                'type' => 'new_session',
                'session_id' => $session['id'],
                'platform' => $extra['platform'],
                'platform_name' => $extra['platform_name'],
                'platform_icon' => $extra['platform_icon'],
                'platform_color' => $extra['platform_color'],
                'user_id' => $session['user_id'],
                'first_message' => $message['content']
            ]
        );

        $websocket->to("service_{$session['service_id']}")
            ->emit(MessageProtocol::MESSAGE_NEW_SESSION_TYPE, $notification);
    }



    /**
     * 生成VChat用户ID
     * @param string $platform 平台标识
     * @param string $thirdPartyUserId 第三方平台用户ID
     * @return int VChat用户ID
     */
    protected function generateVchatUserId(string $platform, string $thirdPartyUserId): int
    {
        $prefix = $this->platformConfig[$platform]['user_id_prefix'] ?? 'tp_';
        $cacheKey = "vchat_user_mapping:{$platform}:{$thirdPartyUserId}";
        
        // 从缓存获取映射关系
        $vchatUserId = Cache::get($cacheKey);
        
        if (!$vchatUserId) {
            // 生成新的VChat用户ID（使用哈希确保唯一性）
            $vchatUserId = abs(crc32($prefix . $thirdPartyUserId)) % 1000000 + 1000000;
            
            // 缓存映射关系
            Cache::set($cacheKey, $vchatUserId, 86400 * 30); // 缓存30天
        }
        
        return $vchatUserId;
    }

    /**
     * 映射第三方平台消息类型到VChat类型
     * @param string $thirdPartyType 第三方平台类型
     * @return string VChat类型
     */
    protected function mapThirdPartyTypeToVchat(string $thirdPartyType): string
    {
        $mapping = [
            'text' => MessageProtocol::TYPE_TEXT,
            'image' => MessageProtocol::TYPE_IMAGE,
            'voice' => MessageProtocol::TYPE_VOICE,
            'video' => MessageProtocol::TYPE_VIDEO,
            'file' => MessageProtocol::TYPE_FILE,
            'location' => MessageProtocol::TYPE_TEXT, // 位置消息转为文本
            'link' => MessageProtocol::TYPE_TEXT, // 链接消息转为文本
        ];

        return $mapping[$thirdPartyType] ?? MessageProtocol::TYPE_TEXT;
    }

    /**
     * 映射VChat消息类型到第三方平台类型
     * @param string $vchatType VChat类型
     * @return string 第三方平台类型
     */
    protected function mapVchatTypeToThirdParty(string $vchatType): string
    {
        $mapping = [
            MessageProtocol::TYPE_TEXT => 'text',
            MessageProtocol::TYPE_IMAGE => 'image',
            MessageProtocol::TYPE_VOICE => 'voice',
            MessageProtocol::TYPE_VIDEO => 'video',
            MessageProtocol::TYPE_FILE => 'file',
        ];

        return $mapping[$vchatType] ?? 'text';
    }

    /**
     * 获取第三方平台管理器
     * @return object 第三方平台管理器实例
     */
    protected function getThirdPartyManager()
    {
        // 这里返回第三方平台管理器实例
        // 由于我们现在是集成模式，这个方法主要用于发送消息到第三方平台
        return new \app\common\third_official_link\core\CustomerServiceManager();
    }

    /**
     * 分配客服（使用VChat现有逻辑）
     * @return int 客服ID
     */
    protected function assignCustomerService(): int
    {
        // 简单的客服分配逻辑：选择当前会话数最少的在线客服
        $service = CustomerService::where('status', 1)
            ->orderBy('current_sessions', 'asc')
            ->first();

        if ($service) {
            // 增加当前会话数
            $service->inc('current_sessions')->save();
            return $service['id'];
        }

        // 如果没有在线客服，返回0表示待分配
        return 0;
    }
}
