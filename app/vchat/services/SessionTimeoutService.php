<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\model\ChatSession;
use app\vchat\core\MessageProtocol;
use app\vchat\utils\Logger;
use think\facade\Cache;
use think\facade\Config;
use think\swoole\Websocket;

/**
 * 会话超时管理服务
 */
class SessionTimeoutService
{
    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var array 配置信息
     */
    protected $config;

    /**
     * @var ChatSession
     */
    protected $sessionModel;

    protected $customerServiceModel;

    /**
     * Redis前缀
     */
    const REDIS_PREFIX = 'vchat:timeout:';
    const SESSION_ACTIVITY_PREFIX = 'session:';
    const USER_ACTIVITY_PREFIX = 'user:';
    const SERVICE_ACTIVITY_PREFIX = 'service:';
    const WARNING_SENT_PREFIX = 'warning:';

    public function __construct()
    {
        $this->logger = new Logger();
        $this->config = Config::get('vchat.session_timeout');
        $this->sessionModel = new ChatSession();
        $this->customerServiceModel = new \app\model\CustomerService();
    }

    /**
     * 记录用户活动
     * @param int $userId 用户ID
     * @param int $sessionId 会话ID
     */
    public function recordUserActivity(int $userId, int $sessionId): void
    {
        $now = time();
        $key = self::REDIS_PREFIX . self::USER_ACTIVITY_PREFIX . $userId;
        
        Cache::set($key, [
            'session_id' => $sessionId,
            'last_activity' => $now,
            'type' => 'user'
        ], $this->config['user_inactive_timeout'] + 300);
        
        // 同时更新会话活动时间
        $this->recordSessionActivity($sessionId, 'user', $now);
    }

    /**
     * 记录客服活动
     * @param int $serviceId 客服ID
     * @param int $sessionId 会话ID
     */
    public function recordServiceActivity(int $serviceId, int $sessionId): void
    {
        $now = time();
        $key = self::REDIS_PREFIX . self::SERVICE_ACTIVITY_PREFIX . $serviceId;
        
        Cache::set($key, [
            'session_id' => $sessionId,
            'last_activity' => $now,
            'type' => 'service'
        ], $this->config['service_inactive_timeout'] + 300);
        
        // 同时更新会话活动时间
        $this->recordSessionActivity($sessionId, 'service', $now);
    }

    /**
     * 记录会话活动
     * @param int $sessionId 会话ID
     * @param string $actorType 活动者类型：user/service
     * @param int $timestamp 时间戳
     */
    protected function recordSessionActivity(int $sessionId, string $actorType, int $timestamp): void
    {
        $key = self::REDIS_PREFIX . self::SESSION_ACTIVITY_PREFIX . $sessionId;
        $activity = Cache::get($key, []);
        
        $activity['session_id'] = $sessionId;
        $activity['last_activity'] = $timestamp;
        $activity['last_' . $actorType . '_activity'] = $timestamp;
        
        if (!isset($activity['start_time'])) {
            $activity['start_time'] = $timestamp;
        }
        
        Cache::set($key, $activity, $this->config['max_duration'] + 300);
    }

    /**
     * 检查会话超时
     * @param int $sessionId 会话ID
     * @return array 超时检查结果
     */
    public function checkSessionTimeout(int $sessionId): array
    {
        $key = self::REDIS_PREFIX . self::SESSION_ACTIVITY_PREFIX . $sessionId;
        $activity = Cache::get($key);
        
        if (!$activity) {
            return ['timeout' => false, 'reason' => 'no_activity_record'];
        }
        
        $now = time();
        $result = [
            'timeout' => false,
            'reason' => '',
            'warning' => false,
            'remaining_time' => 0
        ];
        
        // 检查会话最大持续时间
        if (isset($activity['start_time'])) {
            $duration = $now - $activity['start_time'];
            if ($duration >= $this->config['max_duration']) {
                $result['timeout'] = true;
                $result['reason'] = 'max_duration_exceeded';
                return $result;
            }
            
            // 检查是否需要发送持续时间警告
            $remainingDuration = $this->config['max_duration'] - $duration;
            if ($remainingDuration <= $this->config['warning_before_timeout']) {
                $result['warning'] = true;
                $result['remaining_time'] = $remainingDuration;
            }
        }
        
        // 检查用户无响应超时
        if (isset($activity['last_user_activity'])) {
            $userInactiveTime = $now - $activity['last_user_activity'];
            if ($userInactiveTime >= $this->config['user_inactive_timeout']) {
                $result['timeout'] = true;
                $result['reason'] = 'user_inactive_timeout';
                return $result;
            }
            
            // 检查是否需要发送用户无响应警告
            $remainingUserTime = $this->config['user_inactive_timeout'] - $userInactiveTime;
            if ($remainingUserTime <= $this->config['warning_before_timeout']) {
                $result['warning'] = true;
                $result['remaining_time'] = min($result['remaining_time'] ?: PHP_INT_MAX, $remainingUserTime);
            }
        }
        
        // 检查客服无响应超时
        if (isset($activity['last_service_activity'])) {
            $serviceInactiveTime = $now - $activity['last_service_activity'];
            if ($serviceInactiveTime >= $this->config['service_inactive_timeout']) {
                $result['timeout'] = true;
                $result['reason'] = 'service_inactive_timeout';
                return $result;
            }
            
            // 检查是否需要发送客服无响应警告
            $remainingServiceTime = $this->config['service_inactive_timeout'] - $serviceInactiveTime;
            if ($remainingServiceTime <= $this->config['warning_before_timeout']) {
                $result['warning'] = true;
                $result['remaining_time'] = min($result['remaining_time'] ?: PHP_INT_MAX, $remainingServiceTime);
            }
        }
        
        return $result;
    }

    /**
     * 处理会话超时
     * @param int $sessionId 会话ID
     * @param string $reason 超时原因
     * @return bool
     */
    public function handleSessionTimeout(int $sessionId, string $reason): bool
    {
        try {
            // 获取会话信息
            $session = $this->sessionModel->find($sessionId);
            if (!$session) {
                $this->logger->warning('会话不存在', ['session_id' => $sessionId]);
                return false;
            }

            $this->sessionModel->startTrans();

            $dec = $this->customerServiceModel->decCurrentSession($session['service_id']);
            
            // 更新会话状态为超时结束
            $out = $this->sessionModel->where('id', $sessionId)->update([
                'status' => 3, // 3表示超时结束
                'end_time' => time(),
                'duration' => time() - $session['start_time']
            ]);

            if ($dec === false || $out === false) {
                $this->sessionModel->rollback();
                $this->logger->error('更新会话状态失败', [
                    'session_id' => $sessionId,
                    'reason' => $reason,
                    'dec' => $dec,
                    'out' => $out
                ]);
                return false;
            }

            $this->sessionModel->commit();
            
            // 发送超时通知
            $this->sendTimeoutNotification($session->toArray(), $reason);
            
            // 清理活动记录
            $this->cleanupSessionActivity($sessionId);
            
            $this->logger->info('会话超时处理完成', [
                'session_id' => $sessionId,
                'reason' => $reason,
                'user_id' => $session['user_id'],
                'service_id' => $session['service_id']
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('处理会话超时失败', [
                'session_id' => $sessionId,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送超时警告
     * @param int $sessionId 会话ID
     * @param int $remainingTime 剩余时间（秒）
     * @return bool
     */
    public function sendTimeoutWarning(int $sessionId, int $remainingTime): bool
    {
        try {
            // 检查是否已发送过警告（避免重复发送）
            $warningKey = self::REDIS_PREFIX . self::WARNING_SENT_PREFIX . $sessionId;
            if (Cache::has($warningKey)) {
                return true;
            }
            
            // 获取会话信息
            $session = $this->sessionModel->find($sessionId);
            if (!$session) {
                return false;
            }
            
            // 构造警告消息
            $minutes = ceil($remainingTime / 60);
            $message = [
                'message_type' => MessageProtocol::MESSAGE_TIMEOUT_WARNING,
                'message' => "会话将在 {$minutes} 分钟后超时，请及时回复以保持会话活跃",
                'remaining_time' => $remainingTime,
                'session_id' => $sessionId
            ];
            
            // 发送给用户和客服
            $this->sendNotificationToSession($session->toArray(), $message);
            
            // 标记已发送警告
            Cache::set($warningKey, true, $this->config['warning_before_timeout']);
            
            $this->logger->info('发送超时警告', [
                'session_id' => $sessionId,
                'remaining_time' => $remainingTime
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('发送超时警告失败', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送超时通知
     * @param array $session 会话信息
     * @param string $reason 超时原因
     */
    public function sendTimeoutNotification(array $session, string $reason): void
    {
        $reasonMessages = [
            'user_inactive_timeout' => '用户长时间无响应，会话已自动结束',
            'service_inactive_timeout' => '客服长时间无响应，会话已自动结束',
            'max_duration_exceeded' => '会话时间过长，已自动结束'
        ];
        
        $message = [
            'message_type' => MessageProtocol::MESSAGE_SESSION_TIMEOUT,
            'message' => $reasonMessages[$reason] ?? '会话已超时结束',
            'reason' => $reason,
            'session_id' => $session['id']
        ];
        
        $this->sendNotificationToSession($session, $message);
    }

    /**
     * 向会话参与者发送通知
     * @param array $session 会话信息
     * @param array $message 消息内容
     */
    protected function sendNotificationToSession(array $session, array $message): void
    {
        try {
            $this->logger->info('准备发送会话通知', [
                'session_id' => $session['id'],
                'user_id' => $session['user_id'],
                'service_id' => $session['service_id'],
                'message_type' => $message['message_type']
            ]);
            
            // 获取WebSocket实例
            $websocket = app()->make(Websocket::class);
            $this->logger->info('WebSocket实例获取成功');
            
            // 发送给用户
            $this->logger->info('准备发送消息给用户', ['user_room' => 'user_' . $session['user_id']]);
            $websocket->to('user_' . $session['user_id'])->emit(MessageProtocol::MESSAGE_NOTICE, $message);
            $this->logger->info('用户消息发送完成');
            
            // 发送给客服
            $this->logger->info('准备发送消息给客服', ['service_room' => 'service_' . $session['service_id']]);
            $websocket->to('service_' . $session['service_id'])->emit(MessageProtocol::MESSAGE_NOTICE, $message);
            $this->logger->info('客服消息发送完成');
            
            $this->logger->info('发送会话通知成功', [
                'session_id' => $session['id'],
                'user_id' => $session['user_id'],
                'service_id' => $session['service_id'],
                'message_type' => $message['message_type']
            ]);
        } catch (\Exception $e) {
            $this->logger->error('发送会话通知失败', [
                'session_id' => $session['id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 清理会话活动记录
     * @param int $sessionId 会话ID
     */
    protected function cleanupSessionActivity(int $sessionId): void
    {
        $keys = [
            self::REDIS_PREFIX . self::SESSION_ACTIVITY_PREFIX . $sessionId,
            self::REDIS_PREFIX . self::WARNING_SENT_PREFIX . $sessionId
        ];
        
        foreach ($keys as $key) {
            Cache::delete($key);
        }
    }

    /**
     * 批量检查所有活跃会话的超时状态（定时任务调用）
     */
    public function checkAllSessionsTimeout(): array
    {
        try {
            // 获取所有活跃会话
            $activeSessions = $this->sessionModel
                ->where('status', 1) // 1表示进行中
                ->field('id,user_id,service_id,start_time')
                ->select()
                ->toArray();
            
            $timeoutCount = 0;
            $warningCount = 0;
            
            foreach ($activeSessions as $session) {
                $timeoutResult = $this->checkSessionTimeout($session['id']);
                $this->logger->info('会话超时检查', [
                    'session_id' => $session['id'],
                    'timeout' => $timeoutResult['timeout'] ?? false,
                    'warning' => $timeoutResult['warning'] ?? false,
                    'remaining_time' => $timeoutResult['remaining_time'] ?? false,
                    'timeoutResult' => $timeoutResult
                ]);
                
                if ($timeoutResult['timeout']) {
                    $this->handleSessionTimeout($session['id'], $timeoutResult['reason']);
                    $timeoutCount++;
                } else if (isset($timeoutResult['warning']) && $timeoutResult['warning'] && $timeoutResult['remaining_time'] > 0) {
                    $this->sendTimeoutWarning($session['id'], $timeoutResult['remaining_time']);
                    $warningCount++;
                }
            }
            
            $this->logger->info('批量超时检查完成', [
                'total_sessions' => count($activeSessions),
                'timeout_sessions' => $timeoutCount,
                'warning_sessions' => $warningCount
            ]);

            return [
                'total_sessions' => count($activeSessions),
                'timeout_sessions' => $timeoutCount,
                'warning_sessions' => $warningCount
            ];
        } catch (\Exception $e) {
            $this->logger->error('批量超时检查失败', [
                'error' => $e->getMessage()
            ]);
        }

        return [];
    }

    /**
     * 获取会话剩余时间信息
     * @param int $sessionId 会话ID
     * @return array
     */
    public function getSessionTimeInfo(int $sessionId): array
    {
        $key = self::REDIS_PREFIX . self::SESSION_ACTIVITY_PREFIX . $sessionId;
        $activity = Cache::get($key);
        
        if (!$activity) {
            return [
                'active' => false,
                'remaining_duration' => 0,
                'user_inactive_time' => 0,
                'service_inactive_time' => 0
            ];
        }
        
        $now = time();
        $info = ['active' => true];
        
        // 计算剩余持续时间
        if (isset($activity['start_time'])) {
            $elapsed = $now - $activity['start_time'];
            $info['remaining_duration'] = max(0, $this->config['max_duration'] - $elapsed);
        }
        
        // 计算用户无响应时间
        if (isset($activity['last_user_activity'])) {
            $info['user_inactive_time'] = $now - $activity['last_user_activity'];
        }
        
        // 计算客服无响应时间
        if (isset($activity['last_service_activity'])) {
            $info['service_inactive_time'] = $now - $activity['last_service_activity'];
        } else {
            $info['service_inactive_time'] = 0;
        }
        
        return $info;
    }

    /**
     * 延长会话时间（特殊情况下使用）
     * @param int $sessionId 会话ID
     * @param int $extraTime 延长时间（秒）
     * @return bool
     */
    public function extendSessionTime(int $sessionId, int $extraTime): bool
    {
        try {
            $key = self::REDIS_PREFIX . self::SESSION_ACTIVITY_PREFIX . $sessionId;
            $activity = Cache::get($key);
            
            if (!$activity) {
                return false;
            }
            
            // 调整开始时间，相当于延长了会话时间
            $activity['start_time'] -= $extraTime;
            
            Cache::set($key, $activity, $this->config['max_duration'] + 300);
            
            $this->logger->info('会话时间延长', [
                'session_id' => $sessionId,
                'extra_time' => $extraTime
            ]);
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('延长会话时间失败', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 清理活动记录
     * @param int $userId 用户ID
     * @param int $serviceId 客服ID
     */
    public function clearActivity(int $userId, int $serviceId): void
    {
        try {
            // 清理用户活动记录
            $userKey = self::REDIS_PREFIX . self::USER_ACTIVITY_PREFIX . $userId;
            Cache::delete($userKey);
            
            // 清理客服活动记录
            $serviceKey = self::REDIS_PREFIX . self::SERVICE_ACTIVITY_PREFIX . $serviceId;
            Cache::delete($serviceKey);
            
            $this->logger->info('清理活动记录', [
                'user_id' => $userId,
                'service_id' => $serviceId
            ]);
        } catch (\Exception $e) {
            $this->logger->error('清理活动记录失败', [
                'user_id' => $userId,
                'service_id' => $serviceId,
                'error' => $e->getMessage()
            ]);
        }
    }
}