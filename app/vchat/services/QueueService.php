<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\vchat\core\MessageProtocol;
use app\vchat\utils\Log;
use app\vchat\utils\Logger;
use think\facade\Cache;
use think\facade\Config;
use think\swoole\Websocket;

/**
 * 队列管理服务
 */
class QueueService
{
    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var array 配置信息
     */
    protected $config;

    /**
     * Redis前缀
     */
    const REDIS_PREFIX = 'vchat:queue:';
    const GLOBAL_QUEUE_KEY = 'global';
    const SERVICE_QUEUE_PREFIX = 'service:';
    const USER_QUEUE_INFO_PREFIX = 'user:';
    const NOTIFICATION_HISTORY_PREFIX = 'notification:';
    const LAST_NOTIFICATION_PREFIX = 'last_notify:';

    public function __construct()
    {
        $this->logger = new Logger();
        $this->config = Config::get('vchat');
    }

    /**
     * 用户加入排队
     * @param int $userId 用户ID
     * @param int|null $serviceId 指定客服ID（可选）
     * @param int $priority 优先级（默认1，VIP用户可设置更高）
     * @return array 排队信息
     */
    public function joinQueue(int $userId, ?int $serviceId = null, int $priority = 1): array
    {
        try {
            // 检查用户是否已在队列中
            if ($this->isUserInQueue($userId)) {
                return $this->getUserQueueInfo($userId);
            }

            // 检查全局队列是否已满
            $globalQueueSize = $this->getGlobalQueueSize();
            if ($globalQueueSize >= $this->config['queue']['max_global_queue']) {
                throw new \Exception('队列已满，请稍后再试');
            }

            // 如果指定了客服，检查该客服的队列
            if ($serviceId) {
                $serviceQueueSize = $this->getServiceQueueSize($serviceId);
                if ($serviceQueueSize >= $this->config['queue']['max_per_service_queue']) {
                    throw new \Exception('该客服队列已满，请选择其他客服或等待');
                }
            }

            // 计算预估等待时间
            $estimatedWaitTime = $this->calculateWaitTime($serviceId, $priority);

            // 创建队列项
            $queueItem = [
                'user_id' => $userId,
                'service_id' => $serviceId,
                'priority' => $priority,
                'join_time' => time(),
                'estimated_wait_time' => $estimatedWaitTime,
                'position' => 0, // 将在添加到队列时计算
            ];

            // 添加到全局队列
            $this->addToGlobalQueue($queueItem);

            // 如果指定了客服，添加到客服队列
            if ($serviceId) {
                $this->addToServiceQueue($serviceId, $queueItem);
            }

            // 保存用户队列信息
            $this->saveUserQueueInfo($userId, $queueItem);

            // 更新队列位置
            $queueItem['position'] = $this->getUserQueuePosition($userId);

            // 发送排队通知
            if ($this->config['notification']['queue_notification']) {
                $this->sendQueueNotification($userId, $queueItem);
            }

            $this->logger->info('用户加入排队', [
                'user_id' => $userId,
                'service_id' => $serviceId,
                'priority' => $priority,
                'estimated_wait_time' => $estimatedWaitTime,
                'position' => $queueItem['position']
            ]);

            return $queueItem;
        } catch (\Exception $e) {
            $this->logger->error('用户加入排队失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 用户离开排队
     * @param int $userId 用户ID
     * @return bool
     */
    public function leaveQueue(int $userId): bool
    {
        try {
            $userInfo = $this->getUserQueueInfo($userId);
            Log::info('[用户离开排队] :' . json_encode($userInfo));
            if (!$userInfo) {
                return true; // 用户不在队列中
            }

            // 从全局队列移除
            $this->removeFromGlobalQueue($userId);

            // 从客服队列移除
            if (isset($userInfo['service_id']) && $userInfo['service_id']) {
                $this->removeFromServiceQueue($userInfo['service_id'], $userId);
            }

            // 删除用户队列信息
            $this->deleteUserQueueInfo($userId);

            $this->logger->info('用户离开排队', [
                'user_id' => $userId,
                'service_id' => $userInfo['service_id'] ?? null
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error('用户离开排队失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取下一个用户（供客服调用）
     * @param int $serviceId 客服ID
     * @return array|null 用户信息
     */
    public function getNextUser(int $serviceId): ?array
    {
        try {
            // 优先从指定客服的队列获取
            $userId = $this->getNextFromServiceQueue($serviceId);
            
            // 如果客服队列为空，从全局队列获取
            if (!$userId) {
                $userId = $this->getNextFromGlobalQueue();
            }

            if (!$userId) {
                return null;
            }

            $userInfo = $this->getUserQueueInfo($userId);
            if (!$userInfo) {
                return null;
            }

            // 从队列中移除用户
            $this->leaveQueue($userId);

            $this->logger->info('客服获取下一个用户', [
                'service_id' => $serviceId,
                'user_id' => $userId
            ]);

            return $userInfo;
        } catch (\Exception $e) {
            $this->logger->error('获取下一个用户失败', [
                'service_id' => $serviceId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 计算等待时间
     * @param int|null $serviceId 客服ID
     * @param int $priority 优先级
     * @return int 预估等待时间（秒）
     */
    public function calculateWaitTime(?int $serviceId, int $priority): int
    {
        $config = $this->config['waiting'];
        $baseTime = $config['base_time'];
        
        // 获取排队人数
        $queueSize = $serviceId ? 
            $this->getServiceQueueSize($serviceId) : 
            $this->getGlobalQueueSize();
        
        // 基于排队人数计算等待时间
        $waitTime = $baseTime + ($queueSize * $config['increment_per_user']);
        
        // 如果指定了客服，考虑客服负载
        if ($serviceId) {
            $serviceLoad = $this->getServiceLoad($serviceId);
            $loadFactor = $config['load_factor'];
            $waitTime += (int)($waitTime * $serviceLoad * $loadFactor);
        }
        
        // VIP用户减少等待时间
        if ($priority > 1) {
            $waitTime = (int)($waitTime / $priority);
        }
        
        // 限制在最小和最大时间范围内
        $waitTime = max($config['min_time'], min($config['max_time'], $waitTime));
        
        return $waitTime;
    }

    /**
     * 获取客服负载（0-1之间的值）
     * @param int $serviceId 客服ID
     * @return float
     */
    protected function getServiceLoad(int $serviceId): float
    {
        // 这里可以根据客服的当前会话数、响应时间等计算负载
        // 暂时返回一个基于队列长度的简单计算
        $queueSize = $this->getServiceQueueSize($serviceId);
        $maxQueue = $this->config['queue']['max_per_service_queue'];
        
        return min(1.0, $queueSize / $maxQueue);
    }

    /**
     * 检查用户是否在队列中
     * @param int $userId 用户ID
     * @return bool
     */
    public function isUserInQueue(int $userId): bool
    {
        return Cache::has(self::REDIS_PREFIX . self::USER_QUEUE_INFO_PREFIX . $userId);
    }

    /**
     * 获取用户队列信息
     * @param int $userId 用户ID
     * @return array|null
     */
    public function getUserQueueInfo(int $userId): ?array
    {
        $info = Cache::get(self::REDIS_PREFIX . self::USER_QUEUE_INFO_PREFIX . $userId);
        Log::info('[获取用户队列信息] :'. json_encode(['userId' => $userId, 'info' => $info]));
        return $info ? json_decode($info, true) : null;
    }

    /**
     * 获取用户在队列中的位置
     * @param int $userId 用户ID
     * @return int
     */
    public function getUserQueuePosition(int $userId): int
    {
        $userInfo = $this->getUserQueueInfo($userId);
        if (!$userInfo) {
            return 0;
        }

        $queueKey = $userInfo['service_id'] ? 
            self::REDIS_PREFIX . self::SERVICE_QUEUE_PREFIX . $userInfo['service_id'] :
            self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY;

        // 这里简化处理，实际应该根据优先级和加入时间排序
        $queue = Cache::get($queueKey, []);
        $position = 1;
        
        foreach ($queue as $index => $item) {
            if ($item['user_id'] == $userId) {
                return $position;
            }
            $position++;
        }
        
        return 0;
    }

    /**
     * 获取全局队列大小
     * @return int
     */
    public function getGlobalQueueSize(): int
    {
        $queue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
        return count($queue);
    }

    /**
     * 获取客服队列大小
     * @param int $serviceId 客服ID
     * @return int
     */
    public function getServiceQueueSize(int $serviceId): int
    {
        $queue = Cache::get(self::REDIS_PREFIX . self::SERVICE_QUEUE_PREFIX . $serviceId, []);
        return count($queue);
    }

    /**
     * 添加到全局队列
     * @param array $queueItem 队列项
     */
    protected function addToGlobalQueue(array $queueItem): void
    {
        $queue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
        $queue[] = $queueItem;
        Cache::set(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, $queue, 3600);
    }

    /**
     * 添加到客服队列
     * @param int $serviceId 客服ID
     * @param array $queueItem 队列项
     */
    protected function addToServiceQueue(int $serviceId, array $queueItem): void
    {
        $queueKey = self::REDIS_PREFIX . self::SERVICE_QUEUE_PREFIX . $serviceId;
        $queue = Cache::get($queueKey, []);
        $queue[] = $queueItem;
        Cache::set($queueKey, $queue, 3600);
    }

    /**
     * 保存用户队列信息
     * @param int $userId 用户ID
     * @param array $queueItem 队列项
     */
    protected function saveUserQueueInfo(int $userId, array $queueItem): void
    {
        $key = self::REDIS_PREFIX . self::USER_QUEUE_INFO_PREFIX . $userId;
        Cache::set($key, json_encode($queueItem), 3600);
    }

    /**
     * 从全局队列移除
     * @param int $userId 用户ID
     */
    protected function removeFromGlobalQueue(int $userId): void
    {
        $queue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
        Log::info('[删除全局队列信息前] :' . json_encode(['userId' => $userId, 'queue' => json_encode($queue)]));
        $queue = array_filter($queue, function($item) use ($userId) {
            return $item['user_id'] != $userId;
        });
        Cache::set(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, array_values($queue), 3600);
        Log::info('[删除全局队列信息后] :' . json_encode(['userId' => $userId, 'queue' => array_values($queue)]));
    }

    /**
     * 从客服队列移除
     * @param int $serviceId 客服ID
     * @param int $userId 用户ID
     */
    protected function removeFromServiceQueue(int $serviceId, int $userId): void
    {
        $queueKey = self::REDIS_PREFIX . self::SERVICE_QUEUE_PREFIX . $serviceId;
        $queue = Cache::get($queueKey, []);
        $queue = array_filter($queue, function($item) use ($userId) {
            return $item['user_id'] != $userId;
        });
        Cache::set($queueKey, array_values($queue), 3600);
    }

    /**
     * 删除用户队列信息
     * @param int $userId 用户ID
     */
    protected function deleteUserQueueInfo(int $userId): void
    {
        $key = self::REDIS_PREFIX . self::USER_QUEUE_INFO_PREFIX . $userId;
        $deleteSuccess = Cache::delete($key);
        Log::info('[删除用户队列信息] :' . json_encode(['userId' => $userId, 'key' => $key, 'success' => $deleteSuccess]));
    }

    /**
     * 从客服队列获取下一个用户
     * @param int $serviceId 客服ID
     * @return int|null 用户ID
     */
    protected function getNextFromServiceQueue(int $serviceId): ?int
    {
        $queueKey = self::REDIS_PREFIX . self::SERVICE_QUEUE_PREFIX . $serviceId;
        $queue = Cache::get($queueKey, []);
        
        if (empty($queue)) {
            return null;
        }
        
        // 按优先级和时间排序
        usort($queue, function($a, $b) {
            if ($a['priority'] != $b['priority']) {
                return $b['priority'] - $a['priority']; // 优先级高的在前
            }
            return $a['join_time'] - $b['join_time']; // 时间早的在前
        });
        
        return $queue[0]['user_id'] ?? null;
    }

    /**
     * 从全局队列获取下一个用户
     * @return int|null 用户ID
     */
    protected function getNextFromGlobalQueue(): ?int
    {
        $queue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
        
        if (empty($queue)) {
            return null;
        }
        
        // 按优先级和时间排序
        usort($queue, function($a, $b) {
            if ($a['priority'] != $b['priority']) {
                return $b['priority'] - $a['priority']; // 优先级高的在前
            }
            return $a['join_time'] - $b['join_time']; // 时间早的在前
        });
        
        return $queue[0]['user_id'] ?? null;
    }

    /**
     * 更新队列状态（定时任务调用）
     */
    public function updateQueueStatus(): void
    {
        try {
            // 更新所有用户的队列位置和等待时间
            $globalQueue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
            
            foreach ($globalQueue as $index => $item) {
                $userId = $item['user_id'];
                $userInfo = $this->getUserQueueInfo($userId);
                
                if ($userInfo) {
                    // 更新位置
                    $userInfo['position'] = $this->getUserQueuePosition($userId);
                    
                    // 重新计算等待时间
                    $userInfo['estimated_wait_time'] = $this->calculateWaitTime(
                        $userInfo['service_id'], 
                        $userInfo['priority']
                    );
                    
                    $this->saveUserQueueInfo($userId, $userInfo);

                    $this->sendQueueNotification($userId, $userInfo);
                }
            }
            
            $this->logger->info('队列状态更新完成', [
                'queue_size' => count($globalQueue)
            ]);
        } catch (\Exception $e) {
            $this->logger->error('队列状态更新失败', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取过期的用户列表
     * @param int $maxWaitTime 最大等待时间（秒）
     * @return array 过期用户ID列表
     */
    public function getExpiredUsers(int $maxWaitTime): array
    {
        $expiredUsers = [];
        $currentTime = time();
        
        try {
            // 检查全局队列中的过期用户
            $globalQueue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
            
            foreach ($globalQueue as $item) {
                if (($currentTime - $item['join_time']) > $maxWaitTime) {
                    $expiredUsers[] = $item['user_id'];
                }
            }
            
            $this->logger->info('获取过期用户列表', [
                'max_wait_time' => $maxWaitTime,
                'expired_count' => count($expiredUsers)
            ]);
        } catch (\Exception $e) {
            $this->logger->error('获取过期用户列表失败', [
                'error' => $e->getMessage()
            ]);
        }
        
        return $expiredUsers;
    }

    /**
     * 从队列中移除用户（公共方法）
     * @param int $userId 用户ID
     * @return bool
     */
    public function removeFromQueue(int $userId): bool
    {
        return $this->leaveQueue($userId);
    }

    /**
     * 清理无效的队列数据
     */
    public function cleanupInvalidData(): void
    {
        try {
            // 清理全局队列中的无效数据
            $globalQueue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
            $validQueue = [];
            
            foreach ($globalQueue as $item) {
                // 检查用户队列信息是否存在
                if ($this->getUserQueueInfo($item['user_id'])) {
                    $validQueue[] = $item;
                }
            }
            
            Cache::set(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, $validQueue, 3600);
            
            // 清理客服队列中的无效数据
            $this->cleanupServiceQueues();
            
            $this->logger->info('清理无效队列数据完成', [
                'original_count' => count($globalQueue),
                'valid_count' => count($validQueue),
                'cleaned_count' => count($globalQueue) - count($validQueue)
            ]);
        } catch (\Exception $e) {
            $this->logger->error('清理无效队列数据失败', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新用户等待时间
     * @param int $userId 用户ID
     * @param int $waitTime 等待时间（秒）
     */
    public function updateUserWaitTime(int $userId, int $waitTime): void
    {
        try {
            $userInfo = $this->getUserQueueInfo($userId);
            if ($userInfo) {
                $userInfo['estimated_wait_time'] = $waitTime;
                $userInfo['updated_at'] = time();
                $this->saveUserQueueInfo($userId, $userInfo);
                
                $this->logger->debug('更新用户等待时间', [
                    'user_id' => $userId,
                    'wait_time' => $waitTime
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('更新用户等待时间失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 清理客服队列中的无效数据
     */
    protected function cleanupServiceQueues(): void
    {
        // 这里可以根据实际需要实现客服队列的清理逻辑
        // 例如：遍历所有客服队列，移除无效的用户记录
        $this->logger->debug('客服队列清理完成');
    }

    /**
     * 获取队列信息（供定时任务使用）
     * @return array
     */
    public function getQueueInfo(): array
    {
        $globalQueue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
        
        $users = [];
        foreach ($globalQueue as $item) {
            $users[] = [
                'user_id' => $item['user_id'],
                'service_id' => $item['service_id'],
                'join_time' => $item['join_time'],
                'priority' => $item['priority']
            ];
        }
        
        return [
            'length' => count($globalQueue),
            'users' => $users,
            'estimated_wait_time' => $this->calculateAverageWaitTime()
        ];
    }

    /**
     * 计算平均等待时间
     * @return int
     */
    protected function calculateAverageWaitTime(): int
    {
        $globalQueue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
        
        if (empty($globalQueue)) {
            return 0;
        }
        
        $totalWaitTime = 0;
        foreach ($globalQueue as $item) {
            $totalWaitTime += $item['estimated_wait_time'] ?? 0;
        }
        
        return (int)($totalWaitTime / count($globalQueue));
    }

    /**
     * 发送排队通知
     * @param int $userId 用户ID
     * @param array $queueInfo 队列信息
     */
    public function sendQueueNotification(int $userId, array $queueInfo): void
    {
        try {
            // 检查是否启用排队通知
            if (!$this->config['notification']['queue_notification']) {
                return;
            }

            // 检查通知间隔
            if (!$this->shouldSendNotification($userId, 'queue')) {
                return;
            }

            $message = [
                'message_type' => MessageProtocol::MESSAGE_QUEUE_NOTIFICATION,
                'user_id' => $userId,
                'position' => $queueInfo['position'],
                'estimated_wait_time' => $queueInfo['estimated_wait_time'],
                'service_id' => $queueInfo['service_id'] ?? null,
                'message' => "您当前排队位置：第{$queueInfo['position']}位，预计等待时间：{$queueInfo['estimated_wait_time']}秒",
                'timestamp' => time()
            ];

            // 记录通知历史
            $this->recordNotification($userId, 'queue', $message);

            $this->logger->info('发送排队通知', [
                'user_id' => $userId,
                'position' => $queueInfo['position'],
                'estimated_wait_time' => $queueInfo['estimated_wait_time']
            ]);

            // 这里可以集成具体的消息推送服务
            // 例如：WebSocket推送、短信通知等
            $this->pushNotificationToUser($userId, $message);

        } catch (\Exception $e) {
            $this->logger->error('发送排队通知失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送超时警告通知
     * @param int $userId 用户ID
     * @param int $remainingTime 剩余时间（秒）
     */
    public function sendTimeoutWarning(int $userId, int $remainingTime): void
    {
        try {
            // 检查是否启用超时警告
            if (!$this->config['notification']['timeout_warning']) {
                return;
            }

            // 检查通知间隔
            if (!$this->shouldSendNotification($userId, 'timeout_warning')) {
                return;
            }

            $message = [
                'message_type' => MessageProtocol::MESSAGE_TIMEOUT_WARNING,
                'user_id' => $userId,
                'remaining_time' => $remainingTime,
                'message' => "您的排队即将超时，剩余时间：{$remainingTime}秒，请保持在线",
                'timestamp' => time()
            ];

            // 记录通知历史
            $this->recordNotification($userId, 'timeout_warning', $message);

            $this->logger->info('发送超时警告', [
                'user_id' => $userId,
                'remaining_time' => $remainingTime
            ]);

            // 推送通知给用户
            $this->pushNotificationToUser($userId, $message);

        } catch (\Exception $e) {
            $this->logger->error('发送超时警告失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查是否应该发送通知（基于通知间隔）
     * @param int $userId 用户ID
     * @param string $notificationType 通知类型
     * @return bool
     */
    protected function shouldSendNotification(int $userId, string $notificationType): bool
    {
        $key = self::REDIS_PREFIX . self::LAST_NOTIFICATION_PREFIX . $userId . ':' . $notificationType;
        $lastNotifyTime = Cache::get($key, 0);
        $currentTime = time();
        $interval = $this->config['notification']['notification_interval'];

        return ($currentTime - $lastNotifyTime) >= $interval;
    }

    /**
     * 记录通知历史
     * @param int $userId 用户ID
     * @param string $notificationType 通知类型
     * @param array $message 消息内容
     */
    protected function recordNotification(int $userId, string $notificationType, array $message): void
    {
        try {
            // 更新最后通知时间
            $lastNotifyKey = self::REDIS_PREFIX . self::LAST_NOTIFICATION_PREFIX . $userId . ':' . $notificationType;
            Cache::set($lastNotifyKey, time(), 3600);

            // 保存通知历史
            $historyKey = self::REDIS_PREFIX . self::NOTIFICATION_HISTORY_PREFIX . $userId;
            $history = Cache::get($historyKey, []);
            $history[] = $message;

            // 只保留最近50条通知记录
            if (count($history) > 50) {
                $history = array_slice($history, -50);
            }

            Cache::set($historyKey, $history, 3600);

        } catch (\Exception $e) {
            $this->logger->error('记录通知历史失败', [
                'user_id' => $userId,
                'notification_type' => $notificationType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 推送通知给用户
     * @param int $userId 用户ID
     * @param array $message 消息内容
     */
    protected function pushNotificationToUser(int $userId, array $message): void
    {
        try {
            // 这里可以集成具体的推送服务
            // 例如：WebSocket、消息队列、短信、邮件等
            
            // 示例：记录到日志（实际使用时应该替换为真实的推送逻辑）
            $this->logger->info('推送通知给用户', [
                'user_id' => $userId,
                'message_type' => $message['message_type'],
                'message' => $message['message']
            ]);

            // TODO: 集成WebSocket推送
            // $this->webSocketService->pushToUser($userId, $message);
            // 获取WebSocket实例
            $websocket = app()->make(Websocket::class);
            $this->logger->info('WebSocket实例获取成功');

            // 发送给用户
            $this->logger->info('准备发送排队消息给用户', ['user_room' => 'user_' . $userId]);
            $websocket->to('user_' . $userId)->emit(MessageProtocol::MESSAGE_NOTICE, $message);
            $this->logger->info('用户排队消息发送完成');
            
            // TODO: 集成消息队列
            // $this->messageQueueService->push($message);
            
        } catch (\Exception $e) {
            $this->logger->error('推送通知失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取用户通知历史
     * @param int $userId 用户ID
     * @param int $limit 限制数量
     * @return array
     */
    public function getUserNotificationHistory(int $userId, int $limit = 20): array
    {
        try {
            $historyKey = self::REDIS_PREFIX . self::NOTIFICATION_HISTORY_PREFIX . $userId;
            $history = Cache::get($historyKey, []);
            
            // 返回最新的通知记录
            return array_slice($history, -$limit);
            
        } catch (\Exception $e) {
            $this->logger->error('获取用户通知历史失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 清理过期的通知数据
     */
    public function cleanupExpiredNotifications(): void
    {
        try {
            // 这里可以实现清理过期通知数据的逻辑
            // 例如：清理超过24小时的通知记录
            
            $this->logger->info('清理过期通知数据完成');
            
        } catch (\Exception $e) {
            $this->logger->error('清理过期通知数据失败', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 批量发送队列状态更新通知
     */
    public function sendBatchQueueStatusNotifications(): void
    {
        try {
            // 检查是否启用排队通知
            if (!$this->config['notification']['queue_notification']) {
                return;
            }

            $globalQueue = Cache::get(self::REDIS_PREFIX . self::GLOBAL_QUEUE_KEY, []);
            
            foreach ($globalQueue as $index => $item) {
                $userId = $item['user_id'];
                $userInfo = $this->getUserQueueInfo($userId);
                
                if ($userInfo) {
                    // 更新队列位置
                    $userInfo['position'] = $index + 1;
                    
                    // 重新计算等待时间
                    $userInfo['estimated_wait_time'] = $this->calculateWaitTime(
                        $userInfo['service_id'], 
                        $userInfo['priority']
                    );
                    
                    // 发送更新通知
                    $this->sendQueueNotification($userId, $userInfo);
                    
                    // 更新用户队列信息
                    $this->saveUserQueueInfo($userId, $userInfo);
                }
            }
            
            $this->logger->info('批量发送队列状态更新通知完成', [
                'queue_size' => count($globalQueue)
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error('批量发送队列状态更新通知失败', [
                'error' => $e->getMessage()
            ]);
        }
    }
}