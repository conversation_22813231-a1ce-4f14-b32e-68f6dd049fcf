<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\vchat\utils\Logger;
use think\swoole\Websocket;
use think\swoole\websocket\Room;

/**
 * 用户状态服务类
 */
class UserStatusService
{
    /**
     * @var Room
     */
    protected $room;

    /**
     * @var RoomService
     */
    protected $roomService;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->room = app()->make(Room::class);
        $this->roomService = new RoomService();
        $this->logger = new Logger();
    }

    /**
     * 更新用户在线状态
     * @param int $userId 用户ID
     * @param string $type 类型(user/service)
     * @param bool $isOnline 是否在线
     * @return bool
     */
    public function updateOnlineStatus(Websocket $websocket, int $userId, string $type, bool $isOnline): bool
    {
        try {
            $roomId = $this->getRoomId($type, $userId);
            if ($isOnline) {
                $this->roomService->join($websocket, $roomId);
            } else {
                $this->roomService->leave($websocket, $roomId);
            }
            return true;
        } catch (\Exception $e) {
            $this->logger->error('更新用户在线状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 获取用户在线状态
     * @param int $userId 用户ID
     * @param string $type 类型(user/service)
     * @return bool
     */
    public function getOnlineStatus(int $userId, string $type): bool
    {
        try {
            $roomId = $this->getRoomId($type, $userId);
            $members = $this->roomService->getRoomMembers($roomId);
            return !empty($members);
        } catch (\Exception $e) {
            $this->logger->error('获取用户在线状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 获取在线用户列表
     * @param string $type 类型(user/service)
     * @return array
     */
    public function getOnlineUsers(Websocket $websocket, string $type): array
    {
        try {
            $onlineUsers = [];
            $prefix = $type . '_';
            
            // 获取所有房间信息
            $rooms = $this->room->getRooms($websocket->getSender());
            foreach ($rooms as $roomId => $fds) {
                if (strpos($roomId, $prefix) === 0) {
                    $userId = (int)substr($roomId, strlen($prefix));
                    if (!in_array($userId, $onlineUsers)) {
                        $onlineUsers[] = $userId;
                    }
                }
            }
            
            return $onlineUsers;
        } catch (\Exception $e) {
            $this->logger->error('获取在线用户列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * 广播用户状态变更
     * @param Websocket $websocket
     * @param int $userId 用户ID
     * @param string $type 类型(user/service)
     * @param bool $isOnline 是否在线
     */
    public function broadcastStatusChange(Websocket $websocket, int $userId, string $type, bool $isOnline): void
    {
        try {
            $targetType = $type === 'user' ? 'service' : 'user';
            $this->roomService->broadcast($websocket, $targetType . '_*', 'status_change', [
                'user_id' => $userId,
                'type' => $type,
                'status' => $isOnline ? 'online' : 'offline'
            ]);
        } catch (\Exception $e) {
            $this->logger->error('广播用户状态变更失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 获取房间ID
     * @param string $type
     * @param int $id
     * @return string
     */
    protected function getRoomId(string $type, int $id): string
    {
        return $type . '_' . $id;
    }
} 