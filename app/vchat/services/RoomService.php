<?php
declare(strict_types=1);

namespace app\vchat\services;

use app\vchat\utils\Logger;
use think\swoole\Websocket;
use think\swoole\websocket\Room;

/**
 * 房间服务
 */
class RoomService
{
    /**
     * @var Room
     */
    protected $room;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->room = app()->make(Room::class);
        $this->logger = new Logger();
    }

    public function getRoom()
    {
        return $this->room;
    }

    /**
     * 加入房间
     * @param Websocket $websocket
     * @param string $roomId
     */
    public function join(Websocket $websocket, string $roomId): void
    {
        try {
            $websocket->join($roomId);
            $this->logger->info('加入房间', [
                'fd' => $websocket->getSender(),
                'room' => $roomId
            ]);
        } catch (\Exception $e) {
            $this->logger->error('加入房间失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 离开房间
     * @param Websocket $websocket
     * @param string $roomId
     */
    public function leave(Websocket $websocket, string $roomId): void
    {
        try {
            $websocket->leave($roomId);
            $this->logger->info('离开房间', [
                'fd' => $websocket->getSender(),
                'room' => $roomId
            ]);
        } catch (\Exception $e) {
            $this->logger->error('离开房间失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function isInRoom(string $fd, string $roomId): bool
    {
        try {
            return $this->room->isInRoom($fd, $roomId);
        } catch (\Exception $e) {
            $this->logger->error('判断用户是否在房间中失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    public function getRoomId(string $fd): string
    {
        try {
            return $this->room->getRoom($fd);
        } catch (\Exception $e) {
            $this->logger->error('获取房间ID失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return '';
        }
    }

    public function getRoomIds(string $fd): array
    {
        try {
            return $this->room->getRooms($fd);
        } catch (\Exception $e) {
            $this->logger->error('获取房间ID列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * 获取房间成员
     * @param string $roomId
     * @return array
     */
    public function getRoomMembers(string $roomId): array
    {
        try {
            return $this->room->getClients($roomId);
        } catch (\Exception $e) {
            $this->logger->error('获取房间成员失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * 处理连接断开
     * @param string $fd
     */
    public function handleDisconnect(string $fd): void
    {
        try {
            $rooms = $this->room->getRooms($fd);
            foreach ($rooms as $room) {
                $this->room->delete($fd);
            }
            $this->logger->info('处理连接断开', [
                'fd' => $fd,
                'rooms' => $rooms
            ]);
        } catch (\Exception $e) {
            $this->logger->error('处理连接断开失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 广播消息到房间
     * @param Websocket $websocket
     * @param string $roomId
     * @param string $event
     * @param array $data
     */
    public function broadcast(Websocket $websocket, string $roomId, string $event, array $data): void
    {
        try {
            $websocket->to($roomId)->emit($event, $data);
            $this->logger->info('广播消息', [
                'room' => $roomId,
                'event' => $event,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            $this->logger->error('广播消息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
} 