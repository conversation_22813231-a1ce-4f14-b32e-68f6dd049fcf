# VChat Swoole 集成指南

## 概述

`ScheduleService` 现在已经集成到 Swoole 服务中，当 Swoole 启动时会自动启动定时任务服务。这样可以确保定时任务与 WebSocket 服务在同一个进程中运行，实现更好的通信和资源共享。

## 集成方式

### 1. 自动启动

定时任务服务通过 `VChatInitMiddleware` 中间件自动启动：

- 当 Swoole 服务启动时，中间件会检测 Swoole 环境
- 自动创建并启动 `ScheduleService` 实例
- 确保只启动一次（通过静态标志位控制）

### 2. 启动流程

```
Swoole 启动 → VChatInitMiddleware → ScheduleService.start() → 定时任务运行
```

## 定时任务类型

### 1. 会话超时检查
- **功能**: 检查并处理超时的用户会话
- **默认间隔**: 60秒
- **配置项**: `vchat.timeout.check_interval`

### 2. 队列清理
- **功能**: 清理过期的队列数据和无效用户
- **默认间隔**: 300秒（5分钟）
- **配置项**: `vchat.queue.cleanup_interval`

### 3. 队列状态更新
- **功能**: 更新用户等待时间和队列状态
- **默认间隔**: 30秒
- **配置项**: `vchat.queue.status_update_interval`

## 启动 Swoole 服务

### 开发环境
```bash
# 启动 Swoole 服务（包含定时任务）
php think swoole
```

### 生产环境
```bash
# 后台启动
nohup php think swoole > /dev/null 2>&1 &

# 或使用 systemd 管理
sudo systemctl start swoole-vchat
```

## 配置说明

### Swoole 配置 (`config/swoole.php`)

```php
return [
    'http' => [
        'enable' => true,
        'host' => '0.0.0.0',
        'port' => 9502,
        'worker_num' => swoole_cpu_num(),
        // ... 其他配置
    ],
    'websocket' => [
        'enable' => true,
        'route' => true,
        // ... WebSocket 配置
    ],
    // ... 其他配置
];
```

### VChat 配置 (`config/vchat.php`)

```php
return [
    'timeout' => [
        'check_interval' => 60,  // 会话超时检查间隔（秒）
    ],
    'queue' => [
        'cleanup_interval' => 300,        // 队列清理间隔（秒）
        'status_update_interval' => 30,   // 状态更新间隔（秒）
    ],
];
```

## 监控和日志

### 日志位置
- **Swoole 日志**: `runtime/swoole.log`
- **VChat 日志**: `runtime/vchat/`

### 监控定时任务

定时任务的启动和执行情况会记录在日志中：

```
[INFO] ScheduleService started successfully in Swoole environment
[INFO] 会话超时检查任务已启动 {"interval":60,"timer_id":1}
[INFO] 队列清理任务已启动 {"interval":300,"timer_id":2}
[INFO] 队列状态更新任务已启动 {"interval":30,"timer_id":3}
```

## 优势

### 1. 统一进程管理
- 定时任务与 WebSocket 服务在同一进程中
- 减少进程间通信开销
- 简化部署和监控

### 2. 资源共享
- 共享数据库连接池
- 共享 Redis 连接
- 共享内存表（Swoole Table）

### 3. 实时通信
- 定时任务可以直接使用 WebSocket 发送通知
- 无需额外的消息队列或通信机制

### 4. 配置统一
- 使用相同的配置文件
- 统一的日志系统
- 一致的错误处理

## 故障排除

### 1. 定时任务未启动

**检查项**:
- Swoole 是否正常启动
- `VChatInitMiddleware` 是否正确配置
- 日志中是否有错误信息

**解决方案**:
```bash
# 检查 Swoole 状态
ps aux | grep swoole

# 查看日志
tail -f runtime/swoole.log
tail -f runtime/vchat/schedule.log
```

### 2. 定时任务执行异常

**检查项**:
- 数据库连接是否正常
- Redis 连接是否正常
- 相关服务类是否正确注入

**解决方案**:
- 检查配置文件
- 查看详细错误日志
- 重启 Swoole 服务

### 3. 内存泄漏

**监控方法**:
```bash
# 监控内存使用
top -p $(pgrep -f "php think swoole")

# 查看 Swoole 统计信息
php think swoole:stats
```

## 迁移说明

### 从独立脚本迁移

如果之前使用独立的定时任务脚本，需要：

1. **停止旧的定时任务**:
```bash
# 停止独立脚本
php app/vchat/schedule_stop.php

# 或杀死进程
pkill -f schedule_start.php
```

2. **清理 crontab**:
```bash
# 编辑 crontab
crontab -e

# 删除相关的定时任务条目
```

3. **启动 Swoole 服务**:
```bash
php think swoole
```

### 配置迁移

旧的独立脚本配置需要迁移到 `config/vchat.php` 中。

## 最佳实践

### 1. 生产环境部署

```bash
# 使用 systemd 管理
sudo tee /etc/systemd/system/swoole-vchat.service > /dev/null <<EOF
[Unit]
Description=Swoole VChat Service
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/path/to/anchor
ExecStart=/usr/bin/php think swoole
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl enable swoole-vchat
sudo systemctl start swoole-vchat
```

### 2. 监控脚本

```bash
#!/bin/bash
# monitor_swoole.sh

PID=$(pgrep -f "php think swoole")
if [ -z "$PID" ]; then
    echo "Swoole service is not running, restarting..."
    systemctl restart swoole-vchat
else
    echo "Swoole service is running (PID: $PID)"
fi
```

### 3. 日志轮转

```bash
# /etc/logrotate.d/swoole-vchat
/path/to/anchor/runtime/swoole.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    postrotate
        systemctl reload swoole-vchat
    endscript
}
```

## 总结

通过将 `ScheduleService` 集成到 Swoole 服务中，我们实现了：

- ✅ 统一的服务管理
- ✅ 更好的资源利用
- ✅ 简化的部署流程
- ✅ 实时的 WebSocket 通信
- ✅ 一致的配置和日志

这种集成方式更符合现代微服务架构的设计理念，提供了更好的性能和可维护性。