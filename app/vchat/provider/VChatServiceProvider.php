<?php

namespace app\vchat\provider;

use app\vchat\services\FdManagerService;
use Swoole\WebSocket\Server as SwooleServer;
use think\Service;

/**
 * VChat 服务提供者
 * 负责注册 VChat 相关服务到容器
 */
class VChatServiceProvider extends Service
{
    /**
     * 注册服务
     */
    public function register()
    {
        // 绑定 FdManagerService
        // 注意：FdManagerService 需要 SwooleServer 参数，应该在使用时手动创建
        // 这里不进行绑定，避免容器依赖问题
    }

    /**
     * 启动服务
     */
    public function boot()
    {
        // 服务启动逻辑
    }
}