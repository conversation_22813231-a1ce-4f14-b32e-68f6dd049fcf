# VChat 第三方平台集成指南

## 🎯 系统概述

VChat第三方平台集成系统将微信、QQ、钉钉、飞书等第三方平台的客服功能完全集成到VChat系统中，实现：

- **统一客服工作台** - 客服在同一个界面接收所有平台的消息
- **实时消息同步** - 第三方平台消息实时推送到VChat WebSocket
- **无缝消息发送** - 客服回复自动发送到对应的第三方平台
- **统一会话管理** - 所有平台会话在VChat中统一管理
- **智能自动回复** - 跨平台的统一自动回复规则

## 🏗️ 集成架构

```
┌─────────────────────────────────────────────────────────────┐
│                    第三方平台                                │
│  微信小程序 | 微信公众号 | 企业微信 | QQ | 钉钉 | 飞书        │
└─────────────────────┬───────────────────────────────────────┘
                      │ Webhook
                      ▼
┌─────────────────────────────────────────────────────────────┐
│              VChat 第三方平台集成层                          │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │ ThirdPartyMessage│  │ ThirdPartyIntegra│                  │
│  │ Handler         │  │ tionService     │                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────┬───────────────────────────────────────┘
                      │ 消息转换 & 推送
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                  VChat 核心系统                              │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │ MessageHandler  │  │ WebSocket       │                   │
│  │                 │  │ 实时通信        │                   │
│  └─────────────────┘  └─────────────────┘                   │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │ 消息存储        │  │ 会话管理        │                   │
│  │                 │  │                 │                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────┬───────────────────────────────────────┘
                      │ WebSocket 推送
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                   客服工作台                                 │
│  统一接收所有平台消息 | 统一发送回复 | 统一会话管理           │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 环境配置

在 `.env` 文件中添加配置：

```env
# VChat第三方平台集成
VCHAT_INTEGRATION_ENABLED=true
VCHAT_AUTO_REPLY_ENABLED=true

# 第三方平台开关
WECHAT_MINIPROGRAM_ENABLED=true
WECHAT_OFFICIAL_ACCOUNT_ENABLED=true
WECHAT_WORK_ENABLED=true

# 第三方平台配置（复用原有配置）
WECHAT_MINIPROGRAM_APP_ID=your_app_id
WECHAT_MINIPROGRAM_SECRET=your_secret
```

### 2. 路由配置

在 `route/app.php` 中引入路由：

```php
// 引入VChat第三方平台集成路由
require_once app_path('vchat/route/third_party.php');
```

### 3. Webhook URL 配置

各平台Webhook URL配置：

```
微信小程序：    https://yourdomain.com/vchat/webhook/wechat/miniprogram
微信公众号：    https://yourdomain.com/vchat/webhook/wechat/officialaccount
企业微信：      https://yourdomain.com/vchat/webhook/wechat/work
QQ机器人：      https://yourdomain.com/vchat/webhook/qq/bot
钉钉机器人：    https://yourdomain.com/vchat/webhook/dingtalk/bot
飞书机器人：    https://yourdomain.com/vchat/webhook/feishu/bot
```

## 🔄 消息流程

### 1. 接收第三方平台消息

```
第三方平台用户发送消息
    ↓
Webhook推送到VChat
    ↓
ThirdPartyMessageHandler处理
    ↓
ThirdPartyIntegrationService转换消息格式
    ↓
保存到VChat数据库
    ↓
通过WebSocket推送给客服
    ↓
客服在VChat界面看到消息
```

### 2. 客服回复消息

```
客服在VChat界面回复
    ↓
MessageHandler处理消息
    ↓
保存到VChat数据库
    ↓
WebSocket推送给其他客服
    ↓
ThirdPartyIntegrationService发送到第三方平台
    ↓
第三方平台用户收到回复
```

## 📝 消息格式统一

### VChat标准消息格式

所有第三方平台消息都会转换为VChat标准格式：

```php
[
    'id' => 'unique_message_id',
    'type' => 'text|image|voice|video|file',
    'content' => 'message_content',
    'from_id' => 'vchat_user_id',
    'from_type' => 'user',
    'to_id' => 'service_id',
    'to_type' => 'service',
    'session_id' => 'session_id',
    'message_type' => 'chat_message',
    'status' => 'sent',
    'timestamp' => 1234567890,
    'extra' => [
        'platform' => 'wechat.miniprogram',
        'platform_name' => '微信小程序',
        'platform_icon' => 'wechat',
        'platform_color' => '#07C160',
        'third_party_user_id' => 'original_user_id',
        'third_party_message_id' => 'original_message_id',
        'raw_data' => [...]
    ]
]
```

### 用户ID映射

第三方平台用户ID会自动映射为VChat用户ID：

```php
// 微信小程序用户：wx_mini_1234567
// 微信公众号用户：wx_mp_1234567  
// 企业微信用户：wx_work_1234567
// QQ用户：qq_1234567
```

## 🤖 自动回复系统

### 配置自动回复规则

在 `config/vchat_integration.php` 中配置：

```php
'auto_reply' => [
    'enabled' => true,
    'delay' => 1, // 延迟1秒发送
    'default_rules' => [
        [
            'keywords' => ['你好', 'hello', '在吗'],
            'reply' => '您好！欢迎咨询，有什么可以帮助您的吗？',
            'type' => 'text',
            'priority' => 1
        ],
        [
            'keywords' => ['价格', '多少钱', '费用'],
            'reply' => '关于价格问题，请稍等，我为您查询详细信息...',
            'type' => 'text',
            'priority' => 2
        ]
    ]
]
```

### 工作时间控制

```php
'auto_reply' => [
    'working_hours' => [
        'enabled' => true,
        'start' => '09:00',
        'end' => '18:00',
        'timezone' => 'Asia/Shanghai'
    ]
]
```

## 👥 会话管理

### 自动客服分配

系统会自动为第三方平台用户分配客服：

```php
'session_management' => [
    'auto_assign' => true,
    'assignment_strategy' => 'least_busy', // 分配给最空闲的客服
    'max_sessions_per_service' => 10
]
```

### 会话状态同步

- 第三方平台新用户自动创建VChat会话
- 会话状态实时同步
- 支持会话转接和结束

## 🔧 API接口

### 发送消息到第三方平台

```http
POST /vchat/api/third-party/send-message
{
    "platform": "wechat.miniprogram",
    "message_id": "vchat_message_id"
}
```

### 获取平台状态

```http
GET /vchat/api/third-party/platform-status
```

### 获取会话统计

```http
GET /vchat/api/third-party/session-stats?start_date=2024-01-01&end_date=2024-01-31
```

## 🎨 客服界面集成

### 消息显示

在VChat客服界面中，第三方平台消息会显示：

- **平台标识** - 显示消息来源平台图标和名称
- **用户信息** - 显示第三方平台用户信息
- **消息内容** - 统一的消息格式显示
- **平台颜色** - 不同平台使用不同的主题色

### 发送回复

客服回复消息时：

- 自动识别会话来源平台
- 自动发送到对应的第三方平台
- 实时显示发送状态
- 支持各种消息类型（文本、图片、语音等）

## 📊 统计和监控

### 平台统计

```php
// 获取各平台会话统计
$stats = [
    'wechat.miniprogram' => [
        'sessions' => 150,
        'messages' => 1200,
        'avg_messages_per_session' => 8.0
    ],
    'wechat.work' => [
        'sessions' => 80,
        'messages' => 640,
        'avg_messages_per_session' => 8.0
    ]
];
```

### 实时监控

- 平台连接状态监控
- 消息处理成功率
- 自动回复触发统计
- 客服响应时间统计

## 🛠️ 开发和调试

### 日志查看

```bash
# 查看第三方平台集成日志
tail -f runtime/logs/vchat_integration/info.log

# 查看错误日志
tail -f runtime/logs/vchat_integration/error.log
```

### 调试模式

```php
// 启用调试模式
'development' => [
    'debug' => true,
    'log_requests' => true,
    'log_responses' => true
]
```

## 🔒 安全配置

### Webhook验证

```php
'security' => [
    'webhook_verification' => true,
    'ip_whitelist' => '127.0.0.1,***********/24'
]
```

### 频率限制

```php
'security' => [
    'rate_limit' => [
        'enabled' => true,
        'max_requests' => 1000,
        'window' => 3600
    ]
]
```

## 🚀 性能优化

### 消息队列

```php
// 启用消息队列处理
'queue' => [
    'enabled' => true,
    'driver' => 'redis',
    'queue_name' => 'vchat_integration'
]
```

### 缓存优化

```php
// 缓存配置
'cache' => [
    'ttl' => [
        'user_mapping' => 86400 * 30, // 用户ID映射缓存30天
        'auto_reply_rules' => 3600,   // 自动回复规则缓存1小时
        'platform_status' => 300      // 平台状态缓存5分钟
    ]
]
```

## 🔧 故障排除

### 常见问题

1. **Webhook接收不到消息**
   - 检查URL配置是否正确
   - 验证网络连通性
   - 查看日志文件

2. **消息发送失败**
   - 检查第三方平台配置
   - 验证用户ID映射
   - 查看错误日志

3. **WebSocket推送失败**
   - 检查WebSocket服务状态
   - 验证客服连接状态
   - 查看推送日志

### 测试连接

```bash
# 测试平台连接
curl -X POST "https://yourdomain.com/vchat/api/third-party/test-connection" \
     -H "Content-Type: application/json" \
     -d '{"platform": "wechat.miniprogram"}'
```

---

**这个集成方案实现了真正的统一客服工作台，客服可以在VChat界面中无缝处理所有平台的客服请求！** 🎉
