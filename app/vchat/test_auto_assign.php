<?php
/**
 * 自动分配功能测试脚本
 * 用于测试客服自动分配排队用户的功能
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use app\vchat\services\ScheduleService;
use app\vchat\services\AutoAssignService;
use app\model\CustomerService;
use app\vchat\services\QueueService;
use app\vchat\services\SessionService;
use app\vchat\utils\Logger;
use think\facade\Config;

// 初始化配置
Config::load(__DIR__ . '/../../config/vchat.php', 'vchat');

class AutoAssignTest
{
    private $logger;
    private $scheduleService;
    private $autoAssignService;
    private $customerService;
    private $queueService;
    private $sessionService;

    public function __construct()
    {
        $this->logger = new Logger();
        $this->scheduleService = new ScheduleService();
        $this->autoAssignService = new AutoAssignService();
        $this->customerService = new CustomerService();
        $this->queueService = new QueueService();
        $this->sessionService = new SessionService();
    }

    /**
     * 测试定时任务状态
     */
    public function testScheduleStatus()
    {
        echo "\n=== 测试定时任务状态 ===\n";
        
        $status = $this->scheduleService->getStatus();
        
        echo "定时任务状态:\n";
        foreach ($status as $task => $info) {
            echo "- {$task}: " . ($info['running'] ? '运行中' : '已停止') . 
                 " (间隔: {$info['interval']}秒)\n";
        }
    }

    /**
     * 测试可用客服获取
     */
    public function testAvailableServices()
    {
        echo "\n=== 测试可用客服获取 ===\n";
        
        $availableServices = $this->customerService->getAvailableServices();
        
        echo "可用客服数量: " . count($availableServices) . "\n";
        foreach ($availableServices as $service) {
            echo "- 客服ID: {$service['id']}, 姓名: {$service['name']}, "
                . "当前会话: {$service['current_sessions']}/{$service['max_sessions']}\n";
        }
    }

    /**
     * 测试排队用户获取
     */
    public function testQueuedUsers()
    {
        echo "\n=== 测试排队用户获取 ===\n";
        
        // 获取所有排队用户
        $queuedUsers = $this->queueService->getAllQueuedUsers();
        
        echo "排队用户数量: " . count($queuedUsers) . "\n";
        foreach ($queuedUsers as $user) {
            echo "- 用户ID: {$user['user_id']}, 排队时间: {$user['queue_time']}, "
                . "优先级: {$user['priority']}\n";
        }
    }

    /**
     * 测试自动分配逻辑
     */
    public function testAutoAssignment()
    {
        echo "\n=== 测试自动分配逻辑 ===\n";
        
        // 获取可用客服
        $availableServices = $this->customerService->getAvailableServices();
        
        if (empty($availableServices)) {
            echo "没有可用的客服，无法进行自动分配测试\n";
            return;
        }
        
        // 获取排队用户
        $queuedUsers = $this->queueService->getAllQueuedUsers();
        
        if (empty($queuedUsers)) {
            echo "没有排队的用户，无法进行自动分配测试\n";
            return;
        }
        
        echo "开始自动分配测试...\n";
        
        foreach ($availableServices as $service) {
            $nextUser = $this->queueService->getNextUser($service['group_id']);
            
            if ($nextUser) {
                echo "为客服 {$service['name']} (ID: {$service['id']}) 分配用户 {$nextUser['user_id']}\n";
                
                // 这里只是模拟，不实际创建会话
                echo "  - 模拟创建会话成功\n";
                echo "  - 模拟更新会话计数\n";
                echo "  - 模拟记录活动\n";
            } else {
                echo "客服 {$service['name']} (ID: {$service['id']}) 没有可分配的用户\n";
            }
        }
    }

    /**
     * 测试配置加载
     */
    public function testConfigLoading()
    {
        echo "\n=== 测试配置加载 ===\n";
        
        $config = Config::get('vchat', []);
        
        echo "配置项:\n";
        echo "- 自动分配间隔: " . ($config['queue']['auto_assign_interval'] ?? '未设置') . "秒\n";
        echo "- 超时检查间隔: " . ($config['timeout']['check_interval'] ?? '未设置') . "秒\n";
        echo "- 队列清理间隔: " . ($config['queue']['cleanup_interval'] ?? '未设置') . "秒\n";
        echo "- 队列状态更新间隔: " . ($config['queue']['status_update_interval'] ?? '未设置') . "秒\n";
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始自动分配功能测试...\n";
        
        try {
            $this->testConfigLoading();
            $this->testScheduleStatus();
            $this->testAvailableServices();
            $this->testQueuedUsers();
            $this->testAutoAssignment();
            
            echo "\n=== 测试完成 ===\n";
            echo "所有测试已完成，请检查上述输出结果\n";
            
        } catch (\Exception $e) {
            echo "\n测试过程中发生错误: " . $e->getMessage() . "\n";
            echo "错误堆栈: " . $e->getTraceAsString() . "\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new AutoAssignTest();
    $test->runAllTests();
} else {
    echo "请在命令行环境下运行此测试脚本\n";
}