<?php

// +----------------------------------------------------------------------
// | VChat 菜单配置
// +----------------------------------------------------------------------

return [
    // 菜单配置
    'menu' => [
        // 是否启用菜单功能
        'enabled' => true,
        
        // 菜单显示模式：button(按钮菜单) | list(列表菜单)
        'display_mode' => 'button',
        
        // 菜单最大层级
        'max_level' => 3,
        
        // 菜单缓存时间（秒）
        'cache_time' => 3600,
        
        // 菜单缓存前缀
        'cache_prefix' => 'vchat:menu:',
    ],
    
    // 默认菜单结构
    'default_menus' => [
        [
            'id' => 'main_menu',
            'name' => '主菜单',
            'type' => 'button',
            'level' => 1,
            'sort' => 1,
            'status' => 1,
            'children' => [
                [
                    'id' => 'service',
                    'name' => '在线客服',
                    'type' => 'click',
                    'level' => 2,
                    'sort' => 1,
                    'status' => 1,
                    'handler' => 'CustomerServiceHandler',
                    'params' => [
                        'action' => 'start_service'
                    ]
                ],
                [
                    'id' => 'help',
                    'name' => '帮助中心',
                    'type' => 'button',
                    'level' => 2,
                    'sort' => 2,
                    'status' => 1,
                    'children' => [
                        [
                            'id' => 'faq',
                            'name' => '常见问题',
                            'type' => 'click',
                            'level' => 3,
                            'sort' => 1,
                            'status' => 1,
                            'handler' => 'HelpHandler',
                            'params' => [
                                'action' => 'show_faq'
                            ]
                        ],
                        [
                            'id' => 'contact',
                            'name' => '联系我们',
                            'type' => 'click',
                            'level' => 3,
                            'sort' => 2,
                            'status' => 1,
                            'handler' => 'ContactHandler',
                            'params' => [
                                'action' => 'show_contact'
                            ]
                        ]
                    ]
                ],
                [
                    'id' => 'products',
                    'name' => '产品介绍',
                    'type' => 'view',
                    'level' => 2,
                    'sort' => 3,
                    'status' => 1,
                    'handler' => 'UrlHandler',
                    'params' => [
                        'url' => 'https://example.com/products'
                    ]
                ]
            ]
        ]
    ],
    
    // 菜单类型配置
    'menu_types' => [
        'click' => [
            'name' => '点击事件',
            'description' => '用户点击菜单时触发事件处理器'
        ],
        'view' => [
            'name' => '跳转链接',
            'description' => '用户点击菜单时跳转到指定URL'
        ],
        'button' => [
            'name' => '子菜单',
            'description' => '包含子菜单的按钮'
        ],
        'media' => [
            'name' => '媒体消息',
            'description' => '发送图片、视频等媒体消息'
        ],
        'text' => [
            'name' => '文本消息',
            'description' => '发送文本消息'
        ]
    ],
    
    // 处理器映射
    'handlers' => [
        'CustomerServiceHandler' => \app\vchat\menu\handlers\CustomerServiceHandler::class,
        'HelpHandler' => \app\vchat\menu\handlers\HelpHandler::class,
        'ContactHandler' => \app\vchat\menu\handlers\ContactHandler::class,
        'UrlHandler' => \app\vchat\menu\handlers\UrlHandler::class,
        'MediaHandler' => \app\vchat\menu\handlers\MediaHandler::class,
        'TextHandler' => \app\vchat\menu\handlers\TextHandler::class,
    ],
    
    // 权限配置
    'permissions' => [
        // 是否启用权限验证
        'enabled' => false,
        
        // 默认权限
        'default_permission' => 'guest',
        
        // 权限级别
        'levels' => [
            'guest' => 0,
            'user' => 1,
            'vip' => 2,
            'admin' => 9
        ]
    ]
];