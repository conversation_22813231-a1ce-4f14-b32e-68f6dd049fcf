<?php
declare(strict_types=1);

/**
 * VChat 定时任务状态检查脚本
 * 用于检查定时任务的运行状态
 */

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../../app/common.php';

use app\vchat\services\ScheduleService;
use app\vchat\utils\Logger;
use think\facade\Config;

// 初始化应用
\think\App::getInstance()->initialize();

$pidFile = __DIR__ . '/schedule.pid';

echo "=== VChat 定时任务状态 ===\n\n";

if (!file_exists($pidFile)) {
    echo "状态: 未运行\n";
    echo "PID文件: 不存在\n";
    exit(0);
}

$pid = file_get_contents($pidFile);
if (!$pid) {
    echo "状态: 异常 (无效的PID文件)\n";
    echo "建议: 删除PID文件并重新启动\n";
    exit(1);
}

$pid = (int)$pid;

// 检查进程是否存在
if (posix_kill($pid, 0)) {
    echo "状态: 运行中\n";
    echo "PID: {$pid}\n";
    
    // 获取进程信息
    $processInfo = shell_exec("ps -p {$pid} -o pid,ppid,etime,pcpu,pmem,cmd");
    if ($processInfo) {
        echo "\n进程信息:\n";
        echo $processInfo;
    }
    
    // 显示配置信息
    try {
        $scheduleService = new ScheduleService();
        $status = $scheduleService->getStatus();
        
        echo "\n任务配置:\n";
        foreach ($status as $taskName => $taskConfig) {
            echo "- {$taskName}: ";
            echo ($taskConfig['enabled'] ? '启用' : '禁用');
            echo " (间隔: {$taskConfig['interval']}秒)\n";
        }
    } catch (\Exception $e) {
        echo "\n获取任务配置失败: {$e->getMessage()}\n";
    }
    
    // 检查日志文件
    $logPath = runtime_path('log');
    if (is_dir($logPath)) {
        $logFiles = glob($logPath . '/vchat_*.log');
        if ($logFiles) {
            echo "\n最近日志文件:\n";
            foreach (array_slice($logFiles, -3) as $logFile) {
                $fileTime = date('Y-m-d H:i:s', filemtime($logFile));
                $fileSize = round(filesize($logFile) / 1024, 2);
                echo "- " . basename($logFile) . " ({$fileTime}, {$fileSize}KB)\n";
            }
        }
    }
    
} else {
    echo "状态: 已停止\n";
    echo "PID文件存在但进程不存在\n";
    echo "建议: 清理PID文件\n";
    
    // 提供清理选项
    echo "\n是否清理PID文件? (y/N): ";
    $input = trim(fgets(STDIN));
    if (strtolower($input) === 'y') {
        unlink($pidFile);
        echo "PID文件已清理\n";
    }
}

echo "\n";