<?php
declare(strict_types=1);

/**
 * VChat 定时任务停止脚本
 * 用于停止正在运行的定时任务
 */

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../../app/common.php';

use app\vchat\utils\Logger;

// 初始化应用
\think\App::getInstance()->initialize();

// 创建日志记录器
$logger = new Logger();

$pidFile = __DIR__ . '/schedule.pid';

if (!file_exists($pidFile)) {
    echo "定时任务未运行\n";
    exit(0);
}

$pid = file_get_contents($pidFile);
if (!$pid) {
    echo "无效的PID文件\n";
    unlink($pidFile);
    exit(1);
}

$pid = (int)$pid;

// 检查进程是否存在
if (!posix_kill($pid, 0)) {
    echo "进程不存在，清理PID文件\n";
    unlink($pidFile);
    exit(0);
}

// 发送SIGTERM信号
if (posix_kill($pid, SIGTERM)) {
    echo "正在停止定时任务 (PID: {$pid})...\n";
    
    // 等待进程结束
    $timeout = 10; // 10秒超时
    $start = time();
    
    while (posix_kill($pid, 0) && (time() - $start) < $timeout) {
        usleep(500000); // 0.5秒
        echo ".";
    }
    
    echo "\n";
    
    // 检查进程是否已结束
    if (!posix_kill($pid, 0)) {
        echo "定时任务已停止\n";
        if (file_exists($pidFile)) {
            unlink($pidFile);
        }
        $logger->info('定时任务已手动停止', ['pid' => $pid]);
    } else {
        echo "进程未响应SIGTERM，尝试强制终止...\n";
        if (posix_kill($pid, SIGKILL)) {
            echo "定时任务已强制停止\n";
            if (file_exists($pidFile)) {
                unlink($pidFile);
            }
            $logger->warning('定时任务已强制停止', ['pid' => $pid]);
        } else {
            echo "无法停止进程\n";
            exit(1);
        }
    }
} else {
    echo "无法发送停止信号\n";
    exit(1);
}