<?php

use think\facade\Route;

/**
 * VChat第三方平台集成路由配置
 */

// 第三方平台Webhook路由（直接集成到VChat）
Route::group('vchat/webhook', function () {
    // 微信平台
    Route::any('wechat/miniprogram', 'vchat.ThirdPartyController/wechatMiniprogram');
    Route::any('wechat/officialaccount', 'vchat.ThirdPartyController/wechatOfficialAccount');
    Route::any('wechat/work', 'vchat.ThirdPartyController/wechatWork');
    
    // 其他平台
    Route::any('qq/bot', 'vchat.ThirdPartyController/qqBot');
    Route::any('dingtalk/bot', 'vchat.ThirdPartyController/dingtalkBot');
    Route::any('feishu/bot', 'vchat.ThirdPartyController/feishuBot');
});

// VChat第三方平台管理API路由
Route::group('vchat/api/third-party', function () {
    // 消息发送
    Route::post('send-message', 'vchat.ThirdPartyController/sendMessage');
    
    // 平台管理
    Route::get('platform-status', 'vchat.ThirdPartyController/getPlatformStatus');
    Route::post('test-connection', 'vchat.ThirdPartyController/testConnection');
    Route::get('config', 'vchat.ThirdPartyController/getConfig');
    
    // 统计数据
    Route::get('session-stats', 'vchat.ThirdPartyController/getSessionStats');
});
