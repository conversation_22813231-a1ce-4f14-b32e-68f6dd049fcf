<?php

namespace app\vchat\models;

use think\Model;
use think\facade\Cache;

/**
 * 菜单模型
 */
class Menu extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $table = 'vchat_menu';
    
    /**
     * 主键
     * @var string
     */
    protected $pk = 'id';
    
    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;
    
    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'params' => 'json',
        'children' => 'json',
        'status' => 'integer',
        'level' => 'integer',
        'sort' => 'integer'
    ];
    
    /**
     * 菜单状态常量
     */
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;
    
    /**
     * 菜单类型常量
     */
    const TYPE_CLICK = 'click';
    const TYPE_VIEW = 'view';
    const TYPE_BUTTON = 'button';
    const TYPE_MEDIA = 'media';
    const TYPE_TEXT = 'text';
    
    /**
     * 获取启用的菜单列表
     * 
     * @param int $parentId 父菜单ID
     * @return array
     */
    public static function getEnabledMenus(int $parentId = 0): array
    {
        $cacheKey = 'vchat:menu:enabled:' . $parentId;
        
        return Cache::remember($cacheKey, function() use ($parentId) {
            return self::where('parent_id', $parentId)
                      ->where('status', self::STATUS_ENABLED)
                      ->order('sort', 'asc')
                      ->order('id', 'asc')
                      ->select()
                      ->toArray();
        }, 3600);
    }
    
    /**
     * 获取菜单树结构
     * 
     * @param int $parentId 父菜单ID
     * @param int $maxLevel 最大层级
     * @return array
     */
    public static function getMenuTree(int $parentId = 0, int $maxLevel = 3): array
    {
        $menus = self::getEnabledMenus($parentId);
        $tree = [];
        
        foreach ($menus as $menu) {
            $menuItem = [
                'id' => $menu['menu_id'],
                'name' => $menu['name'],
                'type' => $menu['type'],
                'level' => $menu['level'],
                'sort' => $menu['sort'],
                'handler' => $menu['handler'],
                'params' => $menu['params'] ?? []
            ];
            
            // 如果是按钮类型且未达到最大层级，获取子菜单
            if ($menu['type'] === self::TYPE_BUTTON && $menu['level'] < $maxLevel) {
                $menuItem['children'] = self::getMenuTree($menu['id'], $maxLevel);
            }
            
            $tree[] = $menuItem;
        }
        
        return $tree;
    }
    
    /**
     * 根据菜单ID获取菜单信息
     * 
     * @param string $menuId 菜单ID
     * @return array|null
     */
    public static function getMenuById(string $menuId): ?array
    {
        $cacheKey = 'vchat:menu:info:' . $menuId;
        
        return Cache::remember($cacheKey, function() use ($menuId) {
            $menu = self::where('menu_id', $menuId)
                       ->where('status', self::STATUS_ENABLED)
                       ->find();
            
            return $menu ? $menu->toArray() : null;
        }, 3600);
    }
    
    /**
     * 创建菜单
     * 
     * @param array $data 菜单数据
     * @return bool
     */
    public static function createMenu(array $data): bool
    {
        try {
            $menu = new self();
            $menu->save($data);
            
            // 清除相关缓存
            self::clearMenuCache();
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 更新菜单
     * 
     * @param string $menuId 菜单ID
     * @param array $data 更新数据
     * @return bool
     */
    public static function updateMenu(string $menuId, array $data): bool
    {
        try {
            $menu = self::where('menu_id', $menuId)->find();
            if (!$menu) {
                return false;
            }
            
            $menu->save($data);
            
            // 清除相关缓存
            self::clearMenuCache();
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 删除菜单
     * 
     * @param string $menuId 菜单ID
     * @return bool
     */
    public static function deleteMenu(string $menuId): bool
    {
        try {
            $menu = self::where('menu_id', $menuId)->find();
            if (!$menu) {
                return false;
            }
            
            // 删除子菜单
            self::where('parent_id', $menu->id)->delete();
            
            // 删除菜单
            $menu->delete();
            
            // 清除相关缓存
            self::clearMenuCache();
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 启用/禁用菜单
     * 
     * @param string $menuId 菜单ID
     * @param int $status 状态
     * @return bool
     */
    public static function setMenuStatus(string $menuId, int $status): bool
    {
        try {
            $menu = self::where('menu_id', $menuId)->find();
            if (!$menu) {
                return false;
            }
            
            $menu->status = $status;
            $menu->save();
            
            // 清除相关缓存
            self::clearMenuCache();
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 批量导入菜单
     * 
     * @param array $menus 菜单数据
     * @param int $parentId 父菜单ID
     * @return bool
     */
    public static function importMenus(array $menus, int $parentId = 0): bool
    {
        try {
            foreach ($menus as $menuData) {
                $data = [
                    'menu_id' => $menuData['id'],
                    'parent_id' => $parentId,
                    'name' => $menuData['name'],
                    'type' => $menuData['type'],
                    'level' => $menuData['level'] ?? 1,
                    'sort' => $menuData['sort'] ?? 0,
                    'status' => $menuData['status'] ?? self::STATUS_ENABLED,
                    'handler' => $menuData['handler'] ?? '',
                    'params' => $menuData['params'] ?? []
                ];
                
                $menu = new self();
                $menu->save($data);
                
                // 处理子菜单
                if (!empty($menuData['children'])) {
                    self::importMenus($menuData['children'], $menu->id);
                }
            }
            
            // 清除相关缓存
            self::clearMenuCache();
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取菜单统计信息
     * 
     * @return array
     */
    public static function getMenuStats(): array
    {
        return [
            'total' => self::count(),
            'enabled' => self::where('status', self::STATUS_ENABLED)->count(),
            'disabled' => self::where('status', self::STATUS_DISABLED)->count(),
            'by_type' => self::field('type, count(*) as count')
                            ->group('type')
                            ->select()
                            ->toArray(),
            'by_level' => self::field('level, count(*) as count')
                             ->group('level')
                             ->select()
                             ->toArray()
        ];
    }
    
    /**
     * 验证菜单数据
     * 
     * @param array $data 菜单数据
     * @return array 验证结果
     */
    public static function validateMenuData(array $data): array
    {
        $errors = [];
        
        // 必填字段验证
        $required = ['menu_id', 'name', 'type'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[] = "字段 {$field} 不能为空";
            }
        }
        
        // 菜单ID唯一性验证
        if (!empty($data['menu_id'])) {
            $exists = self::where('menu_id', $data['menu_id'])->find();
            if ($exists) {
                $errors[] = "菜单ID {$data['menu_id']} 已存在";
            }
        }
        
        // 菜单类型验证
        $allowedTypes = [self::TYPE_CLICK, self::TYPE_VIEW, self::TYPE_BUTTON, self::TYPE_MEDIA, self::TYPE_TEXT];
        if (!empty($data['type']) && !in_array($data['type'], $allowedTypes)) {
            $errors[] = "不支持的菜单类型: {$data['type']}";
        }
        
        // 层级验证
        if (isset($data['level']) && ($data['level'] < 1 || $data['level'] > 3)) {
            $errors[] = "菜单层级必须在1-3之间";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 清除菜单缓存
     * 
     * @return void
     */
    public static function clearMenuCache(): void
    {
        Cache::tag('vchat_menu')->clear();
    }
    
    /**
     * 获取菜单类型列表
     * 
     * @return array
     */
    public static function getMenuTypes(): array
    {
        return [
            self::TYPE_CLICK => '点击事件',
            self::TYPE_VIEW => '跳转链接',
            self::TYPE_BUTTON => '子菜单',
            self::TYPE_MEDIA => '媒体消息',
            self::TYPE_TEXT => '文本消息'
        ];
    }
    
    /**
     * 获取菜单状态列表
     * 
     * @return array
     */
    public static function getMenuStatuses(): array
    {
        return [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用'
        ];
    }
}