<?php
declare(strict_types=1);

namespace app\vchat\core;

/**
 * 消息协议类
 * 定义聊天系统的消息格式和处理方法
 */
class MessageProtocol
{
    /**
     * 消息类型常量
     */
    const TYPE_TEXT = 'text';           // 文本消息
    const TYPE_IMAGE = 'image';         // 图片消息
    const TYPE_VOICE = 'voice';         // 语音消息
    const TYPE_VIDEO = 'video';         // 视频消息
    const TYPE_FILE = 'file';           // 文件消息
    const TYPE_SYSTEM = 'system';       // 系统消息
    const TYPE_NOTICE = 'notice';       // 通知消息
    const TYPE_TYPING = 'typing';       // 输入状态
    const TYPE_READ = 'read';           // 已读状态
    const TYPE_RECALL = 'recall';       // 撤回消息

    /**
     * 消息状态常量
     */
    const STATUS_SENDING = 0;    // 发送中
    const STATUS_SENT = 1;       // 已发送
    const STATUS_DELIVERED = 2;  // 已送达
    const STATUS_READ = 3;       // 已读
    const STATUS_FAILED = 4;     // 发送失败
    const STATUS_RECALLED = 5;   // 已撤回

    /**
     * 消息方向常量
     */
    const DIRECTION_SEND = 'send';     // 发送
    const DIRECTION_RECEIVE = 'received'; // 接收

    /**
     * 消息发送常量
     */
    const MESSAGE_SEND = 'send';     // 发送
    const MESSAGE_SEND_SUCCESS = 'message_status';
    const MESSAGE_SEND_FAILED = 'message_failed';
    const MESSAGE_READ = 'message_read';
    const MESSAGE_RECEIVE = 'receive';     // 接收
    const MESSAGE_RECEIVE_SUCCESS = 'message_receive';
    const MESSAGE_SYSTEM = 'system';     // 系统消息
    const MESSAGE_NOTICE = 'notice';     // 通知消息
    const MESSAGE_RECALL = 'message_recall';     // 撤回消息
    const MESSAGE_TYPING = 'typing';     // 输入状态
    const MESSAGE_WELCOME = 'welcome'; //欢迎消息
    const MESSAGE_TYPE = 'message';
    const MESSAGE_CHAT_TYPE = 'chat_message';
    const MESSAGE_GROUP_TYPE = 'group_message';
    const MESSAGE_GROUP_CHAT_TYPE = 'group_chat_message';
    const MESSAGE_REQUEST_TYPE = 'request_service';
    const MESSAGE_RESPONSE_TYPE = 'service_response';
    const MESSAGE_END_TYPE = 'end_session';
    const MESSAGE_NEW_SESSION_TYPE = 'new_session';
    const MESSAGE_AUTHORIZE = 'authenticate';
    const MESSAGE_AUTHORIZE_SUCCESS = 'auth_success';
    const MESSAGE_TRANSFER = 'transfer';
    const MESSAGE_TRANSFER_SUCCESS = 'transfer_success';
    const MESSAGE_TRANSFER_FAILED = 'transfer_failed';
    const MESSAGE_SERVICE_CHANGED = 'service_changed';
    const MESSAGE_SERVICE_ASSIGNED = 'service_assigned';
    const MESSAGE_QUEUE_TIMEOUT = 'queue_timeout';
    const MESSAGE_QUEUE_STATUS = 'queue_status';
    const MESSAGE_QUEUE_JOINED = 'queue_joined';
    const MESSAGE_QUEUE_NOTIFICATION = 'queue_notification';
    const MESSAGE_SESSION_TIMEOUT = 'session_timeout';
    const MESSAGE_TIMEOUT_WARNING = 'timeout_warning';
    // 新增建议问题消息类型
    const MESSAGE_TYPE_SUGGESTIONS = 'suggestions';
    const MESSAGE_RATE_SUCCESS = 'rate_success';
    const MESSAGE_RATING = 'rating';

    /**
     * 格式化消息
     * @param array $data 消息数据
     * @return array 格式化后的消息
     */
    public static function format(array $data): array
    {
        return [
            'id' => $data['id'] ?? self::generateMessageId(),
            'type' => $data['type'] ?? self::TYPE_TEXT,
            'content' => $data['content'] ?? '',
            'from_id' => $data['from_id'] ?? 0,
            'from_type' => $data['from_type'] ?? 'user',
            'message_type' => $data['message_type'] ?? 'message',
            'to_id' => $data['to_id'] ?? 0,
            'to_type' => $data['to_type'] ?? 'service',
            'session_id' => $data['session_id'] ?? 0,
            'status' => $data['status'] ?? self::STATUS_SENDING,
            'direction' => $data['direction'] ?? self::DIRECTION_SEND,
            'timestamp' => $data['timestamp'] ?? time(),
            'extra' => $data['extra'] ?? [],
        ];
    }

    /**
     * 生成消息ID
     * @return string
     */
    public static function generateMessageId(): string
    {
        return uniqid('msg_', true);
    }

    /**
     * 创建文本消息
     * @param string $content 消息内容
     * @param array $extra 额外数据
     * @return array
     */
    public static function createTextMessage(string $content, array $extra = []): array
    {
        return self::format([
            'type' => self::TYPE_TEXT,
            'content' => $content,
            'extra' => $extra
        ]);
    }

    /**
     * 创建图片消息
     * @param string $url 图片URL
     * @param array $extra 额外数据
     * @return array
     */
    public static function createImageMessage(string $url, array $extra = []): array
    {
        return self::format([
            'type' => self::TYPE_IMAGE,
            'content' => $url,
            'extra' => array_merge($extra, [
                'width' => $extra['width'] ?? 0,
                'height' => $extra['height'] ?? 0,
                'size' => $extra['size'] ?? 0,
            ])
        ]);
    }

    /**
     * 创建语音消息
     * @param string $url 语音URL
     * @param int $duration 时长(秒)
     * @param array $extra 额外数据
     * @return array
     */
    public static function createVoiceMessage(string $url, int $duration, array $extra = []): array
    {
        return self::format([
            'type' => self::TYPE_VOICE,
            'content' => $url,
            'extra' => array_merge($extra, [
                'duration' => $duration,
                'size' => $extra['size'] ?? 0,
            ])
        ]);
    }

    /**
     * 创建视频消息
     * @param string $url 视频URL
     * @param array $extra 额外数据
     * @return array
     */
    public static function createVideoMessage(string $url, array $extra = []): array
    {
        return self::format([
            'type' => self::TYPE_VIDEO,
            'content' => $url,
            'extra' => array_merge($extra, [
                'duration' => $extra['duration'] ?? 0,
                'size' => $extra['size'] ?? 0,
                'thumbnail' => $extra['thumbnail'] ?? '',
            ])
        ]);
    }

    /**
     * 创建文件消息
     * @param string $url 文件URL
     * @param string $name 文件名
     * @param int $size 文件大小
     * @param array $extra 额外数据
     * @return array
     */
    public static function createFileMessage(string $url, string $name, int $size, array $extra = []): array
    {
        return self::format([
            'type' => self::TYPE_FILE,
            'content' => $url,
            'extra' => array_merge($extra, [
                'name' => $name,
                'size' => $size,
                'extension' => pathinfo($name, PATHINFO_EXTENSION),
            ])
        ]);
    }

    /**
     * 创建系统消息
     * @param string $content 消息内容
     * @param array $extra 额外数据
     * @return array
     */
    public static function createSystemMessage(string $content, array $extra = []): array
    {
        return self::format([
            'type' => self::TYPE_SYSTEM,
            'content' => $content,
            'extra' => $extra,
            'message_type' => self::MESSAGE_SYSTEM
        ]);
    }

    /**
     * 创建通知消息
     * @param string $content 消息内容
     * @param array $extra 额外数据
     * @return array
     */
    public static function createNoticeMessage(string $content, array $extra = []): array
    {
        return self::format([
            'type' => self::TYPE_NOTICE,
            'content' => $content,
            'extra' => $extra
        ]);
    }

    /**
     * 创建输入状态消息
     * @param bool $isTyping 是否正在输入
     * @return array
     */
    public static function createTypingMessage(bool $isTyping): array
    {
        return self::format([
            'type' => self::TYPE_TYPING,
            'content' => $isTyping ? 'typing' : 'stop',
            'extra' => ['is_typing' => $isTyping],
            'message_type' => self::MESSAGE_TYPING
        ]);
    }

    /**
     * 创建已读状态消息
     * @param string $messageId 消息ID
     * @return array
     */
    public static function createReadMessage(mixed $messageId): array
    {
        return self::format([
            'type' => self::TYPE_READ,
            'content' => $messageId,
            'extra' => ['message_id' => $messageId],
            'message_type' => self::MESSAGE_READ
        ]);
    }

    /**
     * 创建撤回消息
     * @param string $messageId 消息ID
     * @return array
     */
    public static function createRecallMessage(string $messageId): array
    {
        return self::format([
            'type' => self::TYPE_RECALL,
            'content' => $messageId,
            'extra' => ['message_id' => $messageId]
        ]);
    }

    /**
     * 验证消息格式
     * @param array $message 消息数据
     * @return bool
     */
    public static function validate(array $message): bool
    {
        $required = ['type', 'content', 'from_id', 'from_type', 'to_id', 'to_type', 'message_type'];
        foreach ($required as $field) {
            if (!isset($message[$field])) {
                return false;
            }
        }

        // 验证消息类型
        $validTypes = [
            self::TYPE_TEXT,
            self::TYPE_IMAGE,
            self::TYPE_VOICE,
            self::TYPE_VIDEO,
            self::TYPE_FILE,
            self::TYPE_SYSTEM,
            self::TYPE_NOTICE,
            self::TYPE_TYPING,
            self::TYPE_READ,
            self::TYPE_RECALL
        ];

        if (!in_array($message['type'], $validTypes)) {
            return false;
        }

        return true;
    }

    /**
     * 获取消息类型描述
     * @param string $type 消息类型
     * @return string
     */
    public static function getTypeDescription(string $type): string
    {
        $descriptions = [
            self::TYPE_TEXT => '文本消息',
            self::TYPE_IMAGE => '图片消息',
            self::TYPE_VOICE => '语音消息',
            self::TYPE_VIDEO => '视频消息',
            self::TYPE_FILE => '文件消息',
            self::TYPE_SYSTEM => '系统消息',
            self::TYPE_NOTICE => '通知消息',
            self::TYPE_TYPING => '输入状态',
            self::TYPE_READ => '已读状态',
            self::TYPE_RECALL => '撤回消息'
        ];

        return $descriptions[$type] ?? '未知类型';
    }
} 