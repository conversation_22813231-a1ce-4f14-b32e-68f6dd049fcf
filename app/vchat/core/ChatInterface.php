<?php
declare(strict_types=1);

namespace app\vchat\core;

use think\Request;
use think\swoole\Websocket;

/**
 * 聊天系统接口
 */
interface ChatInterface
{
    /**
     * 处理连接建立
     * @param Websocket $websocket
     * @param array $request
     */
    public function onOpen(Websocket $websocket, Request $request): bool;

    /**
     * 处理消息接收
     * @param Websocket $websocket
     * @param array $data
     */
    public function onMessage(Websocket $websocket, array $data): void;

    /**
     * 处理连接关闭
     * @param Websocket $websocket
     * @param array $request
     */
    public function onClose(Websocket $websocket, Request $request): void;

    /**
     * 处理认证
     * @param Websocket $websocket
     * @param array $data
     * @return bool
     */
    public function handleAuth(Websocket $websocket, array $data): bool;

    /**
     * 发送消息
     * @param Websocket $websocket
     * @param string $event
     * @param array $data
     * @return void
     */
    public function emit(Websocket $websocket, string $event, array $data): void;
} 