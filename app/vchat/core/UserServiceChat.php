<?php
declare(strict_types=1);

namespace app\vchat\core;

use app\vchat\handlers\UserHandler;
use app\vchat\handlers\MessageHandler;
use app\vchat\utils\Logger;
use think\Request;
use think\swoole\Websocket;

/**
 * 用户端聊天实现类
 */
class UserServiceChat extends AbstractChat
{
    /**
     * @var UserHandler
     */
    protected $userHandler;

    /**
     * 构造方法
     */
    public function __construct()
    {
        parent::__construct();
        $this->userHandler = new UserHandler();
    }

    /**
     * 处理认证
     * @param Websocket $websocket
     * @param array $data
     * @return bool
     */
    public function handleAuth(Websocket $websocket, array $data): bool
    {
        return $this->userHandler->handleConnect($websocket, $data);
    }

    /**
     * 处理消息接收
     * @param Websocket $websocket
     * @param array $data
     */
    public function onMessage(Websocket $websocket, array $data): void
    {
        try {
            $this->logger->info('用户收到消息', [
                'fd' => $websocket->getSender(),
                'data' => $data
            ]);

            // 根据消息类型分发处理
            switch ($data['message_type'] ?? '') {
                case MessageProtocol::MESSAGE_TYPE:
                    $this->userHandler->handleMessage($websocket, $data);
                    break;
                case MessageProtocol::MESSAGE_CHAT_TYPE:
                    $this->userHandler->handleMessage($websocket, $data);
                    break;
                case MessageProtocol::MESSAGE_REQUEST_TYPE:
                    $this->userHandler->handleServiceRequest($websocket, $data);
                    break;
                case 'rating':
                    $this->userHandler->handleRating($websocket, $data);
                    break;
                case MessageProtocol::MESSAGE_TYPING:
                    $this->userHandler->handleTypingStatus($websocket, $data);
                    break;
                case MessageProtocol::MESSAGE_READ:
                    $this->userHandler->handleReadReceipt($websocket, $data);
                    break;
                default:
                    // 其他消息类型由MessageHandler处理
                    parent::onMessage($websocket, $data);
                    break;
            }
        } catch (\Exception $e) {
            $this->logger->error('用户消息处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->emit($websocket, 'error', [
                'message' => '消息处理失败',
                'timestamp' => time()
            ]);
        }
    }

    /**
     * 处理连接关闭
     * @param Websocket $websocket
     * @param array $request
     */
    public function onClose(Websocket $websocket, Request $request): void
    {
        try {
            // 获取用户ID
            $userId = $this->getUserId($websocket);
            if ($userId) {
                // 更新用户状态为离线
                $this->userHandler->handleDisconnect($websocket, $userId);
            }

            parent::onClose($websocket, $request);
        } catch (\Exception $e) {
            $this->logger->error('用户连接关闭处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 获取用户ID
     * @param Websocket $websocket
     * @return int|null
     */
    protected function getUserId(Websocket $websocket): ?int
    {
        try {
            $fd = $websocket->getSender();
            $rooms = $this->roomService->getRoomIds($fd);
            foreach ($rooms as $room) {
                if (strpos($room, 'user_') === 0) {
                    return (int)substr($room, 5);
                }
            }
            return null;
        } catch (\Exception $e) {
            $this->logger->error('获取用户ID失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }
} 