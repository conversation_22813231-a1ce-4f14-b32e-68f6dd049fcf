<?php
declare(strict_types=1);

namespace app\vchat\core;

use app\vchat\events\EventManager;
use app\vchat\handlers\MessageHandler;
use app\vchat\services\RoomService;
use app\vchat\utils\Logger;
use think\facade\Log;
use think\Request;
use think\swoole\Websocket;
use think\swoole\websocket\Room;

/**
 * 聊天系统抽象基类
 */
abstract class AbstractChat implements ChatInterface
{
    /**
     * @var Room
     */
    protected $room;

    /**
     * @var EventManager
     */
    protected $eventManager;

    /**
     * @var MessageHandler
     */
    protected $messageHandler;

    /**
     * @var RoomService
     */
    protected $roomService;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->room = app()->make(Room::class);
        $this->eventManager = new EventManager();
        $this->messageHandler = new MessageHandler();
        $this->roomService = new RoomService();
        $this->logger = new Logger();
    }

    /**
     * 处理连接建立
     */
    public function onOpen(Websocket $websocket, Request $request): bool
    {
        try {
            $this->logger->info('WebSocket连接建立', [
                'fd' => $websocket->getSender(),
                'request' => $request
            ]);

            $this->emit($websocket, MessageProtocol::MESSAGE_WELCOME, [
                'message_type' => MessageProtocol::MESSAGE_WELCOME,
                'type' => 'text',
                'message' => '欢迎使用聊天系统',
                'timestamp' => time()
            ]);

        } catch (\Exception $e) {
            $this->logger->error('连接建立失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }

        return true;

    }

    /**
     * 处理消息接收
     */
    public function onMessage(Websocket $websocket, array $data): void
    {
        try {
            $this->logger->info('收到消息', [
                'fd' => $websocket->getSender(),
                'data' => $data
            ]);

            $this->messageHandler->handle($websocket, $data);
        } catch (\Exception $e) {
            $this->logger->error('消息处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理连接关闭
     */
    public function onClose(Websocket $websocket, Request $request): void
    {
        try {
            $fd = $websocket->getSender();
            $this->roomService->handleDisconnect($fd);
            
            $this->logger->info('连接关闭', [
                'fd' => $fd
            ]);
        } catch (\Exception $e) {
            $this->logger->error('连接关闭处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 发送消息
     */
    public function emit(Websocket $websocket, string $event, array $data): void
    {
        try {
            $websocket->emit($event, $data);
            $this->logger->info('发送消息', [
                'event' => $event,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            $this->logger->error('消息发送失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理认证
     */
    abstract public function handleAuth(Websocket $websocket, array $data): bool;
} 