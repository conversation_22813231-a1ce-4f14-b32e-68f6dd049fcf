<?php
declare(strict_types=1);

namespace app\vchat\core;

use app\vchat\handlers\CustomerServiceHandler;
use app\vchat\handlers\MessageHandler;
use app\vchat\utils\Logger;
use think\Request;
use think\swoole\Websocket;

/**
 * 客服端聊天实现类
 */
class CustomerServiceChat extends AbstractChat
{
    /**
     * @var CustomerServiceHandler
     */
    protected $serviceHandler;

    /**
     * 构造方法
     */
    public function __construct()
    {
        parent::__construct();
        $this->serviceHandler = new CustomerServiceHandler();
    }

    /**
     * 处理认证
     * @param Websocket $websocket
     * @param array $data
     * @return bool
     */
    public function handleAuth(Websocket $websocket, array $data): bool
    {
        return $this->serviceHandler->handleConnect($websocket, $data);
    }

    /**
     * 处理消息接收
     * @param Websocket $websocket
     * @param array $data
     */
    public function onMessage(Websocket $websocket, array $data): void
    {
        try {
            $this->logger->info('客服收到消息', [
                'fd' => $websocket->getSender(),
                'data' => $data
            ]);

            // 根据消息类型分发处理
            switch ($data['message_type'] ?? '') {
                case MessageProtocol::MESSAGE_TYPE:
                    $this->serviceHandler->handleMessage($websocket, $data);
                    break;
                case MessageProtocol::MESSAGE_CHAT_TYPE:
                    $this->serviceHandler->handleMessage($websocket, $data);
                    break;
                case MessageProtocol::MESSAGE_TRANSFER:
                    $this->serviceHandler->handleTransferRequest($websocket, $data);
                    break;
                case MessageProtocol::MESSAGE_END_TYPE:
                    $this->serviceHandler->handleEndSession($websocket, $data);
                    break;
                case MessageProtocol::MESSAGE_TYPING:
                    $this->serviceHandler->handleTypingStatus($websocket, $data);
                    break;
                case MessageProtocol::MESSAGE_READ:
                    $this->serviceHandler->handleReadReceipt($websocket, $data);
                    break;
                default:
                    // 其他消息类型由MessageHandler处理
                    parent::onMessage($websocket, $data);
                    break;
            }
        } catch (\Exception $e) {
            $this->logger->error('客服消息处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->emit($websocket, 'error', [
                'message' => '消息处理失败',
                'timestamp' => time()
            ]);
        }
    }

    /**
     * 处理连接关闭
     * @param Websocket $websocket
     * @param array $request
     */
    public function onClose(Websocket $websocket, Request $request): void
    {
        try {
            // 获取客服ID
            $serviceId = $this->getServiceId($websocket);
            $this->logger->info('客服ID:' . $serviceId);

            if ($serviceId) {
                // 更新客服状态为离线
                $this->serviceHandler->handleDisconnect($websocket, $serviceId);
            }

            parent::onClose($websocket, $request);
        } catch (\Exception $e) {
            $this->logger->error('客服连接关闭处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 获取客服ID
     * @param Websocket $websocket
     * @return int|null
     */
    protected function getServiceId(Websocket $websocket): ?int
    {
        try {
            $fd = $websocket->getSender();
            $rooms = $this->roomService->getRoomIds($fd);
            foreach ($rooms as $room) {
                if (strpos($room, 'service_') === 0) {
                    return (int)substr($room, 8);
                }
            }
            return null;
        } catch (\Exception $e) {
            $this->logger->error('获取客服ID失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }
} 