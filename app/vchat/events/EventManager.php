<?php
declare(strict_types=1);

namespace app\vchat\events;

use think\swoole\Websocket;

/**
 * 事件管理器
 */
class EventManager
{
    /**
     * @var array 事件处理器映射
     */
    protected $handlers = [];

    /**
     * 注册事件处理器
     * @param string $event 事件名称
     * @param callable $handler 处理器
     */
    public function register(string $event, callable $handler): void
    {
        $this->handlers[$event] = $handler;
    }

    /**
     * 触发事件
     * @param string $event
     * @param Websocket $websocket
     * @param array $data
     */
    public function trigger(string $event, Websocket $websocket, array $data, ...$args): void
    {
        if (isset($this->handlers[$event])) {
            // 合并所有参数并传递给回调
            $callbackArgs = [$websocket, $data];
            foreach ($args as $arg) {
                $callbackArgs[] = $arg;
            }

            call_user_func_array($this->handlers[$event], $callbackArgs);
        }
    }

    /**
     * 获取事件处理器
     * @param string $event
     * @return callable|null
     */
    public function getHandler(string $event): ?callable
    {
        return $this->handlers[$event] ?? null;
    }

    /**
     * 移除事件处理器
     * @param string $event
     */
    public function removeHandler(string $event): void
    {
        unset($this->handlers[$event]);
    }
} 