<?php
declare(strict_types=1);

namespace app\vchat\handlers;

use app\vchat\core\MessageProtocol;
use app\vchat\services\CustomerService;
use app\vchat\services\MessageService;
use app\vchat\services\SessionService;
use app\vchat\services\UserService;
use app\vchat\services\UserStatusService;
use app\vchat\services\RoomService;
use app\vchat\services\QueueService;
use app\vchat\services\SessionTimeoutService;
use app\vchat\utils\Logger;
use think\swoole\Websocket;

/**
 * 客服端处理器
 */
class CustomerServiceHandler
{
    /**
     * @var MessageService
     */
    protected $messageService;

    /**
     * @var SessionService
     */
    protected $sessionService;

    /**
     * @var UserStatusService
     */
    protected $userStatusService;

    /**
     * @var RoomService
     */
    protected $roomService;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var  CustomerService
     */
    protected $customerService;

    /**
     * @var QueueService
     */
    protected $queueService;

    /**
     * @var SessionTimeoutService
     */
    protected $timeoutService;

    /**
     * @var \app\vchat\services\AutoAssignService
     */
    protected $autoAssignService;

    protected $userService;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->messageService = new MessageService();
        $this->sessionService = new SessionService();
        $this->userStatusService = new UserStatusService();
        $this->customerService = new CustomerService();
        $this->roomService = new RoomService();
        $this->queueService = new QueueService();
        $this->timeoutService = new SessionTimeoutService();
        $this->autoAssignService = new \app\vchat\services\AutoAssignService();
        $this->userService = new UserService();
        $this->logger = new Logger();
    }

    /**
     * 处理连接
     * @param Websocket $websocket
     * @param array $data
     * @return bool
     */
    public function handleConnect(Websocket $websocket, array $data): bool
    {
        try {
            $token = $data['token'] ?? '';
            if (empty($token)) {
                $this->sendError($websocket, '缺少认证token');
                return false;
            }

            if (!$this->handleAuthentication($websocket, $data)) {
                $this->sendError($websocket, '认证失败');
                return false;
            }

            return true;
        } catch (\Exception $e) {
            $this->logger->error('客服连接处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '认证失败');
            return false;
        }
    }

    /**
     * 处理消息
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleMessage(Websocket $websocket, array $data): void
    {
        try {
//            if (!isset($data['to_id'], $data['content'])) {
//                $this->sendError($websocket, '消息格式错误');
//                return;
//            }

            // 验证消息格式
            if (!MessageProtocol::validate($data)) {
                $this->sendError($websocket, '消息格式无效');
                $this->logger->error('消息格式无效', ['data' => $data]);
                return;
            }

            // 验证消息格式
            if (!MessageProtocol::validate($data)) {
                $this->logger->error('消息格式无效', ['data' => $data]);
                return;
            }

            $session = $this->sessionService->getSessionDetail($data['session_id']);
            if (!$session) {
                $this->sendError($websocket, '会话不存在');
                return;
            }

            if ($session['status'] != 1) {
                $this->sendError($websocket, '会话已结束');
                return;
            }

            $message = [
                'from_id' => $data['from_id'],
                'from_type' => 'service',
                'to_id' => $data['to_id'],
                'to_type' => 'user',
                'content' => $data['content'],
                'type' => $data['type'],
                'session_id' => $data['session_id'],
                'message_type' => $data['message_type'],
                'status' => 1,
                'local_id' => $data['local_id'] ?? ''
            ];

            $this->logger->info('客服发送消息', $message);


            $savedMessage = $this->messageService->saveMessage($message);
            if ($savedMessage) {
                $savedMessage['local_id']  = $message['local_id'];
                $this->messageService->sendMessage($websocket, $savedMessage);
                // 记录客服活动，用于会话超时管理
                $this->timeoutService->recordServiceActivity($data['to_id'], $data['from_id']);
            } else {
                $this->sendError($websocket, '消息发送失败');
            }
        } catch (\Exception $e) {
            $this->logger->error('处理客服消息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '消息处理失败');
        }
    }

    /**
     * 处理已读回执
     * @param Websocket $websocket
     * @param array $data 包含 message_id
     */
    public function handleReadReceipt(Websocket $websocket, array $data): void
    {
        try {
            if (!isset($data['message_id'])) {
                $this->sendError($websocket, '缺少消息ID');
                return;
            }

            $messageId = $data['message_id'];
            $this->messageService->updateMessageReadStatus($messageId);

            $this->logger->info('客服处理消息已读回执成功', [
                'message_id' => $messageId,
                'service_fd' => $websocket->getSender()
            ]);

        } catch (\Exception $e) {
            $this->logger->error('客服处理消息已读回执失败', [
                'error' => $e->getMessage(),
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '处理已读回执失败');
        }
    }

    /**
     * 处理转接请求
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleTransferRequest(Websocket $websocket, array $data): void
    {
        try {
            if (!isset($data['to_id'], $data['to_service_id'])) {
                $this->sendError($websocket, '转接参数错误');
                return;
            }

            $fromServiceId = $this->getServiceId($websocket);
            if (!$fromServiceId) {
                $this->sendError($websocket, '无法获取客服ID');
                return;
            }

            $session = $this->sessionService->getSessionDetail($data['session_id']);
            if (!$session) {
                $this->sendError($websocket, '会话不存在');
                return;
            }

            if ($this->sessionService->transferSession($session['id'], $data['to_service_id'])) {
                // 发送系统消息
                $systemMessage = [
                    'type' => $data['type'] ?? 'text',
                    'message_type' => $data['message_type'] ?? 'system',
                    'from_id' => $fromServiceId,
                    'from_type' => 'service',
                    'to_id' => $data['to_id'],
                    'session_id' => $data['session_id'],
                    'to_type' => 'user',
                    'content' => $data['content'] ?? '会话已转接给其他客服',
                    'status' => 1
                ];
                $this->messageService->saveMessage($systemMessage);

                // 通知相关方
                $this->notifyTransferSuccess($websocket, $fromServiceId, $data['to_service_id'], $data['to_id'], $session['id']);
            } else {
                $this->sendError($websocket, '转接失败');
            }
        } catch (\Exception $e) {
            $this->logger->error('处理转接请求失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '转接处理失败');
        }
    }

    /**
     * 处理结束会话
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleEndSession(Websocket $websocket, array $data): void
    {
        try {
            if (!isset($data['session_id'])) {
                $this->sendError($websocket, '会话ID不能为空');
                return;
            }

            $sessionId = (int)$data['session_id'];
            $session = $this->sessionService->getSessionDetail($sessionId);

            if (!$session || $session['status'] != 1) {
                // 向客服端广播会话结束
                $websocket->emit(MessageProtocol::MESSAGE_END_TYPE, [
                    'session_id' => $sessionId
                ]);
                return;
            }
            
            if ($this->sessionService->endSession($sessionId)) {
                // 清理会话超时记录
                if ($session) {
                    $this->timeoutService->clearActivity($session['user_id'], $session['service_id']);
                }
                
                // 向客服端广播会话结束
                $websocket->emit(MessageProtocol::MESSAGE_END_TYPE, [
                    'session_id' => $sessionId
                ]);

                // 向用户端发送会话关闭通知
                $this->notifyUserSessionClosed($websocket, $data['to_id'], $data['from_id'], $sessionId);
                
                // 尝试为该客服分配下一个排队用户
                $this->assignNextQueuedUser($websocket, $data['from_id']);
                
                // 触发自动分配服务，为其他空闲客服分配排队用户
                $this->autoAssignService->assignQueuedUsersToIdleServices($websocket);
            } else {
                $this->sendError($websocket, '结束会话失败');
            }
        } catch (\Exception $e) {
            $this->logger->error('处理结束会话失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '结束会话处理失败');
        }
    }

    /**
     * 通知用户关闭会话
     * @param Websocket $websocket
     * @param int $serviceId
     * @param int $userId
     * @param int $sessionId
     */
    protected function notifyUserSessionClosed(Websocket $websocket, int $serviceId, int $userId, int $sessionId): void
    {
        try {
            $message = [
                'type' => 'text',
                'message_type' => 'end_session',
                'from_id' => $serviceId,
                'from_type' => 'service',
                'to_id' => $userId,
                'to_type' => 'user',
                'session_id' => $sessionId,
                'content' => '会话结束',
                'status' => 1
            ];
            // 保存消息并获取message_id
            $savedMessage = $this->messageService->saveMessage($message);
            if ($savedMessage) {
                $message['id'] = $savedMessage['id'];
                $this->messageService->sendMessage($websocket, $message);
            }
        } catch (\Exception $e) {
            $this->logger->error('通知客服新会话失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理客服断开连接
     * @param Websocket $websocket
     * @param int $serviceId
     */
    public function handleDisconnect(Websocket $websocket, int $serviceId): void
    {
        try {
            // 更新客服在线状态
            $this->userStatusService->updateOnlineStatus($websocket, $serviceId, 'service', false);
            $this->userStatusService->broadcastStatusChange($websocket, $serviceId, 'service', false);

            // 清理房间
            $this->roomService->handleDisconnect($websocket->getSender());
            $this->customerService->getCustomerServiceModel()->updateStatus($serviceId, 0);

            // 更新客服状态
            $this->customerService->getCustomerServiceModel()
                ->where('id', $serviceId)
                ->update(['status' => 0]);

            $this->logger->info('客服断开连接', [
                'service_id' => $serviceId,
                'fd' => $websocket->getSender()
            ]);
        } catch (\Exception $e) {
            $this->logger->error('处理客服断开连接失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理客服认证
     * @param Websocket $websocket
     * @param array $data
     * @return bool
     */
    public function handleAuthentication(Websocket $websocket, array $data): bool
    {
        try {
            $token = $data['token'] ?? '';
            if (empty($token)) {
                $this->sendError($websocket, '缺少认证token');
                return false;
            }

            $tokenData = \app\common\Auth::checkToken($token);
            if (!$tokenData) {
                $this->sendError($websocket, 'token无效');
                return false;
            }

            $serviceUser = $this->customerService->getCustomerServiceModel()
                ->where('admin_id', $tokenData['user_id'])
                ->where('status', '<>', -1)
                ->find();

            if (!$serviceUser) {
                $this->logger->error('客服不存在或已被禁用');
                $this->sendError($websocket, '客服不存在或已被禁用');
                return false;
            }

            // 将客服加入对应房间
            $this->roomService->join($websocket, 'service_' . $serviceUser['id']);
            
            // 更新客服状态
            $this->customerService->getCustomerServiceModel()
                ->where('id', $serviceUser['id'])
                ->update(['status' => 1]);

            // 更新在线状态
            $this->userStatusService->updateOnlineStatus($websocket, $serviceUser['id'], 'service', true);
            $this->userStatusService->broadcastStatusChange($websocket, $serviceUser['id'], 'service', true);

            // 发送认证成功消息
            $websocket->emit(MessageProtocol::MESSAGE_AUTHORIZE_SUCCESS, $serviceUser);

            $this->logger->info('客服认证成功', [
                'service_id' => $serviceUser['id'],
                'fd' => $websocket->getSender()
            ]);

            // 客服上线后自动分配排队用户
            $this->assignNextQueuedUser($websocket, $serviceUser['id']);

            return true;
        } catch (\Exception $e) {
            $this->logger->error('客服认证失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '认证失败');
            return false;
        }
    }

    /**
     * 处理输入状态
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleTypingStatus(Websocket $websocket, array $data): void
    {
        try {
            if (!isset($data['to_id'], $data['status'])) {
                $this->sendError($websocket, '参数错误');
                return;
            }

            $serviceId = $this->getServiceId($websocket);
            if (!$serviceId) {
                $this->sendError($websocket, '无法获取客服ID');
                return;
            }

            $this->messageService->sendTypingStatus($websocket, [
                'from_id' => $serviceId,
                'from_type' => 'service',
                'to_id' => $data['to_id'],
                'to_type' => 'user',
                'content' => $data['status'] ? 'typing' : 'stop',
                'status' => 1
            ]);

            $this->logger->info('客服输入状态更新', [
                'service_id' => $serviceId,
                'user_id' => $data['to_id'],
                'status' => $data['status']
            ]);
        } catch (\Exception $e) {
            $this->logger->error('处理输入状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '更新输入状态失败');
        }
    }

    /**
     * 获取客服ID
     * @param Websocket $websocket
     * @return int|null
     */
    protected function getServiceId(Websocket $websocket): ?int
    {
        try {
            $fd = $websocket->getSender();
            $rooms = $this->roomService->getRoomIds($fd);
            foreach ($rooms as $room) {
                if (strpos($room, 'service_') === 0) {
                    return (int)substr($room, 8);
                }
            }
            return null;
        } catch (\Exception $e) {
            $this->logger->error('获取客服ID失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 通知转接成功
     * @param Websocket $websocket
     * @param int $fromServiceId
     * @param int $toServiceId
     * @param int $userId
     */
    protected function notifyTransferSuccess(Websocket $websocket, int $fromServiceId, int $toServiceId, int $userId, int $sessionId): void
    {
        // 准备批量插入的消息数据
        $messages = [
            [
                'from_id' => $fromServiceId,
                'type' => MessageProtocol::MESSAGE_SYSTEM,
                'message_type' => MessageProtocol::MESSAGE_TRANSFER_SUCCESS,
                'from_type' => 'service',
                'to_id' => $fromServiceId,
                'to_type' => 'service',
                'content' => '转接成功',
                'session_id' => $sessionId,
                'status' => 1
            ],
            [
                'from_id' => $fromServiceId,
                'type' => MessageProtocol::MESSAGE_SYSTEM,
                'message_type' => MessageProtocol::MESSAGE_NEW_SESSION_TYPE,
                'from_type' => 'service',
                'to_id' => $toServiceId,
                'to_type' => 'service',
                'content' => '新会话已转接',
                'session_id' => $sessionId,
                'status' => 1
            ],
            [
                'from_id' => $fromServiceId,
                'type' => MessageProtocol::MESSAGE_SYSTEM,
                'message_type' => MessageProtocol::MESSAGE_SERVICE_CHANGED,
                'from_type' => 'service',
                'to_id' => $userId,
                'to_type' => 'user',
                'content' => '客服已更换',
                'session_id' => $sessionId,
                'status' => 1
            ]
        ];

        // 批量保存消息并获取各自的ID
        $savedMessages = $this->messageService->saveMessages($messages);

        if ($savedMessages && count($savedMessages) === 3) {
            // 通知原客服
            $fromMessage = $savedMessages[0];
            $this->messageService->sendMessage($websocket, $fromMessage);

            $user = $this->userService->getUserById($userId);

            // 通知新客服
            $toMessage = $savedMessages[1];
            $toMessage['user'] = $user->toArray();
            $this->messageService->sendMessage($websocket, $toMessage);

            // 获取新客服信息
            $newService = $this->customerService->getCustomerServiceModel()->getServiceInfo($toServiceId);
            
            // 通知用户
            $userMessage = $savedMessages[2];
            $userMessage['service'] = $newService;
            $this->messageService->sendMessage($websocket, $userMessage);
        }
    }

    /**
     * 为客服分配下一个排队用户
     * @param Websocket $websocket
     * @param int $serviceId
     */
    protected function assignNextQueuedUser(Websocket $websocket, int $serviceId): void
    {
        try {
            // 检查客服是否还能接待新会话
            $customerModel = $this->customerService->getCustomerServiceModel();
            if (!$customerModel->canAcceptNewSession($serviceId)) {
                return;
            }

            // 获取下一个排队用户
            $nextUser = $this->queueService->getNextUser($serviceId);
            if (!$nextUser) {
                return;
            }

            // 从队列中移除用户
//            $this->queueService->removeFromQueue($nextUser['user_id']);

            // 创建新会话
            $sessionData = [
                'user_id' => $nextUser['user_id'],
                'service_id' => $serviceId,
                'status' => 1,
                'start_time' => date('Y-m-d H:i:s')
            ];

            $session = $this->sessionService->createSession($sessionData['user_id'], $sessionData['service_id']);
            if ($session) {
                // 更新客服会话数
                $customerModel->increaseCurrentSessions($serviceId);

                // 记录用户和客服活动
                $this->timeoutService->recordUserActivity($nextUser['user_id'], $session['id']);
                $this->timeoutService->recordServiceActivity($serviceId, $serviceId);

                // 通知用户分配成功
                $this->notifyUserAssigned($websocket, $nextUser['user_id'], $serviceId, $session['id']);

                // 通知客服新会话
                $this->notifyServiceNewSession($websocket, $serviceId, $nextUser['user_id'], $session['id']);

                $this->logger->info('自动分配排队用户成功', [
                    'user_id' => $nextUser['user_id'],
                    'service_id' => $serviceId,
                    'session_id' => $session['id']
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('自动分配排队用户失败', [
                'service_id' => $serviceId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 通知用户分配成功
     * @param Websocket $websocket
     * @param int $userId
     * @param int $serviceId
     * @param int $sessionId
     */
    protected function notifyUserAssigned(Websocket $websocket, int $userId, int $serviceId, int $sessionId): void
    {
        try {
            $message = [
                'type' => 'text',
                'message_type' => MessageProtocol::MESSAGE_SERVICE_ASSIGNED,
                'from_id' => $serviceId,
                'from_type' => 'service',
                'to_id' => $userId,
                'to_type' => 'user',
                'session_id' => $sessionId,
                'content' => '客服已为您分配，开始为您服务',
                'status' => 1
            ];

            $savedMessage = $this->messageService->saveMessage($message);
            if ($savedMessage) {
                $service = $this->sessionService->getCustomerServiceModel()->getServiceInfo($serviceId);
                $message['id'] = $savedMessage['id'];
                $message['service'] = $service;
                $this->messageService->sendMessage($websocket, $message);
            }
        } catch (\Exception $e) {
            $this->logger->error('通知用户分配失败', [
                'user_id' => $userId,
                'service_id' => $serviceId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 通知客服新会话
     * @param Websocket $websocket
     * @param int $serviceId
     * @param int $userId
     * @param int $sessionId
     */
    protected function notifyServiceNewSession(Websocket $websocket, int $serviceId, int $userId, int $sessionId): void
    {
        try {
            $message = [
                'type' => 'text',
                'message_type' => MessageProtocol::MESSAGE_NEW_SESSION_TYPE,
                'from_id' => $userId,
                'from_type' => 'user',
                'to_id' => $serviceId,
                'to_type' => 'service',
                'session_id' => $sessionId,
                'content' => '新用户会话已分配',
                'status' => 1
            ];

            $savedMessage = $this->messageService->saveMessage($message);
            if ($savedMessage) {
                $user = $this->userService->getUserById($userId);

                // 通知新客服
                $message['user'] = $user->toArray();
                $message['id'] = $savedMessage['id'];
                $this->messageService->sendMessage($websocket, $message);
            }
        } catch (\Exception $e) {
            $this->logger->error('通知客服新会话失败', [
                'service_id' => $serviceId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送错误消息
     * @param Websocket $websocket
     * @param string $message
     */
    protected function sendError(Websocket $websocket, string $message): void
    {
        $websocket->emit('error', [
            'message' => $message,
            'timestamp' => time()
        ]);
    }
}