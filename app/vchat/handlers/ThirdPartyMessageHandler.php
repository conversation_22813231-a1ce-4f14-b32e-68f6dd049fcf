<?php

namespace app\vchat\handlers;

use app\vchat\services\ThirdPartyIntegrationService;
use app\vchat\core\MessageProtocol;
use app\vchat\utils\Logger;
use think\Request;
use think\Response;

/**
 * 第三方平台消息处理器
 * 集成到VChat系统的第三方平台消息处理
 */
class ThirdPartyMessageHandler
{
    /**
     * 第三方平台集成服务
     * @var ThirdPartyIntegrationService
     */
    protected ThirdPartyIntegrationService $integrationService;

    /**
     * 日志记录器
     * @var Logger
     */
    protected Logger $logger;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->integrationService = new ThirdPartyIntegrationService();
        $this->logger = new Logger();
    }

    /**
     * 处理第三方平台Webhook
     * @param string $platform 平台标识
     * @param Request $request 请求对象
     * @return Response
     */
    public function handleWebhook(string $platform, Request $request): Response
    {
        try {
            $this->logger->info('接收第三方平台Webhook', [
                'platform' => $platform,
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);

            // GET请求用于验证Webhook URL
            if ($request->method() === 'GET') {
                return $this->handleWebhookVerification($platform, $request);
            }

            // POST请求处理消息
            if ($request->method() === 'POST') {
                return $this->handleWebhookMessage($platform, $request);
            }

            return $this->errorResponse('不支持的请求方法', 405);

        } catch (\Exception $e) {
            $this->logger->error('处理Webhook失败', [
                'platform' => $platform,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse('内部服务器错误', 500);
        }
    }

    /**
     * 处理Webhook验证
     * @param string $platform 平台标识
     * @param Request $request 请求对象
     * @return Response
     */
    protected function handleWebhookVerification(string $platform, Request $request): Response
    {
        try {
            switch ($platform) {
                case 'wechat.miniprogram':
                case 'wechat.officialaccount':
                case 'wechat.work':
                    return $this->handleWechatVerification($request);
                
                case 'qq.bot':
                    return $this->handleQQVerification($request);
                
                case 'dingtalk.bot':
                    return $this->handleDingtalkVerification($request);
                
                case 'feishu.bot':
                    return $this->handleFeishuVerification($request);
                
                default:
                    return $this->successResponse('OK');
            }

        } catch (\Exception $e) {
            $this->logger->error('Webhook验证失败', [
                'platform' => $platform,
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse('验证失败', 403);
        }
    }

    /**
     * 处理Webhook消息
     * @param string $platform 平台标识
     * @param Request $request 请求对象
     * @return Response
     */
    protected function handleWebhookMessage(string $platform, Request $request): Response
    {
        try {
            // 获取请求数据
            $data = $this->extractWebhookData($request);

            // 记录原始数据
            $this->logger->debug('Webhook原始数据', [
                'platform' => $platform,
                'data' => $data
            ]);

            // 处理消息（直接集成到VChat系统）
            $result = $this->integrationService->handleThirdPartyMessage($platform, $data);

            // 记录处理结果
            $this->logger->info('第三方平台消息处理完成', [
                'platform' => $platform,
                'success' => $result['success'],
                'message_id' => $result['message_id'] ?? null,
                'session_id' => $result['session_id'] ?? null
            ]);

            // 返回响应
            if ($result['success']) {
                return $this->successResponse('消息处理成功', $result);
            } else {
                return $this->errorResponse($result['error'] ?? '消息处理失败', 400);
            }

        } catch (\Exception $e) {
            $this->logger->error('处理Webhook消息失败', [
                'platform' => $platform,
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse('消息处理失败', 500);
        }
    }

    /**
     * 微信Webhook验证
     * @param Request $request 请求对象
     * @return Response
     */
    protected function handleWechatVerification(Request $request): Response
    {
        $signature = $request->get('signature', '');
        $timestamp = $request->get('timestamp', '');
        $nonce = $request->get('nonce', '');
        $echostr = $request->get('echostr', '');

        // 这里应该验证签名，暂时直接返回echostr
        return response($echostr);
    }

    /**
     * QQ机器人Webhook验证
     * @param Request $request 请求对象
     * @return Response
     */
    protected function handleQQVerification(Request $request): Response
    {
        // QQ机器人验证逻辑
        return $this->successResponse('OK');
    }

    /**
     * 钉钉机器人Webhook验证
     * @param Request $request 请求对象
     * @return Response
     */
    protected function handleDingtalkVerification(Request $request): Response
    {
        // 钉钉机器人验证逻辑
        return $this->successResponse('OK');
    }

    /**
     * 飞书机器人Webhook验证
     * @param Request $request 请求对象
     * @return Response
     */
    protected function handleFeishuVerification(Request $request): Response
    {
        // 飞书机器人验证逻辑
        return $this->successResponse('OK');
    }

    /**
     * 提取Webhook数据
     * @param Request $request 请求对象
     * @return array
     */
    protected function extractWebhookData(Request $request): array
    {
        $contentType = $request->contentType();
        
        if (strpos($contentType, 'application/json') !== false) {
            // JSON格式数据
            $data = $request->getContent();
            return json_decode($data, true) ?: [];
        } elseif (strpos($contentType, 'application/xml') !== false || strpos($contentType, 'text/xml') !== false) {
            // XML格式数据（微信）
            $data = $request->getContent();
            return $this->xmlToArray($data);
        } else {
            // 表单数据
            return $request->param();
        }
    }

    /**
     * XML转数组
     * @param string $xml XML字符串
     * @return array
     */
    protected function xmlToArray(string $xml): array
    {
        try {
            $data = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
            return json_decode(json_encode($data), true) ?: [];
        } catch (\Exception $e) {
            $this->logger->error('XML解析失败', [
                'xml' => $xml,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 成功响应
     * @param string $message 消息
     * @param array $data 数据
     * @return Response
     */
    protected function successResponse(string $message = 'success', array $data = []): Response
    {
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => time()
        ];

        if (!empty($data)) {
            $response['data'] = $data;
        }

        return json($response);
    }

    /**
     * 错误响应
     * @param string $message 错误消息
     * @param int $code HTTP状态码
     * @return Response
     */
    protected function errorResponse(string $message = 'error', int $code = 400): Response
    {
        $response = [
            'success' => false,
            'error' => $message,
            'timestamp' => time()
        ];

        return json($response, $code);
    }
}
