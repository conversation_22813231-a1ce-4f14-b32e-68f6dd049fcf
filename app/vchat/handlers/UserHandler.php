<?php
declare(strict_types=1);

namespace app\vchat\handlers;

use app\model\system\User;
use app\vchat\core\MessageProtocol;
use app\vchat\events\EventManager;
use app\vchat\services\MessageService;
use app\vchat\services\SessionService;
use app\vchat\services\UserService;
use app\vchat\services\UserStatusService;
use app\vchat\services\RoomService;
use app\vchat\utils\Logger;
use think\swoole\Websocket;
use app\vchat\services\SessionTimeoutService;

/**
 * 用户端处理器
 */
class UserHandler
{
    /**
     * @var MessageService
     */
    protected $messageService;

    /**
     * @var SessionService
     */
    protected $sessionService;

    /**
     * @var UserStatusService
     */
    protected $userStatusService;

    /**
     * @var RoomService
     */
    protected $roomService;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var SessionTimeoutService
     */
    protected $timeoutService;

    protected $userService;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->messageService = new MessageService();
        $this->sessionService = new SessionService();
        $this->userStatusService = new UserStatusService();
        $this->roomService = new RoomService();
        $this->logger = new Logger();
        $this->timeoutService = new SessionTimeoutService();
        $this->userService = new UserService();
    }

    /**
     * 处理连接
     * @param Websocket $websocket
     * @param array $data
     * @return bool
     */
    public function handleConnect(Websocket $websocket, array $data): bool
    {
        try {
            $token = $data['token'] ?? '';
            if (empty($token)) {
                $this->sendError($websocket, '缺少认证token');
                return false;
            }

            if (!$this->handleAuthentication($websocket, $data)) {
                $this->sendError($websocket, '认证失败');
                return false;
            }

            return true;
        } catch (\Exception $e) {
            $this->logger->error('用户连接处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '认证失败');
            return false;
        }
    }

    /**
     * 处理用户认证
     * @param Websocket $websocket
     * @param array $data
     * @return bool
     */
    public function handleAuthentication(Websocket $websocket, array $data): bool
    {
        try {
            $token = $data['token'] ?? '';
            if (empty($token)) {
                $this->sendError($websocket, '缺少认证token');
                return false;
            }

            $tokenData = \app\common\Auth::checkToken($token);

            if (!$tokenData) {
                $this->sendError($websocket, 'token无效');
                return false;
            }

//            $user = User::where('id', $tokenData['user_id'])
//                ->where('enabled', 1)
//                ->find();

            $user = $this->userService->getUserById($tokenData['user_id'], ['enabled' => 1]);

            if (!$user) {
                $this->sendError($websocket, '用户状态异常');
                return false;
            }

            // 将用户加入对应房间
            $this->roomService->join($websocket, 'user_' . $user['id']);

            // 更新用户在线状态
            $this->userStatusService->updateOnlineStatus($websocket, $user['id'], 'user', true);
            $this->userStatusService->broadcastStatusChange($websocket, $user['id'], 'user', true);

            // 发送认证成功消息
            $websocket->emit(MessageProtocol::MESSAGE_AUTHORIZE_SUCCESS, $user);

            // 更新客服状态
            $this->userService
                ->updateUser($user['id'], ['status' => 1]);

            // 通知客服系统有新用户连接
            $this->notifyServiceNewUserConnected($user['id']);

            $this->logger->info('用户认证成功', [
                'user_id' => $user['id'],
                'fd' => $websocket->getSender()
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error('用户认证失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '认证失败');
            return false;
        }
    }

    /**
     * 处理消息
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleMessage(Websocket $websocket, array $data): void
    {
        try {
            // 验证消息格式
            if (!isset($data['content'])) {
                $this->sendError($websocket, '消息格式错误');
                return;
            }


            $userId = $this->getUserId($websocket);
            if (!$userId) {
                $this->sendError($websocket, '无法获取用户ID');
                return;
            }

            $session_id = $data['session_id'] ?? '';

            $message = [
                'from_id' => $userId,
                'from_type' => 'user',
                'to_id' => $data['to_id'] ?? 0,
                'to_type' => 'service',
                'content' => $data['content'],
                'session_id' => $session_id,
                'type' => $data['type'],
                'message_type' => $data['message_type'],
                'status' => 1,
                'local_id' => $data['local_id'] ?? ''
            ];

            // 验证会话状态
            $session = $this->sessionService->getSessionModel()->find($session_id);
            if (!$session || $session['status'] != 1) { // 1表示活跃会话
//                $this->sendError($websocket, '会话不存在或已关闭');
                $this->logger->info('message_auto_reply', $message);

                $eventManager = app()->make(EventManager::class);
                $eventManager->trigger('message_auto_reply', $websocket, $message, $this->messageService);
                return;
            }

            $savedMessage = $this->messageService->saveMessage($message);
            if ($savedMessage) {
                $savedMessage['local_id']  = $message['local_id'];
                $this->messageService->sendMessage($websocket, $savedMessage);

                // Record user activity to prevent timeout
                $this->timeoutService->recordUserActivity($userId, (int)$session_id);

                $this->logger->info('message_auto_reply', $message);

                $eventManager = app()->make(EventManager::class);
                $eventManager->trigger('message_auto_reply', $websocket, $message, $this->messageService);
            } else {
                $this->sendError($websocket, '消息发送失败');
            }
        } catch (\Exception $e) {
            $this->logger->error('处理用户消息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '消息处理失败');
        }
    }

    /**
     * 处理已读回执
     * @param Websocket $websocket
     * @param array $data 包含 message_id
     */
    public function handleReadReceipt(Websocket $websocket, array $data): void
    {
        try {
            if (!isset($data['message_id'])) {
                $this->sendError($websocket, '缺少消息ID');
                return;
            }

            $messageId = $data['message_id'];
            $this->messageService->updateMessageReadStatus($messageId);

            $this->logger->info('用户处理消息已读回执成功', [
                'message_id' => $messageId,
                'user_fd' => $websocket->getSender()
            ]);

        } catch (\Exception $e) {
            $this->logger->error('用户处理消息已读回执失败', [
                'error' => $e->getMessage(),
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '处理已读回执失败');
        }
    }

    /**
     * 处理输入状态
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleTypingStatus(Websocket $websocket, array $data): void
    {
        try {
            if (!isset($data['to_id'], $data['status'])) {
                $this->sendError($websocket, '参数错误');
                return;
            }

            $userId = $this->getUserId($websocket);
            if (!$userId) {
                $this->sendError($websocket, '无法获取用户ID');
                return;
            }

            $this->messageService->sendTypingStatus($websocket, [
                'from_id' => $userId,
                'from_type' => 'user',
                'to_id' => $data['to_id'],
                'to_type' => 'service',
                'content' => $data['status'] ? 'typing' : 'stop',
                'status' => 1
            ]);

            $this->logger->info('用户输入状态更新', [
                'user_id' => $userId,
                'service_id' => $data['to_id'],
                'status' => $data['status']
            ]);
        } catch (\Exception $e) {
            $this->logger->error('处理输入状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '更新输入状态失败');
        }
    }

    /**
     * 处理用户断开连接
     * @param Websocket $websocket
     * @param int $userId
     */
    public function handleDisconnect(Websocket $websocket, int $userId): void
    {
        try {
            // 更新用户在线状态
            $this->userStatusService->updateOnlineStatus($websocket, $userId, 'user', false);
            $this->userStatusService->broadcastStatusChange($websocket, $userId, 'user', false);

            // 清理房间
            $this->roomService->handleDisconnect($websocket->getSender());

            $this->handleLeaveQueue($websocket, ['user_id' => $userId]);

            // 更新客服状态
            $this->userService
                ->updateUser($userId, ['status' => 0]);

            // 通知相关客服用户已断开连接
            $this->notifyServiceUserDisconnected($userId);

            $this->logger->info('用户断开连接', [
                'user_id' => $userId,
                'fd' => $websocket->getSender()
            ]);
        } catch (\Exception $e) {
            $this->logger->error('处理用户断开连接失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理请求客服服务
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleServiceRequest(Websocket $websocket, array $data): void
    {
        try {
            $userId = $this->getUserId($websocket);
            if (!$userId) {
                $this->sendError($websocket, '无法获取用户ID');
                return;
            }

            // 检查用户是否有进行中的会话
            $activeSession = $this->sessionService->getSessionModel()->getUserActiveSession($userId);
            if ($activeSession) {
                $this->logger->info('上一个会话未结束', [
                    'service' => $activeSession
                ]);
                $service = $this->sessionService->getCustomerServiceModel()->getServiceInfo($activeSession['service_id']);
                // 通知用户已分配客服
                $websocket->emit(MessageProtocol::MESSAGE_RESPONSE_TYPE, [
                    'success' => true,
                    'service' => $service,
                    'session' => $activeSession,
                    'session_id' => $activeSession['id']
                ]);
                return;
            }

            // 初始化队列服务
            $queueService = new \app\vchat\services\QueueService();

            // 检查用户是否已在队列中
            if ($queueService->isUserInQueue($userId)) {
                $queueInfo = $queueService->getUserQueueInfo($userId);
                $websocket->emit(MessageProtocol::MESSAGE_NOTICE, [
                    'message_type' => MessageProtocol::MESSAGE_QUEUE_STATUS,
                    'success' => true,
                    'in_queue' => true,
                    'position' => $queueService->getUserQueuePosition($userId),
                    'estimated_wait_time' => $queueInfo['estimated_wait_time'],
                    'message' => '您已在排队中，请耐心等待'
                ]);
                return;
            }

            // 尝试直接分配客服
            $service = $this->sessionService->getCustomerServiceModel()->assignService();
            $this->logger->info('可用的客服列表', [
                'service' => $service
            ]);

            if ($service && $this->sessionService->getCustomerServiceModel()->canAcceptNewSession($service['id'])) {
                // 直接分配客服
                $this->assignServiceToUser($websocket, $userId, $service);
            } else {
                // 加入排队
                $priority = $data['priority'] ?? 1; // 可以根据用户VIP等级设置优先级
                $serviceId = $data['service_id'] ?? null; // 用户可以指定客服

                try {
                    $queueInfo = $queueService->joinQueue($userId, $serviceId, $priority);

                    $websocket->emit(MessageProtocol::MESSAGE_NOTICE, [
                        'message_type' => MessageProtocol::MESSAGE_QUEUE_JOINED,
                        'success' => true,
                        'position' => $queueInfo['position'],
                        'estimated_wait_time' => $queueInfo['estimated_wait_time'],
                        'message' => "当前排队人数较多，您的位置是第 {$queueInfo['position']} 位，预计等待时间 " . ceil($queueInfo['estimated_wait_time'] / 60) . " 分钟"
                    ]);

                    $queueModel = new \app\model\QueueModel();

                    $queueInfo = $queueModel->addToQueue($userId, $serviceId, $priority, $queueInfo['position'], $queueInfo['estimated_wait_time']);
                } catch (\Exception $e) {
                    $websocket->emit(MessageProtocol::MESSAGE_RESPONSE_TYPE, [
                        'success' => false,
                        'message' => $e->getMessage()
                    ]);
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('处理请求客服失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '请求客服失败');
        }
    }

    /**
     * 分配客服给用户
     * @param Websocket $websocket
     * @param int $userId
     * @param array $service
     */
    protected function assignServiceToUser(Websocket $websocket, int $userId, array $service): void
    {
        try {
            // 更新客服当前会话数
            \think\facade\Db::table('ad_customer_service')
                ->where('id', $service['id'])
                ->inc('current_sessions')
                ->update();

            // 创建会话
            $session = $this->sessionService->createSession($userId, $service['id']);
            if (!$session) {
                $this->sendError($websocket, '创建会话失败');
                return;
            }

            // 初始化会话超时管理
            $timeoutService = new \app\vchat\services\SessionTimeoutService();
            $timeoutService->recordUserActivity($userId, $session['id']);

            // 通知用户已分配客服
            $websocket->emit(MessageProtocol::MESSAGE_RESPONSE_TYPE, [
                'success' => true,
                'service' => $service,
                'session_id' => $session['id']
            ]);

            // 通知客服有新用户接入
            $this->notifyServiceNewSession($websocket, $service['id'], $userId, $session['id']);

            $this->logger->info('用户分配客服成功', [
                'user_id' => $userId,
                'service_id' => $service['id'],
                'session_id' => $session['id']
            ]);
        } catch (\Exception $e) {
            $this->logger->error('分配客服失败', [
                'user_id' => $userId,
                'service_id' => $service['id'],
                'error' => $e->getMessage()
            ]);
            $this->sendError($websocket, '分配客服失败');
        }
    }

    /**
     * 处理用户离开排队
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleLeaveQueue(Websocket $websocket, array $data): void
    {
        try {
            $userId = $this->getUserId($websocket);
            if (!$userId) {
                $this->sendError($websocket, '无法获取用户ID');
                return;
            }

            $queueService = new \app\vchat\services\QueueService();
            $result = $queueService->leaveQueue($userId);

            $websocket->emit('queue_left', [
                'success' => $result,
                'message' => $result ? '已离开排队' : '离开排队失败'
            ]);

            $queueModel = new \app\model\QueueModel();

            $queueModel->checkTimeout($userId);

            $this->logger->info('用户离开排队', [
                'user_id' => $userId,
                'result' => $result
            ]);
        } catch (\Exception $e) {
            $this->logger->error('处理离开排队失败', [
                'error' => $e->getMessage()
            ]);
            $this->sendError($websocket, '离开排队失败');
        }
    }

    /**
     * 处理获取排队状态
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleGetQueueStatus(Websocket $websocket, array $data): void
    {
        try {
            $userId = $this->getUserId($websocket);
            if (!$userId) {
                $this->sendError($websocket, '无法获取用户ID');
                return;
            }

            $queueService = new \app\vchat\services\QueueService();

            if ($queueService->isUserInQueue($userId)) {
                $queueInfo = $queueService->getUserQueueInfo($userId);
                $position = $queueService->getUserQueuePosition($userId);

                $websocket->emit(MessageProtocol::MESSAGE_NOTICE, [
                    'message_type' => MessageProtocol::MESSAGE_QUEUE_STATUS,
                    'success' => true,
                    'in_queue' => true,
                    'position' => $position,
                    'estimated_wait_time' => $queueInfo['estimated_wait_time'],
                    'join_time' => $queueInfo['join_time']
                ]);
            } else {
                $websocket->emit(MessageProtocol::MESSAGE_NOTICE, [
                    'message_type' => MessageProtocol::MESSAGE_QUEUE_STATUS,
                    'success' => true,
                    'in_queue' => false
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('获取排队状态失败', [
                'error' => $e->getMessage()
            ]);
            $this->sendError($websocket, '获取排队状态失败');
        }
    }

    /**
     * 获取用户ID
     * @param Websocket $websocket
     * @return int|null
     */
    protected function getUserId(Websocket $websocket): ?int
    {
        try {
            $fd = $websocket->getSender();
            $rooms = $this->roomService->getRoomIds($fd);
            $this->logger->info('获取用户ID', [
                'fd' => $fd,
                'rooms' => $rooms,
            ]);
            foreach ($rooms as $room) {
                if (strpos($room, 'user_') === 0) {
                    return (int)substr($room, 5);
                }
            }
            return null;
        } catch (\Exception $e) {
            $this->logger->error('获取用户ID失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 通知客服系统有新用户连接
     * @param int $userId
     */
    protected function notifyServiceNewUserConnected(int $userId): void
    {
        try {
            $websocket = app()->make(Websocket::class);
            // 获取所有在线客服
            $onlineServices = \think\facade\Db::table('ad_customer_service')
                ->where('status', 1)
                ->column('id');

            $userInfo = \think\facade\Db::table('qi_users')
                ->where('id', $userId)
                ->find();

            // 广播新用户连接消息给所有在线客服
            foreach ($onlineServices as $serviceId) {
                $message = [
                    'type' => MessageProtocol::TYPE_SYSTEM,
                    'message_type' => 'user_connected',
                    'from_id' => $userId,
                    'from_type' => 'user',
                    'to_id' => $serviceId,
                    'to_type' => 'service',
                    'content' => json_encode(['user_info' => $userInfo]),
                    'status' => 1
                ];

                // 保存消息并获取message_id
                $savedMessage = $this->messageService->saveMessage($message);
                if ($savedMessage) {
                    $message['id'] = $savedMessage['id'];
                    $message['content'] = ['user_info' => $userInfo];
                    $this->messageService->sendMessage($websocket, $message);
                }
            }

            $this->logger->info('通知客服新用户连接', [
                'user_id' => $userId,
                'online_services' => $onlineServices
            ]);
        } catch (\Exception $e) {
            $this->logger->error('通知客服新用户连接失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 通知客服用户已断开连接
     * @param int $userId
     */
    protected function notifyServiceUserDisconnected(int $userId): void
    {
        try {
            $websocket = app()->make(Websocket::class);
            // 获取所有在线客服
            $onlineServices = \think\facade\Db::table('ad_customer_service')
                ->where('status', 1)
                ->column('id');

            // 广播用户断开连接消息给所有在线客服
            foreach ($onlineServices as $serviceId) {
                $message = [
                    'type' => MessageProtocol::TYPE_SYSTEM,
                    'message_type' => 'user_disconnected',
                    'from_id' => $userId,
                    'from_type' => 'user',
                    'to_id' => $serviceId,
                    'to_type' => 'service',
                    'content' => '用户已断开连接',
                    'status' => 1
                ];

                // 保存消息并获取message_id
                $savedMessage = $this->messageService->saveMessage($message);
                if ($savedMessage) {
                    $message['id'] = $savedMessage['id'];
                    $this->messageService->sendMessage($websocket, $message);
                }
            }

            $this->logger->info('通知客服用户断开连接', [
                'user_id' => $userId,
                'online_services' => $onlineServices
            ]);
        } catch (\Exception $e) {
            $this->logger->error('通知客服用户断开连接失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理评价
     * @param Websocket $websocket
     * @param array $data
     */
    public function handleRating(Websocket $websocket, array $data): void
    {
        try {
            if (!isset($data['session_id'], $data['rating'])) {
                $this->sendError($websocket, '评价参数错误');
                return;
            }

            $sessionId = (int)$data['session_id'];
            $session = $this->sessionService->getSessionDetail($sessionId);

            if (!$session) {
                $this->sendError($websocket, '会话不存在');
                return;
            }

            if ($this->sessionService->endSession($sessionId)) {
                $result = $this->sessionService->updateSatisfaction(
                    (int)$data['session_id'],
                    (int)$data['rating'],
                    $data['feedback'] ?? ''
                );

                if ($result) {

                    $this->timeoutService->clearActivity($session['user_id'], $session['service_id']);

                    $message = [
                        [
                            'type' => MessageProtocol::TYPE_SYSTEM,
                            'message_type' => MessageProtocol::MESSAGE_RATE_SUCCESS,
                            'from_id' => $session['service_id'],
                            'from_type' => 'service',
                            'to_id' => $session['user_id'],
                            'to_type' => 'user',
                            'content' => '评价成功',
                            'session_id' => $sessionId,
                            'status' => 1
                        ],
                        [
                            'type' => MessageProtocol::TYPE_SYSTEM,
                            'message_type' => MessageProtocol::MESSAGE_RATE_SUCCESS,
                            'from_id' => $session['user_id'],
                            'from_type' => 'user',
                            'to_id' => $session['service_id'],
                            'to_type' => 'service',
                            'content' => '评价成功',
                            'session_id' => $sessionId,
                            'status' => 1
                        ]
                    ];

                    // 保存消息并获取message_id
                    $savedMessage = $this->messageService->saveMessages($message);
                    if ($savedMessage && count($savedMessage) == 2) {

                        // 通知用户
                        $fromMessage = $savedMessage[0];
                        $this->messageService->sendMessage($websocket, $fromMessage, MessageProtocol::MESSAGE_NOTICE);

                        // 通知客服
                        $toMessage = $savedMessage[1];
                        $this->messageService->sendMessage($websocket, $toMessage, MessageProtocol::MESSAGE_NOTICE);

                    }

                } else {
                    $this->sendError($websocket, '评价失败');
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('处理评价失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError($websocket, '评价处理失败');
        }
    }

    /**
     * 通知客服新会话
     * @param Websocket $websocket
     * @param int $serviceId
     * @param int $userId
     * @param int $sessionId
     */
    protected function notifyServiceNewSession(Websocket $websocket, int $serviceId, int $userId, int $sessionId): void
    {
        try {
            $message = [
                'type' => MessageProtocol::TYPE_SYSTEM,
                'message_type' => MessageProtocol::MESSAGE_NEW_SESSION_TYPE,
                'from_id' => $userId,
                'from_type' => 'user',
                'to_id' => $serviceId,
                'to_type' => 'service',
                'session_id' => $sessionId,
                'content' => json_encode([
                    'session_id' => $sessionId,
                    'timestamp' => time()
                ]),
                'status' => 1
            ];
            // 保存消息并获取message_id
            $savedMessage = $this->messageService->saveMessage($message);
            if ($savedMessage) {
                $message['id'] = $savedMessage['id'];
                $user = $this->userService->getUserById($userId);
                $message['user'] = $user->toArray();
                $this->messageService->sendMessage($websocket, $message);
            }
            $eventManager = app()->make(EventManager::class);
            $message['message_type'] = MessageProtocol::MESSAGE_TYPE;
            $eventManager->trigger('message_auto_reply', $websocket, $message, $this->messageService);
        } catch (\Exception $e) {
            $this->logger->error('通知客服新会话失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 发送错误消息
     * @param Websocket $websocket
     * @param string $message
     */
    protected function sendError(Websocket $websocket, string $message): void
    {
        $websocket->emit('error', [
            'message' => $message,
            'timestamp' => time()
        ]);
    }
}