<?php
declare(strict_types=1);

namespace app\vchat\handlers;

use app\vchat\core\MessageProtocol;
use app\vchat\services\MessageService;
use app\vchat\handlers\MenuHandler;
use app\vchat\utils\Logger;
use think\swoole\Websocket;

/**
 * 消息处理器
 */
class MessageHandler
{
    /**
     * @var MessageService
     */
    protected $messageService;

    /**
     * @var Logger
     */
    protected $logger;
    
    /**
     * @var MenuHandler
     */
    protected $menuHandler;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->messageService = new MessageService();
        $this->logger = new Logger();
        $this->menuHandler = new MenuHandler();
    }

    /**
     * 处理消息
     * @param Websocket $websocket
     * @param array $data
     * @param mixed ...$args 其他可选参数
     * @return void
     */
    public function handle(Websocket $websocket, array $data, ...$args): void
    {
        try {
            // 验证消息格式
            if (!MessageProtocol::validate($data)) {
                $this->logger->error('消息格式无效', ['data' => $data]);
                return;
            }
            
            // 检查是否为菜单相关消息
            if ($this->isMenuMessage($data)) {
                $this->menuHandler->handle($websocket, $data);
                return;
            }
            
            // 检查文本消息中的菜单关键词
            if ($data['type'] === 'text' && !empty($data['content'])) {
                $userId = $data['user_id'] ?? '';
                if (!empty($userId) && $this->menuHandler->handleTextMenuKeywords($websocket, $userId, $data['content'])) {
                    return;
                }
            }

            // 输出接收到的额外参数
            if (!empty($args)) {
                $this->logger->info('接收到额外参数', ['args' => $args]);
            }

            // 根据消息类型处理
            switch ($data['type']) {
                case MessageProtocol::TYPE_TEXT:
                    $this->handleTextMessage($websocket, $data);
                    break;
                case MessageProtocol::TYPE_IMAGE:
                    $this->handleImageMessage($websocket, $data);
                    break;
                case MessageProtocol::TYPE_VOICE:
                    $this->handleVoiceMessage($websocket, $data);
                    break;
                case MessageProtocol::TYPE_VIDEO:
                    $this->handleVideoMessage($websocket, $data);
                    break;
                case MessageProtocol::TYPE_FILE:
                    $this->handleFileMessage($websocket, $data);
                    break;
                case MessageProtocol::TYPE_SYSTEM:
                    $this->handleSystemMessage($websocket, $data);
                    break;
                case MessageProtocol::TYPE_NOTICE:
                    $this->handleNoticeMessage($websocket, $data);
                    break;
                case MessageProtocol::TYPE_TYPING:
                    $this->handleTypingMessage($websocket, $data);
                    break;
                case MessageProtocol::TYPE_READ:
                    $this->handleReadMessage($websocket, $data);
                    break;
                case MessageProtocol::TYPE_RECALL:
                    $this->handleRecallMessage($websocket, $data);
                    break;
                default:
                    $this->logger->warning('未知消息类型', ['type' => $data['type']]);
                    break;
            }
        } catch (\Exception $e) {
            $this->logger->error('消息处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 判断是否为菜单相关消息
     * 
     * @param array $data 消息数据
     * @return bool
     */
    protected function isMenuMessage(array $data): bool
    {
        $type = $data['type'] ?? '';
        $action = $data['action'] ?? '';
        
        // 菜单相关的消息类型和动作
        $menuTypes = ['menu', 'menu_click', 'menu_request'];
        $menuActions = ['get_menu', 'menu_click', 'show_main_menu'];
        
        return in_array($type, $menuTypes) || in_array($action, $menuActions);
    }

    /**
     * 处理文本消息
     */
    protected function handleTextMessage(Websocket $websocket, array $data): void
    {
        // 保存消息
        $message = $this->messageService->saveMessage($data);
        if (!$message) {
            $this->logger->error('保存文本消息失败', ['data' => $data]);
            return;
        }

        // 发送消息
        $this->messageService->sendMessage($websocket, $message);

        // 检查是否需要发送到第三方平台
        $this->handleThirdPartySending($message);
    }

    /**
     * 处理图片消息
     */
    protected function handleImageMessage(Websocket $websocket, array $data): void
    {
        // 验证图片信息
        if (!isset($data['extra']['width']) || !isset($data['extra']['height'])) {
            $this->logger->error('图片消息缺少必要信息', ['data' => $data]);
            return;
        }

        // 保存消息
        $message = $this->messageService->saveMessage($data);
        if (!$message) {
            $this->logger->error('保存图片消息失败', ['data' => $data]);
            return;
        }

        // 发送消息
        $this->messageService->sendMessage($websocket, $message);

        // 检查是否需要发送到第三方平台
        $this->handleThirdPartySending($message);
    }

    /**
     * 处理语音消息
     */
    protected function handleVoiceMessage(Websocket $websocket, array $data): void
    {
        // 验证语音信息
        if (!isset($data['extra']['duration'])) {
            $this->logger->error('语音消息缺少必要信息', ['data' => $data]);
            return;
        }

        // 保存消息
        $message = $this->messageService->saveMessage($data);
        if (!$message) {
            $this->logger->error('保存语音消息失败', ['data' => $data]);
            return;
        }

        // 发送消息
        $this->messageService->sendMessage($websocket, $message);
    }

    /**
     * 处理视频消息
     */
    protected function handleVideoMessage(Websocket $websocket, array $data): void
    {
        // 验证视频信息
        if (!isset($data['extra']['duration'])) {
            $this->logger->error('视频消息缺少必要信息', ['data' => $data]);
            return;
        }

        // 保存消息
        $message = $this->messageService->saveMessage($data);
        if (!$message) {
            $this->logger->error('保存视频消息失败', ['data' => $data]);
            return;
        }

        // 发送消息
        $this->messageService->sendMessage($websocket, $message);
    }

    /**
     * 处理文件消息
     */
    protected function handleFileMessage(Websocket $websocket, array $data): void
    {
        // 验证文件信息
        if (!isset($data['extra']['name']) || !isset($data['extra']['size'])) {
            $this->logger->error('文件消息缺少必要信息', ['data' => $data]);
            return;
        }

        // 保存消息
        $message = $this->messageService->saveMessage($data);
        if (!$message) {
            $this->logger->error('保存文件消息失败', ['data' => $data]);
            return;
        }

        // 发送消息
        $this->messageService->sendMessage($websocket, $message);
    }

    /**
     * 处理系统消息
     */
    protected function handleSystemMessage(Websocket $websocket, array $data): void
    {
        // 保存消息
        $message = $this->messageService->saveMessage($data);
        if (!$message) {
            $this->logger->error('保存系统消息失败', ['data' => $data]);
            return;
        }

        // 发送消息
        $this->messageService->sendMessage($websocket, $message);
    }

    /**
     * 处理通知消息
     */
    protected function handleNoticeMessage(Websocket $websocket, array $data): void
    {
        // 保存消息
        $message = $this->messageService->saveMessage($data);
        if (!$message) {
            $this->logger->error('保存通知消息失败', ['data' => $data]);
            return;
        }

        // 发送消息
        $this->messageService->sendMessage($websocket, $message);
    }

    /**
     * 处理输入状态消息
     */
    protected function handleTypingMessage(Websocket $websocket, array $data): void
    {
        // 发送输入状态
        $this->messageService->sendTypingStatus($websocket, $data);
    }

    /**
     * 处理已读状态消息
     */
    protected function handleReadMessage(Websocket $websocket, array $data): void
    {
        // 更新消息已读状态
        $this->messageService->updateMessageReadStatus($data['content']);
    }

    /**
     * 处理撤回消息
     */
    protected function handleRecallMessage(Websocket $websocket, array $data): void
    {
        // 撤回消息
        $this->messageService->recallMessage($data['content']);
    }

    /**
     * 处理第三方平台消息发送
     * @param array $message 消息数据
     */
    protected function handleThirdPartySending(array $message): void
    {
        try {
            // 检查消息是否来自客服且需要发送到第三方平台
            if ($message['from_type'] !== 'service' || $message['to_type'] !== 'user') {
                return;
            }

            // 获取会话信息
            $session = \app\model\ChatSession::find($message['session_id']);
            if (!$session || empty($session['source'])) {
                return;
            }

            // 检查是否为第三方平台会话
            $thirdPartyPlatforms = ['wechat.miniprogram', 'wechat.officialaccount', 'wechat.work', 'qq.bot', 'dingtalk.bot', 'feishu.bot'];
            if (!in_array($session['source'], $thirdPartyPlatforms)) {
                return;
            }

            // 异步发送到第三方平台
            $this->sendToThirdPartyAsync($session['source'], $message);

        } catch (\Exception $e) {
            $this->logger->error('处理第三方平台发送失败', [
                'message_id' => $message['id'] ?? null,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 异步发送消息到第三方平台
     * @param string $platform 平台标识
     * @param array $message 消息数据
     */
    protected function sendToThirdPartyAsync(string $platform, array $message): void
    {
        try {
            // 使用队列或直接发送
            if (config('vchat_integration.queue.enabled', false)) {
                // 推送到队列
                $queueData = [
                    'platform' => $platform,
                    'message' => $message,
                    'action' => 'send_to_third_party'
                ];

                // 这里可以使用ThinkPHP的队列功能
                // Queue::push('ThirdPartyMessageJob', $queueData);

                $this->logger->info('消息已推送到第三方平台发送队列', [
                    'platform' => $platform,
                    'message_id' => $message['id']
                ]);
            } else {
                // 直接发送
                $integrationService = new \app\vchat\services\ThirdPartyIntegrationService();
                $result = $integrationService->sendMessageToThirdParty($platform, $message);

                $this->logger->info('消息已发送到第三方平台', [
                    'platform' => $platform,
                    'message_id' => $message['id'],
                    'success' => $result['success'] ?? false
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->error('异步发送到第三方平台失败', [
                'platform' => $platform,
                'message_id' => $message['id'] ?? null,
                'error' => $e->getMessage()
            ]);
        }
    }
}