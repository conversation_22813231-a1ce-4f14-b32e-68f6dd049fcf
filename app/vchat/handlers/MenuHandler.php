<?php

namespace app\vchat\handlers;

use app\vchat\services\MenuService;
use app\vchat\utils\Logger;
use think\swoole\Websocket;

/**
 * 菜单处理器
 * 负责处理菜单相关的WebSocket消息
 */
class MenuHandler
{
    /**
     * @var MenuService
     */
    protected $menuService;
    
    /**
     * @var Logger
     */
    protected $logger;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->menuService = new MenuService();
        $this->logger = new Logger();
    }
    
    /**
     * 处理菜单消息
     * 
     * @param Websocket $websocket WebSocket连接
     * @param array $data 消息数据
     * @return void
     */
    public function handle(Websocket $websocket, array $data): void
    {
        try {
            $action = $data['action'] ?? '';
            $userId = $data['user_id'] ?? '';
            
            if (empty($userId)) {
                $this->logger->error('菜单处理器：用户ID为空', ['data' => $data]);
                return;
            }
            
            $this->logger->info('处理菜单消息', [
                'user_id' => $userId,
                'action' => $action,
                'data' => $data
            ]);
            
            switch ($action) {
                case 'get_menu':
                    $this->handleGetMenu($websocket, $data);
                    break;
                    
                case 'menu_click':
                    $this->handleMenuClick($websocket, $data);
                    break;
                    
                case 'show_main_menu':
                    $this->handleShowMainMenu($websocket, $data);
                    break;
                    
                default:
                    $this->logger->warning('未知的菜单操作', [
                        'action' => $action,
                        'user_id' => $userId
                    ]);
                    break;
            }
            
        } catch (\Exception $e) {
            $this->logger->error('菜单处理器异常', [
                'data' => $data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 处理获取菜单请求
     * 
     * @param Websocket $websocket WebSocket连接
     * @param array $data 消息数据
     * @return void
     */
    protected function handleGetMenu(Websocket $websocket, array $data): void
    {
        $userId = $data['user_id'];
        $menuId = $data['menu_id'] ?? '';
        
        $result = $this->menuService->getUserMenu($userId, $menuId);
        
        $response = [
            'type' => 'menu_response',
            'action' => 'get_menu',
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['data'] ?? [],
            'timestamp' => time()
        ];
        
        $websocket->to($userId)->emit('message', $response);
    }
    
    /**
     * 处理菜单点击事件
     * 
     * @param Websocket $websocket WebSocket连接
     * @param array $data 消息数据
     * @return void
     */
    protected function handleMenuClick(Websocket $websocket, array $data): void
    {
        $userId = $data['user_id'];
        $menuId = $data['menu_id'] ?? '';
        
        if (empty($menuId)) {
            $this->sendErrorResponse($websocket, $userId, 'menu_click', '菜单ID不能为空');
            return;
        }
        
        $result = $this->menuService->handleMenuClick($userId, $menuId, $websocket);
        
        $response = [
            'type' => 'menu_response',
            'action' => 'menu_click',
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['data'] ?? [],
            'menu_id' => $menuId,
            'timestamp' => time()
        ];
        
        $websocket->to($userId)->emit('message', $response);
    }
    
    /**
     * 处理显示主菜单请求
     * 
     * @param Websocket $websocket WebSocket连接
     * @param array $data 消息数据
     * @return void
     */
    protected function handleShowMainMenu(Websocket $websocket, array $data): void
    {
        $userId = $data['user_id'];
        
        // 获取主菜单
        $result = $this->menuService->getUserMenu($userId, '');
        
        if ($result['success']) {
            // 发送菜单消息
            $menuMessage = [
                'type' => 'menu',
                'menu_data' => $result['data'],
                'title' => '主菜单',
                'timestamp' => time(),
                'from' => 'system'
            ];
            
            $websocket->to($userId)->emit('message', $menuMessage);
            
            // 发送欢迎文本
            $welcomeMessage = [
                'type' => 'text',
                'content' => "🎉 欢迎使用我们的服务！\n\n" .
                           "请选择您需要的服务：\n" .
                           "• 在线客服 - 专业客服为您解答\n" .
                           "• 帮助中心 - 查看常见问题\n" .
                           "• 联系我们 - 获取联系方式\n\n" .
                           "💡 点击下方菜单按钮开始使用",
                'timestamp' => time(),
                'from' => 'system'
            ];
            
            $websocket->to($userId)->emit('message', $welcomeMessage);
        }
        
        $response = [
            'type' => 'menu_response',
            'action' => 'show_main_menu',
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['data'] ?? [],
            'timestamp' => time()
        ];
        
        $websocket->to($userId)->emit('message', $response);
    }
    
    /**
     * 发送错误响应
     * 
     * @param Websocket $websocket WebSocket连接
     * @param string $userId 用户ID
     * @param string $action 操作类型
     * @param string $message 错误消息
     * @return void
     */
    protected function sendErrorResponse(Websocket $websocket, string $userId, string $action, string $message): void
    {
        $response = [
            'type' => 'menu_response',
            'action' => $action,
            'success' => false,
            'message' => $message,
            'data' => [],
            'timestamp' => time()
        ];
        
        $websocket->to($userId)->emit('message', $response);
    }
    
    /**
     * 处理文本消息中的菜单关键词
     * 
     * @param Websocket $websocket WebSocket连接
     * @param string $userId 用户ID
     * @param string $content 消息内容
     * @return bool 是否处理了菜单关键词
     */
    public function handleTextMenuKeywords(Websocket $websocket, string $userId, string $content): bool
    {
        $content = trim($content);
        
        // 菜单关键词映射
        $menuKeywords = [
            '菜单' => 'show_main_menu',
            'menu' => 'show_main_menu',
            '主菜单' => 'show_main_menu',
            '客服' => 'service',
            '在线客服' => 'service',
            '人工客服' => 'service',
            '帮助' => 'help',
            '帮助中心' => 'help',
            '常见问题' => 'faq',
            '联系我们' => 'contact',
            '联系方式' => 'contact'
        ];
        
        foreach ($menuKeywords as $keyword => $menuId) {
            if (stripos($content, $keyword) !== false) {
                $this->handleKeywordMenu($websocket, $userId, $menuId, $keyword);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 处理关键词菜单
     * 
     * @param Websocket $websocket WebSocket连接
     * @param string $userId 用户ID
     * @param string $menuId 菜单ID
     * @param string $keyword 触发关键词
     * @return void
     */
    protected function handleKeywordMenu(Websocket $websocket, string $userId, string $menuId, string $keyword): void
    {
        $this->logger->info('处理关键词菜单', [
            'user_id' => $userId,
            'menu_id' => $menuId,
            'keyword' => $keyword
        ]);
        
        switch ($menuId) {
            case 'show_main_menu':
                $this->handleShowMainMenu($websocket, ['user_id' => $userId]);
                break;
                
            case 'service':
                $this->handleMenuClick($websocket, [
                    'user_id' => $userId,
                    'menu_id' => 'service'
                ]);
                break;
                
            case 'help':
            case 'faq':
                $this->handleMenuClick($websocket, [
                    'user_id' => $userId,
                    'menu_id' => 'help'
                ]);
                break;
                
            case 'contact':
                $this->handleMenuClick($websocket, [
                    'user_id' => $userId,
                    'menu_id' => 'contact'
                ]);
                break;
        }
    }
    
    /**
     * 发送菜单提示
     * 
     * @param Websocket $websocket WebSocket连接
     * @param string $userId 用户ID
     * @return void
     */
    public function sendMenuHint(Websocket $websocket, string $userId): void
    {
        $hintMessage = [
            'type' => 'text',
            'content' => "💡 小提示：\n\n" .
                       "您可以发送以下关键词快速访问功能：\n" .
                       "• 发送 '菜单' 显示主菜单\n" .
                       "• 发送 '客服' 联系在线客服\n" .
                       "• 发送 '帮助' 查看帮助中心\n" .
                       "• 发送 '联系我们' 获取联系方式\n\n" .
                       "或者点击下方菜单按钮进行操作",
            'timestamp' => time(),
            'from' => 'system'
        ];
        
        $websocket->to($userId)->emit('message', $hintMessage);
    }
}