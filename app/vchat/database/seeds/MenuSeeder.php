<?php

use think\migration\Seeder;

/**
 * VChat菜单数据填充
 */
class MenuSeeder extends Seeder
{
    /**
     * 运行数据填充
     */
    public function run()
    {
        $data = $this->getDefaultMenuData();
        
        $this->table('vchat_menu')->insert($data)->save();
    }
    
    /**
     * 获取默认菜单数据
     * 
     * @return array
     */
    protected function getDefaultMenuData(): array
    {
        return [
            // 主菜单
            [
                'menu_id' => 'main_menu',
                'parent_id' => 0,
                'name' => '主菜单',
                'type' => 'button',
                'level' => 1,
                'sort' => 1,
                'status' => 1,
                'handler' => '',
                'params' => null,
                'description' => '系统主菜单',
                'icon' => 'menu',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            
            // 在线客服
            [
                'menu_id' => 'service',
                'parent_id' => 1,
                'name' => '在线客服',
                'type' => 'click',
                'level' => 2,
                'sort' => 1,
                'status' => 1,
                'handler' => 'CustomerServiceHandler',
                'params' => json_encode(['action' => 'start_service']),
                'description' => '联系在线客服',
                'icon' => 'service',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            
            // 帮助中心
            [
                'menu_id' => 'help',
                'parent_id' => 1,
                'name' => '帮助中心',
                'type' => 'button',
                'level' => 2,
                'sort' => 2,
                'status' => 1,
                'handler' => '',
                'params' => null,
                'description' => '帮助中心入口',
                'icon' => 'help',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            
            // 常见问题
            [
                'menu_id' => 'faq',
                'parent_id' => 3,
                'name' => '常见问题',
                'type' => 'click',
                'level' => 3,
                'sort' => 1,
                'status' => 1,
                'handler' => 'HelpHandler',
                'params' => json_encode(['action' => 'show_faq']),
                'description' => '查看常见问题',
                'icon' => 'faq',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            
            // 联系我们
            [
                'menu_id' => 'contact',
                'parent_id' => 3,
                'name' => '联系我们',
                'type' => 'click',
                'level' => 3,
                'sort' => 2,
                'status' => 1,
                'handler' => 'ContactHandler',
                'params' => json_encode(['action' => 'show_contact']),
                'description' => '获取联系方式',
                'icon' => 'contact',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            
            // 产品介绍
            [
                'menu_id' => 'products',
                'parent_id' => 1,
                'name' => '产品介绍',
                'type' => 'view',
                'level' => 2,
                'sort' => 3,
                'status' => 1,
                'handler' => 'UrlHandler',
                'params' => json_encode([
                    'url' => 'https://example.com/products',
                    'title' => '产品介绍',
                    'description' => '了解我们的产品和服务'
                ]),
                'description' => '产品介绍页面',
                'icon' => 'products',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            
            // 意见反馈
            [
                'menu_id' => 'feedback',
                'parent_id' => 3,
                'name' => '意见反馈',
                'type' => 'click',
                'level' => 3,
                'sort' => 3,
                'status' => 1,
                'handler' => 'ContactHandler',
                'params' => json_encode(['action' => 'show_feedback_form']),
                'description' => '提交意见反馈',
                'icon' => 'feedback',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            
            // 使用指南
            [
                'menu_id' => 'guide',
                'parent_id' => 3,
                'name' => '使用指南',
                'type' => 'view',
                'level' => 3,
                'sort' => 4,
                'status' => 1,
                'handler' => 'UrlHandler',
                'params' => json_encode([
                    'url' => 'https://help.example.com/guide',
                    'title' => '使用指南',
                    'description' => '详细的使用说明和教程'
                ]),
                'description' => '系统使用指南',
                'icon' => 'guide',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            
            // 排队状态
            [
                'menu_id' => 'queue_status',
                'parent_id' => 0,
                'name' => '排队状态',
                'type' => 'click',
                'level' => 1,
                'sort' => 2,
                'status' => 1,
                'handler' => 'CustomerServiceHandler',
                'params' => json_encode(['action' => 'queue_status']),
                'description' => '查看当前排队状态',
                'icon' => 'queue',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ],
            
            // 结束服务
            [
                'menu_id' => 'end_service',
                'parent_id' => 0,
                'name' => '结束服务',
                'type' => 'click',
                'level' => 1,
                'sort' => 3,
                'status' => 1,
                'handler' => 'CustomerServiceHandler',
                'params' => json_encode(['action' => 'end_service']),
                'description' => '结束当前客服服务',
                'icon' => 'end',
                'permission' => null,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]
        ];
    }
}