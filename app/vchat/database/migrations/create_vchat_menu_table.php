<?php

use think\migration\Migrator;
use think\migration\db\Column;

/**
 * VChat菜单表迁移
 */
class CreateVchatMenuTable extends Migrator
{
    /**
     * 创建表
     */
    public function up()
    {
        $table = $this->table('ad_chat_menu', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => 'VChat菜单表'
        ]);
        
        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '主键ID'
        ])
        ->addColumn('menu_id', 'string', [
            'limit' => 50,
            'null' => false,
            'comment' => '菜单唯一标识'
        ])
        ->addColumn('parent_id', 'integer', [
            'signed' => false,
            'default' => 0,
            'comment' => '父菜单ID'
        ])
        ->addColumn('name', 'string', [
            'limit' => 100,
            'null' => false,
            'comment' => '菜单名称'
        ])
        ->addColumn('type', 'string', [
            'limit' => 20,
            'null' => false,
            'comment' => '菜单类型：click|view|button|media|text'
        ])
        ->addColumn('level', 'integer', [
            'signed' => false,
            'default' => 1,
            'comment' => '菜单层级'
        ])
        ->addColumn('sort', 'integer', [
            'signed' => false,
            'default' => 0,
            'comment' => '排序权重'
        ])
        ->addColumn('status', 'integer', [
            'limit' => 1,
            'default' => 1,
            'comment' => '状态：0禁用 1启用'
        ])
        ->addColumn('handler', 'string', [
            'limit' => 100,
            'null' => true,
            'comment' => '处理器类名'
        ])
        ->addColumn('params', 'text', [
            'null' => true,
            'comment' => '菜单参数JSON'
        ])
        ->addColumn('description', 'string', [
            'limit' => 255,
            'null' => true,
            'comment' => '菜单描述'
        ])
        ->addColumn('icon', 'string', [
            'limit' => 100,
            'null' => true,
            'comment' => '菜单图标'
        ])
        ->addColumn('permission', 'string', [
            'limit' => 50,
            'null' => true,
            'comment' => '权限标识'
        ])
        ->addColumn('create_time', 'datetime', [
            'null' => true,
            'comment' => '创建时间'
        ])
        ->addColumn('update_time', 'datetime', [
            'null' => true,
            'comment' => '更新时间'
        ])
        ->addIndex(['menu_id'], ['unique' => true, 'name' => 'idx_menu_id'])
        ->addIndex(['parent_id'], ['name' => 'idx_parent_id'])
        ->addIndex(['type'], ['name' => 'idx_type'])
        ->addIndex(['status'], ['name' => 'idx_status'])
        ->addIndex(['level'], ['name' => 'idx_level'])
        ->addIndex(['sort'], ['name' => 'idx_sort'])
        ->create();
    }
    
    /**
     * 删除表
     */
    public function down()
    {
        $this->dropSchema('vchat_menu');
    }
}