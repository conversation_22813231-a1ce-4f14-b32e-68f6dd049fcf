/*
 地区数据表结构
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ad_region
-- ----------------------------
DROP TABLE IF EXISTS `ad_region`;
CREATE TABLE `ad_region` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '地区ID',
  `parent_id` int NOT NULL DEFAULT '0' COMMENT '父级ID',
  `name` varchar(50) NOT NULL COMMENT '地区名称',
  `code` varchar(20) NOT NULL COMMENT '地区编码',
  `level` tinyint NOT NULL COMMENT '地区级别：1-省 2-市 3-区/县',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
  `createtime` int NOT NULL COMMENT '创建时间',
  `updatetime` int NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='地区表';

SET FOREIGN_KEY_CHECKS = 1;