<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 聊天自动回复规则模型
 */
class AutoReplyRule extends Model
{
    // 表名
    protected $table = 'ad_chat_auto_reply_rules';

    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 允许写入的字段
    protected $field = [
        'keyword',
        'reply_content',
        'match_type',
        'priority',
        'enabled',
        'createtime',
        'updatetime',
        'user_id',
    ];

    // 匹配类型常量
    const MATCH_TYPE_EXACT = 1; // 精确匹配
    const MATCH_TYPE_FUZZY = 2; // 模糊匹配
    const MATCH_TYPE_REGEX = 3; // 正则匹配

    // 状态常量
    const ENABLED_ENABLED = 1; // 启用
    const ENABLED_DISABLED = 0; // 禁用
}