<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 限时秒杀活动模型
 */
class Seckill extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'seckill';
    
    /**
     * 自动写入时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;
    
    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'updatetime';
    
    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'createtime';
    
    /**
     * 商品关联
     * @return \think\model\relation\BelongsTo
     */
    public function goods()
    {
        return $this->belongsTo(Goods::class, 'goods_id', 'id');
    }
    
    /**
     * 更新活动状态
     * @return bool
     */
    public function updateStatus()
    {
        $time = time();
        $status = 0;
        
        if ($time >= $this->start_time && $time <= $this->end_time) {
            $status = 1; // 进行中
        } elseif ($time > $this->end_time) {
            $status = 2; // 已结束
        }
        
        if ($status !== $this->status) {
            return $this->save(['status' => $status]);
        }
        
        return true;
    }
}