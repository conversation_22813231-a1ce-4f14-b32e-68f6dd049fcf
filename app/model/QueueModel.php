<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 队列模型
 */
class QueueModel extends Model
{
    protected $table = 'ad_chat_queue';
    
    // 队列状态常量
    const STATUS_WAITING = 1;  // 等待中
    const STATUS_ASSIGNED = 2; // 已分配
    const STATUS_TIMEOUT = 3;  // 已超时
    const STATUS_CANCELED = 4; // 已取消

    /**
     * 添加用户到队列
     * @param int $userId 用户ID
     * @param int|null $serviceId 客服ID
     * @param int $priority 优先级
     * @return array
     */
    public function addToQueue(int $userId, ?int $serviceId = null, int $priority = 1, int $position = 1, int $estimated_wait_time = 0): array
    {
        $data = [
            'user_id' => $userId,
            'service_id' => $serviceId,
            'priority' => $priority,
            'position' => $position,
            'estimated_wait_time' => $estimated_wait_time,
            'join_time' => time(),
            'status' => self::STATUS_WAITING,
        ];

        $this->insert($data);
        return $data;
    }

    /**
     * 检查并处理超时队列
     */
    public function checkTimeout($userId): int
    {
        // 处理字符串类型的userId，转换为数组
        if (is_string($userId)) {
            $userId = explode(',', $userId);
            $userId = array_filter(array_map('trim', $userId)); // 清理和去空
        }

        // 确保userId是数组且非空
        if (!is_array($userId) || empty($userId)) {
            return 0; // 或抛出异常
        }

        return $this->where('status', self::STATUS_WAITING)
            ->whereIn('user_id', $userId)
            ->update([
                'status' => self::STATUS_TIMEOUT,
                'timeout_time' => date('Y-m-d H:i:s', time())
            ]);
    }

    /**
     * 分配用户出队列
     * @param int $userId
     * @param int $serviceId
     */
    public function assignUser(int $userId, int $serviceId): int
    {
        return $this->where('user_id', $userId)
            ->where('status', self::STATUS_WAITING)
            ->update([
                'status' => self::STATUS_ASSIGNED,
                'assign_time' => date('Y-m-d H:i:s', time()),
                'service_id' => $serviceId
            ]);
    }

    /**
     * 获取队列中的用户数量
     * @return int
     */
    public function getQueueCount(): int
    {
        return $this->where('status', self::STATUS_WAITING)->count();
    }

    /**
     * 检查用户是否在队列中
     * @param int $userId
     * @return bool
     */
    public function isUserInQueue(int $userId): bool
    {
        return $this->where('user_id', $userId)
        ->where('status', self::STATUS_WAITING)
            ->count() > 0;
    }

    /**
     * 获取用户在队列中的位置
     * @param int $userId
     * @return int
     */
    public function getUserPosition(int $userId): int
    {
        return $this->where('user_id', $userId)
        ->where('status', self::STATUS_WAITING)
            ->value('position', 0);
    }

    /**
     * 从队列中移除用户
     * @param int $userId
     * @return bool
     */
    public function removeFromQueue(int $userId): bool
    {
        return $this->where('user_id', $userId)
            ->where('status', self::STATUS_WAITING)
            ->update(['status' => self::STATUS_CANCELED, 'cancel_time' => date('Y-m-d H:i:s', time())]);
    }
}