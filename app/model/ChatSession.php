<?php
declare (strict_types = 1);

namespace app\model;

use app\model\system\User;
use think\Model;

/**
 * 聊天会话模型类
 */
class ChatSession extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'ad_chat_session';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'createtime';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'updatetime';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'user_id',
        'service_id',
        'status',
        'start_time',
        'end_time',
        'duration',
        'satisfaction',
        'source',
        'last_message_time',
        'createtime',
        'updatetime',
        'feedback'
    ];

    /**
     * 创建新会话
     * @param int $userId 用户ID
     * @param int $serviceId 客服ID
     * @param string $source 会话来源
     * @return array|null
     */
    public function createSession($userId, $serviceId, $source = 'web')
    {
        $data = [
            'user_id' => $userId,
            'service_id' => $serviceId,
            'status' => 1,
            'start_time' => time(),
            'source' => $source,
            'last_message_time' => time()
        ];

        if ($this->save($data)) {
            return $this->toArray();
        }

        return null;
    }

    /**
     * 结束会话
     * @param int $sessionId 会话ID
     * @return bool|int
     */
    public function endSession($sessionId)
    {
        $session = $this->find($sessionId);
        if (!$session || $session['status'] != 1) {
            return false;
        }

        $endTime = time();
        $duration = $endTime - $session['start_time'];

        $result = $this->where('id', $sessionId)->update([
            'status' => 0,
            'end_time' => $endTime,
            'duration' => $duration
        ]) !== false;

        if ($result) {
            return true;
        }

        return $session->service_id;
    }

    /**
     * 转接会话
     * @param int $sessionId 会话ID
     * @param int $newServiceId 新客服ID
     * @return bool
     */
    public function transferSession($sessionId, $newServiceId)
    {
        return $this->where('id', $sessionId)->update([
            'service_id' => $newServiceId,
            'updatetime' => time()
        ]) !== false;
    }

    /**
     * 更新会话满意度评分
     * @param int $sessionId 会话ID
     * @param int $satisfaction 满意度评分
     * @return bool
     */
    public function updateSatisfaction($sessionId, $satisfaction, $feedback)
    {
        if ($satisfaction < 1 || $satisfaction > 5) {
            return false;
        }

        return $this->where('id', $sessionId)->update([
            'satisfaction' => $satisfaction,
                'feedback' => $feedback
        ]) !== false;
    }

    /**
     * 获取用户当前活动会话
     * @param int $userId 用户ID
     * @return array|null
     */
    public function getUserActiveSession($userId)
    {
        return $this->where([
            ['user_id', '=', $userId],
            ['status', '=', 1]
        ])->find();
    }

    /**
     * 获取客服当前活动会话列表
     * @param int $serviceId 客服ID
     * @param int $limit 每页限制
     * @param int $lastId 最后消息ID
     * @return array
     */
    public function getServiceActiveSessions($serviceId, $limit = 20, $lastId = null)
    {
        $query = $this->with(['user', 'lastMessage'])
            ->where([
                ['service_id', '=', $serviceId],
                ['status', '=', 1]
            ]);

        if ($lastId) {
            $query->where('id', '<', $lastId);
        }

        return$query->limit($limit)
            ->order('last_message_time', 'desc')
            ->select()
            ->toArray();
    }

    public function getServiceActiveSessionCountByServiceId($serviceId)
    {
        return $this->where('status', '=', 1)
            ->where('service_id', '=', $serviceId)
            ->count();
    }

    /**
     * 更新最后消息时间
     * @param int $sessionId 会话ID
     * @param int $timestamp 时间戳
     * @return bool
     */
    public function updateLastMessageTime($sessionId, $timestamp)
    {
        return $this->where('id', $sessionId)->update([
            'last_message_time' => $timestamp
        ]) !== false;
    }

    /**
     * 获取会话统计数据
     * @param array $condition 查询条件
     * @return array
     */
    public function getSessionStats($condition = [])
    {
        $query = $this->where($condition);

        return [
            'total' => $query->count(),
            'avg_duration' => $query->avg('duration'),
            'avg_satisfaction' => $query->avg('satisfaction'),
            'source_stats' => $query->group('source')
                ->field(['source', 'count(*) as count'])
                ->select()
                ->toArray()
        ];
    }

    /**
     * 用户关联
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 客服关联
     * @return \think\model\relation\BelongsTo
     */
    public function service()
    {
        return $this->belongsTo(CustomerService::class, 'service_id');
    }

    /**
     * 最后一条消息关联
     * @return \think\model\relation\HasOne
     */
    public function lastMessage()
    {
        return $this->hasOne(ChatMessage::class, 'session_id')
            ->where('message_type', '=', 'chat_message')
            ->order('id', 'desc');
    }
}