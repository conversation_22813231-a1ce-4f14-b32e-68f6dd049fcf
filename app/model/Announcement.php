<?php
namespace app\model;

use think\Model;

/**
 * 网站公告模型
 */
class Announcement extends Model
{
    // 设置表名
    protected $table = 'sys_announcement';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 状态获取器
     * @param $value
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '已禁用', 1 => '已启用'];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 类型获取器
     * @param $value
     * @return string
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = [1 => '普通公告', 2 => '重要公告', 3 => '紧急公告'];
        return $types[$data['type']] ?? '未知';
    }
    
    /**
     * 开始时间获取器
     * @param $value
     * @return string
     */
    public function getStartTimeTextAttr($value, $data)
    {
        return $data['start_time'] ? date('Y-m-d H:i:s', $data['start_time']) : '';
    }
    
    /**
     * 结束时间获取器
     * @param $value
     * @return string
     */
    public function getEndTimeTextAttr($value, $data)
    {
        return $data['end_time'] ? date('Y-m-d H:i:s', $data['end_time']) : '';
    }
}