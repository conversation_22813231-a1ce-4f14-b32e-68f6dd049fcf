<?php

namespace app\model;

use think\Model;

/**
 * 用户优惠券模型
 */
class UserCoupon extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'ad_user_coupon';
    
    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;
    
    /**
     * 优惠券状态常量
     */
    const STATUS_UNUSED = 1;      // 未使用
    const STATUS_USED = 2;         // 已使用
    const STATUS_EXPIRED = 3;      // 已过期
    
    /**
     * 与优惠券的关联
     */
    public function coupon()
    {
        return $this->belongsTo(Coupon::class, 'coupon_id', 'id');
    }
    
    /**
     * 与用户的关联
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_UNUSED => '未使用',
            self::STATUS_USED => '已使用',
            self::STATUS_EXPIRED => '已过期'
        ];
        return isset($statusMap[$data['status']]) ? $statusMap[$data['status']] : '未知状态';
    }
    
    /**
     * 格式化创建时间
     */
    public function getCreateTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['create_time']);
    }
    
    /**
     * 格式化使用时间
     */
    public function getUseTimeTextAttr($value, $data)
    {
        return !empty($data['use_time']) ? date('Y-m-d H:i:s', $data['use_time']) : '';
    }
    
    /**
     * 检查优惠券是否可用
     * @param float $orderAmount 订单金额
     * @return bool|string 可用返回true，不可用返回原因
     */
    public function checkAvailable($orderAmount)
    {
        // 检查状态
        if ($this->status != self::STATUS_UNUSED) {
            return '优惠券已使用或已过期';
        }
        
        // 加载优惠券信息
        $coupon = $this->coupon;
        if (!$coupon) {
            return '优惠券不存在';
        }
        
        // 检查有效期
        $now = time();
        if ($coupon->start_time > $now) {
            return '优惠券活动未开始';
        }
        if ($coupon->end_time < $now) {
            return '优惠券已过期';
        }
        
        // 检查订单金额
        if ($orderAmount < $coupon->min_order_amount) {
            return '订单金额未达到优惠券使用条件';
        }
        
        return true;
    }
    
    /**
     * 计算优惠金额
     * @param float $orderAmount 订单金额
     * @return float 优惠金额
     */
    public function calculateDiscount($orderAmount)
    {
        $coupon = $this->coupon;
        if (!$coupon) {
            return 0;
        }
        
        if ($coupon->type == Coupon::TYPE_FIXED) {
            // 固定金额
            return $coupon->value > $orderAmount ? $orderAmount : $coupon->value;
        } else {
            // 百分比折扣
            return round($orderAmount * $coupon->value / 100, 2);
        }
    }
    
    /**
     * 验证规则
     */
    protected $rule = [
        'user_id' => 'require|integer|gt:0',
        'coupon_id' => 'require|integer|gt:0',
        'status' => 'require|in:1,2,3'
    ];
    
    /**
     * 验证提示信息
     */
    protected $message = [
        'user_id.require' => '用户ID不能为空',
        'user_id.integer' => '用户ID必须为整数',
        'user_id.gt' => '用户ID必须大于0',
        'coupon_id.require' => '优惠券ID不能为空',
        'coupon_id.integer' => '优惠券ID必须为整数',
        'coupon_id.gt' => '优惠券ID必须大于0',
        'status.require' => '状态不能为空',
        'status.in' => '无效的状态值'
    ];
}