<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 电子面单模板模型
 * Class WaybillTemplate
 * @package app\model
 */
class WaybillTemplate extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ad_waybill_template';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 关联物流公司
     */
    public function logistics()
    {
        return $this->belongsTo(LogisticsCompany::class, 'logistics_id', 'id');
    }
    
    /**
     * 关联发件人
     */
    public function sender()
    {
        return $this->belongsTo(WaybillSender::class, 'sender_id', 'id');
    }
    
    /**
     * 关联打印配置
     */
    public function printer()
    {
        return $this->belongsTo(WaybillPrintConfig::class, 'printer_id', 'id');
    }
    
    /**
     * 设置默认模板
     * @param int $id
     * @return bool
     */
    public function setDefaultTemplate(int $id): bool
    {
        try {
            // 开启事务
            self::startTrans();
            
            // 先将所有模板设置为非默认
            self::where('is_default', 1)->update(['is_default' => 0]);
            
            // 将指定ID的模板设置为默认
            $result = self::where('id', $id)->update(['is_default' => 1]);
            
            // 提交事务
            self::commit();
            
            return $result !== false;
        } catch (\Exception $e) {
            // 回滚事务
            self::rollback();
            return false;
        }
    }
    
    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data): string
    {
        $status = [
            0 => '禁用',
            1 => '启用'
        ];
        return $status[$data['status']] ?? '';
    }
    
    /**
     * 获取是否默认文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getIsDefaultTextAttr($value, $data): string
    {
        $isDefault = [
            0 => '否',
            1 => '是'
        ];
        return $isDefault[$data['is_default']] ?? '';
    }
}