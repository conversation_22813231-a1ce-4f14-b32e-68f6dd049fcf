<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 商品规格模型
 */
class GoodsSpec extends Model
{
    // 设置表名
    protected $name = 'ad_goods_spec';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 关联规格值
     */
    public function values()
    {
        return $this->hasMany(GoodsSpecValue::class, 'spec_id')
            ->order('sort', 'asc');
    }
    
    /**
     * 获取所有规格
     * @param bool $withValues 是否包含规格值
     * @return array
     */
    public static function getAllSpecs($withValues = false)
    {
        $query = self::where('status', 1)->order('sort', 'asc');
        
        if ($withValues) {
            $query->with(['values' => function($query) {
                $query->where('status', 1)->order('sort', 'asc');
            }]);
        }
        
        return $query->select();
    }
    
    /**
     * 获取规格值组合描述
     * @param string $specValueIds 规格值ID组合
     * @return string
     */
    public static function getSpecValueStr($specValueIds)
    {
        if (empty($specValueIds)) {
            return '';
        }
        
        $valueIds = explode(',', $specValueIds);
        $values = GoodsSpecValue::whereIn('id', $valueIds)
            ->field('id,spec_id,value')
            ->select();
        
        if ($values->isEmpty()) {
            return '';
        }
        
        // 按规格ID分组
        $groupValues = [];
        foreach ($values as $value) {
            $groupValues[$value['spec_id']][] = $value['value'];
        }
        
        // 获取规格名称
        $specs = self::whereIn('id', array_keys($groupValues))
            ->column('name', 'id');
        
        // 组合规格描述
        $str = [];
        foreach ($groupValues as $specId => $values) {
            if (isset($specs[$specId])) {
                $str[] = $specs[$specId] . ':' . implode(',', $values);
            }
        }
        
        return implode(';', $str);
    }
}