<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 发件人信息模型
 * Class WaybillSender
 * @package app\model
 */
class WaybillSender extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ad_waybill_sender';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 获取默认发件人
     * @return array|null
     */
    public static function getDefaultSender()
    {
        return self::where('is_default', 1)
            ->find();
    }
    
    /**
     * 设置默认发件人
     * @param int $id
     * @return bool
     */
    public static function setDefaultSender(int $id): bool
    {
        try {
            // 开启事务
            self::startTrans();
            
            // 先将所有发件人设置为非默认
            self::where('is_default', 1)->update(['is_default' => 0]);
            
            // 将指定ID的发件人设置为默认
            $result = self::where('id', $id)->update(['is_default' => 1]);
            
            // 提交事务
            self::commit();
            
            return $result !== false;
        } catch (\Exception $e) {
            // 回滚事务
            self::rollback();
            return false;
        }
    }
    
    /**
     * 获取是否默认文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getIsDefaultTextAttr($value, $data)
    {
        $isDefault = [
            0 => '否',
            1 => '是'
        ];
        return $isDefault[$data['is_default']] ?? '';
    }
    
    /**
     * 获取完整地址
     * @param $value
     * @param $data
     * @return string
     */
    public function getFullAddressAttr($value, $data)
    {
        return $data['province'] . $data['city'] . $data['district'] . $data['address'];
    }
}