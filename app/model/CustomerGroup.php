<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

class CustomerGroup extends Model
{
    protected $name = 'ad_customer_service_group';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 创建时间字段
    protected $createTime = 'createtime';
    
    // 更新时间字段
    protected $updateTime = 'updatetime';
    
    // 字段类型转换
    protected $type = [
        'id'          => 'integer',
        'name'        => 'string',
        'description' => 'string',
        'status'      => 'integer',
        'sort'        => 'integer',
        'create_time' => 'datetime',
        'update_time' => 'datetime'
    ];
}