<?php

namespace app\model;

use think\Model;

/**
 * 安全事件模型
 */
class SecurityEvent extends Model
{
    // 表名
    protected $name = 'sys_security_events';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'int';
    protected $createTime = 'createtime';
    protected $updateTime = false;
    
    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'handled_time' => 'integer',
        'createtime' => 'integer',
        'details' => 'json'
    ];
    
    // 允许写入的字段
    protected $field = [
        'event_type', 'severity', 'user_id', 'ip_address', 'user_agent',
        'description', 'details', 'status', 'handled_by', 'handled_time', 'createtime'
    ];
    
    // 事件类型常量
    const EVENT_TYPE_SUSPICIOUS_IP = 'suspicious_ip';
    const EVENT_TYPE_RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded';
    const EVENT_TYPE_INVALID_TOKEN = 'invalid_token';
    const EVENT_TYPE_TOKEN_REPLAY = 'token_replay';
    const EVENT_TYPE_BLACKLIST_ACCESS = 'blacklist_access';
    const EVENT_TYPE_MULTIPLE_FAILURES = 'multiple_failures';
    const EVENT_TYPE_UNUSUAL_ACTIVITY = 'unusual_activity';
    
    // 严重程度常量
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';
    
    // 状态常量
    const STATUS_NEW = 'new';
    const STATUS_INVESTIGATING = 'investigating';
    const STATUS_RESOLVED = 'resolved';
    const STATUS_IGNORED = 'ignored';
    
    /**
     * 记录安全事件
     * 
     * @param string $eventType 事件类型
     * @param string $description 事件描述
     * @param string $severity 严重程度
     * @param array $details 事件详情
     * @param int $userId 用户ID
     * @return bool|SecurityEvent
     */
    public static function record($eventType, $description, $severity = self::SEVERITY_MEDIUM, $details = [], $userId = 0)
    {
        $data = [
            'event_type' => $eventType,
            'description' => $description,
            'severity' => $severity,
            'user_id' => $userId,
            'ip_address' => self::getClientIp(),
            'user_agent' => self::getUserAgent(),
            'details' => $details,
            'status' => self::STATUS_NEW,
            'createtime' => time()
        ];
        
        try {
            return self::create($data);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 记录可疑IP事件
     * 
     * @param string $ipAddress IP地址
     * @param string $reason 原因
     * @param array $details 详细信息
     * @return bool|SecurityEvent
     */
    public static function recordSuspiciousIp($ipAddress, $reason, $details = [])
    {
        $description = "检测到可疑IP访问: {$ipAddress}, 原因: {$reason}";
        
        $eventDetails = array_merge([
            'ip_address' => $ipAddress,
            'reason' => $reason,
            'detection_time' => date('Y-m-d H:i:s')
        ], $details);
        
        return self::record(
            self::EVENT_TYPE_SUSPICIOUS_IP,
            $description,
            self::SEVERITY_HIGH,
            $eventDetails
        );
    }
    
    /**
     * 记录速率限制超出事件
     * 
     * @param string $ipAddress IP地址
     * @param int $attempts 尝试次数
     * @param int $limit 限制次数
     * @param int $userId 用户ID
     * @return bool|SecurityEvent
     */
    public static function recordRateLimitExceeded($ipAddress, $attempts, $limit, $userId = 0)
    {
        $description = "IP {$ipAddress} 超出速率限制: {$attempts}/{$limit}";
        
        $details = [
            'ip_address' => $ipAddress,
            'attempts' => $attempts,
            'limit' => $limit,
            'exceeded_by' => $attempts - $limit
        ];
        
        return self::record(
            self::EVENT_TYPE_RATE_LIMIT_EXCEEDED,
            $description,
            self::SEVERITY_MEDIUM,
            $details,
            $userId
        );
    }
    
    /**
     * 记录无效Token事件
     * 
     * @param string $token Token值（脱敏）
     * @param string $reason 原因
     * @param int $userId 用户ID
     * @return bool|SecurityEvent
     */
    public static function recordInvalidToken($token, $reason, $userId = 0)
    {
        // Token脱敏处理
        $maskedToken = strlen($token) > 8 ? substr($token, 0, 8) . '***' : $token;
        
        $description = "检测到无效Token访问: {$maskedToken}, 原因: {$reason}";
        
        $details = [
            'token' => $maskedToken,
            'reason' => $reason,
            'ip_address' => self::getClientIp()
        ];
        
        return self::record(
            self::EVENT_TYPE_INVALID_TOKEN,
            $description,
            self::SEVERITY_MEDIUM,
            $details,
            $userId
        );
    }
    
    /**
     * 记录Token重放攻击事件
     * 
     * @param string $token Token值（脱敏）
     * @param string $jwtId JWT ID
     * @param int $userId 用户ID
     * @return bool|SecurityEvent
     */
    public static function recordTokenReplay($token, $jwtId, $userId = 0)
    {
        $maskedToken = strlen($token) > 8 ? substr($token, 0, 8) . '***' : $token;
        
        $description = "检测到Token重放攻击: {$maskedToken}";
        
        $details = [
            'token' => $maskedToken,
            'jwt_id' => $jwtId,
            'ip_address' => self::getClientIp(),
            'attack_type' => 'token_replay'
        ];
        
        return self::record(
            self::EVENT_TYPE_TOKEN_REPLAY,
            $description,
            self::SEVERITY_HIGH,
            $details,
            $userId
        );
    }
    
    /**
     * 记录黑名单Token访问事件
     * 
     * @param string $token Token值（脱敏）
     * @param int $userId 用户ID
     * @return bool|SecurityEvent
     */
    public static function recordBlacklistAccess($token, $userId = 0)
    {
        $maskedToken = strlen($token) > 8 ? substr($token, 0, 8) . '***' : $token;
        
        $description = "检测到黑名单Token访问尝试: {$maskedToken}";
        
        $details = [
            'token' => $maskedToken,
            'ip_address' => self::getClientIp(),
            'access_time' => date('Y-m-d H:i:s')
        ];
        
        return self::record(
            self::EVENT_TYPE_BLACKLIST_ACCESS,
            $description,
            self::SEVERITY_HIGH,
            $details,
            $userId
        );
    }
    
    /**
     * 记录多次失败尝试事件
     * 
     * @param string $ipAddress IP地址
     * @param int $failureCount 失败次数
     * @param string $timeWindow 时间窗口
     * @param int $userId 用户ID
     * @return bool|SecurityEvent
     */
    public static function recordMultipleFailures($ipAddress, $failureCount, $timeWindow, $userId = 0)
    {
        $description = "IP {$ipAddress} 在 {$timeWindow} 内出现 {$failureCount} 次失败尝试";
        
        $details = [
            'ip_address' => $ipAddress,
            'failure_count' => $failureCount,
            'time_window' => $timeWindow,
            'detection_time' => date('Y-m-d H:i:s')
        ];
        
        return self::record(
            self::EVENT_TYPE_MULTIPLE_FAILURES,
            $description,
            self::SEVERITY_MEDIUM,
            $details,
            $userId
        );
    }
    
    /**
     * 更新事件状态
     * 
     * @param int $eventId 事件ID
     * @param string $status 新状态
     * @param string $handledBy 处理人
     * @return bool
     */
    public static function updateStatus($eventId, $status, $handledBy = '')
    {
        try {
            $data = [
                'status' => $status,
                'handled_time' => time()
            ];
            
            if ($handledBy) {
                $data['handled_by'] = $handledBy;
            }
            
            return self::where('id', $eventId)->update($data);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取未处理的安全事件
     * 
     * @param string $severity 严重程度过滤
     * @param int $limit 限制数量
     * @return array
     */
    public static function getUnhandledEvents($severity = '', $limit = 50)
    {
        try {
            $query = self::where('status', self::STATUS_NEW);
            
            if ($severity) {
                $query->where('severity', $severity);
            }
            
            $list = $query->order('createtime', 'desc')
                ->limit($limit)
                ->select();
                
            return $list ? $list->toArray() : [];
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取IP地址的安全事件统计
     * 
     * @param string $ipAddress IP地址
     * @param int $hours 统计时间范围（小时）
     * @return array
     */
    public static function getIpEventStatistics($ipAddress, $hours = 24)
    {
        try {
            $startTime = time() - ($hours * 3600);
            
            $total = self::where('ip_address', $ipAddress)
                ->where('createtime', '>=', $startTime)
                ->count();
                
            $highSeverity = self::where('ip_address', $ipAddress)
                ->where('createtime', '>=', $startTime)
                ->where('severity', 'in', [self::SEVERITY_HIGH, self::SEVERITY_CRITICAL])
                ->count();
                
            $unhandled = self::where('ip_address', $ipAddress)
                ->where('createtime', '>=', $startTime)
                ->where('status', self::STATUS_NEW)
                ->count();
            
            return [
                'total' => $total,
                'high_severity' => $highSeverity,
                'unhandled' => $unhandled,
                'risk_level' => self::calculateRiskLevel($total, $highSeverity, $unhandled)
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'high_severity' => 0,
                'unhandled' => 0,
                'risk_level' => 'low'
            ];
        }
    }
    
    /**
     * 获取安全事件统计报告
     * 
     * @param int $days 统计天数
     * @return array
     */
    public static function getStatisticsReport($days = 7)
    {
        try {
            $startTime = time() - ($days * 24 * 3600);
            
            $total = self::where('createtime', '>=', $startTime)->count();
            
            $bySeverity = self::where('createtime', '>=', $startTime)
                ->field('severity, COUNT(*) as count')
                ->group('severity')
                ->select()
                ->toArray();
                
            $byType = self::where('createtime', '>=', $startTime)
                ->field('event_type, COUNT(*) as count')
                ->group('event_type')
                ->select()
                ->toArray();
                
            $byStatus = self::where('createtime', '>=', $startTime)
                ->field('status, COUNT(*) as count')
                ->group('status')
                ->select()
                ->toArray();
            
            return [
                'total' => $total,
                'by_severity' => $bySeverity,
                'by_type' => $byType,
                'by_status' => $byStatus,
                'period' => $days . ' days'
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'by_severity' => [],
                'by_type' => [],
                'by_status' => [],
                'period' => $days . ' days'
            ];
        }
    }
    
    /**
     * 清理旧的安全事件记录
     * 
     * @param int $days 保留天数
     * @param bool $onlyResolved 是否只清理已处理的事件
     * @return int 清理的记录数
     */
    public static function cleanup($days = 180, $onlyResolved = true)
    {
        try {
            $expireTime = time() - ($days * 24 * 3600);
            
            $query = self::where('createtime', '<', $expireTime);
            
            if ($onlyResolved) {
                $query->where('status', 'in', [self::STATUS_RESOLVED, self::STATUS_IGNORED]);
            }
            
            return $query->delete();
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * 计算风险级别
     * 
     * @param int $total 总事件数
     * @param int $highSeverity 高严重程度事件数
     * @param int $unhandled 未处理事件数
     * @return string
     */
    private static function calculateRiskLevel($total, $highSeverity, $unhandled)
    {
        if ($highSeverity >= 5 || $unhandled >= 10) {
            return 'critical';
        } elseif ($highSeverity >= 2 || $unhandled >= 5 || $total >= 20) {
            return 'high';
        } elseif ($total >= 5) {
            return 'medium';
        } else {
            return 'low';
        }
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @return string
     */
    private static function getClientIp()
    {
        if (function_exists('request')) {
            return request()->ip();
        }
        
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
              $_SERVER['HTTP_X_REAL_IP'] ?? 
              $_SERVER['REMOTE_ADDR'] ?? 
              'unknown';
              
        return $ip;
    }
    
    /**
     * 获取用户代理
     * 
     * @return string
     */
    private static function getUserAgent()
    {
        if (function_exists('request')) {
            return request()->header('User-Agent', 'unknown');
        }
        
        return $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    }
}