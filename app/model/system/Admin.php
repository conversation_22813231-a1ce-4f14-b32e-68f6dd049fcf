<?php

namespace app\model\system;

use think\Model;


class Admin extends Model
{
    // 表名
    protected $table = 'sys_admin';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'datetime';

    protected $updateTime = 'updatetime';

    protected $createTime = 'createtime';

    protected $deleteTime = 'deletetime';

    protected $defaultSoftDelete = 0;

    protected $field = [
        'username',
        'login_ip',
        'logintime',
        'loginfailure',
        'user_type',
        'user_no',
        'nickname',
        'mobile',
        'mail',
        'avatar',
        'enabled',
        'createtime',
        'updatetime',
        'deleteTime',
        'password',
        'salt',
        'deletetime',
        'profile'
    ];

    protected $visible = [
        'id',
        'username',
        'login_ip',
        'logintime',
        'user_no',
        'nickname',
        'mobile',
        'mail',
        'avatar',
        'enabled',
        'createtime',
        'updatetime',
        'profile'
    ];
}
