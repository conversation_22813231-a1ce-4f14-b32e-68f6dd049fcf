<?php

namespace app\model\system;

use think\Model;


class User extends Model
{
    // 表名
    protected $table = 'qi_users';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'datetime';

    protected $updateTime = 'updatetime';

    protected $createTime = 'createtime';

    protected $deleteTime = 'deletetime';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'username',
        'login_ip',
        'logintime',
        'loginfailure',
        'user_type',
        'user_no',
        'nickname',
        'mobile',
        'mail',
        'head_img',
        'level',
        'enabled',
        'createtime',
        'updatetime',
        'deleteTime',
        'password',
        'salt'
    ];

    protected $visible = [
        'id',
        'username',
        'login_ip',
        'logintime',
        'user_no',
        'nickname',
        'mobile',
        'mail',
        'head_img',
        'level',
        'enabled',
        'createtime',
        'updatetime'
    ];

    /**
     * 隐藏的字段
     * @var array
     */
    protected $hidden = [
        'password',
        'login_ip',
        'salt',
        'logintime'
    ];

    protected $defaultSoftDelete = 0;
}
