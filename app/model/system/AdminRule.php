<?php

namespace app\model\system;

use think\Model;


class AdminRule extends Model
{
    // 表名
    protected $table = 'sys_admin_rule';

    // 开启自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 自定义字段名（可选）
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 时间字段类型（支持datetime/timestamp/int）
    protected $dateFormat = 'Y-m-d H:i:s'; // 设置datetime类型时的输出格式

    protected $defaultSoftDelete = 0;
}
