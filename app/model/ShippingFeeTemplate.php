<?php
declare(strict_types=1);

namespace app\model;

use think\Model;
use think\facade\Db;

/**
 * 运费模板模型
 * Class ShippingFeeTemplate
 * @package app\model
 */
class ShippingFeeTemplate extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ad_shipping_fee_template';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 获取所有启用的运费模板
     * @return array
     */
    public static function getEnabledTemplates()
    {
        $templates = self::where('enabled', 1)
            ->field('id,name,type')
            ->select()
            ->toArray();

        if (!empty($templates)) {
            $templateIds = array_column($templates, 'id');
            $areas = Db::name('ad_shipping_fee_area')
                ->where('template_id', 'in', $templateIds)
                ->select()
                ->toArray();

            $areaMap = [];
            foreach ($areas as $area) {
                $areaMap[$area['template_id']][] = [
                    'area_ids' => $area['area_ids'],
                    'area_names' => $area['area_names'],
                    'first_unit' => $area['first_unit'],
                    'first_fee' => $area['first_fee'],
                    'additional_unit' => $area['additional_unit'],
                    'additional_fee' => $area['additional_fee']
                ];
            }

            foreach ($templates as &$template) {
                $template['areas'] = $areaMap[$template['id']] ?? [];
            }
        }

        return $templates;
    }
    
    /**
     * 计算运费
     * @param float $weight 重量或件数
     * @param int $templateId 运费模板ID
     * @param string $areaId 区域ID
     * @return float
     */
    public static function calculateFee(float $weight, int $templateId, string $areaId): float
    {
        $template = self::where('id', $templateId)
            ->where('enabled', 1)
            ->find();
            
        if (!$template) {
            return 0.00;
        }
        
        // 获取区域运费设置
        $area = Db::name('ad_shipping_fee_area')
            ->where('template_id', $templateId)
            ->where('area_ids', 'like', '%' . $areaId . '%')
            ->find();
            
        if (!$area) {
            return 0.00;
        }
        
        // 如果重量/件数小于等于首重/首件
        if ($weight <= $area['first_unit']) {
            return floatval($area['first_fee']);
        }
        
        // 计算续重/续件费用
        $additionalUnits = ceil(($weight - $area['first_unit']) / $area['additional_unit']);
        $additionalFee = $additionalUnits * $area['additional_fee'];
        
        return floatval($area['first_fee'] + $additionalFee);
    }

    /**
     * 保存运费模板及区域设置
     * @param array $data
     * @return bool
     */
    public function saveTemplate($data)
    {
        Db::startTrans();
        try {
            // 保存模板基本信息
            if (isset($data['id'])) {
                $template = $this->find($data['id']);
                if (!$template) {
                    return false;
                }
                $template->save([
                    'name' => $data['name'],
                    'type' => $data['type'],
                    'enabled' => $data['enabled'] ?? 1
                ]);
                $templateId = $data['id'];
                // 删除原有区域设置
                Db::name('ad_shipping_fee_area')->where('template_id', $templateId)->delete();
            } else {
                $template = $this->create([
                    'name' => $data['name'],
                    'type' => $data['type'],
                    'enabled' => $data['enabled'] ?? 1
                ]);
                $templateId = $template->id;
            }

            // 保存区域设置
            if (!empty($data['areas'])) {
                $areaData = [];
                $now = time();
                foreach ($data['areas'] as $area) {
                    $areaData[] = [
                        'template_id' => $templateId,
                        'area_ids' => $area['area_ids'],
                        'area_names' => $area['area_names'],
                        'first_unit' => $area['first_unit'],
                        'first_fee' => $area['first_fee'],
                        'additional_unit' => $area['additional_unit'],
                        'additional_fee' => $area['additional_fee'],
                        'createtime' => $now,
                        'updatetime' => $now
                    ];
                }
                Db::name('ad_shipping_fee_area')->insertAll($areaData);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }
}