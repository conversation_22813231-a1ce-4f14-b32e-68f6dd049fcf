<?php

namespace app\model;

use think\Model;

/**
 * Token操作日志模型
 */
class TokenLog extends Model
{
    // 表名
    protected $name = 'sys_token_logs';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'int';
    protected $createTime = 'createtime';
    protected $updateTime = false;
    
    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'createtime' => 'integer',
        'extra_data' => 'json'
    ];
    
    // 允许写入的字段
    protected $field = [
        'token', 'action', 'user_id', 'ip_address', 'user_agent',
        'result', 'error_code', 'error_message', 'extra_data', 'createtime'
    ];
    
    /**
     * 记录Token操作日志
     * 
     * @param string $action 操作类型
     * @param string $result 操作结果 success|failed|error
     * @param array $data 额外数据
     * @return bool|TokenLog
     */
    public static function log($action, $result = 'success', $data = [])
    {
        $logData = [
            'action' => $action,
            'result' => $result,
            'ip_address' => self::getClientIp(),
            'user_agent' => self::getUserAgent(),
            'createtime' => time()
        ];
        
        // 合并额外数据
        if (!empty($data)) {
            foreach (['token', 'user_id', 'error_code', 'error_message'] as $field) {
                if (isset($data[$field])) {
                    $logData[$field] = $data[$field];
                    unset($data[$field]);
                }
            }
            
            // 剩余数据作为extra_data
            if (!empty($data)) {
                $logData['extra_data'] = $data;
            }
        }
        
        // 对Token进行脱敏处理
        if (isset($logData['token']) && strlen($logData['token']) > 8) {
            $logData['token'] = substr($logData['token'], 0, 8) . '***';
        }
        
        try {
            return self::create($logData);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 记录Token生成日志
     * 
     * @param int $userId 用户ID
     * @param string $tokenType Token类型
     * @param bool $success 是否成功
     * @param string $errorMessage 错误信息
     * @return bool|TokenLog
     */
    public static function logGenerate($userId, $tokenType = 'access', $success = true, $errorMessage = '', $extraData = [])
    {
        $data = [
            'user_id' => $userId,
            'token_type' => $tokenType,
            'extraData' => $extraData
        ];
        
        if (!$success && $errorMessage) {
            $data['error_message'] = $errorMessage;
        }
        
        return self::log('generate', $success ? 'success' : 'failed', $data);
    }
    
    /**
     * 记录Token验证日志
     * 
     * @param string $token Token值
     * @param int $userId 用户ID
     * @param bool $success 是否成功
     * @param string $errorCode 错误代码
     * @param string $errorMessage 错误信息
     * @return bool|TokenLog
     */
    public static function logValidate($token, $userId = 0, $success = true, $errorCode = '', $errorMessage = '', $extraData = [])
    {
        $data = [
            'token' => $token,
            'user_id' => $userId,
            'extraData' => $extraData
        ];
        
        if (!$success) {
            if ($errorCode) $data['error_code'] = $errorCode;
            if ($errorMessage) $data['error_message'] = $errorMessage;
        }
        
        return self::log('validate', $success ? 'success' : 'failed', $data);
    }
    
    /**
     * 记录Token刷新日志
     * 
     * @param string $oldToken 旧Token
     * @param string $newToken 新Token
     * @param int $userId 用户ID
     * @param bool $success 是否成功
     * @param string $errorMessage 错误信息
     * @return bool|TokenLog
     */
    public static function logRefresh($oldToken, $newToken = '', $userId = 0, $success = true, $errorMessage = '', $extraData = [])
    {
        $data = [
            'token' => $oldToken,
            'user_id' => $userId,
            'new_token' => $newToken,
            'extraData' => $extraData
        ];
        
        if (!$success && $errorMessage) {
            $data['error_message'] = $errorMessage;
        }
        
        return self::log('refresh', $success ? 'success' : 'failed', $data);
    }
    
    /**
     * 记录Token撤销日志
     * 
     * @param string $token Token值
     * @param int $userId 用户ID
     * @param bool $addToBlacklist 是否加入黑名单
     * @param string $reason 撤销原因
     * @return bool|TokenLog
     */
    public static function logRevoke($token, $userId = 0, $addToBlacklist = false, $reason = '', $extraData = [])
    {
        $data = [
            'token' => $token,
            'user_id' => $userId,
            'add_to_blacklist' => $addToBlacklist,
            'reason' => $reason,
            'extraData' => $extraData
        ];
        
        return self::log('revoke', 'success', $data);
    }
    
    /**
     * 获取用户的操作日志
     * 
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $action 操作类型过滤
     * @return array
     */
    public static function getUserLogs($userId, $page = 1, $limit = 20, $action = '')
    {
        try {
            $query = self::where('user_id', $userId);
            
            if ($action) {
                $query->where('action', $action);
            }
            
            $list = $query->order('createtime', 'desc')
                ->page($page, $limit)
                ->select();
                
            return $list ? $list->toArray() : [];
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取失败的操作日志
     * 
     * @param int $hours 最近几小时
     * @param string $action 操作类型
     * @return array
     */
    public static function getFailedLogs($hours = 24, $action = '')
    {
        try {
            $startTime = time() - ($hours * 3600);
            
            $query = self::where('result', 'in', ['failed', 'error'])
                ->where('createtime', '>=', $startTime);
                
            if ($action) {
                $query->where('action', $action);
            }
            
            $list = $query->order('createtime', 'desc')
                ->limit(100)
                ->select();
                
            return $list ? $list->toArray() : [];
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取IP地址的操作统计
     * 
     * @param string $ipAddress IP地址
     * @param int $hours 统计时间范围（小时）
     * @return array
     */
    public static function getIpStatistics($ipAddress, $hours = 1)
    {
        try {
            $startTime = time() - ($hours * 3600);
            
            $total = self::where('ip_address', $ipAddress)
                ->where('createtime', '>=', $startTime)
                ->count();
                
            $failed = self::where('ip_address', $ipAddress)
                ->where('createtime', '>=', $startTime)
                ->where('result', 'in', ['failed', 'error'])
                ->count();
                
            $success = $total - $failed;
            
            return [
                'total' => $total,
                'success' => $success,
                'failed' => $failed,
                'success_rate' => $total > 0 ? round($success / $total * 100, 2) : 0
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'success' => 0,
                'failed' => 0,
                'success_rate' => 0
            ];
        }
    }
    
    /**
     * 清理旧的日志记录
     * 
     * @param int $days 保留天数
     * @return int 清理的记录数
     */
    public static function cleanup($days = 90)
    {
        try {
            $expireTime = time() - ($days * 24 * 3600);
            return self::where('createtime', '<', $expireTime)->delete();
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @return string
     */
    private static function getClientIp()
    {
        if (function_exists('request')) {
            return request()->ip();
        }
        
        // 备用方法
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
              $_SERVER['HTTP_X_REAL_IP'] ?? 
              $_SERVER['REMOTE_ADDR'] ?? 
              'unknown';
              
        return $ip;
    }
    
    /**
     * 获取用户代理
     * 
     * @return string
     */
    private static function getUserAgent()
    {
        if (function_exists('request')) {
            return request()->header('User-Agent', 'unknown');
        }
        
        return $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    }
}