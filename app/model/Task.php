<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 定时任务模型
 */
class Task extends Model
{
    // 设置表名
    protected $name = 'sys_task';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 类型转换
    protected $type = [
        'notify_type' => 'array',
        'notify_target' => 'array',
        'createtime' => 'timestamp',
        'updatetime' => 'timestamp'
    ];
    
    /**
     * 获取任务列表
     * @param array $where 查询条件
     * @param array $sort 排序条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getTaskList($where = [], $sort = [], $page = 1, $limit = 10)
    {
        $query = self::where($where);
        
        if (!empty($sort)) {
            foreach ($sort as $field => $order) {
                $query->order($field, $order);
            }
        } else {
            $query->order('createtime', 'desc');
        }
        
        return $query->paginate([
            'list_rows' => $limit,
            'page' => $page
        ]);
    }
    
    /**
     * 获取任务详情
     * @param int $id 任务ID
     * @return Task|null
     */
    public static function getTaskDetail($id)
    {
        return self::find($id);
    }
    
    /**
     * 更新任务状态
     * @param int $id 任务ID
     * @param int $status 状态值
     * @return bool
     */
    public static function updateTaskStatus($id, $status)
    {
        return self::where('id', $id)->update(['status' => $status, 'updatetime' => time()]);
    }
    
    /**
     * 获取所有启用的任务
     * @return array
     */
    public static function getEnabledTasks()
    {
        return self::where('status', 1)->select()->toArray();
    }
    
    /**
     * 记录任务执行日志
     * @param int $taskId 任务ID
     * @param int $status 执行状态 0:执行中 1:成功 2:失败
     * @param string $result 执行结果
     * @param int $startTime 开始时间
     * @param int $endTime 结束时间
     * @return int
     */
    public static function recordTaskExecution($taskId, $status, $result = '', $startTime = null, $endTime = null)
    {
        $data = [
            'task_id' => $taskId,
            'status' => $status,
            'result' => $result,
            'start_time' => $startTime ?: time(),
            'end_time' => $endTime ?: time()
        ];
        
        return 	\think\facade\Db::table('sys_task_record')->insertGetId($data);
    }
    
    /**
     * 获取任务执行记录
     * @param int $taskId 任务ID
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getTaskRecords($taskId, $where = [], $page = 1, $limit = 10)
    {
        $query = 	\think\facade\Db::table('sys_task_record')
            ->where('task_id', $taskId);
            
        if (!empty($where)) {
            $query->where($where);
        }
        
        return $query->order('id', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);
    }
    
    /**
     * 清理过期的执行记录
     * @param int $days 保留天数
     * @return int
     */
    public static function cleanExpiredRecords($days = 30)
    {
        $expireTime = time() - ($days * 86400);
        return 	\think\facade\Db::table('sys_task_record')
            ->where('start_time', '<', $expireTime)
            ->delete();
    }
}