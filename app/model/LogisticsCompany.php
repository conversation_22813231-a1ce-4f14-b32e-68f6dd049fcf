<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 物流公司模型
 * Class LogisticsCompany
 * @package app\model
 */
class LogisticsCompany extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ad_logistics_company';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 获取所有启用的物流公司
     * @return array
     */
    public static function getEnabledCompanies()
    {
        return self::where('status', 1)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
    }
    
    /**
     * 根据快递鸟编码获取物流公司信息
     * @param string $code
     * @return array|null
     */
    public static function getByKdnCode(string $code)
    {
        return self::where('kdn_code', $code)
            ->where('enabled', 1)
            ->find();
    }
    
    /**
     * 根据快递100编码获取物流公司信息
     * @param string $code
     * @param bool $isPaid 是否使用付费版编码
     * @return array|null
     */
    public static function getByKd100Code(string $code, bool $isPaid = false)
    {
        $field = $isPaid ? 'kd100_pay_code' : 'kd100_free_code';
        return self::where($field, $code)
            ->where('status', 1)
            ->find();
    }
    
    /**
     * 根据菜鸟物流编码获取物流公司信息
     * @param string $code
     * @return array|null
     */
    public static function getByCaiNiaoCode(string $code)
    {
        return self::where('cainiao_bird_code', $code)
            ->where('enabled', 1)
            ->find();
    }
    
    /**
     * 根据快递查询接口编码获取物流公司信息
     * @param string $code
     * @return array|null
     */
    public static function getByExpressQueryCode(string $code)
    {
        return self::where('express_query_code', $code)
            ->where('enabled', 1)
            ->find();
    }
}