<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 商品品牌模型
 */
class GoodsBrand extends Model
{
    // 设置表名
    protected $name = 'ad_brand';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 获取品牌列表
     * 
     * @param array $params 查询参数
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getBrandList($params = [], $page = 1, $limit = 10)
    {
        $query = self::alias('b');
        
        // 关键词搜索
        if (!empty($params['keyword'])) {
            $query->where('b.name|b.description', 'like', '%' . $params['keyword'] . '%');
        }
        
        // 状态筛选
        if (isset($params['enabled'])) {
            $query->where('b.enabled', $params['enabled']);
        }
        
        // 排序
        $orderField = !empty($params['order_field']) ? $params['order_field'] : 'b.sort';
        $orderType = !empty($params['order_type']) ? $params['order_type'] : 'asc';
        $query->order($orderField, $orderType);
        
        // 查询字段
        $query->field('b.*');
        
        // 分页查询
        $count = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();
        
        return [
            'total' => $count,
            'per_page' => $limit,
            'current_page' => $page,
            'last_page' => ceil($count / $limit),
            'data' => $list
        ];
    }
    
    /**
     * 获取品牌详情
     * 
     * @param int $id 品牌ID
     * @return array|null
     */
    public static function getBrandDetail($id)
    {
        return self::find($id);
    }
}