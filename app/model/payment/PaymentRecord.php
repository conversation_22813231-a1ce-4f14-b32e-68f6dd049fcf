<?php

namespace app\model\payment;

use think\Model;

class PaymentRecord extends Model
{
    // 设置表名
    protected $name = 'ad_payment_record';
    
    // 设置字段信息
    protected $schema = [
        'id'           => 'int',
        'record_no'    => 'string',
        'type'         => 'int',
        'amount'       => 'float',
        'category'     => 'int',
        'pay_method'   => 'int',
        'status'       => 'int',
        'operator_name'=> 'string',
        'remark'       => 'string',
        'create_time'  => 'datetime',
        'confirm_time' => 'datetime'
    ];

    // 类型映射
    public static $typeMap = [
        1 => '收入',
        2 => '支出'
    ];

    // 分类映射
    public static $categoryMap = [
        1 => '销售收入',
        2 => '服务收入',
        3 => '退款',
        4 => '其他收入',
        5 => '采购支出',
        6 => '工资支出',
        7 => '租金支出',
        8 => '其他支出'
    ];

    // 支付方式映射
    public static $payMethodMap = [
        1 => '微信支付',
        2 => '支付宝',
        3 => '银行转账',
        4 => '现金'
    ];

    // 状态映射
    public static $statusMap = [
        1 => '已确认',
        2 => '待确认',
        3 => '已取消'
    ];
}