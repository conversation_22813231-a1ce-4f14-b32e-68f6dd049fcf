<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 商品评价模型
 */
class GoodsComment extends Model
{
    // 设置表名
    protected $name = 'ad_goods_comment';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 评分获取器
     */
    public function getRatingAttr($value)
    {
        return (int)$value;
    }
    
    /**
     * 图片获取器
     */
    public function getImagesAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }
    
    /**
     * 图片修改器
     */
    public function setImagesAttr($value)
    {
        if (is_array($value)) {
            return implode(',', $value);
        }
        return $value;
    }
    
    /**
     * 关联商品
     */
    public function goods()
    {
        return $this->belongsTo(Goods::class, 'goods_id', 'id');
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}