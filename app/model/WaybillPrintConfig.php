<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 电子面单打印配置模型
 * Class WaybillPrintConfig
 * @package app\model
 */
class WaybillPrintConfig extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ad_waybill_print_config';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 打印尺寸常量
    const SIZE_80_180 = 1;   // 80mm × 180mm
    const SIZE_100_150 = 2;  // 100mm × 150mm
    const SIZE_100_180 = 3;  // 100mm × 180mm
    const SIZE_CUSTOM = 4;   // 自定义尺寸
    
    // 打印时机常量
    const PRINT_TIME_CREATE = 1;  // 订单创建后立即打印
    const PRINT_TIME_PAY = 2;     // 订单支付后打印
    
    /**
     * 获取打印尺寸列表
     * @return array
     */
    public static function getPrintSizes(): array
    {
        return [
            self::SIZE_80_180 => '80mm × 180mm',
            self::SIZE_100_150 => '100mm × 150mm',
            self::SIZE_100_180 => '100mm × 180mm',
            self::SIZE_CUSTOM => '自定义尺寸'
        ];
    }
    
    /**
     * 获取打印时机列表
     * @return array
     */
    public static function getPrintTimes(): array
    {
        return [
            self::PRINT_TIME_CREATE => '订单创建后立即打印',
            self::PRINT_TIME_PAY => '订单支付后打印'
        ];
    }
    
    /**
     * 获取打印内容项列表
     * @return array
     */
    public static function getPrintContentItems(): array
    {
        return [
            'sender_info' => '发件人信息',
            'receiver_info' => '收件人信息',
            'goods_info' => '商品信息',
            'order_info' => '订单信息',
            'barcode' => '条形码',
            'qrcode' => '二维码'
        ];
    }
    
    /**
     * 获取当前配置
     * @return array|Model|null
     */
    public static function getCurrentConfig($id = null)
    {
        if (!$id) {
            return null;
        }
        // 获取指定配置
        return self::where('id', $id)->find();
    }
    
    /**
     * 保存配置
     * @param array $data
     * @return bool
     */
    public static function saveConfig(array $data): bool
    {
        // 处理打印内容数组
        if (isset($data['print_content']) && is_array($data['print_content'])) {
            $data['print_content'] = implode(',', $data['print_content']);
        }
        
        // 如果是自定义尺寸，验证宽高
        if ($data['print_size'] == self::SIZE_CUSTOM) {
            if (empty($data['custom_width']) || empty($data['custom_height'])) {
                throw new \Exception('自定义尺寸时必须设置宽度和高度');
            }
        } else {
            // 非自定义尺寸时清空自定义宽高
            $data['custom_width'] = null;
            $data['custom_height'] = null;
        }

        // 获取当前配置
        $config = self::getCurrentConfig(isset($data['id']) ?? $data['id']);

        if ($config) {
            unset($data['id']);
            // 更新配置
            return $config->save($data) !== false;
        } else {
            // 创建配置
            return self::create($data) !== false;
        }
    }
    
    /**
     * 获取打印尺寸文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getPrintSizeTextAttr($value, $data): string
    {
        $sizes = self::getPrintSizes();
        if ($data['print_size'] == self::SIZE_CUSTOM) {
            return sprintf('自定义尺寸(%dmm × %dmm)', $data['custom_width'], $data['custom_height']);
        }
        return $sizes[$data['print_size']] ?? '';
    }
    
    /**
     * 获取打印时机文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getPrintTimeTextAttr($value, $data): string
    {
        $times = self::getPrintTimes();
        return $times[$data['print_time']] ?? '';
    }
    
    /**
     * 获取打印内容数组
     * @param $value
     * @param $data
     * @return array
     */
    public function getPrintContentArrAttr($value, $data): array
    {
        return $data['print_content'] ? explode(',', $data['print_content']) : [];
    }
    
    /**
     * 获取自动打印文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getAutoPrintTextAttr($value, $data): string
    {
        return $data['auto_print'] ? '是' : '否';
    }
    
    /**
     * 获取打印预览文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getPrintPreviewTextAttr($value, $data): string
    {
        return $data['print_preview'] ? '是' : '否';
    }
}