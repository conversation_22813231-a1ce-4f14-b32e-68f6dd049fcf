<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 商品分类模型
 */
class Category extends Model
{
    // 设置表名
    protected $name = 'sys_category';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 获取分类树形结构
     * 
     * @param int $pid 父级ID
     * @param bool $recursive 是否递归获取子分类
     * @return array
     */
    public static function getCategoryTree($pid = 0, $recursive = true)
    {
        $query = self::where('pid', $pid)->where('enabled', 1)->order('sort', 'asc');
        $categories = $query->select()->toArray();
        
        if ($recursive) {
            foreach ($categories as &$category) {
                $children = self::getCategoryTree($category['id'], true);
                $category['children'] = $children;
                $category['has_children'] = !empty($children);
            }
        }
        
        return $categories;
    }
    
    /**
     * 获取分类及其所有父级分类
     * 
     * @param int $categoryId 分类ID
     * @return array
     */
    public static function getCategoryPath($categoryId)
    {
        $path = [];
        $category = self::find($categoryId);
        
        if (!$category) {
            return $path;
        }
        
        $path[] = $category->toArray();
        
        if ($category->pid > 0) {
            $parentPath = self::getCategoryPath($category->pid);
            $path = array_merge($parentPath, $path);
        }
        
        return $path;
    }
    
    /**
     * 获取所有子分类ID
     * 
     * @param int $categoryId 分类ID
     * @return array 子分类ID数组
     */
    public static function getChildrenIds($categoryId)
    {
        $childIds = [];
        $children = self::where('pid', $categoryId)->select()->toArray();
        
        foreach ($children as $child) {
            $childIds[] = $child['id'];
            $grandChildIds = self::getChildrenIds($child['id']);
            $childIds = array_merge($childIds, $grandChildIds);
        }
        
        return $childIds;
    }
}