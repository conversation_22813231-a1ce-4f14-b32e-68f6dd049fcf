<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 商品图片模型
 */
class GoodsImage extends Model
{
    // 设置表名
    protected $name = 'ad_goods_image';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 关联商品
     */
    public function goods()
    {
        return $this->belongsTo(Goods::class, 'goods_id');
    }
    
    /**
     * 获取商品的所有图片
     * @param int $goodsId 商品ID
     * @return array
     */
    public static function getGoodsImages($goodsId)
    {
        return self::where('goods_id', $goodsId)
            ->order('sort', 'asc')
            ->column('url');
    }
    
    /**
     * 保存商品图片
     * @param int $goodsId 商品ID
     * @param array $images 图片URL数组
     * @return bool
     */
    public static function saveGoodsImages($goodsId, array $images)
    {
        // 删除原有图片
        self::where('goods_id', $goodsId)->delete();
        
        // 添加新图片
        $data = [];
        foreach ($images as $key => $url) {
            $data[] = [
                'goods_id' => $goodsId,
                'url' => $url,
                'sort' => $key,
                'is_thumb' => $key === 0 ? 1 : 0
            ];
        }
        
        return self::insertAll($data);
    }
    
    /**
     * 更新图片排序
     * @param array $sorts [图片ID => 排序值]
     * @return bool
     */
    public static function updateSort(array $sorts)
    {
        $data = [];
        foreach ($sorts as $id => $sort) {
            $data[] = [
                'id' => $id,
                'sort' => $sort
            ];
        }
        
        return (new self)->saveAll($data);
    }
}