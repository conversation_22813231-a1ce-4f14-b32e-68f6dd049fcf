<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;
use app\model\CustomerGroup;

/**
 * 客服系统模型类
 */
class CustomerService extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'ad_customer_service';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'createtime';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'updatetime';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'admin_id',
        'name',
        'avatar',
        'status',
        'max_sessions',
        'auto_reply',
        'group_id',
        'createtime',
        'updatetime',
        'current_sessions',
        'phone',
        'wechat',
        'qq'
    ];

    /**
     * 获取在线客服列表
     * @param int|null $groupId 客服分组ID
     * @return array
     */
    public function getOnlineServices($groupId = null)
    {
        $query = $this->where('status', 1);
        
        if ($groupId !== null) {
            $query->where('group_id', $groupId);
        }
        
        return $query->select()->toArray();
    }

    /**
     * 获取所有在线客服（按分组）
     * @return array 按分组组织的客服列表
     */
    public function getOnlineServicesByGroup()
    {
        $services = $this->getOnlineServices();
        $groupedServices = [];
        
        // 从CustomerGroup模型获取分组名称
        $groupNames = CustomerGroup::where('enabled', 1)
            ->column('name', 'id');

        foreach ($services as $service) {
            $groupId = $service['group_id'];
            if (!isset($groupedServices[$groupId])) {
                $groupedServices[$groupId] = [
                    'groupName' => $groupNames[$groupId] ?? '未分组',
                    'services' => []
                ];
            }
            
            $groupedServices[$groupId]['services'][] = [
                'id' => $service['id'],
                'name' => $service['name'],
                'avatar' => $service['avatar']
            ];
        }
        
        // 转换为索引数组
        return array_values($groupedServices);
    }

    public function getServices($groupId = null)
    {

        if ($groupId !== null) {
            $this->where('group_id', $groupId);
        }

        return $this->select()->toArray();
    }

    public function getOnlineServiceTotal($groupId = null)
    {
        $query = $this->where('status', 1);

        if ($groupId !== null) {
            $query->where('group_id', $groupId);
        }

        return $query->count();
    }

    /**
     * 获取客服当前会话数
     * @param int $serviceId 客服ID
     * @return int
     */
    public function getCurrentSessionCount($serviceId)
    {
        return $this->where([
            ['id', '=', $serviceId],
        ])->value('current_sessions');
    }

    public function decCurrentSession($service_id)
    {
        $current = $this->where('id', $service_id)->value('current_sessions');
        if ($current <= 0) {
            return true; // 已为0，无需更新
        }

        return $this->where('id', $service_id)
            ->dec('current_sessions')
            ->save();
    }

    /**
     * 检查客服是否可以接待新会话
     * @param int $serviceId 客服ID
     * @return bool
     */
    public function canAcceptNewSession($serviceId)
    {
        $service = $this->find($serviceId);
        if (!$service || $service['status'] == 0) {
            return false;
        }

        $currentSessions = $this->getCurrentSessionCount($serviceId);
        return $currentSessions < $service['max_sessions'];
    }

    /**
     * 获取可用的客服列表（在线且有空闲会话槽位）
     * @param int|null $groupId 客服分组ID
     * @return array
     */
    public function getAvailableServices($groupId = null)
    {
        $onlineServices = $this->getOnlineServices($groupId);
        $availableServices = [];
        
        foreach ($onlineServices as $service) {
            if ($this->canAcceptNewSession($service['id'])) {
                $availableServices[] = $service;
            }
        }
        
        return $availableServices;
    }

    /**
     * 增加客服当前会话数
     * @param int $serviceId 客服ID
     * @return bool
     */
    public function increaseCurrentSessions($serviceId)
    {
        return $this->where('id', $serviceId)
            ->inc('current_sessions')
            ->save();
    }

    /**
     * 分配最合适的客服
     * @param int|null $groupId 客服分组ID
     * @return array|null
     */
    public function assignService($groupId = null)
    {
        // 获取在线客服列表
        $onlineServices = $this->getOnlineServices($groupId);
        if (empty($onlineServices)) {
            return null;
        }

        // 查找负载最小的客服
        $minLoad = null;
        $selectedService = null;

        foreach ($onlineServices as $service) {
            $currentSessions = $this->getCurrentSessionCount($service['id']);
            $load = $currentSessions / $service['max_sessions'];

            if ($minLoad === null || $load < $minLoad) {
                $minLoad = $load;
                $selectedService = $service;

                // 如果找到完全空闲的客服，直接返回
                if ($load == 0) {
                    break;
                }
            }
        }

        return $selectedService;
    }

    /**
     * 更新客服状态
     * @param int $serviceId 客服ID
     * @param int $status 状态值
     * @return bool
     */
    public function updateStatus($serviceId, $status)
    {
        return $this->where('id', $serviceId)->update(['status' => $status]) !== false;
    }

    /**
     * 获取客服基本信息
     * @param int $serviceId 客服ID
     * @return array|null
     */
    public function getServiceInfo($serviceId)
    {
        $service = $this->find($serviceId);
        if (!$service) {
            return null;
        }

        return [
            'id' => $service['id'],
            'name' => $service['name'],
            'avatar' => $service['avatar'],
            'status' => $service['status'],
            'group_id' => $service['group_id'],
            'work_id' => $service['work_id'],
            'phone' => $service['phone'],
            'auto_reply' => $service['auto_reply'],
            'wechat' => $service['wechat'],
            'qq' => $service['qq'],
        ];
    }

    /**
     * 获取客服性能统计
     * @param int $serviceId 客服ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return array
     */
    public function getPerformanceStats($serviceId, $startTime, $endTime)
    {
        // 会话统计
        $sessionStats = ChatSession::where([
            ['service_id', '=', $serviceId],
            ['createtime', 'between', [strtotime($startTime), strtotime($endTime)]]
        ])->field([
            'COUNT(*) as total_sessions',
            'AVG(duration) as avg_duration',
            'COUNT(CASE WHEN satisfaction > 0 THEN 1 END) as rated_sessions',
            'AVG(satisfaction) as avg_satisfaction'
        ])->find();

        // 消息统计
        $messageStats = ChatMessage::where([
            ['service_id', '=', $serviceId],
            ['createtime', 'between', [strtotime($startTime), strtotime($endTime)]]
        ])->field([
            'COUNT(*) as total_messages',
            'AVG(response_time) as avg_response_time'
        ])->find();

        return [
            'total_sessions' => $sessionStats['total_sessions'] ?? 0,
            'avg_duration' => $sessionStats['avg_duration'] ?? 0,
            'rated_sessions' => $sessionStats['rated_sessions'] ?? 0,
            'avg_satisfaction' => $sessionStats['avg_satisfaction'] ?? 0,
            'total_messages' => $messageStats['total_messages'] ?? 0,
            'avg_response_time' => $messageStats['avg_response_time'] ?? 0
        ];
    }
}