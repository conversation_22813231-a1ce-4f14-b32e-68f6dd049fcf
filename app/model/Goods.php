<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 商品模型
 */
class Goods extends Model
{
    // 设置表名
    protected $name = 'ad_goods';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 设置字段类型
    protected $type = [
        'images' => 'array',
        'specs' => 'array',
        'params' => 'array',
    ];
    
    // 设置json字段
    protected $json = ['images', 'specs', 'params'];
    
    /**
     * 关联商品分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }
    
    /**
     * 获取商品列表
     * 
     * @param array $params 查询参数
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getGoodsList($params = [], $page = 1, $limit = 10)
    {
        $query = self::alias('g')
            ->join('goods_category c', 'g.category_id = c.id', 'left');
        
        // 分类筛选
        if (!empty($params['category_id'])) {
            $query->where('g.category_id', $params['category_id']);
        }
        
        // 关键词搜索
        if (!empty($params['keyword'])) {
            $query->where('g.title|g.keywords|g.description', 'like', '%' . $params['keyword'] . '%');
        }
        
        // 价格区间
        if (!empty($params['min_price'])) {
            $query->where('g.price', '>=', $params['min_price']);
        }
        if (!empty($params['max_price'])) {
            $query->where('g.price', '<=', $params['max_price']);
        }
        
        // 上架状态
        if (isset($params['status'])) {
            $query->where('g.status', $params['status']);
        }
        
        // 排序
        $orderField = !empty($params['order_field']) ? $params['order_field'] : 'g.create_time';
        $orderType = !empty($params['order_type']) ? $params['order_type'] : 'desc';
        $query->order($orderField, $orderType);
        
        // 查询字段
        $query->field('g.*, c.name as category_name');
        
        // 分页查询
        $count = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();
        
        return [
            'total' => $count,
            'per_page' => $limit,
            'current_page' => $page,
            'last_page' => ceil($count / $limit),
            'data' => $list
        ];
    }
    
    /**
     * 更新商品库存
     * 
     * @param int $goodsId 商品ID
     * @param int $num 变更数量，正数增加，负数减少
     * @return bool
     */
    public static function updateStock($goodsId, $num)
    {
        $goods = self::find($goodsId);
        if (!$goods) {
            return false;
        }
        
        // 计算新库存
        $newStock = $goods->stock + $num;
        if ($newStock < 0) {
            return false; // 库存不足
        }
        
        // 更新库存
        $goods->stock = $newStock;
        return $goods->save();
    }
}