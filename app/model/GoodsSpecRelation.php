<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 商品规格关联模型
 */
class GoodsSpecRelation extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ad_goods_spec_relation';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'goods_id'    => 'int',
        'spec_id'     => 'int',
        'sort'        => 'int'
    ];
    
    /**
     * 关联商品模型
     */
    public function goods()
    {
        return $this->belongsTo(Goods::class, 'goods_id', 'id');
    }
    
    /**
     * 关联规格模型
     */
    public function spec()
    {
        return $this->belongsTo(GoodsSpec::class, 'spec_id', 'id');
    }
}