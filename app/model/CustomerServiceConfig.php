<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

class CustomerServiceConfig extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ad_customer_service_config';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    
    // 创建时间字段
    protected $createTime = 'createtime';
    
    // 更新时间字段
    protected $updateTime = 'updatetime';

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'reception_mode' => 'string',
        'max_concurrent_customers' => 'integer',
        'auto_assign_wait_time' => 'integer',
        'session_timeout' => 'integer',
        'work_time_mode' => 'string',
        'non_work_time_message' => 'string',
        'enable_non_work_time_message' => 'boolean',
        'max_queue_size' => 'integer',
        'queue_message' => 'string',
        'createtime' => 'datetime',
        'updatetime' => 'datetime'
    ];

    // 允许写入的字段
    protected $field = [
        'reception_mode',
        'max_concurrent_customers',
        'auto_assign_wait_time',
        'session_timeout',
        'work_time_mode',
        'non_work_time_message',
        'enable_non_work_time_message',
        'max_queue_size',
        'queue_message'
    ];
}