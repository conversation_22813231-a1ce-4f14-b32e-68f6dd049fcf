<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 用户地址模型
 */
class UserAddress extends Model
{
    // 设置表名
    protected $name = 'user_address';
    protected $prefix = 'ad_';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'is_default' => 'boolean',
        'createtime' => 'integer',
        'updatetime' => 'integer'
    ];

    /**
     * 获取用户的默认地址
     * @param int $userId 用户ID
     * @return UserAddress|null
     */
    public static function getDefaultAddress($userId)
    {
        return self::where('user_id', $userId)
            ->where('is_default', 1)
            ->find();
    }

    /**
     * 获取用户的所有地址
     * @param int $userId 用户ID
     * @return array
     */
    public static function getUserAddresses($userId)
    {
        return self::where('user_id', $userId)
            ->order(['is_default' => 'desc', 'id' => 'desc'])
            ->select()
            ->toArray();
    }

    /**
     * 设置为默认地址
     * @param int $userId 用户ID
     * @return bool
     */
    public function setAsDefault($userId)
    {
        // 开启事务
        self::startTrans();
        try {
            // 将该用户的所有地址设置为非默认
            self::where('user_id', $userId)->update(['is_default' => 0]);
            
            // 将当前地址设置为默认
            $this->is_default = 1;
            $this->save();

            self::commit();
            return true;
        } catch (\Exception $e) {
            self::rollback();
            return false;
        }
    }

    /**
     * 新增地址时，如果是第一个地址则自动设置为默认地址
     */
    public static function onBeforeInsert($model)
    {
        // 检查是否是用户的第一个地址
        $count = self::where('user_id', $model->user_id)->count();
        if ($count === 0) {
            $model->is_default = 1;
        }
    }

    /**
     * 删除默认地址后，将最新的地址设置为默认地址
     */
    public static function onAfterDelete($model)
    {
        if ($model->is_default) {
            // 获取该用户最新的一个地址
            $latestAddress = self::where('user_id', $model->user_id)
                ->order('id', 'desc')
                ->find();
            
            if ($latestAddress) {
                $latestAddress->setAsDefault($model->user_id);
            }
        }
    }
}