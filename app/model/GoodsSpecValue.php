<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 商品规格值模型
 */
class GoodsSpecValue extends Model
{
    // 设置表名
    protected $name = 'ad_goods_spec_value';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 关联规格
     */
    public function spec()
    {
        return $this->belongsTo(GoodsSpec::class, 'spec_id');
    }
    
    /**
     * 获取规格的所有值
     * @param int $specId 规格ID
     * @return array
     */
    public static function getSpecValues($specId)
    {
        return self::where('spec_id', $specId)
            ->where('status', 1)
            ->order('sort', 'asc')
            ->select();
    }
    
    /**
     * 批量保存规格值
     * @param int $specId 规格ID
     * @param array $values 规格值数组
     * @return bool
     */
    public static function saveSpecValues($specId, array $values)
    {
        // 删除原有规格值
        self::where('spec_id', $specId)->delete();
        
        if (empty($values)) {
            return true;
        }
        
        // 添加新规格值
        $data = [];
        foreach ($values as $key => $value) {
            $data[] = [
                'spec_id' => $specId,
                'value' => $value,
                'sort' => $key,
                'status' => 1
            ];
        }
        
        return self::insertAll($data);
    }
    
    /**
     * 更新规格值排序
     * @param array $sorts [规格值ID => 排序值]
     * @return \think\Collection
     */
    public static function updateSort(array $sorts)
    {
        $data = [];
        foreach ($sorts as $id => $sort) {
            $data[] = [
                'id' => $id,
                'sort' => $sort
            ];
        }
        return self::saveAll($data);
        
    }
}