<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 运费模板区域费用模型
 * Class ShippingFeeArea
 * @package app\model
 */
class ShippingFeeArea extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ad_shipping_fee_area';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 获取区域费用设置
     * @param int|array $templateIds 运费模板ID或ID数组
     * @return array
     */
    public static function getAreaSettings($templateIds)
    {
        $query = self::where('template_id', is_array($templateIds) ? 'in' : '=', $templateIds);
        $areas = $query->select()->toArray();
        
        if (is_array($templateIds)) {
            $areaMap = [];
            foreach ($areas as $area) {
                $areaMap[$area['template_id']][] = [
                    'area_ids' => $area['area_ids'],
                    'area_names' => $area['area_names'],
                    'first_unit' => $area['first_unit'],
                    'first_fee' => $area['first_fee'],
                    'additional_unit' => $area['additional_unit'],
                    'additional_fee' => $area['additional_fee']
                ];
            }
            return $areaMap;
        }
        
        return array_map(function($area) {
            return [
                'area_ids' => $area['area_ids'],
                'area_names' => $area['area_names'],
                'first_unit' => $area['first_unit'],
                'first_fee' => $area['first_fee'],
                'additional_unit' => $area['additional_unit'],
                'additional_fee' => $area['additional_fee']
            ];
        }, $areas);
    }
}