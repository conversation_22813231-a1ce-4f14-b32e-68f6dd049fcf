<?php
declare (strict_types = 1);

namespace app\model;

use app\model\system\User;
use think\facade\Log;
use think\Model;

/**
 * 聊天消息模型类
 */
class ChatMessage extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'ad_chat_message';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'createtime';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'updatetime';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'session_id',
        'from_id',
        'from_type',
        'to_id',
        'content',
        'content_type',
        'status',
        'read_status',
        'response_time',
        'createtime',
        'updatetime',
        'message_type',
        'to_type'
    ];

    /**
     * 创建新消息
     * @param array $data 消息数据
     * @return array|null
     */
    public function createMessage($data)
    {
        // 计算响应时间
        if ($data['from_type'] == 'service') {
            $lastUserMessage = $this->where([
                ['session_id', '=', $data['session_id']],
                ['from_type', '=', 'user']
            ])->order('id', 'desc')->find();

            Log::info('last user message: ' . $lastUserMessage);

            if ($lastUserMessage) {
                // 确保createtime是时间戳格式
                $createTime = is_numeric($lastUserMessage['createtime'])
                    ? (int)$lastUserMessage['createtime']
                    : strtotime($lastUserMessage['createtime']);
                $data['response_time'] = time() - $createTime;
                Log::info('计算响应时间', [
                    'last_message_time' => $createTime,
                    'current_time' => time(),
                    'response_time' => $data['response_time']
                ]);
            }
        }

        $data['createtime'] = time();
        $data['updatetime'] = time();

        if ($this->insert($data)) {
            // 更新会话最后消息时间
            ChatSession::where('id', $data['session_id'])
                ->update(['last_message_time' => time()]);

            return $this->toArray();
        }

        return null;
    }

    /**
     * 批量创建消息
     * @param array $messages 消息数据数组
     * @return bool
     */
    public function createMessages(array $messages): bool
    {
        if (empty($messages)) {
            return false;
        }

        $currentTime = time();
        $sessionIds = [];
        
        // 处理每条消息的数据
        foreach ($messages as &$data) {
            $data['createtime'] = $currentTime;
            $data['updatetime'] = $currentTime;
            
            // 收集会话ID用于后续更新
            if (!in_array($data['session_id'], $sessionIds)) {
                $sessionIds[] = $data['session_id'];
            }
        }
        unset($data);

        try {
            // 批量插入消息
            $result = $this->insertAll($messages);
            
            if ($result) {
                // 批量更新会话最后消息时间
                if (!empty($sessionIds)) {
                    ChatSession::whereIn('id', $sessionIds)
                        ->update(['last_message_time' => $currentTime]);
                }
                
                Log::info('批量创建消息成功', ['count' => count($messages)]);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('批量创建消息失败', [
                'error' => $e->getMessage(),
                'count' => count($messages)
            ]);
            return false;
        }
    }

    /**
     * 获取会话历史消息
     * @param int $sessionId 会话ID
     * @param int $limit 每页限制
     * @param int $lastId 最后消息ID
     * @return array
     */
    public function getSessionMessages($sessionId, $limit = 20, $lastId = null)
    {
        $query = $this->with(['sender', 'receiver'])
            ->where('session_id', $sessionId)
            ->where('message_type', '=', 'chat_message');

        if ($lastId) {
            $query->where('id', '<', $lastId);
        }

        return $query->order('id', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 标记消息为已读
     * @param int $sessionId 会话ID
     * @param int $userId 用户ID
     * @param string $userType 用户类型
     * @return bool
     */
    public function markAsRead($sessionId, $userId, $userType)
    {
        return $this->where([
            ['session_id', '=', $sessionId],
            ['to_id', '=', $userId],
            ['read_status', '=', 0]
        ])->update(['read_status' => 1]) !== false;
    }

    /**
     * 获取未读消息数
     * @param int $userId 用户ID
     * @param string $userType 用户类型
     * @return int
     */
    public function getUnreadCount($userId, $userType)
    {
        return $this->where([
            ['to_id', '=', $userId],
            ['read_status', '=', 0]
        ])->count();
    }

    /**
     * 获取会话的最后一条消息
     * @param int $sessionId 会话ID
     * @return array|null
     */
    public function getLastMessage($sessionId)
    {
        return $this->where('session_id', $sessionId)
            ->order('id', 'desc')
            ->find();
    }

    /**
     * 删除消息
     * @param int $messageId 消息ID
     * @param int $userId 用户ID
     * @param string $userType 用户类型
     * @return bool
     */
    public function deleteMessage($messageId, $userId, $userType)
    {
        $message = $this->find($messageId);
        if (!$message || $message['from_id'] != $userId || $message['from_type'] != $userType) {
            return false;
        }

        return $this->where('id', $messageId)->update([
            'status' => 0
        ]) !== false;
    }

    /**
     * 获取消息统计数据
     * @param array $condition 查询条件
     * @return array
     */
    public function getMessageStats($condition = [])
    {
        $query = $this->where($condition);

        return [
            'total_messages' => $query->count(),
            'avg_response_time' => $query->where('response_time', '>', 0)
                ->avg('response_time'),
            'type_stats' => $query->group('content_type')
                ->field(['content_type', 'count(*) as count'])
                ->select()
                ->toArray()
        ];
    }

    /**
     * 会话关联
     * @return \think\model\relation\BelongsTo
     */
    public function session()
    {
        return $this->belongsTo(ChatSession::class, 'session_id');
    }

    /**
     * 发送者关联
     * @return \think\model\relation\BelongsTo
     */
    public function sender()
    {
        return $this->belongsTo($this->getSenderModel(), 'from_id');
    }

    /**
     * 接收者关联
     * @return \think\model\relation\BelongsTo
     */
    public function receiver()
    {
        return $this->belongsTo($this->getReceiverModel(), 'to_id');
    }

    /**
     * 获取发送者模型类
     * @return string
     */
    protected function getSenderModel()
    {
        return $this->from_type == 'service' ? CustomerService::class : User::class;
    }

    /**
     * 获取接收者模型类
     * @return string
     */
    protected function getReceiverModel()
    {
        return $this->to_type == 'service' ? CustomerService::class : User::class;
    }
}