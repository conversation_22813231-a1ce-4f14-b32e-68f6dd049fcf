<?php

namespace app\model;

use think\Model;

/**
 * 优惠券模型
 */
class Coupon extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'ad_coupon';
    
    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;
    
    /**
     * 优惠券类型常量
     */
    const TYPE_FIXED = 1;        // 固定金额
    const TYPE_PERCENTAGE = 2;    // 百分比折扣
    
    /**
     * 优惠券状态常量
     */
    const STATUS_NORMAL = 1;     // 正常
    const STATUS_DISABLED = 0;    // 禁用
    
    /**
     * 与用户优惠券的关联
     */
    public function userCoupons()
    {
        return $this->hasMany(UserCoupon::class, 'coupon_id', 'id');
    }
    
    /**
     * 获取优惠券类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::TYPE_FIXED => '满减券',
            self::TYPE_PERCENTAGE => '折扣券'
        ];
        return isset($typeMap[$data['type']]) ? $typeMap[$data['type']] : '未知类型';
    }
    
    /**
     * 获取优惠券价值文本
     */
    public function getValueTextAttr($value, $data)
    {
        if ($data['type'] == self::TYPE_PERCENTAGE) {
            return $data['value'] . '%';
        }
        return '¥' . $data['value'];
    }
    
    /**
     * 格式化开始时间
     */
    public function getStartTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['start_time']);
    }
    
    /**
     * 格式化结束时间
     */
    public function getEndTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['end_time']);
    }
    
    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|max:50',
        'type' => 'require|in:1,2',
        'value' => 'require|float|gt:0',
        'min_order_amount' => 'require|float|egt:0',
        'stock' => 'require|integer|egt:0',
        'per_limit' => 'require|integer|egt:1',
        'start_time' => 'require|integer|gt:0',
        'end_time' => 'require|integer|gt:start_time',
        'status' => 'require|in:0,1'
    ];
    
    /**
     * 验证提示信息
     */
    protected $message = [
        'name.require' => '优惠券名称不能为空',
        'name.max' => '优惠券名称最多50个字符',
        'type.require' => '优惠券类型不能为空',
        'type.in' => '无效的优惠券类型',
        'value.require' => '优惠券面值不能为空',
        'value.float' => '优惠券面值必须为数字',
        'value.gt' => '优惠券面值必须大于0',
        'min_order_amount.require' => '最低订单金额不能为空',
        'min_order_amount.float' => '最低订单金额必须为数字',
        'min_order_amount.egt' => '最低订单金额不能小于0',
        'stock.require' => '库存不能为空',
        'stock.integer' => '库存必须为整数',
        'stock.egt' => '库存不能小于0',
        'per_limit.require' => '每人限领数量不能为空',
        'per_limit.integer' => '每人限领数量必须为整数',
        'per_limit.egt' => '每人限领数量必须大于0',
        'start_time.require' => '开始时间不能为空',
        'start_time.integer' => '开始时间格式错误',
        'start_time.gt' => '开始时间必须大于0',
        'end_time.require' => '结束时间不能为空',
        'end_time.integer' => '结束时间格式错误',
        'end_time.gt' => '结束时间必须大于开始时间',
        'status.require' => '状态不能为空',
        'status.in' => '无效的状态值'
    ];
}