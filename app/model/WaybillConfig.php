<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 电子面单配置模型
 * Class WaybillConfig
 * @package app\model
 */
class WaybillConfig extends Model
{
    // 打印配置表名
    const PRINT_CONFIG_TABLE = 'ad_waybill_print_config';

    /**
     * 获取打印配置
     * @return array|null
     */
    public static function getPrintConfig()
    {
        return self::table(self::PRINT_CONFIG_TABLE)
            ->order('id', 'desc')
            ->find();
    }
    
    /**
     * 更新打印配置
     * @param array $data
     * @return WaybillConfig
     */
    public static function updatePrintConfig(array $data): WaybillConfig
    {
        $config = self::table(self::PRINT_CONFIG_TABLE)->find();
        if ($config) {
            return self::table(self::PRINT_CONFIG_TABLE)
                ->where('id', $config['id'])
                ->update(array_merge($data, ['updatetime' => time()]));
        } else {
            return self::table(self::PRINT_CONFIG_TABLE)
                ->insert(array_merge($data, [
                    'createtime' => time(),
                    'updatetime' => time()
                ]));
        }
    }
    
    /**
     * 获取API配置
     * @return array|null
     */
    public static function getApiConfig()
    {
        $config = config('waybill');
        $platform = $config['default_platform'];
        
        if (!isset($config['platforms'][$platform])) {
            return null;
        }
        
        return [
            'api_type' => $platform,
            'app_id' => $config['platforms'][$platform]['app_id'] ?? '',
            'app_key' => $config['platforms'][$platform]['app_key'] ?? '',
            'request_url' => '',
            'sandbox' => $config['platforms'][$platform]['sandbox'] ?? false,
            'debug' => $config['platforms'][$platform]['debug'] ?? false,
            'timeout' => $config['platforms'][$platform]['timeout'] ?? 30,
        ];
    }
    
    /**
     * 测试API连接
     * @return bool
     */
    public static function testApiConnection(): bool
    {
        $config = self::getApiConfig();
        if (!$config) {
            return false;
        }
        
        // TODO: 实现具体的API连接测试逻辑
        return true;
    }
    
    /**
     * 测试打印功能
     * @return bool
     */
    public static function testPrint(): bool
    {
        $config = self::getPrintConfig();
        if (!$config) {
            return false;
        }
        
        // TODO: 实现具体的打印测试逻辑
        return true;
    }
}