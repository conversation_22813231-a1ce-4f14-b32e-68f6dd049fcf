<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 地区模型
 */
class Region extends Model
{
    // 设置表名
    protected $name = 'sys_region';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 类型转换
    protected $type = [
        'id' => 'integer',
        'parent_id' => 'integer',
        'level' => 'integer',
        'sort' => 'integer',
        'status' => 'boolean',
        'createtime' => 'integer',
        'updatetime' => 'integer'
    ];

    /**
     * 获取所有省份
     * @return array
     */
    public static function getProvinces()
    {
        return self::where('level', 1)
            ->where('status', 1)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 获取指定省份的城市列表
     * @param int $provinceId 省份ID
     * @return array
     */
    public static function getCities($provinceId)
    {
        return self::where('parent_id', $provinceId)
            ->where('level', 2)
            ->where('status', 1)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 获取指定城市的区县列表
     * @param int $cityId 城市ID
     * @return array
     */
    public static function getDistricts($cityId)
    {
        return self::where('parent_id', $cityId)
            ->where('level', 3)
            ->where('status', 1)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 根据编码获取地区信息
     * @param string $code 地区编码
     * @return Region|null
     */
    public static function getByCode($code)
    {
        return self::where('code', $code)
            ->where('status', 1)
            ->find();
    }

    /**
     * 获取地区完整名称（省市区）
     * @param int $districtId 区县ID
     * @return string
     */
    public static function getFullName($districtId)
    {
        $names = [];
        $region = self::find($districtId);
        
        if ($region) {
            $names[] = $region->name;
            
            // 获取城市名称
            $city = self::find($region->parent_id);
            if ($city) {
                $names[] = $city->name;
                
                // 获取省份名称
                $province = self::find($city->parent_id);
                if ($province) {
                    $names[] = $province->name;
                }
            }
        }
        
        return implode(' ', array_reverse($names));
    }

    /**
     * 验证地区编码是否有效
     * @param string $provinceCode 省份编码
     * @param string $cityCode 城市编码
     * @param string $districtCode 区县编码
     * @return bool
     */
    public static function validateCodes($provinceCode, $cityCode, $districtCode)
    {
        // 验证省份
        $province = self::where('code', $provinceCode)
            ->where('level', 1)
            ->where('status', 1)
            ->find();
            
        if (!$province) {
            return false;
        }

        // 验证城市
        $city = self::where('code', $cityCode)
            ->where('parent_id', $province->id)
            ->where('level', 2)
            ->where('status', 1)
            ->find();
            
        if (!$city) {
            return false;
        }

        // 验证区县
        return self::where('code', $districtCode)
            ->where('parent_id', $city->id)
            ->where('level', 3)
            ->where('status', 1)
            ->find() !== null;
    }

    /**
     * 获取下级地区列表
     * @param int $parentId 父级ID
     * @return array
     */
    public static function getChildren($parentId)
    {
        return self::where('parent_id', $parentId)
            ->where('status', 1)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 根据地区编码获取父级ID
     * @param string $code 地区编码
     * @return int|null
     */
    public static function getParentIdByCode($code)
    {
        $region = self::where('code', $code)
            ->where('status', 1)
            ->find();
        return $region ? $region->id : null;
    }

    /**
     * 根据地区编码获取下级地区列表
     * @param string $code 地区编码
     * @return array
     */
    public static function getChildrenByCode($code)
    {
        $region = self::where('code', $code)
            ->where('status', 1)
            ->find();
        
        if (!$region) {
            return [];
        }
        
        return self::where('parent_id', $region->id)
            ->where('status', 1)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
    }
}