<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 商品SKU模型
 */
class GoodsSku extends Model
{
    // 设置表名
    protected $name = 'ad_goods_sku';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 关联商品
     */
    public function goods()
    {
        return $this->belongsTo(Goods::class, 'goods_id');
    }
    
    /**
     * 获取商品的所有SKU
     * @param int $goodsId 商品ID
     * @return array
     */
    public static function getGoodsSkus($goodsId)
    {
        return self::where('goods_id', $goodsId)
            ->where('status', 1)
            ->select();
    }
    
    /**
     * 保存商品SKU
     * @param int $goodsId 商品ID
     * @param array $skuData SKU数据
     * @return bool
     */
    public static function saveGoodsSkus($goodsId, array $skuData)
    {
        // 删除原有SKU
        self::where('goods_id', $goodsId)->delete();
        
        if (empty($skuData)) {
            return true;
        }
        
        // 添加新SKU
        $data = [];
        foreach ($skuData as $sku) {
            // 生成规格值描述
            $specValueStr = GoodsSpec::getSpecValueStr($sku['spec_value_ids']);
            
            $data[] = [
                'goods_id' => $goodsId,
                'spec_value_ids' => $sku['spec_value_ids'],
                'spec_value_str' => $specValueStr,
                'price' => $sku['price'],
                'original_price' => $sku['original_price'] ?? 0,
                'cost_price' => $sku['cost_price'] ?? 0,
                'stock' => $sku['stock'],
                'code' => $sku['code'] ?? '',
                'weight' => $sku['weight'] ?? 0,
                'volume' => $sku['volume'] ?? 0,
                'status' => 1
            ];
        }
        
        return self::insertAll($data);
    }
    
    /**
     * 更新SKU库存
     * @param int $id SKU ID
     * @param int $num 变更数量
     * @param string $type 变更类型：increase=增加，decrease=减少
     * @return bool
     */
    public static function updateStock($id, $num, $type = 'decrease')
    {
        $sku = self::find($id);
        if (!$sku) {
            return false;
        }
        
        switch ($type) {
            case 'increase':
                return $sku->inc('stock', $num)->save();
            case 'decrease':
                if ($sku->stock < $num) {
                    return false;
                }
                return $sku->dec('stock', $num)->save();
            default:
                return false;
        }
    }
    
    /**
     * 检查SKU库存是否足够
     * @param int $id SKU ID
     * @param int $num 需要的数量
     * @return bool
     */
    public static function checkStock($id, $num)
    {
        $stock = self::where('id', $id)
            ->where('status', 1)
            ->value('stock');
        
        return $stock !== null && $stock >= $num;
    }
    
    /**
     * 关联SKU规格值关联表
     */
    public function specValues()
    {
        return $this->hasMany(GoodsSkuSpecValue::class, 'sku_id', 'id');
    }
}