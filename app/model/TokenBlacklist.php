<?php

namespace app\model;

use think\Model;

/**
 * Token黑名单模型
 */
class TokenBlacklist extends Model
{
    // 表名
    protected $name = 'sys_token_blacklist';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'int';
    protected $createTime = 'createtime';
    protected $updateTime = false;
    
    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'expire_time' => 'integer',
        'createtime' => 'integer',
    ];
    
    // 允许写入的字段
    protected $field = [
        'token', 'token_type', 'user_id', 'reason', 
        'created_by', 'expire_time', 'createtime'
    ];
    
    /**
     * 添加Token到黑名单
     * 
     * @param string $token Token值
     * @param string $tokenType Token类型 access|refresh
     * @param int $userId 用户ID
     * @param string $reason 加入原因
     * @param string $createdBy 操作者
     * @param int $expireTime 过期时间
     * @return bool|TokenBlacklist
     */
    public static function addToBlacklist($token, $tokenType = 'access', $userId = 0, $reason = '', $createdBy = '', $expireTime = null)
    {
        if ($expireTime === null) {
            $expireTime = time() + 86400; // 默认24小时后过期
        }
        
        $data = [
            'token' => $token,
            'token_type' => $tokenType,
            'user_id' => $userId,
            'reason' => $reason,
            'created_by' => $createdBy,
            'expire_time' => $expireTime,
            'createtime' => time()
        ];
        
        try {
            return self::create($data);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 检查Token是否在黑名单中
     * 
     * @param string $token Token值
     * @return bool
     */
    public static function isBlacklisted($token)
    {
        $count = self::where('token', $token)
            ->where('expire_time', '>', time())
            ->count();
            
        return $count > 0;
    }
    
    /**
     * 从黑名单中移除Token
     * 
     * @param string $token Token值
     * @return bool
     */
    public static function removeFromBlacklist($token)
    {
        try {
            return self::where('token', $token)->delete();
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 批量撤销用户的所有Token
     * 
     * @param int $userId 用户ID
     * @param string $reason 撤销原因
     * @return int 撤销的Token数量
     */
    public static function revokeAllUserTokens($userId, $reason = 'security_cleanup')
    {
        try {
            $revokedCount = 0;
            
            // 查找用户在用户Token表中的所有有效Token
            $userTokens = \think\facade\Db::table('qi_user_token')
                ->where('user_id', $userId)
                ->where('expire_time', '>', time())
                ->field('token, jwt_id')
                ->select();
                
            foreach ($userTokens as $tokenData) {
                $token = $tokenData['token'];
                $jwtId = $tokenData['jwt_id'] ?? '';
                
                // 添加到黑名单
                if (self::addToBlacklist($token, 'access', $userId, $reason, 'system')) {
                    $revokedCount++;
                }
            }
            
            // 查找用户在管理员Token表中的所有有效Token
            $adminTokens = \think\facade\Db::table('sys_admin_token')
                ->where('user_id', $userId)
                ->where('expire_time', '>', time())
                ->field('token, jwt_id')
                ->select();
                
            foreach ($adminTokens as $tokenData) {
                $token = $tokenData['token'];
                $jwtId = $tokenData['jwt_id'] ?? '';
                
                // 添加到黑名单
                if (self::addToBlacklist($token, 'access', $userId, $reason, 'system')) {
                    $revokedCount++;
                }
            }
            
            // 更新Token表中的状态（标记为已撤销）
            if ($revokedCount > 0) {
                // 更新用户Token表
                \think\facade\Db::table('qi_user_token')
                    ->where('user_id', $userId)
                    ->where('expire_time', '>', time())
                    ->update([
                        'expire_time' => time() - 1, // 设置为已过期
                        'last_used_time' => time()
                    ]);
                    
                // 更新管理员Token表
                \think\facade\Db::table('sys_admin_token')
                    ->where('user_id', $userId)
                    ->where('expire_time', '>', time())
                    ->update([
                        'expire_time' => time() - 1, // 设置为已过期
                        'last_used_time' => time()
                    ]);
            }
            
            return $revokedCount;
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * 清理过期的黑名单记录
     * 
     * @return int 清理的记录数
     */
    public static function cleanupExpired()
    {
        try {
            return self::where('expire_time', '<', time())->delete();
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * 获取用户的黑名单Token列表
     * 
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getUserBlacklistTokens($userId, $page = 1, $limit = 20)
    {
        try {
            $list = self::where('user_id', $userId)
                ->where('expire_time', '>', time())
                ->order('createtime', 'desc')
                ->page($page, $limit)
                ->select();
                
            return $list ? $list->toArray() : [];
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取黑名单统计信息
     * 
     * @return array
     */
    public static function getStatistics()
    {
        try {
            $total = self::count();
            $active = self::where('expire_time', '>', time())->count();
            $expired = $total - $active;
            
            $accessTokens = self::where('token_type', 'access')
                ->where('expire_time', '>', time())
                ->count();
                
            $refreshTokens = self::where('token_type', 'refresh')
                ->where('expire_time', '>', time())
                ->count();
            
            return [
                'total' => $total,
                'active' => $active,
                'expired' => $expired,
                'access_tokens' => $accessTokens,
                'refresh_tokens' => $refreshTokens
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'active' => 0,
                'expired' => 0,
                'access_tokens' => 0,
                'refresh_tokens' => 0
            ];
        }
    }
}