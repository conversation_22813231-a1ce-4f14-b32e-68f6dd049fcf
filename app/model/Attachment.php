<?php
declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 附件模型
 */
class Attachment extends Model
{
    // 设置表名
    protected $table = 'sys_attachment';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 获取存储位置文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStorageTextAttr($value, $data)
    {
        $storage = [
            'local' => '本地存储',
            'oss'   => '云存储'
        ];
        return $storage[$data['storage']] ?? '';
    }
    
    /**
     * 获取用户类型文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getUserTypeTextAttr($value, $data)
    {
        $types = [
            'admin' => '管理员',
            'user'  => '用户'
        ];
        return $types[$data['user_type']] ?? '';
    }
    
    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getEnabledTextAttr($value, $data)
    {
        $enabled = [
            0 => '禁用',
            1 => '正常'
        ];
        return $enabled[$data['enabled']] ?? '';
    }
}