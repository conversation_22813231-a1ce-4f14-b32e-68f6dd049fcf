<?php

namespace app\model;

use think\Model;

/**
 * 卡密管理模型
 */
class Card extends Model
{
    // 设置表名
    protected $name = 'ad_card';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    
    /**
     * 状态获取器
     * @param $value
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '已禁用', 1 => '未使用', 2 => '已使用'];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 类型获取器
     * @param $value
     * @return string
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = ['recharge' => '充值卡', 'member' => '会员卡', 'gift' => '礼品卡'];
        return $types[$data['type']] ?? '未知';
    }
    
    /**
     * 有效期开始时间获取器
     * @param $value
     * @return string
     */
    public function getValidStartTimeTextAttr($value, $data)
    {
        return $data['valid_start_time'] ? date('Y-m-d H:i:s', $data['valid_start_time']) : '';
    }
    
    /**
     * 有效期结束时间获取器
     * @param $value
     * @return string
     */
    public function getValidEndTimeTextAttr($value, $data)
    {
        return $data['valid_end_time'] ? date('Y-m-d H:i:s', $data['valid_end_time']) : '';
    }
    
    /**
     * 创建时间获取器
     * @param $value
     * @return string
     */
    public function getCreateTimeTextAttr($value, $data)
    {
        return $data['create_time'] ? date('Y-m-d H:i:s', $data['create_time']) : '';
    }
    
    /**
     * 使用时间获取器
     * @param $value
     * @return string
     */
    public function getUseTimeTextAttr($value, $data)
    {
        return $data['use_time'] ? date('Y-m-d H:i:s', $data['use_time']) : '';
    }
}