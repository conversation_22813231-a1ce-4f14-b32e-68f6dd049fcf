<?php

namespace app\model;

use app\model\system\User;
use think\Model;

class ChatComment extends Model
{
    protected $table = 'ad_chat_comment';
    
    protected $field = [
        'user_id', 
        'mobile', 
        'wechat', 
        'content', 
        'status'
    ];
    
    // 状态常量
    const STATUS_UNPROCESSED = 0;
    const STATUS_PROCESSED = 1;
    
    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    // 获取状态文本
    public function getStatusTextAttribute()
    {
        return [
            self::STATUS_UNPROCESSED => '未处理',
            self::STATUS_PROCESSED => '已处理'
        ][$this->status];
    }

    public function confirm(int $id) :bool
    {
        $comment = $this->find($id);
        if ($comment && $comment->process()) {
            // 处理成功
            return true;
        } else {
            // 处理失败
            return false;
        }
    }
    
    /**
     * 处理留言
     * @return bool 是否处理成功
     */
    public function process()
    {
        if ($this->status != self::STATUS_UNPROCESSED) {
            return false;
        }
        
        $this->status = self::STATUS_PROCESSED;
        return $this->save();
    }
}