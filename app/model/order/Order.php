<?php

namespace app\model\order;

use think\Model;

class Order extends Model
{
    // 表名
    protected $table = 'ad_order';

    // 开启自动写入时间戳
    protected $autoWriteTimestamp = true;

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 订单状态常量
    const STATUS_PENDING_PAYMENT = 1;    // 待支付
    const STATUS_PAID = 2;               // 已支付
    const STATUS_SHIPPED = 3;            // 已发货
    const STATUS_COMPLETED = 4;          // 已完成
    const STATUS_CANCELLED = 5;          // 已取消
    const STATUS_REFUNDING = 6;          // 退款中
    const STATUS_REFUNDED = 7;           // 已退款

    // 获取状态文本
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_PENDING_PAYMENT => '待支付',
            self::STATUS_PAID => '已支付',
            self::STATUS_SHIPPED => '已发货',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_REFUNDING => '退款中',
            self::STATUS_REFUNDED => '已退款'
        ];
        return $statusMap[$data['status']] ?? '未知状态';
    }
}