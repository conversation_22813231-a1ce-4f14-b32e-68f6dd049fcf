<?php

namespace app\model\order\search;

use think\Model;
use think\model\concern\SoftDelete;

class Order extends Model
{
    public function searchStatusAttr($query, $value)
    {
        if ($value > 0) {
            $query->where('status', $value);
        }
    }

    public function searchKeywordAttr($query, $value)
    {
        if ($value) {
            $query->whereLike('order_no|goods_name|receiver_name|receiver_phone', "%{$value}%");
        }
    }
}