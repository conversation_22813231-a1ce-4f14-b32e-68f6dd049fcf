<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * SKU规格值关联模型
 */
class GoodsSkuSpecValue extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'ad_goods_sku_spec_value';
    
    // 设置字段信息
    protected $schema = [
        'id'             => 'int',
        'sku_id'         => 'int',
        'spec_id'        => 'int',
        'spec_value_id'  => 'int'
    ];
    
    /**
     * 关联SKU模型
     */
    public function sku()
    {
        return $this->belongsTo(GoodsSku::class, 'sku_id', 'id');
    }
    
    /**
     * 关联规格模型
     */
    public function spec()
    {
        return $this->belongsTo(GoodsSpec::class, 'spec_id', 'id');
    }
    
    /**
     * 关联规格值模型
     */
    public function specValue()
    {
        return $this->belongsTo(GoodsSpecValue::class, 'spec_value_id', 'id');
    }
    
    /**
     * 批量保存SKU规格值关联
     * @param int $skuId SKU ID
     * @param array $specValues 规格值数据，格式：[['spec_id' => 1, 'spec_value_id' => 1], ...]
     * @return bool
     */
    public static function saveSkuSpecValues($skuId, $specValues)
    {
        if (empty($skuId) || empty($specValues)) {
            return false;
        }
        
        // 删除原有关联
        self::where('sku_id', $skuId)->delete();
        
        // 保存新的关联
        $data = [];
        foreach ($specValues as $value) {
            if (!empty($value['spec_id']) && !empty($value['spec_value_id'])) {
                $data[] = [
                    'sku_id' => $skuId,
                    'spec_id' => $value['spec_id'],
                    'spec_value_id' => $value['spec_value_id']
                ];
            }
        }
        
        if (!empty($data)) {
            return self::insertAll($data);
        }
        
        return true;
    }
}