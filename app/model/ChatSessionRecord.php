<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 用户会话记录模型类
 */
class ChatSessionRecord extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'ad_chat_session_records';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'createtime';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'updatetime';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $insert = [
        'session_id',
        'user_id',
        'service_id',
        'platform',
        'device_info',
        'ip_address',
        'geo_location',
    ];

    /**
     * 创建新的会话记录
     * @param array $data
     * @return static|null
     */
    public static function createRecord(array $data)
    {
        $record = new static();
        $record->save($data);
        return $record->exists() ? $record : null;
    }
} 