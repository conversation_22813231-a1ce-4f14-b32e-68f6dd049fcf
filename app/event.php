<?php
// 事件定义文件
use Swoole\Constant;

return [
    'bind'      => [
        // 移除重复的swoole.workerStart绑定，避免与listen中的配置冲突
    ],

    'listen'    => [
        'AppInit'  => [],
        'HttpRun'  => [],
        'HttpEnd'  => [],
        'LogLevel' => [],
        'LogWrite' => [],
        'swoole.workerStart' => [app\vchat\listener\SwooleStartListener::class],
        'beforeWorkerStop' => [app\vchat\listener\SwooleStopListener::class],
        'swoole.workerStop' => [app\vchat\listener\SwooleStopListener::class],
        'swoole.websocket.Open' => [app\vchat\listener\SwooleOpenListener::class]
    ],

    'subscribe' => [
    ]
];
