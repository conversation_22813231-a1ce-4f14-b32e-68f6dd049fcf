<?php
// 全局中间件定义文件
return [
    // 全局请求缓存
    // \think\middleware\CheckRequestCache::class,
    // 多语言加载
    // \think\middleware\LoadLangPack::class,
    // Session初始化
    // \think\middleware\SessionInit::class,
    // IP拦截中间件
    \app\middleware\CheckBannedIpMiddleware::class,
    // 插件初始化中间件
    \app\middleware\PluginInitMiddleware::class,
    \app\middleware\CrossOrigin::class,
    \app\middleware\VChatInitMiddleware::class,
];
