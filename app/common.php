<?php

use think\facade\Db;
use think\Response;
use app\common\plugin\PluginService;

// 应用公共文件
if (!function_exists('plugin_instance')) {
    /**
     * 获取插件实例
     * @param string $name 插件名称
     * @return mixed|null 插件实例
     */
    function plugin_instance($name)
    {
        return PluginService::getInstance($name);
    }
}

if (!function_exists('getUserRole')) {
    /**
     * Create a new Dot object with the given items
     *
     * @param mixed $items
     * @return \Adbar\Dot
     */
    function getUserRole($userId)
    {
        $roles = Db::table("sys_user_role")->where("user_id", $userId)->select()->toArray();
        return array_column($roles, 'role_id');
    }
}

if (!function_exists('errorResponse')) {
    function errorResponse($message)
    {
        $data['code'] = 500;
        $data['message'] = $message;
        return Response::create($data, 'json', 500)->send()->code(500)->end();
    }
}

if (!function_exists('successResponse')) {
    function successResponse($message, $responseData)
    {
        $data['code'] = 200;
        $data['message'] = $message;
        $data['data'] = $responseData;
        return Response::create($data, 'json')->send()->status(200)->end();
    }
}

if (!function_exists('generatePassword')) {
    function generatePassword($password, $salt = '')
    {
        return password_hash($password . $salt, PASSWORD_BCRYPT);
//        return md5(md5($password) . $salt);
    }
}

if (!function_exists('passwordVerify')) {
    function passwordVerify($password, $hashedPassword)
    {
        return password_verify($password, $hashedPassword);
    }
}

