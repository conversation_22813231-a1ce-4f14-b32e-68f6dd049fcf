<?php

namespace app\command;

use app\common\Auth;
use app\common\TokenStorage;
use app\common\TokenSecurity;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Config;

/**
 * Token管理命令
 * 提供Token的维护和管理功能
 */
class TokenManagement extends Command
{
    protected function configure()
    {
        $this->setName('token:manage')
            ->setDescription('Token管理工具')
            ->addArgument('action', null, '操作类型：cleanup|stats|revoke|test')
            ->addOption('user-id', 'u', null, '用户ID')
            ->addOption('token', 't', null, 'Token值')
            ->addOption('days', 'd', null, '天数（用于清理）')
            ->addOption('force', 'f', null, '强制执行');
    }

    protected function execute(Input $input, Output $output)
    {
        $action = $input->getArgument('action');
        
        switch ($action) {
            case 'cleanup':
                $this->cleanup($input, $output);
                break;
            case 'stats':
                $this->showStats($input, $output);
                break;
            case 'revoke':
                $this->revokeToken($input, $output);
                break;
            case 'test':
                $this->testSecurity($input, $output);
                break;
            case 'blacklist':
                $this->manageBlacklist($input, $output);
                break;
            default:
                $this->showHelp($output);
                break;
        }
    }
    
    /**
     * 清理过期Token
     */
    protected function cleanup(Input $input, Output $output)
    {
        $days = $input->getOption('days') ?: 7;
        $force = $input->getOption('force');
        
        $output->writeln("<info>开始清理 {$days} 天前的过期Token...</info>");
        
        try {
            $driver = Config::get('token.driver', 'mysql');
            
            if ($driver === 'mysql') {
                $this->cleanupMysqlTokens($days, $force, $output);
            } else {
                $output->writeln("<comment>Redis存储会自动清理过期Token</comment>");
            }
            
            // 清理安全相关缓存
            TokenSecurity::cleanup();
            
            $output->writeln("<info>清理完成</info>");
        } catch (\Exception $e) {
            $output->writeln("<error>清理失败: {$e->getMessage()}</error>");
        }
    }
    
    /**
     * 清理MySQL中的过期Token
     */
    protected function cleanupMysqlTokens($days, $force, Output $output)
    {
        $expireTime = time() - ($days * 24 * 3600);
        
        // 清理访问令牌
        $accessTable = Config::get('token.mysql.table', 'sys_admin_token');
        $accessCount = Db::table($accessTable)
            ->where('expiretime', '<', $expireTime)
            ->count();
            
        if ($accessCount > 0) {
            if ($force || $this->confirm($output, "发现 {$accessCount} 个过期的访问令牌，是否删除？")) {
                $deleted = Db::table($accessTable)
                    ->where('expiretime', '<', $expireTime)
                    ->delete();
                $output->writeln("<info>删除了 {$deleted} 个过期的访问令牌</info>");
            }
        } else {
            $output->writeln("<comment>没有发现过期的访问令牌</comment>");
        }
        
        // 清理刷新令牌
        $refreshTable = Config::get('token.mysql.refresh_table', 'sys_admin_refresh_token');
        $refreshCount = Db::table($refreshTable)
            ->where('expiretime', '<', $expireTime)
            ->count();
            
        if ($refreshCount > 0) {
            if ($force || $this->confirm($output, "发现 {$refreshCount} 个过期的刷新令牌，是否删除？")) {
                $deleted = Db::table($refreshTable)
                    ->where('expiretime', '<', $expireTime)
                    ->delete();
                $output->writeln("<info>删除了 {$deleted} 个过期的刷新令牌</info>");
            }
        } else {
            $output->writeln("<comment>没有发现过期的刷新令牌</comment>");
        }
    }
    
    /**
     * 显示Token统计信息
     */
    protected function showStats(Input $input, Output $output)
    {
        $output->writeln("<info>Token统计信息</info>");
        $output->writeln("==================");
        
        try {
            $driver = Config::get('token.driver', 'mysql');
            $output->writeln("存储驱动: {$driver}");
            
            if ($driver === 'mysql') {
                $this->showMysqlStats($output);
            } else {
                $output->writeln("<comment>Redis统计信息需要通过Redis命令查看</comment>");
            }
            
            // 显示安全统计
            $this->showSecurityStats($output);
            
        } catch (\Exception $e) {
            $output->writeln("<error>获取统计信息失败: {$e->getMessage()}</error>");
        }
    }
    
    /**
     * 显示MySQL Token统计
     */
    protected function showMysqlStats(Output $output)
    {
        $accessTable = Config::get('token.mysql.table', 'sys_admin_token');
        $refreshTable = Config::get('token.mysql.refresh_table', 'sys_admin_refresh_token');
        
        // 访问令牌统计
        $totalAccess = Db::table($accessTable)->count();
        $validAccess = Db::table($accessTable)
            ->where('expiretime', '>', time())
            ->count();
        $expiredAccess = $totalAccess - $validAccess;
        
        $output->writeln("访问令牌:");
        $output->writeln("  总数: {$totalAccess}");
        $output->writeln("  有效: {$validAccess}");
        $output->writeln("  过期: {$expiredAccess}");
        
        // 刷新令牌统计
        if (Db::query("SHOW TABLES LIKE '{$refreshTable}'")) {
            $totalRefresh = Db::table($refreshTable)->count();
            $validRefresh = Db::table($refreshTable)
                ->where('expiretime', '>', time())
                ->count();
            $expiredRefresh = $totalRefresh - $validRefresh;
            
            $output->writeln("刷新令牌:");
            $output->writeln("  总数: {$totalRefresh}");
            $output->writeln("  有效: {$validRefresh}");
            $output->writeln("  过期: {$expiredRefresh}");
        }
    }
    
    /**
     * 显示安全统计
     */
    protected function showSecurityStats(Output $output)
    {
        $output->writeln("\n安全统计:");
        
        // 这里可以添加更多安全相关的统计信息
        $encryptionEnabled = Config::get('token.encryption.enabled', false);
        $blacklistEnabled = Config::get('token.security.blacklist.enabled', true);
        $rateLimitEnabled = Config::get('token.security.rate_limit.max_attempts', 0) > 0;
        
        $output->writeln("  数据加密: " . ($encryptionEnabled ? '启用' : '禁用'));
        $output->writeln("  黑名单: " . ($blacklistEnabled ? '启用' : '禁用'));
        $output->writeln("  速率限制: " . ($rateLimitEnabled ? '启用' : '禁用'));
    }
    
    /**
     * 撤销Token
     */
    protected function revokeToken(Input $input, Output $output)
    {
        $userId = $input->getOption('user-id');
        $token = $input->getOption('token');
        
        if (!$userId && !$token) {
            $output->writeln("<error>请指定用户ID或Token</error>");
            return;
        }
        
        try {
            if ($token) {
                // 撤销指定Token
                $result = Auth::removeToken($token, true);
                if ($result) {
                    $output->writeln("<info>Token已成功撤销</info>");
                } else {
                    $output->writeln("<error>Token撤销失败</error>");
                }
            } elseif ($userId) {
                // 撤销用户的所有Token
                $result = Auth::revokeAllUserTokens($userId);
                if ($result) {
                    $output->writeln("<info>用户 {$userId} 的所有Token已撤销</info>");
                } else {
                    $output->writeln("<error>撤销失败</error>");
                }
            }
        } catch (\Exception $e) {
            $output->writeln("<error>撤销失败: {$e->getMessage()}</error>");
        }
    }
    
    /**
     * 测试安全功能
     */
    protected function testSecurity(Input $input, Output $output)
    {
        $output->writeln("<info>测试Token安全功能...</info>");
        
        // 测试加密功能
        if (class_exists('\\app\\common\\token\\EncryptedTokenStorage')) {
            $encryptionAvailable = \app\common\token\EncryptedTokenStorage::isEncryptionAvailable();
            $output->writeln("加密功能: " . ($encryptionAvailable ? '可用' : '不可用'));
        }
        
        // 测试Token格式验证
        $validToken = '12345678-1234-1234-1234-123456789012';
        $invalidToken = 'invalid-token';
        
        $output->writeln("Token格式验证:");
        $output->writeln("  有效格式: " . (TokenSecurity::validateTokenFormat($validToken) ? '通过' : '失败'));
        $output->writeln("  无效格式: " . (!TokenSecurity::validateTokenFormat($invalidToken) ? '通过' : '失败'));
        
        $output->writeln("<info>安全功能测试完成</info>");
    }
    
    /**
     * 管理黑名单
     */
    protected function manageBlacklist(Input $input, Output $output)
    {
        $token = $input->getOption('token');
        
        if (!$token) {
            $output->writeln("<error>请指定Token</error>");
            return;
        }
        
        // 检查Token是否在黑名单中
        $isBlacklisted = Cache::has('token_blacklist:' . $token);
        $output->writeln("Token {$token} 黑名单状态: " . ($isBlacklisted ? '已加入' : '未加入'));
    }
    
    /**
     * 显示帮助信息
     */
    protected function showHelp(Output $output)
    {
        $output->writeln("<info>Token管理工具使用说明</info>");
        $output->writeln("========================");
        $output->writeln("php think token:manage cleanup [--days=7] [--force]  # 清理过期Token");
        $output->writeln("php think token:manage stats                         # 显示统计信息");
        $output->writeln("php think token:manage revoke --user-id=123          # 撤销用户Token");
        $output->writeln("php think token:manage revoke --token=xxx            # 撤销指定Token");
        $output->writeln("php think token:manage test                          # 测试安全功能");
        $output->writeln("php think token:manage blacklist --token=xxx         # 检查黑名单状态");
    }
    
    /**
     * 确认操作
     */
    protected function confirm(Output $output, $message)
    {
        $output->write($message . ' (y/N): ');
        $handle = fopen('php://stdin', 'r');
        $response = trim(fgets($handle));
        fclose($handle);
        
        return strtolower($response) === 'y' || strtolower($response) === 'yes';
    }
}