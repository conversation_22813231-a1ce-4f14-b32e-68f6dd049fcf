<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

class ImportRegion extends Command
{
    protected function configure()
    {
        $this->setName('import:region')
            ->setDescription('导入省市区数据')
            ->addArgument('file', Argument::OPTIONAL, '数据文件路径', app_path() . 'sql/region_data.sql')
            ->addOption('force', 'f', Option::VALUE_NONE, '强制导入（清空原有数据）');
    }

    protected function execute(Input $input, Output $output)
    {
        $file = $input->getArgument('file');
        $force = $input->getOption('force');

        if (!file_exists($file)) {
            $output->error('数据文件不存在：' . $file);
            return;
        }

        try {
            // 开启事务
            Db::startTrans();

            // 如果强制导入，则清空原有数据
            if ($force) {
                Db::execute('TRUNCATE TABLE ad_region');
                $output->info('已清空原有数据');
            }

            // 读取SQL文件内容
            $sql = file_get_contents($file);
            $statements = array_filter(array_map('trim', explode(';', $sql)));

            $count = 0;
            foreach ($statements as $statement) {
                if (empty($statement)) continue;
                
                // 执行SQL语句
                Db::execute($statement);
                $count++;
            }

            // 提交事务
            Db::commit();
            $output->info("成功导入 {$count} 条数据");

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $output->error('导入失败：' . $e->getMessage());
        }
    }
}