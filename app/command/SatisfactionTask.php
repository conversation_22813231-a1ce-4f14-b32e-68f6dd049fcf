<?php

declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Log;
use app\im\controller\customer\SatisfactionTaskController;
use think\App;

/**
 * 满意度任务命令
 */
class SatisfactionTask extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('satisfaction')
            ->addArgument('action', Argument::REQUIRED, '执行的操作: daily-report|monthly-report|alert-check|data-cleanup')
            ->addOption('date', 'd', Option::VALUE_OPTIONAL, '指定日期 (YYYY-MM-DD)')
            ->addOption('force', 'f', Option::VALUE_NONE, '强制执行')
            ->setDescription('满意度相关定时任务');
    }

    protected function execute(Input $input, Output $output)
    {
        $action = $input->getArgument('action');
        $date = $input->getOption('date');
        $force = $input->getOption('force');

        $output->writeln('开始执行满意度任务: ' . $action);
        Log::info('满意度任务开始执行', ['action' => $action, 'date' => $date, 'force' => $force]);

        try {
            // 创建控制器实例
            $app = App::getInstance();
            $controller = new SatisfactionTaskController($app);

            $result = null;
            $startTime = microtime(true);

            switch ($action) {
                case 'daily-report':
                    $result = $this->executeDailyReport($controller, $date, $force, $output);
                    break;
                    
                case 'monthly-report':
                    $result = $this->executeMonthlyReport($controller, $date, $force, $output);
                    break;
                    
                case 'alert-check':
                    $result = $this->executeAlertCheck($controller, $force, $output);
                    break;
                    
                case 'data-cleanup':
                    $result = $this->executeDataCleanup($controller, $force, $output);
                    break;
                    
                default:
                    $output->writeln('<error>不支持的操作: ' . $action . '</error>');
                    Log::error('不支持的满意度任务操作', ['action' => $action]);
                    return 1;
            }

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            if ($result && isset($result['code']) && $result['code'] === 1) {
                $output->writeln('<info>任务执行成功，耗时: ' . $duration . 'ms</info>');
                Log::info('满意度任务执行成功', [
                    'action' => $action,
                    'duration' => $duration,
                    'result' => $result['data'] ?? []
                ]);
                return 0;
            } else {
                $errorMsg = $result['msg'] ?? '未知错误';
                $output->writeln('<error>任务执行失败: ' . $errorMsg . '</error>');
                Log::error('满意度任务执行失败', [
                    'action' => $action,
                    'error' => $errorMsg,
                    'duration' => $duration
                ]);
                return 1;
            }

        } catch (\Exception $e) {
            $output->writeln('<error>任务执行异常: ' . $e->getMessage() . '</error>');
            Log::error('满意度任务执行异常', [
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * 执行日报生成
     * @param SatisfactionTaskController $controller
     * @param string|null $date
     * @param bool $force
     * @param Output $output
     * @return array
     */
    private function executeDailyReport($controller, $date, $force, $output)
    {
        $output->writeln('正在生成满意度日报...');
        
        if ($date) {
            $output->writeln('指定日期: ' . $date);
            // 这里可以扩展支持指定日期的报告生成
        }
        
        if ($force) {
            $output->writeln('强制执行模式');
        }
        
        $response = $controller->generateDailyReport();
        return $this->parseResponse($response);
    }

    /**
     * 执行月报生成
     * @param SatisfactionTaskController $controller
     * @param string|null $date
     * @param bool $force
     * @param Output $output
     * @return array
     */
    private function executeMonthlyReport($controller, $date, $force, $output)
    {
        $output->writeln('正在生成满意度月报...');
        
        if ($date) {
            $output->writeln('指定月份: ' . substr($date, 0, 7));
        }
        
        if ($force) {
            $output->writeln('强制执行模式');
        }
        
        $response = $controller->generateMonthlyReport();
        return $this->parseResponse($response);
    }

    /**
     * 执行预警检查
     * @param SatisfactionTaskController $controller
     * @param bool $force
     * @param Output $output
     * @return array
     */
    private function executeAlertCheck($controller, $force, $output)
    {
        $output->writeln('正在执行满意度预警检查...');
        
        if ($force) {
            $output->writeln('强制执行模式');
        }
        
        $response = $controller->checkSatisfactionAlert();
        $result = $this->parseResponse($response);
        
        // 显示预警信息
        if (isset($result['data']['alerts']) && !empty($result['data']['alerts'])) {
            $output->writeln('<comment>发现 ' . count($result['data']['alerts']) . ' 个预警:</comment>');
            foreach ($result['data']['alerts'] as $alert) {
                $output->writeln('  - ' . $alert['message'] . ' (当前值: ' . $alert['current_value'] . ', 阈值: ' . $alert['threshold'] . ')');
            }
        } else {
            $output->writeln('<info>未发现预警</info>');
        }
        
        return $result;
    }

    /**
     * 执行数据清理
     * @param SatisfactionTaskController $controller
     * @param bool $force
     * @param Output $output
     * @return array
     */
    private function executeDataCleanup($controller, $force, $output)
    {
        $output->writeln('正在执行满意度数据清理...');
        
        if ($force) {
            $output->writeln('强制执行模式');
        }
        
        try {
            // 清理过期的缓存数据
            $cacheKeys = [
                'satisfaction_daily_report_*',
                'satisfaction_monthly_report_*'
            ];
            
            $cleanedCount = 0;
            foreach ($cacheKeys as $pattern) {
                // 这里简化处理，实际应该使用缓存的清理方法
                $output->writeln('清理缓存模式: ' . $pattern);
                $cleanedCount++;
            }
            
            $output->writeln('<info>数据清理完成，清理了 ' . $cleanedCount . ' 个缓存模式</info>');
            
            return [
                'code' => 1,
                'msg' => '数据清理成功',
                'data' => [
                    'cleaned_patterns' => $cleanedCount
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => '数据清理失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 解析控制器响应
     * @param mixed $response
     * @return array
     */
    private function parseResponse($response)
    {
        if (is_object($response) && method_exists($response, 'getData')) {
            return $response->getData();
        }
        
        if (is_array($response)) {
            return $response;
        }
        
        return [
            'code' => 0,
            'msg' => '响应格式错误'
        ];
    }
}