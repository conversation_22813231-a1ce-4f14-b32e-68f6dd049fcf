<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use app\service\TaskService;
use app\model\Task as TaskModel;

/**
 * 定时任务命令行工具
 */
class Task extends Command
{
    /**
     * 配置指令
     */
    protected function configure()
    {
        $this->setName('task')
            ->addArgument('action', Argument::OPTIONAL, '执行的操作: run|clean', 'run')
            ->addOption('task', 't', Option::VALUE_OPTIONAL, '指定任务ID')
            ->addOption('days', 'd', Option::VALUE_OPTIONAL, '清理天数', 30)
            ->setDescription('定时任务管理工具');
    }

    /**
     * 执行命令
     * @param Input $input 输入对象
     * @param Output $output 输出对象
     * @return int|void
     */
    protected function execute(Input $input, Output $output)
    {
        try {
            $action = $input->getArgument('action');
            switch ($action) {
                case 'run':
                    $this->runTasks($input, $output);
                    break;
                case 'clean':
                    $this->cleanRecords($input, $output);
                    break;
                default:
                    $output->error('未知的操作：' . $action);
                    return;
            }
        } catch (\Exception $e) {
            $output->error('命令执行异常：' . $e->getMessage());
        }
    }

    /**
     * 执行定时任务
     * @param Input $input 输入对象
     * @param Output $output 输出对象
     */
    protected function runTasks(Input $input, Output $output)
    {
        $taskId = $input->getOption('task');
        $service = new TaskService();

        if ($taskId) {
            // 执行指定任务
            $task = TaskModel::find($taskId);
            if (!$task) {
                $output->error('任务不存在：' . $taskId);
                return;
            }

            if ($task->status == 0) {
                $output->error('任务已禁用：' . $task->name);
                return;
            }

            $output->writeln('开始执行任务：' . $task->name);
            $service->executeTask($task->toArray());
            $output->writeln('任务执行完成：' . $task->name);
        } else {
            // 执行所有启用的任务
            $output->writeln('开始执行所有启用的定时任务...');
            $service->runTasks();
            $output->writeln('所有任务执行完成');
        }
    }

    /**
     * 清理过期的执行记录
     * @param Input $input 输入对象
     * @param Output $output 输出对象
     */
    protected function cleanRecords(Input $input, Output $output)
    {
        $days = $input->getOption('days');
        if ($days <= 0) {
            $output->error('清理天数必须大于0');
            return;
        }

        $output->writeln('开始清理' . $days . '天前的执行记录...');
        $count = TaskModel::cleanExpiredRecords($days);
        $output->writeln('清理完成，共删除' . $count . '条记录');
    }
}