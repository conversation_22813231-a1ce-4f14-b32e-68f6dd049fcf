<?php

namespace app\command;

use tests\TokenSecurityTest;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * Token安全功能测试命令
 */
class TestTokenSecurity extends Command
{
    protected function configure()
    {
        $this->setName('test:token-security')
            ->setDescription('运行Token安全功能测试')
            ->addOption('cleanup', 'c', null, '测试后清理数据')
            ->addOption('verbose', 'v', null, '详细输出');
    }

    protected function execute(Input $input, Output $output)
    {
        $cleanup = $input->getOption('cleanup');
        $verbose = $input->getOption('verbose');
        
        $output->writeln("<info>开始Token安全功能测试...</info>");
        
        try {
            // 运行测试
            $result = TokenSecurityTest::runAllTests();
            
            if ($result) {
                $output->writeln("<info>\n🎉 所有测试通过！</info>");
            } else {
                $output->writeln("<error>\n❌ 部分测试失败，请检查上述输出</error>");
            }
            
            // 清理测试数据
            if ($cleanup) {
                $output->writeln("\n<comment>清理测试数据...</comment>");
                TokenSecurityTest::cleanup();
            }
            
        } catch (\Exception $e) {
            $output->writeln("<error>测试执行异常: {$e->getMessage()}</error>");
            if ($verbose) {
                $output->writeln("<error>异常堆栈: {$e->getTraceAsString()}</error>");
            }
        }
    }
}