<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class ConvertRegionToJson extends Command
{
    protected function configure()
    {
        $this->setName('convert:region-json')
            ->setDescription('Convert region SQL data to JSON format');
    }

    protected function execute(Input $input, Output $output)
    {
        $sqlFile = app()->getRootPath() . 'app/sql/region_data.sql';
        $jsonFile = app()->getRootPath() . 'app/sql/region_data.json';
        
        if (!file_exists($sqlFile)) {
            $output->error('SQL file not found: ' . $sqlFile);
            return;
        }
        
        // 读取SQL文件内容
        $sql = file_get_contents($sqlFile);
        
        // 解析SQL插入语句
        preg_match_all("/INSERT INTO `ad_region` \(.*?\) VALUES \((.*?)\);/", $sql, $matches);
        
        $regions = [];
        foreach ($matches[1] as $values) {
            // 解析字段值
            preg_match_all("/(?:'([^']*)'|([0-9]+)|NULL)/", $values, $fieldMatches);
            $fieldValues = [];
            foreach ($fieldMatches[0] as $value) {
                if ($value === 'NULL') {
                    $fieldValues[] = null;
                } else if (preg_match('/^[0-9]+$/', $value)) {
                    $fieldValues[] = intval($value);
                } else {
                    $fieldValues[] = trim($value, "'");
                }
            }
            
            // 构建地区数据
            $region = [
                'parent_id' => intval($fieldValues[0]),
                'name' => $fieldValues[1],
                'code' => $fieldValues[2],
                'level' => intval($fieldValues[3]),
                'sort' => intval($fieldValues[4]),
                'status' => intval($fieldValues[5]),
                'children' => []
            ];
            
            $regions[] = $region;
        }
        
        // 构建树状结构
        $tree = [];
        $regionMap = [];
        
        // 先建立索引
        foreach ($regions as &$region) {
            $regionMap[$region['code']] = &$region;
        }
        
        // 构建树状结构
        foreach ($regions as &$region) {
            if ($region['level'] === 1) {
                // 省级
                $tree[] = &$region;
            } else {
                // 查找父级
                $parentCode = substr($region['code'], 0, 2) . '0000';
                if ($region['level'] === 3) {
                    $parentCode = substr($region['code'], 0, 4) . '00';
                }
                if (isset($regionMap[$parentCode])) {
                    $regionMap[$parentCode]['children'][] = &$region;
                }
            }
        }
        
        // 保存为JSON文件
        file_put_contents($jsonFile, json_encode($tree, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        
        $output->info('Successfully converted region data to JSON format: ' . $jsonFile);
    }
    
    protected function findParentAndAddChild(&$tree, $region)
    {
        foreach ($tree as &$node) {
            if ($this->isParent($node, $region)) {
                $node['children'][] = $region;
                return true;
            }
            if (!empty($node['children'])) {
                if ($this->findParentAndAddChild($node['children'], $region)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    protected function isParent($parent, $child)
    {
        // 通过行政区划编码判断父子关系
        $parentCode = substr($parent['code'], 0, 2);
        $childCode = substr($child['code'], 0, 2);
        
        return $parentCode === $childCode && strlen($parent['code']) < strlen($child['code']);
    }
}