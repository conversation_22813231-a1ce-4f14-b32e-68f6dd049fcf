<?php

namespace app\common\token;

/**
 * Token存储接口
 */
interface TokenStorageInterface
{
    /**
     * 存储令牌
     * @param array $token 令牌
     * @param mixed $data 令牌关联的数据
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function store($token, $data, $expire);

    /**
     * 获取令牌关联的数据
     * @param string $token 令牌
     * @return mixed|null 成功返回数据，失败返回null
     */
    public function get($token, $refresh = false);

    /**
     * 刷新令牌过期时间
     * @param string $token 令牌
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function refresh($token, $expire);

    /**
     * 删除令牌
     * @param string $token 令牌
     * @return bool
     */
    public function remove($token, $refresh = false);
    
    /**
     * 刷新令牌并生成新的token
     * @param string $token 旧令牌
     * @param int $expire 过期时间（秒）
     * @return string|false 成功返回新令牌，失败返回false
     */
    public function refreshWithNewToken($token, $tokenData, $newToken, $expire, $expireRefresh);
}
