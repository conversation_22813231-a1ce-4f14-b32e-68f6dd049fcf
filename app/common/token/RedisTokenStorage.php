<?php

namespace app\common\token;

use think\facade\Cache;

/**
 * Redis令牌存储实现
 */
class RedisTokenStorage extends TokenStorageAbstract implements TokenStorageInterface
{

    /**
     * 存储令牌
     * @param array $token 令牌
     * @param mixed $data 令牌关联的数据
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function store($token, $data, $expire)
    {
        $key = $this->getKey($token['access_token']);
        
        // 序列化数据
        $serializedData = is_array($data) || is_object($data) ? json_encode($data) : $data;
        
        // 提取user_id用于映射
        $userId = $this->extractUserId($token);
        
        // 存储token数据
        $result = Cache::set($key, $serializedData, $expire);
        
        // 如果有user_id，创建user_id到token的映射
        if ($result && $userId > 0) {
            $userTokenKey = $this->getUserTokenKey($userId);
            // 存储用户token映射，过期时间与token相同
            Cache::set($userTokenKey, $token['access_token'], $expire);
        }
        
        return $result;
    }

    /**
     * 获取令牌关联的数据
     * @param string $token 令牌
     * @param bool $refresh 是否为刷新令牌
     * @return mixed|null 成功返回数据，失败返回null
     */
    public function get($token, $refresh = false)
    {
        $key = $this->getKey($token, $refresh);
        $data = Cache::get($key);
        
        if ($data === null) {
            return null;
        }
        
        // 尝试解析JSON数据
        if (is_string($data)) {
            $decoded = json_decode($data, true);
            return json_last_error() === JSON_ERROR_NONE ? $decoded : $data;
        }
        
        return $data;
    }

    /**
     * 刷新令牌过期时间
     * @param string $token 令牌
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function refresh($token, $expire)
    {
        $key = $this->getKey($token);
        $data = Cache::get($key);
        if ($data === null) {
            return false;
        }
        
        // 刷新token过期时间
        $result = Cache::set($key, $data, $expire);
        
        // 如果有user_id映射，也需要刷新
        if ($result) {
            $userId = $this->extractUserId($data);
            if ($userId > 0) {
                $userTokenKey = $this->getUserTokenKey($userId);
                Cache::set($userTokenKey, $token, $expire);
            }
        }
        
        return $result;
    }

    /**
     * 删除令牌
     * @param string $token 令牌
     * @param bool $refresh 是否为刷新令牌
     * @return bool
     */
    public function remove($token, $refresh = false)
    {
        $key = $this->getKey($token, $refresh);
        
        // 获取数据以便删除user_id映射
        $data = Cache::get($key);
        
        // 删除token
        $result = Cache::delete($key);
        
        // 删除user_id映射
        if ($result && $data !== null && !$refresh) {
            $userId = $this->extractUserId($data);
            if ($userId > 0) {
                $userTokenKey = $this->getUserTokenKey($userId);
                Cache::delete($userTokenKey);
            }
        }
        
        return $result;
    }

    /**
     * 获取缓存键名
     * @param string $token 令牌
     * @param bool $refresh 是否为刷新令牌
     * @return string
     */
    protected function getKey($token, $refresh = false)
    {
        $prefix = $refresh ? $this->prefix . 'refresh:' : $this->prefix;
        return $prefix . $token;
    }
    
    /**
     * 获取用户token映射键名
     * @param int $userId 用户ID
     * @return string
     */
    protected function getUserTokenKey($userId)
    {
        return $this->prefix . 'user:' . $userId;
    }
    
    /**
     * 提取用户ID
     * @param mixed $data 数据
     * @return int
     */
    protected function extractUserId($data)
    {
        $userId = 0;
        
        if (is_array($data) && isset($data['user_id'])) {
            $userId = (int)$data['user_id'];
        } elseif (is_string($data)) {
            $decoded = json_decode($data, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($decoded['user_id'])) {
                $userId = (int)$decoded['user_id'];
            }
        }
        
        return $userId;
    }
    
    /**
     * 通过用户ID获取access token
     * @param int $userId 用户ID
     * @return string|null
     */
    public function getTokenByUserId($userId)
    {
        $userTokenKey = $this->getUserTokenKey($userId);
        return Cache::get($userTokenKey);
    }
    
    /**
     * 撤销用户的所有token
     * @param int $userId 用户ID
     * @return bool
     */
    public function revokeUserTokens($userId)
    {
        $token = $this->getTokenByUserId($userId);
        if ($token) {
            return $this->remove($token);
        }
        return true;
    }
    
    /**
     * 刷新令牌并生成新的token
     * @param array $token 包含access_token的数组
     * @param string $tokenData 令牌数据
     * @param array $newToken 新令牌数组
     * @param int $expire access token过期时间（秒）
     * @param int $expireRefresh refresh token过期时间（秒）
     * @return array|false 成功返回新令牌数组，失败返回false
     */
    public function refreshWithNewToken($token, $tokenData, $newToken, $expire, $expireRefresh)
    {
        try {
            // 序列化token数据
            $serializedData = is_array($tokenData) || is_object($tokenData) ? json_encode($tokenData) : $tokenData;
            
            // 提取user_id
            $userId = $this->extractUserId($token);
            
            // 检查是否为Redis驱动，如果是则使用事务
            if ($this->isRedisDriver()) {
                return $this->refreshWithRedisTransaction($token, $newToken, $serializedData, $userId, $expire, $expireRefresh);
            } else {
                return $this->refreshWithFallback($token, $newToken, $serializedData, $userId, $expire, $expireRefresh);
            }
            
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return false;
        }
    }
    
    /**
     * 检查是否为Redis驱动
     * @return bool
     */
    protected function isRedisDriver()
    {
        try {
            // 尝试获取Redis实例
            $redis = Cache::redis();
            return $redis !== null;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 使用Redis事务刷新token
     * @param array $token
     * @param array $newToken
     * @param string $serializedData
     * @param int $userId
     * @param int $expire
     * @param int $expireRefresh
     * @return array|false
     */
    protected function refreshWithRedisTransaction($token, $newToken, $serializedData, $userId, $expire, $expireRefresh)
    {
        try {
            // 获取Redis实例
            $redis = Cache::redis();
            
            // 使用Redis事务确保原子性
            $redis->multi();
            
            // 存储新的access token
            $accessTokenKey = $this->getKey($token['access_token']);
            $redis->setex($accessTokenKey, $expire, $serializedData);
            
            // 存储新的refresh token
            $refreshTokenKey = $this->getKey($newToken['access_token'], true);
            $redis->setex($refreshTokenKey, $expireRefresh, $newToken['data'] ?? json_encode($newToken));
            
            // 更新用户token映射
            if ($userId > 0) {
                $userTokenKey = $this->getUserTokenKey($userId);
                $redis->setex($userTokenKey, $expire, $token['access_token']);
            }
            
            // 执行事务
            $results = $redis->exec();
            
            // 检查所有操作是否成功
            if ($results && !in_array(false, $results)) {
                return $newToken;
            }
            
            return false;
            
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return false;
        }
    }
    
    /**
     * 使用普通缓存操作刷新token（兼容file等其他驱动）
     * @param array $token
     * @param array $newToken
     * @param string $serializedData
     * @param int $userId
     * @param int $expire
     * @param int $expireRefresh
     * @return array|false
     */
    protected function refreshWithFallback($token, $newToken, $serializedData, $userId, $expire, $expireRefresh)
    {
        try {
            // 存储新的access token
            $accessTokenKey = $this->getKey($token['access_token']);
            $accessResult = Cache::set($accessTokenKey, $serializedData, $expire);
            
            if (!$accessResult) {
                return false;
            }
            
            // 存储新的refresh token
            $refreshTokenKey = $this->getKey($newToken['access_token'], true);
            $refreshResult = Cache::set($refreshTokenKey, $newToken['data'] ?? json_encode($newToken), $expireRefresh);
            
            if (!$refreshResult) {
                // 回滚：删除已创建的access token
                Cache::delete($accessTokenKey);
                return false;
            }
            
            // 更新用户token映射
            if ($userId > 0) {
                $userTokenKey = $this->getUserTokenKey($userId);
                $userResult = Cache::set($userTokenKey, $token['access_token'], $expire);
                
                if (!$userResult) {
                    // 回滚：删除已创建的tokens
                    Cache::delete($accessTokenKey);
                    Cache::delete($refreshTokenKey);
                    return false;
                }
            }
            
            return $newToken;
            
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            return false;
        }
    }
}
