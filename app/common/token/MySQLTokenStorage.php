<?php

namespace app\common\token;

use think\facade\Db;

/**
 * MySQL令牌存储实现
 */
class MySQLTokenStorage extends TokenStorageAbstract implements TokenStorageInterface
{

    /**
     * 存储令牌
     * @param array $token 令牌
     * @param mixed $data 令牌关联的数据
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function store($token, $data, $expire)
    {
        $expireTime = time() + $expire;
        $data = is_array($data) || is_object($data) ? json_encode($data) : $data;

        // 检查令牌是否已存在
        $exists = Db::table($this->table)->where('token', $token['access_token'])->find();

        if ($exists) {
            // 更新已存在的令牌
            return Db::table($this->table)->where('token', $token['access_token'])->update([
                    'data' => $data,
                    'expiretime' => $expireTime,
                    'updatetime' => time()
                ]) !== false;
        } else {
            // 提取user_id
            $userId = $this->extractUserId($token);
            // 创建新令牌
            return Db::table($this->table)->insert([
                    'token' => $token['access_token'],
                    'data' => $data,
                    'user_id' => $userId,
                    'expiretime' => $expireTime,
                    'createtime' => time(),
                    'updatetime' => time()
                ]) !== false;
        }
    }

    /**
     * 获取令牌关联的数据
     * @param string $token 令牌
     * @return mixed|null 成功返回数据，失败返回null
     */
    public function get($token, $refresh = false)
    {

        if ($refresh) {

            $table = $this->refreshTable;
        } else {
            $table = $this->table;
        }

        $result = Db::table($table)
            ->where('token', $token)
            ->where('expiretime', '>', time())
            ->find();

        if (!$result) {
            return null;
        }

        // 尝试解析JSON数据
        $data = $result['data'];
        $decoded = json_decode($data, true);
        return json_last_error() === JSON_ERROR_NONE ? $decoded : $data;
    }

    /**
     * 刷新令牌过期时间
     * @param string $token 令牌
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function refresh($token, $expire)
    {
        $expireTime = time() + $expire;

        return Db::table($this->table)
                ->where('token', $token)
                ->where('expiretime', '>', time())
                ->update([
                    'expiretime' => $expireTime,
                    'updatetime' => time()
                ]) !== false;
    }
    
    /**
     * 刷新令牌并生成新的token
     * @param array $token 旧令牌
     * @param string $refreshToken
     * @param int $expire 过期时间（秒）
     * @return string|false 成功返回新令牌，失败返回false
     */
    public function refreshWithNewToken($token, $tokenData, $newToken, $expire, $expireRefresh)
    {

        // 生成新令牌
        $expireTime = time() + $expire;
        $expireRefreshTime = time() + $expireRefresh;

        // 开始事务
        Db::startTrans();
        try {

            $generateToken = $token;

            // 提取user_id
            $userId = $this->extractUserId($generateToken);

            // 存储新令牌
            $accessTokenResult = Db::table($this->table)->insert([
                'token' => $generateToken['access_token'],
                'data' => $tokenData,
                'user_id' => $userId,
                'expiretime' => $expireTime,
                'createtime' => time(),
                'updatetime' => time()
            ]);

            if ($accessTokenResult === false) {
                Db::rollback();
                return false;
            }

            // 存储新令牌
            $refreshTokenResult = Db::table($this->refreshTable)->insert([
                'token' => $newToken['access_token'],
                'data' => $newToken['data'] ?? $newToken,
                'user_id' => $userId,
                'expiretime' => $expireRefreshTime,
                'createtime' => time()
            ]);

            if ($refreshTokenResult === false) {
                Db::rollback();
                return false;
            }

            
            // 提交事务
            Db::commit();
            return $newToken;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            // 回滚事务
            Db::rollback();
            return false;
        }
    }

    /**
     * 删除令牌
     * @param string $token 令牌
     * @return bool
     */
    public function remove($token, $refresh = false)
    {
        if ($refresh) {

            $table = $this->refreshTable;
        } else {
            $table = $this->table;
        }

        return Db::table($table)
                ->where('token', $token)
                ->delete() !== false;
    }

    /**
     * 提取用户ID
     * @param mixed $data 数据
     * @return int
     */
    protected function extractUserId($data)
    {
        $userId = 0;

        if (is_array($data) && isset($data['user_id'])) {
            $userId = (int)$data['user_id'];
        } elseif (is_string($data)) {
            $decoded = json_decode($data, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($decoded['user_id'])) {
                $userId = (int)$decoded['user_id'];
            }
        }

        return $userId;
    }
}
