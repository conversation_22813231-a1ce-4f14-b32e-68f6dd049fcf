<?php

namespace app\common\token;

use think\facade\Config;

/**
 * 加密Token存储实现
 * 对存储的Token数据进行加密处理
 */
class EncryptedTokenStorage extends TokenStorageAbstract implements TokenStorageInterface
{
    /**
     * 存储驱动实例
     */
    protected $driver;
    
    /**
     * 加密密钥
     */
    protected $encryptionKey;
    
    /**
     * 加密方法
     */
    protected $encryptionMethod = 'AES-256-CBC';
    
    public function __construct($driver)
    {
        $this->driver = $driver;
        $this->encryptionKey = $this->getEncryptionKey();
    }
    
    /**
     * 获取加密密钥
     * 
     * @return string
     */
    protected function getEncryptionKey()
    {
        $appKey = Config::get('app.app_key', 'default_key');
        return hash('sha256', $appKey . 'token_encryption');
    }
    
    /**
     * 加密数据
     * 
     * @param mixed $data 要加密的数据
     * @return string
     */
    protected function encrypt($data)
    {
        $data = is_array($data) || is_object($data) ? json_encode($data) : $data;

        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($this->encryptionMethod));
        $encrypted = openssl_encrypt($data, $this->encryptionMethod, $this->encryptionKey, 0, $iv);

        return base64_encode($iv . $encrypted);
    }
    
    /**
     * 解密数据
     * 
     * @param string $encryptedData 加密的数据
     * @return mixed
     */
    protected function decrypt($encryptedData)
    {
        $data = base64_decode($encryptedData);
        $ivLength = openssl_cipher_iv_length($this->encryptionMethod);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);

        $decrypted = openssl_decrypt($encrypted, $this->encryptionMethod, $this->encryptionKey, 0, $iv);

        // 尝试解析JSON
        $decoded = json_decode($decrypted, true);
        return json_last_error() === JSON_ERROR_NONE ? $decoded : $decrypted;
    }
    
    /**
     * 存储令牌
     * 
     * @param array $token 令牌
     * @param mixed $data 令牌关联的数据
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function store($token, $data, $expire)
    {
        try {
            // 加密数据
            $encryptedData = $this->encrypt($data);
            
            // 使用底层驱动存储
            return $this->driver->store($token, $encryptedData, $expire);
        } catch (\Exception $e) {
            $this->error = '数据加密失败: ' . $e->getMessage();
            return false;
        }
    }
    
    /**
     * 获取令牌关联的数据
     * 
     * @param string $token 令牌
     * @param bool $refresh 是否为刷新令牌
     * @return mixed|null 成功返回数据，失败返回null
     */
    public function get($token, $refresh = false)
    {
        try {
            // 从底层驱动获取数据
            $encryptedData = $this->driver->get($token, $refresh);
            
            if ($encryptedData === null) {
                return null;
            }

            // 解密数据
            return $this->decrypt($encryptedData);
        } catch (\Exception $e) {
            $this->error = '数据解密失败: ' . $e->getMessage();
            return null;
        }
    }
    
    /**
     * 刷新令牌过期时间
     * 
     * @param string $token 令牌
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public function refresh($token, $expire)
    {
        return $this->driver->refresh($token, $expire);
    }
    
    /**
     * 删除令牌
     * 
     * @param string $token 令牌
     * @param bool $refresh 是否为刷新令牌
     * @return bool
     */
    public function remove($token, $refresh = false)
    {
        return $this->driver->remove($token, $refresh);
    }
    
    /**
     * 设置表名
     * 
     * @param string $table 表名
     * @return $this
     */
    public function setTable($table)
    {
        if (method_exists($this->driver, 'setTable')) {
            $this->driver->setTable($table);
        }
        return $this;
    }
    
    /**
     * 设置刷新令牌表名
     * 
     * @param string $table 表名
     * @return $this
     */
    public function setRefreshTable($table)
    {
        if (method_exists($this->driver, 'setRefreshTable')) {
            $this->driver->setRefreshTable($table);
        }
        return $this;
    }
    
    /**
     * 获取底层驱动实例
     * 
     * @return mixed
     */
    public function getDriver()
    {
        return $this->driver;
    }
    
    /**
     * 验证加密功能是否可用
     * 
     * @return bool
     */
    public static function isEncryptionAvailable()
    {
        return function_exists('openssl_encrypt') && function_exists('openssl_decrypt');
    }
    
    /**
     * 测试加密解密功能
     * 
     * @return bool
     */
    public function testEncryption()
    {
        try {
            $testData = ['test' => 'data', 'number' => 123];
            $encrypted = $this->encrypt($testData);
            $decrypted = $this->decrypt($encrypted);
            
            return $testData === $decrypted;
        } catch (\Exception $e) {
            $this->error = '加密测试失败: ' . $e->getMessage();
            return false;
        }
    }

    public function refreshWithNewToken($token, $tokenData, $newToken, $expire, $expireRefresh)
    {
        try {
            // 加密数据
            $encryptedData = $this->encrypt($tokenData);

            // 加密数据
            $encryptedNewTokenData = $this->encrypt($newToken);
            $data['access_token'] = $newToken['access_token'];
            $data['data'] = $encryptedNewTokenData;

            // 使用底层驱动存储
            return $this->driver->refreshWithNewToken($token, $encryptedData, $data, $expire, $expireRefresh);
        } catch (\Exception $e) {
            $this->error = '数据加密失败: ' . $e->getMessage();
            return false;
        }
    }
}