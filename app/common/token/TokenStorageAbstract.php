<?php

namespace app\common\token;

class TokenStorageAbstract
{

    protected $error;

    /**
     * 数据表名
     * @var string
     */
    protected $table = 'sys_admin_token';

    protected $refreshTable = 'sys_admin_refresh_token';

    /**
     * 缓存前缀
     * @var string
     */
    protected $prefix = 'token:';

    /**
     * 配置数组
     * @var array
     */
    protected $config = [];

    /**
     * 存储驱动类型
     * @var string
     */
    protected $driver = 'mysql';

    /**
     * 构造函数，初始化配置
     */
    public function __construct()
    {
        $this->initConfig();
    }

    /**
     * 初始化配置
     */
    protected function initConfig()
    {
        // 获取token配置
        $this->config = config('token', []);
        
        // 设置驱动类型
        $this->driver = $this->config['driver'] ?? 'mysql';
        
        // 根据驱动类型初始化相关配置
        $this->initDriverConfig();
    }

    /**
     * 根据驱动类型初始化配置
     */
    protected function initDriverConfig()
    {
        if ($this->driver === 'redis') {
            $redisConfig = $this->config['redis'] ?? [];
            $this->prefix = $redisConfig['prefix'] ?? 'token:';
        } elseif ($this->driver === 'mysql') {
            $mysqlConfig = $this->config['mysql'] ?? [];
            $this->table = $mysqlConfig['table'] ?? 'sys_admin_token';
            $this->refreshTable = $mysqlConfig['refresh_table'] ?? 'sys_admin_refresh_token';
        }
    }

    /**
     * 获取配置项
     * @param string $key 配置键名，支持点号分隔的多级配置
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getConfig($key = null, $default = null)
    {
        if ($key === null) {
            return $this->config;
        }
        
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }

    /**
     * 获取驱动类型
     * @return string
     */
    public function getDriver()
    {
        return $this->driver;
    }

    /**
     * 获取当前驱动的配置
     * @return array
     */
    public function getDriverConfig()
    {
        return $this->config[$this->driver] ?? [];
    }

    /**
     * 获取过期时间
     * @return int
     */
    public function getExpire()
    {
        $driverConfig = $this->getDriverConfig();
        return $driverConfig['expire'] ?? 7200;
    }

    /**
     * 获取缓存前缀
     * @return string
     */
    public function getPrefix()
    {
        return $this->prefix;
    }

    /**
     * 获取数据表名
     * @return string
     */
    public function getTable()
    {
        return $this->table;
    }

    /**
     * 获取刷新令牌表名
     * @return string
     */
    public function getRefreshTable()
    {
        return $this->refreshTable;
    }

    public function setTable($table)
    {
        $this->table = $table;
        return $this;
    }

    public function setRefreshTable($table)
    {
        $this->refreshTable = $table;
        return $this;
    }

    /**
     * 设置缓存前缀
     * @param string $prefix
     * @return $this
     */
    public function setPrefix($prefix)
    {
        $this->prefix = $prefix;
        return $this;
    }

    public function getError()
    {
        return $this->error;
    }

}