<?php
namespace app\common\traits;

use think\facade\Request;

trait BuildParams
{

    /**
     * 需要过滤的查询参数
     * @var array
     */
    protected $filterParams = [];

    protected $buildParams = [];

    /**
     * 构建查询参数
     * 用于处理分页、排序和查询条件
     * @return array
     */
    protected function buildParames()
    {
        // 获取请求参数
        $request = Request::instance();
        $param = $request->param();

        // 页码和每页数量
        $page = isset($param['pageNum']) ? intval($param['pageNum']) : 1;
        $pageSize = isset($param['pageSize']) ? intval($param['pageSize']) : 10;

        // 排序字段和排序方式
        $sort = [];
        $order = $param['order'] ?? '';
        $field = $param['field'] ?? 'id';
        if (!empty($order) && !empty($field)) {
            $sort = [$field => $order];
        } else {
            $sort = ['id' => 'desc'];
        }
        
        // 构建查询条件
        $where = [];
        foreach ($param as $key => $value) {
            // 过滤分页和排序参数
            if (in_array($key, ['pageNum', 'pageSize', 'order', 'field'])) {
                continue;
            }
            // 过滤分页、排序和自定义过滤参数
            if (in_array($key, $this->filterParams)) {
                continue;
            }

            if (!in_array($key, $this->buildParams)) {
                continue;
            }

            // 过滤空值
            if ($value === '' || $value === null) {
                continue;
            }
            
            // 处理搜索条件
            if (strpos($key, 'search_') === 0) {
                $field = substr($key, 7); // 去掉search_前缀
                if (!empty($field)) {
                    $where[] = [$field, 'like', '%' . $value . '%'];
                }
            } else {
                // 精确匹配
                $where[] = [$key, '=', $value];
            }
        }

        return [$page, $pageSize, $sort, $where];
    }
}