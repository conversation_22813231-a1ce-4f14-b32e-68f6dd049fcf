<?php

namespace app\common;

/**
 * ID生成器示例类
 * 演示如何使用IdGenerator生成类似QQ号的唯一ID
 */
class IdGeneratorExample
{
    /**
     * 演示生成基本ID
     * 
     * @return array 示例结果
     */
    public static function demo()
    {
        $result = [];
        
        // 生成默认10位ID
        $result['default'] = IdGenerator::generate();
        
        // 生成8位ID
        $result['length_8'] = IdGenerator::generate(8);
        
        // 生成带前缀的ID
        $result['with_prefix'] = IdGenerator::generate(10, 'USER_');
        
        // 生成多个ID，演示唯一性和递增性
        $result['multiple'] = [];
        for ($i = 0; $i < 5; $i++) {
            $result['multiple'][] = IdGenerator::generate();
            // 稍微延迟，确保时间戳变化
            usleep(1000); // 1毫秒
        }
        
        // 生成雪花算法ID（适用于高并发场景）
        $result['snowflake'] = IdGenerator::generateSnowflakeId(1, 1);
        
        return $result;
    }
    
    /**
     * 演示生成QQ号风格的ID
     * 
     * @param int $count 生成数量
     * @return array 生成的QQ号风格ID列表
     */
    public static function generateQQStyleIds($count = 5)
    {
        $ids = [];
        for ($i = 0; $i < $count; $i++) {
            // QQ号通常是5-10位数字
            $length = mt_rand(5, 10);
            $ids[] = IdGenerator::generate($length);
            // 稍微延迟，确保时间戳变化
            usleep(1000); // 1毫秒
        }
        return $ids;
    }
    
    /**
     * 演示生成固定长度的数字ID
     * 
     * @param int $length ID长度
     * @param int $count 生成数量
     * @return array 生成的ID列表
     */
    public static function generateFixedLengthIds($length = 10, $count = 5)
    {
        $ids = [];
        for ($i = 0; $i < $count; $i++) {
            $ids[] = IdGenerator::generateNumericId($length);
            // 稍微延迟，确保时间戳变化
            usleep(1000); // 1毫秒
        }
        return $ids;
    }
    
    /**
     * 演示生成雪花算法ID
     * 
     * @param int $count 生成数量
     * @return array 生成的雪花算法ID列表
     */
    public static function generateSnowflakeIds($count = 5)
    {
        $ids = [];
        for ($i = 0; $i < $count; $i++) {
            // 随机工作机器ID和数据中心ID
            $workerId = mt_rand(0, 31);
            $dataCenterId = mt_rand(0, 31);
            $ids[] = [
                'id' => IdGenerator::generateSnowflakeId($workerId, $dataCenterId),
                'worker_id' => $workerId,
                'datacenter_id' => $dataCenterId
            ];
            // 稍微延迟，确保时间戳变化
            usleep(1000); // 1毫秒
        }
        return $ids;
    }
}