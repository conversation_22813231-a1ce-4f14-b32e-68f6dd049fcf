<?php

namespace app\common\waybill\kd100;

use app\common\waybill\AbstractWaybill;
use app\common\waybill\WaybillException;
use app\common\ApiCode;
use liugene\curl\Curl;

/**
 * 快递100电子面单实现
 */
class Kd100Waybill extends AbstractWaybill
{
    /**
     * API地址
     * @var string
     */
    protected $apiUrl = 'https://poll.kuaidi100.com';
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        if (empty($this->config['customer'])) {
            throw new WaybillException('快递100配置错误：customer不能为空', ApiCode::CONFIG_ERROR);
        }
        
        if (empty($this->config['key'])) {
            throw new WaybillException('快递100配置错误：key不能为空', ApiCode::CONFIG_ERROR);
        }
    }
    
    /**
     * 获取电子面单号
     * @param array $params 请求参数
     * @return array
     */
    public function getWaybillNo(array $params)
    {
        // 验证基础参数
        $this->validateBaseParams($params);
        
        // 构建请求参数
        $requestData = [
            'type' => $this->getExpressCode(),
            'partnerId' => $params['partner_id'] ?? '',
            'partnerKey' => $params['partner_key'] ?? '',
            'net' => $params['net'] ?? '',
            'kuaidicom' => $this->getExpressCode(),
            'recMan' => [
                'name' => $params['receiver_name'],
                'mobile' => $params['receiver_phone'],
                'province' => $params['receiver_province'],
                'city' => $params['receiver_city'],
                'address' => $params['receiver_address']
            ],
            'sendMan' => [
                'name' => $params['sender_name'],
                'mobile' => $params['sender_phone'],
                'province' => $params['sender_province'],
                'city' => $params['sender_city'],
                'address' => $params['sender_address']
            ],
            'cargo' => $params['goods_name'] ?? '商品',
            'weight' => $params['weight'] ?? 1,
            'remark' => $params['remark'] ?? '',
            'count' => $params['package_quantity'] ?? 1,
            'tempid' => $params['template_id'] ?? '',
            'siid' => $params['siid'] ?? '',
        ];
        
        $result = $this->request('/electronic/order', $requestData);
        return $this->handleApiResult($result);
    }
    
    /**
     * 取消电子面单
     * @param string $waybillNo 电子面单号
     * @return array
     */
    public function cancel(string $waybillNo)
    {
        $requestData = [
            'type' => $this->getExpressCode(),
            'partnerId' => $this->config['partner_id'],
            'partnerKey' => $this->config['partner_key'],
            'kuaidicom' => $this->getExpressCode(),
            'logisticId' => $waybillNo
        ];
        
        $result = $this->request('/electronic/cancel', $requestData);
        return $this->handleApiResult($result);
    }
    
    /**
     * 获取物流轨迹
     * @param string $waybillNo 电子面单号
     * @return array
     */
    public function getTraces(string $waybillNo)
    {
        $requestData = [
            'com' => $this->getExpressCode(),
            'num' => $waybillNo,
            'phone' => '',
        ];
        
        $result = $this->request('/poll/query', $requestData);
        return $this->handleApiResult($result);
    }
    
    /**
     * 获取支持的快递公司列表
     * @return array
     */
    public function getSupportedExpresses()
    {
        $result = $this->request('/express/company', []);
        return $this->handleApiResult($result);
    }
    
    /**
     * 发送API请求
     * @param string $endpoint API接口地址
     * @param array $data 请求数据
     * @return array
     */
    protected function request($endpoint, array $data)
    {
        $params = [
            'method' => 'post',
            'schema' => 'json',
            'param' => json_encode($data)
        ];
        
        $params['sign'] = $this->generateSign($params['param']);
        $params['customer'] = $this->config['customer'];
        
        try {
            $curl = new Curl(2); // 失败时重试2次
            $curl->setOption([
                CURLOPT_TIMEOUT_MS => $this->config['timeout'] * 1000,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);
            
            $response = $curl->post($this->apiUrl . $endpoint, http_build_query($params));
            return json_decode($response, true) ?: [];
        } catch (\Exception $e) {
            throw new WaybillException(
                sprintf('请求快递100 API失败：%s', $e->getMessage()),
                ApiCode::THIRD_PARTY_ERROR
            );
        }
    }
    
    /**
     * 生成签名
     * @param string $data 请求数据
     * @return string
     */
    protected function generateSign($data)
    {
        return strtoupper(md5($data . $this->config['key']));
    }
}