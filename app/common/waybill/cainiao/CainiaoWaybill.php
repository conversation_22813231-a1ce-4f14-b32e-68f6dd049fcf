<?php

namespace app\common\waybill\cainiao;

use app\common\waybill\AbstractWaybill;
use app\common\waybill\WaybillException;
use app\common\ApiCode;
use liugene\curl\Curl;

/**
 * 菜鸟电子面单实现
 */
class CainiaoWaybill extends AbstractWaybill
{
    /**
     * API地址
     * @var string
     */
    protected $apiUrl = 'https://link.cainiao.com/gateway/link.do';
    
    /**
     * 沙箱环境API地址
     * @var string
     */
    protected $sandboxApiUrl = 'https://link.cainiao.com/gateway/link.do';
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        if (empty($this->config['app_key'])) {
            throw new WaybillException('菜鸟配置错误：app_key不能为空', ApiCode::CONFIG_ERROR);
        }
        
        if (empty($this->config['secret_key'])) {
            throw new WaybillException('菜鸟配置错误：secret_key不能为空', ApiCode::CONFIG_ERROR);
        }
    }
    
    /**
     * 获取默认配置
     * @return array
     */
    protected function getDefaultConfig()
    {
        return array_merge(parent::getDefaultConfig(), [
            'format' => 'json',
            'charset' => 'utf-8',
            'sign_method' => 'hmac',
            'api_version' => '1.0',
        ]);
    }
    
    /**
     * 获取电子面单号
     * @param array $params 请求参数
     * @return array
     */
    public function getWaybillNo(array $params)
    {
        // 验证基础参数
        $this->validateBaseParams($params);
        
        // 构建请求参数
        $requestData = [
            'logistics_interface' => [
                'code' => $this->getExpressCode(),
                'orderType' => $params['order_type'] ?? 'standard',
                'sender' => [
                    'name' => $params['sender_name'],
                    'mobile' => $params['sender_phone'],
                    'province' => $params['sender_province'],
                    'city' => $params['sender_city'],
                    'detailAddress' => $params['sender_address'],
                ],
                'receiver' => [
                    'name' => $params['receiver_name'],
                    'mobile' => $params['receiver_phone'],
                    'province' => $params['receiver_province'],
                    'city' => $params['receiver_city'],
                    'detailAddress' => $params['receiver_address'],
                ],
                'items' => [
                    [
                        'name' => $params['goods_name'] ?? '商品',
                        'count' => $params['goods_quantity'] ?? 1,
                    ]
                ],
                'weight' => $params['weight'] ?? 1,
                'volume' => $params['volume'] ?? 0,
                'remark' => $params['remark'] ?? '',
            ]
        ];
        
        $result = $this->request('waybill.get', $requestData);
        return $this->handleApiResult($result);
    }
    
    /**
     * 取消电子面单
     * @param string $waybillNo 电子面单号
     * @return array
     */
    public function cancel(string $waybillNo)
    {
        $requestData = [
            'logistics_interface' => [
                'code' => $this->getExpressCode(),
                'waybillCode' => $waybillNo,
            ]
        ];
        
        $result = $this->request('waybill.cancel', $requestData);
        return $this->handleApiResult($result);
    }
    
    /**
     * 获取物流轨迹
     * @param string $waybillNo 电子面单号
     * @return array
     */
    public function getTraces(string $waybillNo)
    {
        $requestData = [
            'logistics_interface' => [
                'code' => $this->getExpressCode(),
                'waybillCode' => $waybillNo,
            ]
        ];
        
        $result = $this->request('logistics.trace.search', $requestData);
        return $this->handleApiResult($result);
    }
    
    /**
     * 获取支持的快递公司列表
     * @return array
     */
    public function getSupportedExpresses()
    {
        $result = $this->request('logistics.companies.get', []);
        return $this->handleApiResult($result);
    }
    
    /**
     * 发送API请求
     * @param string $method API方法名
     * @param array $data 请求数据
     * @return array
     */
    protected function request($method, array $data)
    {
        $params = [
            'msg_type' => $method,
            'logistics_interface' => json_encode($data),
            'app_key' => $this->config['app_key'],
            'timestamp' => date('Y-m-d H:i:s'),
            'format' => $this->config['format'],
            'v' => $this->config['api_version'],
            'sign_method' => $this->config['sign_method'],
        ];
        
        $params['sign'] = $this->generateSign($params);
        
        $url = $this->config['sandbox'] ? $this->sandboxApiUrl : $this->apiUrl;
        
        try {
            $curl = new Curl(2); // 失败时重试2次
            $curl->setOption([
                CURLOPT_TIMEOUT_MS => $this->config['timeout'] * 1000,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);
            
            $response = $curl->post($url, http_build_query($params));
            return json_decode($response, true) ?: [];
        } catch (\Exception $e) {
            throw new WaybillException(
                sprintf('请求菜鸟API失败：%s', $e->getMessage()),
                ApiCode::THIRD_PARTY_ERROR
            );
        }
    }
    
    /**
     * 生成签名
     * @param array $params 请求参数
     * @return string
     */
    protected function generateSign($params)
    {
        ksort($params);
        
        $stringToBeSigned = '';
        foreach ($params as $k => $v) {
            if ($k != 'sign' && $k != 'sign_method' && $v !== '' && $v !== null) {
                $stringToBeSigned .= $k . $v;
            }
        }
        
        return strtoupper(hash_hmac('md5', $stringToBeSigned, $this->config['secret_key']));
    }
}