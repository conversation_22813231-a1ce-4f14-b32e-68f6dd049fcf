<?php
declare(strict_types=1);

namespace app\common\waybill\print;

use app\common\ApiCode;

/**
 * 打印异常类
 */
class PrintException extends \Exception
{
    /**
     * 异常数据
     * @var mixed
     */
    protected $data;
    
    /**
     * 构造函数
     * @param string $message 错误信息
     * @param int $code 错误码
     * @param mixed $data 异常数据
     */
    public function __construct($message = '', $code = ApiCode::PRINT_ERROR, $data = null)
    {
        parent::__construct($message, $code);
        $this->data = $data;
    }
    
    /**
     * 获取异常数据
     * @return mixed
     */
    public function getData()
    {
        return $this->data;
    }
}