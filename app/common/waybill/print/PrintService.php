<?php
declare(strict_types=1);

namespace app\common\waybill\print;

use app\model\WaybillPrintConfig;
use app\model\WaybillTemplate;
use app\model\WaybillSender;

/**
 * 电子面单打印服务类
 */
class PrintService
{
    /**
     * 打印机实例
     * @var PrinterInterface
     */
    protected $printer;
    
    /**
     * 打印配置
     * @var array
     */
    protected $config;
    
    /**
     * 构造函数
     * @param array $config 打印配置
     */
    public function __construct(array $config = [])
    {
        // 如果未传入配置，则获取系统配置
        if (empty($config)) {
            $config = WaybillPrintConfig::getCurrentConfig();
            if (!$config) {
                throw new PrintException('打印配置不存在');
            }
            $config = $config->toArray();
        }
        
        $this->config = $config;
        $this->initPrinter();
    }
    
    /**
     * 初始化打印机
     */
    protected function initPrinter()
    {
        // 根据配置创建对应的打印机实例
        $printerClass = $this->getPrinterClass();
        $this->printer = new $printerClass($this->config);
    }
    
    /**
     * 获取打印机类名
     * @return string
     */
    protected function getPrinterClass(): string
    {
        // TODO: 根据配置返回对应的打印机类名
        return LocalPrinter::class;
    }
    
    /**
     * 打印电子面单
     * @param array $data 打印数据
     * @return bool
     */
    public function print(array $data): bool
    {
        try {
            // 准备打印数据
            $printData = $this->preparePrintData($data);
            
            // 设置打印参数
            $this->printer->setPrintSize($this->config['print_size'], $this->config['custom_width'] ?? 0, $this->config['custom_height'] ?? 0)
                         ->setPrintCopies($this->config['print_copies'])
                         ->setPreview($this->config['print_preview']);
            
            // 执行打印
            return $this->printer->print($printData);
        } catch (\Exception $e) {
            throw new PrintException('打印失败：' . $e->getMessage());
        }
    }
    
    /**
     * 准备打印数据
     * @param array $data 原始数据
     * @return array
     */
    protected function preparePrintData(array $data): array
    {
        $printData = [];
        $contentItems = explode(',', $this->config['print_content']);
        
        // 处理打印内容
        foreach ($contentItems as $item) {
            switch ($item) {
                case 'sender_info':
                    $printData['sender'] = $this->getSenderInfo($data['sender_id'] ?? 0);
                    break;
                case 'receiver_info':
                    $printData['receiver'] = $data['receiver'] ?? [];
                    break;
                case 'goods_info':
                    $printData['goods'] = $data['goods'] ?? [];
                    break;
                case 'order_info':
                    $printData['order'] = $data['order'] ?? [];
                    break;
                case 'barcode':
                    $printData['barcode'] = $data['waybill_no'] ?? '';
                    break;
                case 'qrcode':
                    $printData['qrcode'] = $this->generateQrCode($data);
                    break;
            }
        }
        
        return $printData;
    }
    
    /**
     * 获取发件人信息
     * @param int $senderId 发件人ID
     * @return array
     */
    protected function getSenderInfo(int $senderId): array
    {
        if ($senderId > 0) {
            $sender = WaybillSender::find($senderId);
            if ($sender) {
                return $sender->toArray();
            }
        }
        
        // 如果未指定发件人ID或发件人不存在，则获取默认发件人
        $defaultSender = WaybillSender::where('is_default', 1)->find();
        return $defaultSender ? $defaultSender->toArray() : [];
    }
    
    /**
     * 生成二维码数据
     * @param array $data 原始数据
     * @return string
     */
    protected function generateQrCode(array $data): string
    {
        // 组装二维码数据
        $qrData = [
            'waybill_no' => $data['waybill_no'] ?? '',
            'express_code' => $data['express_code'] ?? '',
            'sender_name' => $data['sender']['name'] ?? '',
            'receiver_name' => $data['receiver']['name'] ?? '',
        ];
        
        return json_encode($qrData, JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * 测试打印机连接
     * @return bool
     */
    public function testConnection(): bool
    {
        try {
            return $this->printer->test();
        } catch (\Exception $e) {
            throw new PrintException('打印机连接测试失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取打印预览数据
     * @param array $data 打印数据
     * @return array
     */
    public function getPreviewData(array $data): array
    {
        return $this->preparePrintData($data);
    }
}