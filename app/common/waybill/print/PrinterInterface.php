<?php
declare(strict_types=1);

namespace app\common\waybill\print;

/**
 * 打印机接口
 */
interface PrinterInterface
{
    /**
     * 设置打印尺寸
     * @param int $size 打印尺寸类型
     * @param int $width 自定义宽度(mm)
     * @param int $height 自定义高度(mm)
     * @return $this
     */
    public function setPrintSize(int $size, int $width = 0, int $height = 0);
    
    /**
     * 设置打印份数
     * @param int $copies 打印份数
     * @return $this
     */
    public function setPrintCopies(int $copies);
    
    /**
     * 设置是否预览
     * @param bool $preview 是否预览
     * @return $this
     */
    public function setPreview(bool $preview);
    
    /**
     * 执行打印
     * @param array $data 打印数据
     * @return bool
     */
    public function print(array $data): bool;
    
    /**
     * 测试打印机连接
     * @return bool
     */
    public function test(): bool;
    
    /**
     * 获取打印机状态
     * @return array
     */
    public function getStatus(): array;
    
    /**
     * 获取错误信息
     * @return string
     */
    public function getError(): string;
}