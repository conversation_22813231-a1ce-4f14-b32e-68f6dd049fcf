<?php
declare(strict_types=1);

namespace app\common\waybill\print;

use app\model\WaybillPrintConfig;

/**
 * 打印管理类
 */
class PrintManager
{
    /**
     * 打印服务实例
     * @var array
     */
    protected static $instances = [];
    
    /**
     * 获取打印服务实例
     * @param array $config 打印配置
     * @return PrintService
     */
    public static function getInstance(array $config = []): PrintService
    {
        // 生成配置的唯一标识
        $key = empty($config) ? 'default' : md5(serialize($config));
        
        // 如果实例不存在，则创建新实例
        if (!isset(self::$instances[$key])) {
            self::$instances[$key] = new PrintService($config);
        }
        
        return self::$instances[$key];
    }
    
    /**
     * 打印电子面单
     * @param array $data 打印数据
     * @param array $config 打印配置
     * @return bool
     */
    public static function print(array $data, array $config = []): bool
    {
        return self::getInstance($config)->print($data);
    }
    
    /**
     * 测试打印机连接
     * @param array $config 打印配置
     * @return bool
     */
    public static function testConnection(array $config = []): bool
    {
        return self::getInstance($config)->testConnection();
    }
    
    /**
     * 获取打印预览数据
     * @param array $data 打印数据
     * @param array $config 打印配置
     * @return array
     */
    public static function getPreviewData(array $data, array $config = []): array
    {
        return self::getInstance($config)->getPreviewData($data);
    }
    
    /**
     * 清理打印服务实例
     * @param string|null $key 实例键名，为null时清理所有实例
     * @return void
     */
    public static function clear(?string $key = null): void
    {
        if ($key === null) {
            self::$instances = [];
        } else {
            unset(self::$instances[$key]);
        }
    }
}