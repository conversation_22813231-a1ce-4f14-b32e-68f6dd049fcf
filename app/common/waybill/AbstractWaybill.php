<?php

namespace app\common\waybill;

use app\common\ApiCode;
use think\facade\Config;

/**
 * 电子面单抽象类
 * 实现电子面单接口的基本方法
 */
abstract class AbstractWaybill implements WaybillInterface
{
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];
    
    /**
     * 快递公司编码
     * @var string
     */
    protected $expressCode = '';
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->initialize();
    }
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        // 子类实现具体的初始化逻辑
    }
    
    /**
     * 获取默认配置
     * @return array
     */
    protected function getDefaultConfig()
    {
        return [
            'debug' => false,
            'sandbox' => false,
            'timeout' => 30,
            'express_code' => '',
        ];
    }
    
    /**
     * 获取配置信息
     * @return array
     */
    public function getConfig()
    {
        return $this->config;
    }
    
    /**
     * 设置快递公司编码
     * @param string $code
     * @return $this
     */
    public function setExpressCode(string $code)
    {
        $this->expressCode = $code;
        return $this;
    }
    
    /**
     * 获取快递公司编码
     * @return string
     */
    public function getExpressCode()
    {
        return $this->expressCode;
    }
    
    /**
     * 验证基础参数
     * @param array $params
     * @return bool
     */
    protected function validateBaseParams(array $params)
    {
        $required = [
            'sender_name',
            'sender_phone',
            'sender_province',
            'sender_city',
            'sender_address',
            'receiver_name',
            'receiver_phone',
            'receiver_province',
            'receiver_city',
            'receiver_address',
        ];
        
        foreach ($required as $field) {
            if (empty($params[$field])) {
                throw new WaybillException(
                    sprintf('参数 %s 不能为空', $field),
                    ApiCode::PARAM_INVALID
                );
            }
        }
        
        return true;
    }
    
    /**
     * 统一处理API返回结果
     * @param array $result API返回结果
     * @return array 标准化后的结果
     */
    protected function handleApiResult(array $result)
    {
        return [
            'success' => $result['success'] ?? false,
            'code' => $result['code'] ?? '',
            'message' => $result['message'] ?? '',
            'data' => $result['data'] ?? [],
        ];
    }
}