<?php

namespace app\common\waybill;

use app\common\ApiCode;
use think\facade\Config;

/**
 * 电子面单工厂类
 */
class WaybillFactory
{
    /**
     * 电子面单实例缓存
     * @var array
     */
    protected static $instances = [];
    
    /**
     * 获取电子面单实例
     * @param string $platform 平台类型，如 'kdniao'(快递鸟)、'kd100'(快递100)、'cainiao'(菜鸟)
     * @param array $config 配置信息
     * @return WaybillInterface
     */
    public static function getInstance($platform = null, array $config = [])
    {
        // 如果未指定平台，则使用配置中的默认平台
        if (empty($platform)) {
            $platform = Config::get('waybill.default_platform', 'kdniao');
        }
        
        // 生成缓存键
        $key = md5($platform . serialize($config));
        
        // 如果实例已存在，则直接返回
        if (isset(self::$instances[$key])) {
            return self::$instances[$key];
        }
        
        // 获取平台配置
        $platformConfig = Config::get('waybill.platforms.' . $platform, []);
        
        // 合并配置
        $config = array_merge($platformConfig, $config);
        
        // 创建实例
        $class = self::getWaybillClass($platform);
        $instance = new $class($config);
        
        // 缓存实例
        self::$instances[$key] = $instance;
        
        return $instance;
    }
    
    /**
     * 获取电子面单类名
     * @param string $platform 平台类型
     * @return string 完整类名
     * @throws WaybillException
     */
    protected static function getWaybillClass($platform)
    {
        $map = [
            'kdniao' => '\app\common\waybill\kdniao\KdniaoWaybill',
            'kd100' => '\app\common\waybill\kd100\Kd100Waybill',
            'cainiao' => '\app\common\waybill\cainiao\CainiaoWaybill',
        ];
        
        if (!isset($map[$platform])) {
            throw new WaybillException(
                sprintf('不支持的电子面单平台: %s', $platform),
                ApiCode::PARAM_INVALID
            );
        }
        
        return $map[$platform];
    }
}