<?php

namespace app\common\waybill;

use think\facade\Config;

/**
 * 电子面单服务类
 * 作为统一的服务入口，提供便捷的调用方式
 */
class WaybillService
{
    /**
     * 获取电子面单实例
     * @param string|null $platform 平台类型
     * @param array $config 配置信息
     * @return WaybillInterface
     */
    public static function instance($platform = null, array $config = [])
    {
        return WaybillFactory::getInstance($platform, $config);
    }
    
    /**
     * 获取快递鸟实例
     * @param array $config 配置信息
     * @return WaybillInterface
     */
    public static function kdniao(array $config = [])
    {
        return self::instance('kdniao', $config);
    }
    
    /**
     * 获取快递100实例
     * @param array $config 配置信息
     * @return WaybillInterface
     */
    public static function kd100(array $config = [])
    {
        return self::instance('kd100', $config);
    }
    
    /**
     * 获取菜鸟实例
     * @param array $config 配置信息
     * @return WaybillInterface
     */
    public static function cainiao(array $config = [])
    {
        return self::instance('cainiao', $config);
    }
    
    /**
     * 获取电子面单号
     * @param array $params 请求参数
     * @param string|null $platform 平台类型
     * @param array $config 配置信息
     * @return array
     */
    public static function getWaybillNo(array $params, $platform = null, array $config = [])
    {
        return self::instance($platform, $config)->getWaybillNo($params);
    }
    
    /**
     * 取消电子面单
     * @param string $waybillNo 电子面单号
     * @param string|null $platform 平台类型
     * @param array $config 配置信息
     * @return array
     */
    public static function cancel($waybillNo, $platform = null, array $config = [])
    {
        return self::instance($platform, $config)->cancel($waybillNo);
    }
    
    /**
     * 获取物流轨迹
     * @param string $waybillNo 电子面单号
     * @param string|null $platform 平台类型
     * @param array $config 配置信息
     * @return array
     */
    public static function getTraces($waybillNo, $platform = null, array $config = [])
    {
        return self::instance($platform, $config)->getTraces($waybillNo);
    }
    
    /**
     * 获取支持的快递公司列表
     * @param string|null $platform 平台类型
     * @param array $config 配置信息
     * @return array
     */
    public static function getSupportedExpresses($platform = null, array $config = [])
    {
        return self::instance($platform, $config)->getSupportedExpresses();
    }
}