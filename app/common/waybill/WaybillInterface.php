<?php

namespace app\common\waybill;

/**
 * 电子面单接口
 * 定义电子面单服务的基本方法
 */
interface WaybillInterface
{
    /**
     * 获取电子面单号
     * 
     * @param array $params 请求参数
     * @return array 返回结果
     */
    public function getWaybillNo(array $params);
    
    /**
     * 取消电子面单
     * 
     * @param string $waybillNo 电子面单号
     * @return array 返回结果
     */
    public function cancel(string $waybillNo);
    
    /**
     * 获取物流轨迹
     * 
     * @param string $waybillNo 电子面单号
     * @return array 返回结果
     */
    public function getTraces(string $waybillNo);
    
    /**
     * 获取支持的快递公司列表
     * 
     * @return array 快递公司列表
     */
    public function getSupportedExpresses();
    
    /**
     * 获取配置信息
     * 
     * @return array 配置信息
     */
    public function getConfig();
}