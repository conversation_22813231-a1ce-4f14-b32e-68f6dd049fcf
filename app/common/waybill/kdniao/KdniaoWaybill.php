<?php

namespace app\common\waybill\kdniao;

use app\common\waybill\AbstractWaybill;
use app\common\waybill\WaybillException;
use app\common\ApiCode;
use liugene\curl\Curl;

/**
 * 快递鸟电子面单实现
 */
class KdniaoWaybill extends AbstractWaybill
{
    /**
     * API地址
     * @var string
     */
    protected $apiUrl = 'https://api.kdniao.com/api/v1';
    
    /**
     * 沙箱环境API地址
     * @var string
     */
    protected $sandboxApiUrl = 'https://sandbox.kdniao.com/api/v1';
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        if (empty($this->config['app_id'])) {
            throw new WaybillException('快递鸟配置错误：app_id不能为空', ApiCode::CONFIG_ERROR);
        }
        
        if (empty($this->config['app_key'])) {
            throw new WaybillException('快递鸟配置错误：app_key不能为空', ApiCode::CONFIG_ERROR);
        }
    }
    
    /**
     * 获取默认配置
     * @return array
     */
    protected function getDefaultConfig()
    {
        return array_merge(parent::getDefaultConfig(), [
            'version' => '1.0',
            'format' => 'json',
            'charset' => 'utf-8',
        ]);
    }
    
    /**
     * 获取电子面单号
     * @param array $params 请求参数
     * @return array
     */
    public function getWaybillNo(array $params)
    {
        // 验证基础参数
        $this->validateBaseParams($params);
        
        // 构建请求参数
        $requestData = [
            'OrderCode' => $params['order_code'] ?? '',
            'ShipperCode' => $this->getExpressCode(),
            'PayType' => $params['pay_type'] ?? 1,
            'ExpType' => $params['exp_type'] ?? 1,
            'Sender' => [
                'Name' => $params['sender_name'],
                'Mobile' => $params['sender_phone'],
                'ProvinceName' => $params['sender_province'],
                'CityName' => $params['sender_city'],
                'Address' => $params['sender_address'],
            ],
            'Receiver' => [
                'Name' => $params['receiver_name'],
                'Mobile' => $params['receiver_phone'],
                'ProvinceName' => $params['receiver_province'],
                'CityName' => $params['receiver_city'],
                'Address' => $params['receiver_address'],
            ],
            'Commodity' => [
                [
                    'GoodsName' => $params['goods_name'] ?? '商品',
                    'Quantity' => $params['goods_quantity'] ?? 1,
                ]
            ],
            'Weight' => $params['weight'] ?? 1,
            'Quantity' => $params['package_quantity'] ?? 1,
            'Volume' => $params['volume'] ?? 0,
            'Remark' => $params['remark'] ?? '',
        ];
        
        // 调用API
        $result = $this->request('/EOrderService', $requestData);
        
        return $this->handleApiResult($result);
    }
    
    /**
     * 取消电子面单
     * @param string $waybillNo 电子面单号
     * @return array
     */
    public function cancel(string $waybillNo)
    {
        $requestData = [
            'OrderCode' => '',
            'ShipperCode' => $this->getExpressCode(),
            'LogisticCode' => $waybillNo,
        ];
        
        $result = $this->request('/EOrderCancel', $requestData);
        
        return $this->handleApiResult($result);
    }
    
    /**
     * 获取物流轨迹
     * @param string $waybillNo 电子面单号
     * @return array
     */
    public function getTraces(string $waybillNo)
    {
        $requestData = [
            'OrderCode' => '',
            'ShipperCode' => $this->getExpressCode(),
            'LogisticCode' => $waybillNo,
        ];
        
        $result = $this->request('/Traces', $requestData);
        
        return $this->handleApiResult($result);
    }
    
    /**
     * 获取支持的快递公司列表
     * @return array
     */
    public function getSupportedExpresses()
    {
        $result = $this->request('/ExpressSupport', []);
        return $this->handleApiResult($result);
    }
    
    /**
     * 发送API请求
     * @param string $endpoint API接口地址
     * @param array $data 请求数据
     * @return array
     */
    protected function request($endpoint, array $data)
    {
        $requestData = [
            'EBusinessID' => $this->config['app_id'],
            'RequestType' => substr($endpoint, 1),
            'RequestData' => json_encode($data),
            'DataType' => $this->config['format'],
            'DataSign' => $this->generateSign($data),
        ];
        
        $url = $this->config['sandbox'] ? $this->sandboxApiUrl : $this->apiUrl;
        $url .= $endpoint;
        
        try {
            $curl = new Curl(2); // 失败时重试2次
            $curl->setOption([
                CURLOPT_TIMEOUT_MS => $this->config['timeout'] * 1000,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);
            
            $response = $curl->post($url, http_build_query($requestData));
            return json_decode($response, true) ?: [];
        } catch (\Exception $e) {
            throw new WaybillException(
                sprintf('请求快递鸟API失败：%s', $e->getMessage()),
                ApiCode::THIRD_PARTY_ERROR
            );
        }
    }
    
    /**
     * 生成签名
     * @param array $data 请求数据
     * @return string
     */
    protected function generateSign($data)
    {
        $json = json_encode($data);
        $sign = md5($json . $this->config['app_key']);
        return base64_encode($sign);
    }
}