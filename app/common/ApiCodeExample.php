<?php

namespace app\common;

/**
 * ApiCode使用示例
 */
class ApiCodeExample
{
    /**
     * 示例：登录接口
     * 
     * @param string $username 用户名
     * @param string $password 密码
     * @return array 登录结果
     */
    public function login($username, $password): array
    {
        // 验证参数
        if (empty($username)) {
            return ApiCode::error(ApiCode::USERNAME_EMPTY);
        }
        
        if (empty($password)) {
            return ApiCode::error(ApiCode::PASSWORD_EMPTY);
        }
        
        // 查询用户（模拟）
        $user = $this->getUserByUsername($username);
        
        if (empty($user)) {
            return ApiCode::error(ApiCode::USER_NOT_EXIST);
        }
        
        // 验证密码（模拟）
        if ($password !== '123456') {
            return ApiCode::error(ApiCode::PASSWORD_ERROR);
        }
        
        // 登录成功
        return ApiCode::success([
            'user_id' => $user['id'],
            'username' => $user['username'],
            'nickname' => $user['nickname'],
            'login_time' => time(),
        ]);
    }
    
    /**
     * 示例：获取用户信息
     * 
     * @param int $userId 用户ID
     * @return array 用户信息
     */
    public function getUserInfo($userId): array
    {
        // 参数验证
        if (empty($userId)) {
            return ApiCode::error(ApiCode::BAD_REQUEST, '用户ID不能为空');
        }
        
        // 查询用户（模拟）
        $user = $this->getUserById($userId);
        
        if (empty($user)) {
            return ApiCode::error(ApiCode::USER_NOT_EXIST);
        }
        
        // 返回用户信息
        return ApiCode::success($user);
    }
    
    /**
     * 示例：创建用户
     * 
     * @param array $userData 用户数据
     * @return array 创建结果
     */
    public function createUser(array $userData): array
    {
        // 参数验证
        if (empty($userData['username'])) {
            return ApiCode::error(ApiCode::BAD_REQUEST, '用户名不能为空');
        }
        
        if (empty($userData['password'])) {
            return ApiCode::error(ApiCode::BAD_REQUEST, '密码不能为空');
        }
        
        // 检查用户名是否已存在（模拟）
        $existUser = $this->getUserByUsername($userData['username']);
        if (!empty($existUser)) {
            return ApiCode::error(ApiCode::CONFLICT, '用户名已存在');
        }
        
        // 创建用户（模拟）
        $userId = mt_rand(100, 999); // 模拟生成用户ID
        
        // 返回创建结果
        return ApiCode::success([
            'user_id' => $userId,
            'username' => $userData['username'],
            'create_time' => time(),
        ]);
    }
    
    /**
     * 模拟：根据用户名获取用户
     */
    private function getUserByUsername($username)
    {
        // 模拟数据
        $users = [
            'admin' => [
                'id' => 1,
                'username' => 'admin',
                'nickname' => '管理员',
            ],
            'test' => [
                'id' => 2,
                'username' => 'test',
                'nickname' => '测试用户',
            ],
        ];
        
        return $users[$username] ?? null;
    }
    
    /**
     * 模拟：根据用户ID获取用户
     */
    private function getUserById($userId)
    {
        // 模拟数据
        $users = [
            1 => [
                'id' => 1,
                'username' => 'admin',
                'nickname' => '管理员',
                'email' => '<EMAIL>',
                'phone' => '13800138000',
                'create_time' => '2023-01-01 00:00:00',
            ],
            2 => [
                'id' => 2,
                'username' => 'test',
                'nickname' => '测试用户',
                'email' => '<EMAIL>',
                'phone' => '13900139000',
                'create_time' => '2023-01-02 00:00:00',
            ],
        ];
        
        return $users[$userId] ?? null;
    }
}