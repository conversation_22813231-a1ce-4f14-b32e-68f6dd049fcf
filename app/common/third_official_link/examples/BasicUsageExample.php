<?php

namespace app\common\third_official_link\examples;

use app\common\third_official_link\core\CustomerServiceManager;
use think\facade\Log;

/**
 * 第三方平台客服对接基础使用示例
 */
class BasicUsageExample
{
    /**
     * 客服管理器
     * @var CustomerServiceManager
     */
    protected CustomerServiceManager $manager;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 初始化客服管理器
        $this->manager = new CustomerServiceManager();
    }

    /**
     * 基础消息发送示例
     */
    public function basicMessageSending(): void
    {
        echo "=== 基础消息发送示例 ===\n";

        try {
            // 1. 发送文本消息到微信小程序
            $result = $this->manager->sendMessage(
                'wechat.miniprogram',
                'user_openid_123',
                '您好，欢迎使用我们的客服系统！',
                'text'
            );
            
            echo "微信小程序文本消息发送结果：\n";
            print_r($result);

            // 2. 发送图片消息到微信公众号
            $result = $this->manager->sendMessage(
                'wechat.officialaccount',
                'user_openid_456',
                'media_id_12345',
                'image'
            );
            
            echo "微信公众号图片消息发送结果：\n";
            print_r($result);

            // 3. 发送卡片消息到企业微信
            $cardData = [
                'title' => '产品介绍',
                'description' => '这是我们的最新产品，点击查看详情',
                'url' => 'https://example.com/product/123',
                'btntxt' => '查看详情'
            ];
            
            $result = $this->manager->sendMessage(
                'wechat.work',
                'user_id_789',
                $cardData,
                'card'
            );
            
            echo "企业微信卡片消息发送结果：\n";
            print_r($result);

        } catch (\Exception $e) {
            echo "发送消息失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 批量消息发送示例
     */
    public function batchMessageSending(): void
    {
        echo "\n=== 批量消息发送示例 ===\n";

        try {
            // 批量发送通知消息
            $config = [
                'platform' => 'wechat.miniprogram',
                'users' => ['user1', 'user2', 'user3'],
                'message' => '系统将于今晚22:00进行维护，预计持续2小时',
                'type' => 'text'
            ];

            $results = $this->manager->broadcast($config);
            
            echo "批量发送结果：\n";
            print_r($results);

        } catch (\Exception $e) {
            echo "批量发送失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 消息处理示例
     */
    public function messageHandling(): void
    {
        echo "\n=== 消息处理示例 ===\n";

        try {
            // 模拟接收到的消息
            $incomingMessage = [
                'id' => 'msg_123',
                'type' => 'text',
                'from_user_id' => 'user_openid_123',
                'to_user_id' => 'service_account',
                'content' => '你好，我想咨询产品价格',
                'timestamp' => time(),
                'raw_data' => [
                    'MsgId' => 'msg_123',
                    'MsgType' => 'text',
                    'FromUserName' => 'user_openid_123',
                    'ToUserName' => 'service_account',
                    'Content' => '你好，我想咨询产品价格',
                    'CreateTime' => time()
                ]
            ];

            // 处理消息
            $result = $this->manager->handleMessage('wechat.miniprogram', $incomingMessage);
            
            echo "消息处理结果：\n";
            print_r($result);

        } catch (\Exception $e) {
            echo "消息处理失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * AI集成示例
     */
    public function aiIntegration(): void
    {
        echo "\n=== AI集成示例 ===\n";

        try {
            // 启用AI功能
            $this->manager->enableAI([
                'model' => 'gpt-3.5-turbo',
                'temperature' => 0.7,
                'max_tokens' => 800
            ])->autoReply(true);

            // 发送消息（将通过AI处理）
            $result = $this->manager->sendMessage(
                'wechat.miniprogram',
                'user_openid_123',
                '请介绍一下你们的产品特点',
                'text'
            );
            
            echo "AI处理后的消息发送结果：\n";
            print_r($result);

        } catch (\Exception $e) {
            echo "AI集成失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 平台状态检查示例
     */
    public function platformStatusCheck(): void
    {
        echo "\n=== 平台状态检查示例 ===\n";

        try {
            $platforms = $this->manager->getPlatforms();
            
            foreach ($platforms as $platform) {
                $isAvailable = $this->manager->isPlatformAvailable($platform);
                $status = $isAvailable ? '✅ 可用' : '❌ 不可用';
                
                echo "{$platform}: {$status}\n";
                
                if ($isAvailable) {
                    // 获取平台限制信息
                    $limits = $this->manager->platform($platform)->getRateLimits();
                    echo "  限制信息：\n";
                    foreach ($limits['limits'] as $key => $value) {
                        echo "    {$key}: {$value}\n";
                    }
                }
            }

        } catch (\Exception $e) {
            echo "状态检查失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 统计数据获取示例
     */
    public function statisticsExample(): void
    {
        echo "\n=== 统计数据获取示例 ===\n";

        try {
            $startDate = date('Y-m-d', strtotime('-7 days'));
            $endDate = date('Y-m-d');

            // 获取单个平台统计
            $stats = $this->manager->getPlatformStatistics(
                'wechat.miniprogram',
                $startDate,
                $endDate
            );
            
            echo "微信小程序统计数据：\n";
            print_r($stats);

            // 获取所有平台统计
            $allStats = $this->manager->getAllStatistics($startDate, $endDate);
            
            echo "所有平台统计数据：\n";
            print_r($allStats);

        } catch (\Exception $e) {
            echo "统计数据获取失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 高级功能示例
     */
    public function advancedFeatures(): void
    {
        echo "\n=== 高级功能示例 ===\n";

        try {
            // 1. 会话管理
            $sessions = $this->manager->platform('wechat.miniprogram')
                ->getSessionList(['status' => 'active']);
            
            echo "活跃会话列表：\n";
            print_r($sessions);

            // 2. 会话转接
            if (!empty($sessions['data'])) {
                $sessionId = $sessions['data'][0]['id'] ?? 'session_123';
                
                $transferResult = $this->manager->platform('wechat.miniprogram')
                    ->transferSession($sessionId, 'service_002', '用户需要技术支持');
                
                echo "会话转接结果：\n";
                print_r($transferResult);
            }

            // 3. 设置自动回复规则
            $autoReplyRules = [
                [
                    'keywords' => ['价格', '多少钱', '费用'],
                    'reply' => '我们的产品价格请咨询客服人员，电话：400-123-4567',
                    'type' => 'text'
                ],
                [
                    'keywords' => ['营业时间', '工作时间'],
                    'reply' => '我们的营业时间是周一至周五 9:00-18:00',
                    'type' => 'text'
                ]
            ];

            $autoReplyResult = $this->manager->platform('wechat.miniprogram')
                ->setAutoReply($autoReplyRules);
            
            echo "自动回复设置结果：\n";
            print_r($autoReplyResult);

        } catch (\Exception $e) {
            echo "高级功能演示失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 运行所有示例
     */
    public function runAllExamples(): void
    {
        echo "第三方平台客服对接系统使用示例\n";
        echo str_repeat('=', 50) . "\n";

        $this->basicMessageSending();
        $this->batchMessageSending();
        $this->messageHandling();
        $this->aiIntegration();
        $this->platformStatusCheck();
        $this->statisticsExample();
        $this->advancedFeatures();

        echo "\n" . str_repeat('=', 50) . "\n";
        echo "所有示例运行完成！\n";
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    try {
        $example = new BasicUsageExample();
        $example->runAllExamples();
    } catch (\Exception $e) {
        echo "示例运行失败：" . $e->getMessage() . "\n";
        echo "错误追踪：\n" . $e->getTraceAsString() . "\n";
    }
}
