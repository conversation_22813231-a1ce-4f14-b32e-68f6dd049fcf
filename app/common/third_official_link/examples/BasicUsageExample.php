<?php

namespace app\common\third_official_link\examples;

use app\vchat\services\ThirdPartyIntegrationService;
use app\vchat\core\MessageProtocol;
use think\facade\Log;

/**
 * VChat集成第三方平台客服使用示例
 * 展示如何将第三方平台消息集成到VChat系统中
 */
class BasicUsageExample
{
    /**
     * 第三方平台集成服务
     * @var ThirdPartyIntegrationService
     */
    protected ThirdPartyIntegrationService $integrationService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 初始化第三方平台集成服务
        $this->integrationService = new ThirdPartyIntegrationService();
    }

    /**
     * VChat集成第三方平台消息处理示例
     */
    public function vchatIntegrationExample(): void
    {
        echo "=== VChat集成第三方平台消息处理示例 ===\n";

        try {
            // 1. 模拟接收微信小程序消息
            $wechatMessage = [
                'MsgType' => 'text',
                'FromUserName' => 'user_openid_123',
                'ToUserName' => 'service_account',
                'Content' => '你好，我想咨询产品价格',
                'CreateTime' => time(),
                'MsgId' => 'msg_' . uniqid()
            ];

            echo "处理微信小程序消息：\n";
            $result = $this->integrationService->handleThirdPartyMessage('wechat.miniprogram', $wechatMessage);
            print_r($result);

            // 2. 模拟接收企业微信消息
            $workMessage = [
                'msgtype' => 'text',
                'from' => 'work_user_456',
                'to' => 'work_service',
                'content' => '需要技术支持',
                'timestamp' => time(),
                'msgid' => 'work_msg_' . uniqid()
            ];

            echo "处理企业微信消息：\n";
            $result = $this->integrationService->handleThirdPartyMessage('wechat.work', $workMessage);
            print_r($result);

            // 3. 模拟发送VChat消息到第三方平台
            $vchatMessage = [
                'id' => 'vchat_msg_' . uniqid(),
                'content' => '感谢您的咨询，我们会尽快为您处理',
                'content_type' => MessageProtocol::TYPE_TEXT,
                'from_type' => 'service',
                'to_type' => 'user',
                'extra' => json_encode([
                    'platform' => 'wechat.miniprogram',
                    'third_party_user_id' => 'user_openid_123'
                ])
            ];

            echo "发送VChat消息到第三方平台：\n";
            $result = $this->integrationService->sendMessageToThirdParty('wechat.miniprogram', $vchatMessage);
            print_r($result);

        } catch (\Exception $e) {
            echo "VChat集成处理失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 批量消息发送示例
     */
    public function batchMessageSending(): void
    {
        echo "\n=== 批量消息发送示例 ===\n";

        try {
            // 批量发送通知消息
            $config = [
                'platform' => 'wechat.miniprogram',
                'users' => ['user1', 'user2', 'user3'],
                'message' => '系统将于今晚22:00进行维护，预计持续2小时',
                'type' => 'text'
            ];

            $results = $this->manager->broadcast($config);
            
            echo "批量发送结果：\n";
            print_r($results);

        } catch (\Exception $e) {
            echo "批量发送失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 消息处理示例
     */
    public function messageHandling(): void
    {
        echo "\n=== 消息处理示例 ===\n";

        try {
            // 模拟接收到的消息
            $incomingMessage = [
                'id' => 'msg_123',
                'type' => 'text',
                'from_user_id' => 'user_openid_123',
                'to_user_id' => 'service_account',
                'content' => '你好，我想咨询产品价格',
                'timestamp' => time(),
                'raw_data' => [
                    'MsgId' => 'msg_123',
                    'MsgType' => 'text',
                    'FromUserName' => 'user_openid_123',
                    'ToUserName' => 'service_account',
                    'Content' => '你好，我想咨询产品价格',
                    'CreateTime' => time()
                ]
            ];

            // 处理消息
            $result = $this->manager->handleMessage('wechat.miniprogram', $incomingMessage);
            
            echo "消息处理结果：\n";
            print_r($result);

        } catch (\Exception $e) {
            echo "消息处理失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * VChat集成说明
     */
    public function vchatIntegrationInfo(): void
    {
        echo "\n=== VChat集成说明 ===\n";

        echo "注意：AI和自动回复功能已统一到VChat系统中处理\n";
        echo "第三方平台客服系统专注于：\n";
        echo "1. 消息格式转换和平台适配\n";
        echo "2. Webhook接收和消息发送\n";
        echo "3. 平台连接状态管理\n\n";

        echo "AI和自动回复功能请在VChat系统中配置：\n";
        echo "- 自动回复：config/vchat.php 中的 auto_reply 配置\n";
        echo "- AI客服：config/vchat.php 中的 ai 配置\n";
        echo "- 所有第三方平台消息将自动享受VChat的AI和自动回复功能\n";
    }

    /**
     * 平台状态检查示例
     */
    public function platformStatusCheck(): void
    {
        echo "\n=== 平台状态检查示例 ===\n";

        try {
            $platforms = $this->manager->getPlatforms();
            
            foreach ($platforms as $platform) {
                $isAvailable = $this->manager->isPlatformAvailable($platform);
                $status = $isAvailable ? '✅ 可用' : '❌ 不可用';
                
                echo "{$platform}: {$status}\n";
                
                if ($isAvailable) {
                    // 获取平台限制信息
                    $limits = $this->manager->platform($platform)->getRateLimits();
                    echo "  限制信息：\n";
                    foreach ($limits['limits'] as $key => $value) {
                        echo "    {$key}: {$value}\n";
                    }
                }
            }

        } catch (\Exception $e) {
            echo "状态检查失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 统计数据获取示例
     */
    public function statisticsExample(): void
    {
        echo "\n=== 统计数据获取示例 ===\n";

        try {
            $startDate = date('Y-m-d', strtotime('-7 days'));
            $endDate = date('Y-m-d');

            // 获取单个平台统计
            $stats = $this->manager->getPlatformStatistics(
                'wechat.miniprogram',
                $startDate,
                $endDate
            );
            
            echo "微信小程序统计数据：\n";
            print_r($stats);

            // 获取所有平台统计
            $allStats = $this->manager->getAllStatistics($startDate, $endDate);
            
            echo "所有平台统计数据：\n";
            print_r($allStats);

        } catch (\Exception $e) {
            echo "统计数据获取失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 高级功能示例
     */
    public function advancedFeatures(): void
    {
        echo "\n=== 高级功能示例 ===\n";

        try {
            // 1. 会话管理功能说明
            echo "会话管理功能已统一到VChat系统：\n";
            echo "- 获取会话列表：app\\vchat\\services\\SessionService::getSessionList()\n";
            echo "- 获取消息历史：app\\vchat\\services\\MessageService::getSessionMessages()\n";
            echo "- 转接会话：app\\vchat\\services\\SessionService::transferSession()\n";
            echo "- 结束会话：app\\vchat\\services\\SessionService::endSession()\n\n";

            // 2. 第三方平台专注功能
            echo "第三方平台专注于以下功能：\n";
            echo "- 消息格式转换和平台适配\n";
            echo "- 平台特定API调用\n";
            echo "- 媒体文件上传和处理\n";
            echo "- 平台连接状态管理\n\n";

            // 3. 媒体文件上传示例
            echo "媒体文件上传示例：\n";
            $uploadResult = $this->manager->platform('wechat.miniprogram')
                ->uploadMedia('/path/to/image.jpg', 'image');
            echo "上传结果：\n";
            print_r($uploadResult);

            // 4. 注意：自动回复和AI功能已移至VChat系统
            echo "\n功能统一说明：\n";
            echo "- 自动回复功能已统一到VChat系统中\n";
            echo "- AI客服功能已统一到VChat系统中\n";
            echo "- 会话管理功能已统一到VChat系统中\n";
            echo "- 第三方平台消息将自动享受VChat的所有功能\n";

        } catch (\Exception $e) {
            echo "高级功能演示失败：" . $e->getMessage() . "\n";
        }
    }

    /**
     * 运行所有示例
     */
    public function runAllExamples(): void
    {
        echo "第三方平台客服对接系统使用示例\n";
        echo str_repeat('=', 50) . "\n";

        $this->basicMessageSending();
        $this->batchMessageSending();
        $this->messageHandling();
        $this->vchatIntegrationInfo();
        $this->platformStatusCheck();
        $this->statisticsExample();
        $this->advancedFeatures();

        echo "\n" . str_repeat('=', 50) . "\n";
        echo "所有示例运行完成！\n";
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    try {
        $example = new BasicUsageExample();
        $example->runAllExamples();
    } catch (\Exception $e) {
        echo "示例运行失败：" . $e->getMessage() . "\n";
        echo "错误追踪：\n" . $e->getTraceAsString() . "\n";
    }
}
