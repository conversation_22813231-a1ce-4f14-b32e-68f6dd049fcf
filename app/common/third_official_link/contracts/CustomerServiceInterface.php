<?php

namespace app\common\third_official_link\contracts;

/**
 * 客服服务接口
 * 定义所有第三方平台客服功能的标准接口
 */
interface CustomerServiceInterface
{
    /**
     * 发送文本消息
     * @param string $userId 用户ID
     * @param string $content 消息内容
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendTextMessage(string $userId, string $content, array $options = []): array;

    /**
     * 发送图片消息
     * @param string $userId 用户ID
     * @param string $mediaId 媒体ID或图片URL
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendImageMessage(string $userId, string $mediaId, array $options = []): array;

    /**
     * 发送语音消息
     * @param string $userId 用户ID
     * @param string $mediaId 媒体ID
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendVoiceMessage(string $userId, string $mediaId, array $options = []): array;

    /**
     * 发送视频消息
     * @param string $userId 用户ID
     * @param string $mediaId 媒体ID
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendVideoMessage(string $userId, string $mediaId, array $options = []): array;

    /**
     * 发送卡片消息
     * @param string $userId 用户ID
     * @param array $cardData 卡片数据
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendCardMessage(string $userId, array $cardData, array $options = []): array;

    /**
     * 发送模板消息
     * @param string $userId 用户ID
     * @param string $templateId 模板ID
     * @param array $data 模板数据
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendTemplateMessage(string $userId, string $templateId, array $data, array $options = []): array;

    /**
     * 处理接收到的消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    public function handleIncomingMessage(array $message): array;

    /**
     * 获取用户信息
     * @param string $userId 用户ID
     * @return array 用户信息
     */
    public function getUserInfo(string $userId): array;

    /**
     * 获取会话列表
     * @param array $filters 过滤条件
     * @return array 会话列表
     */
    public function getSessionList(array $filters = []): array;

    /**
     * 获取会话历史消息
     * @param string $sessionId 会话ID
     * @param array $options 选项
     * @return array 历史消息
     */
    public function getSessionHistory(string $sessionId, array $options = []): array;

    /**
     * 设置客服状态
     * @param string $status 状态 (online/offline/busy)
     * @return array 设置结果
     */
    public function setServiceStatus(string $status): array;

    /**
     * 转接会话
     * @param string $sessionId 会话ID
     * @param string $targetServiceId 目标客服ID
     * @param string $reason 转接原因
     * @return array 转接结果
     */
    public function transferSession(string $sessionId, string $targetServiceId, string $reason = ''): array;

    /**
     * 结束会话
     * @param string $sessionId 会话ID
     * @param string $reason 结束原因
     * @return array 结束结果
     */
    public function endSession(string $sessionId, string $reason = ''): array;

    /**
     * 上传媒体文件
     * @param string $filePath 文件路径
     * @param string $type 文件类型 (image/voice/video/file)
     * @return array 上传结果
     */
    public function uploadMedia(string $filePath, string $type): array;

    /**
     * 下载媒体文件
     * @param string $mediaId 媒体ID
     * @return array 下载结果
     */
    public function downloadMedia(string $mediaId): array;

    /**
     * 获取平台配置
     * @return array 配置信息
     */
    public function getConfig(): array;

    /**
     * 验证Webhook签名
     * @param array $data 请求数据
     * @param string $signature 签名
     * @return bool 验证结果
     */
    public function verifyWebhookSignature(array $data, string $signature): bool;

    /**
     * 处理Webhook事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleWebhookEvent(array $event): array;

    /**
     * 获取平台标识
     * @return string 平台标识
     */
    public function getPlatformIdentifier(): string;

    /**
     * 检查平台连接状态
     * @return array 连接状态
     */
    public function checkConnection(): array;

    /**
     * 获取平台限制信息
     * @return array 限制信息
     */
    public function getRateLimits(): array;

    /**
     * 批量发送消息
     * @param array $messages 消息列表
     * @return array 发送结果
     */
    public function batchSendMessages(array $messages): array;

    /**
     * 设置自动回复
     * @param array $rules 自动回复规则
     * @return array 设置结果
     */
    public function setAutoReply(array $rules): array;

    /**
     * 获取统计数据
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param array $metrics 统计指标
     * @return array 统计数据
     */
    public function getStatistics(string $startDate, string $endDate, array $metrics = []): array;
}
