<?php

namespace app\common\third_official_link\contracts;

/**
 * Webhook接口
 * 定义第三方平台Webhook事件处理的标准接口
 */
interface WebhookInterface
{
    /**
     * 处理Webhook请求
     * @param array $request 请求数据
     * @return array 处理结果
     */
    public function handle(array $request): array;

    /**
     * 验证Webhook签名
     * @param array $request 请求数据
     * @param string $signature 签名
     * @return bool 验证结果
     */
    public function verifySignature(array $request, string $signature): bool;

    /**
     * 解析事件类型
     * @param array $data 事件数据
     * @return string 事件类型
     */
    public function parseEventType(array $data): string;

    /**
     * 处理消息事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleMessageEvent(array $event): array;

    /**
     * 处理用户事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleUserEvent(array $event): array;

    /**
     * 处理系统事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleSystemEvent(array $event): array;

    /**
     * 处理菜单事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleMenuEvent(array $event): array;

    /**
     * 处理扫码事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleScanEvent(array $event): array;

    /**
     * 处理位置事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleLocationEvent(array $event): array;

    /**
     * 处理支付事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handlePaymentEvent(array $event): array;

    /**
     * 处理模板消息事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleTemplateEvent(array $event): array;

    /**
     * 处理客服事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleCustomerServiceEvent(array $event): array;

    /**
     * 处理未知事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleUnknownEvent(array $event): array;

    /**
     * 获取支持的事件类型
     * @return array 事件类型列表
     */
    public function getSupportedEventTypes(): array;

    /**
     * 检查事件是否支持
     * @param string $eventType 事件类型
     * @return bool 是否支持
     */
    public function isEventSupported(string $eventType): bool;

    /**
     * 注册事件处理器
     * @param string $eventType 事件类型
     * @param callable $handler 处理器
     * @return self
     */
    public function registerEventHandler(string $eventType, callable $handler): self;

    /**
     * 移除事件处理器
     * @param string $eventType 事件类型
     * @return self
     */
    public function removeEventHandler(string $eventType): self;

    /**
     * 获取事件处理器
     * @param string $eventType 事件类型
     * @return callable|null 处理器
     */
    public function getEventHandler(string $eventType): ?callable;

    /**
     * 设置默认处理器
     * @param callable $handler 默认处理器
     * @return self
     */
    public function setDefaultHandler(callable $handler): self;

    /**
     * 获取默认处理器
     * @return callable|null 默认处理器
     */
    public function getDefaultHandler(): ?callable;

    /**
     * 启用事件日志
     * @param bool $enabled 是否启用
     * @return self
     */
    public function enableEventLogging(bool $enabled = true): self;

    /**
     * 检查是否启用事件日志
     * @return bool 是否启用
     */
    public function isEventLoggingEnabled(): bool;

    /**
     * 记录事件日志
     * @param string $eventType 事件类型
     * @param array $data 事件数据
     * @param array $result 处理结果
     * @return void
     */
    public function logEvent(string $eventType, array $data, array $result): void;

    /**
     * 获取事件统计
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 统计数据
     */
    public function getEventStatistics(string $startDate, string $endDate): array;

    /**
     * 设置事件过滤器
     * @param callable $filter 过滤器
     * @return self
     */
    public function setEventFilter(callable $filter): self;

    /**
     * 移除事件过滤器
     * @return self
     */
    public function removeEventFilter(): self;

    /**
     * 检查事件是否通过过滤器
     * @param array $event 事件数据
     * @return bool 是否通过
     */
    public function passesFilter(array $event): bool;

    /**
     * 设置事件中间件
     * @param array $middleware 中间件列表
     * @return self
     */
    public function setMiddleware(array $middleware): self;

    /**
     * 添加事件中间件
     * @param callable $middleware 中间件
     * @return self
     */
    public function addMiddleware(callable $middleware): self;

    /**
     * 获取事件中间件
     * @return array 中间件列表
     */
    public function getMiddleware(): array;

    /**
     * 通过中间件处理事件
     * @param array $event 事件数据
     * @param callable $next 下一个处理器
     * @return array 处理结果
     */
    public function throughMiddleware(array $event, callable $next): array;

    /**
     * 设置错误处理器
     * @param callable $handler 错误处理器
     * @return self
     */
    public function setErrorHandler(callable $handler): self;

    /**
     * 获取错误处理器
     * @return callable|null 错误处理器
     */
    public function getErrorHandler(): ?callable;

    /**
     * 处理错误
     * @param \Throwable $error 错误
     * @param array $context 上下文
     * @return array 处理结果
     */
    public function handleError(\Throwable $error, array $context = []): array;

    /**
     * 获取平台标识
     * @return string 平台标识
     */
    public function getPlatformIdentifier(): string;

    /**
     * 获取Webhook配置
     * @return array 配置信息
     */
    public function getWebhookConfig(): array;

    /**
     * 验证Webhook配置
     * @return bool 配置是否有效
     */
    public function validateConfig(): bool;

    /**
     * 获取配置验证错误
     * @return array 验证错误
     */
    public function getConfigErrors(): array;
}
