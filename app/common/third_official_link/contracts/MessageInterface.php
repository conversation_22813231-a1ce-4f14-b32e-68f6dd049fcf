<?php

namespace app\common\third_official_link\contracts;

/**
 * 消息接口
 * 定义标准的消息格式和处理方法
 */
interface MessageInterface
{
    /**
     * 获取消息ID
     * @return string
     */
    public function getId(): string;

    /**
     * 获取消息类型
     * @return string text|image|voice|video|card|template|event
     */
    public function getType(): string;

    /**
     * 获取发送者ID
     * @return string
     */
    public function getFromUserId(): string;

    /**
     * 获取接收者ID
     * @return string
     */
    public function getToUserId(): string;

    /**
     * 获取消息内容
     * @return mixed
     */
    public function getContent();

    /**
     * 获取消息时间戳
     * @return int
     */
    public function getTimestamp(): int;

    /**
     * 获取平台标识
     * @return string
     */
    public function getPlatform(): string;

    /**
     * 获取会话ID
     * @return string
     */
    public function getSessionId(): string;

    /**
     * 获取消息元数据
     * @return array
     */
    public function getMetadata(): array;

    /**
     * 设置消息ID
     * @param string $id
     * @return self
     */
    public function setId(string $id): self;

    /**
     * 设置消息类型
     * @param string $type
     * @return self
     */
    public function setType(string $type): self;

    /**
     * 设置发送者ID
     * @param string $userId
     * @return self
     */
    public function setFromUserId(string $userId): self;

    /**
     * 设置接收者ID
     * @param string $userId
     * @return self
     */
    public function setToUserId(string $userId): self;

    /**
     * 设置消息内容
     * @param mixed $content
     * @return self
     */
    public function setContent($content): self;

    /**
     * 设置消息时间戳
     * @param int $timestamp
     * @return self
     */
    public function setTimestamp(int $timestamp): self;

    /**
     * 设置平台标识
     * @param string $platform
     * @return self
     */
    public function setPlatform(string $platform): self;

    /**
     * 设置会话ID
     * @param string $sessionId
     * @return self
     */
    public function setSessionId(string $sessionId): self;

    /**
     * 设置消息元数据
     * @param array $metadata
     * @return self
     */
    public function setMetadata(array $metadata): self;

    /**
     * 添加元数据
     * @param string $key
     * @param mixed $value
     * @return self
     */
    public function addMetadata(string $key, $value): self;

    /**
     * 获取指定元数据
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getMetadataValue(string $key, $default = null);

    /**
     * 检查是否为文本消息
     * @return bool
     */
    public function isTextMessage(): bool;

    /**
     * 检查是否为媒体消息
     * @return bool
     */
    public function isMediaMessage(): bool;

    /**
     * 检查是否为事件消息
     * @return bool
     */
    public function isEventMessage(): bool;

    /**
     * 转换为数组
     * @return array
     */
    public function toArray(): array;

    /**
     * 从数组创建消息
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self;

    /**
     * 转换为JSON
     * @return string
     */
    public function toJson(): string;

    /**
     * 从JSON创建消息
     * @param string $json
     * @return self
     */
    public static function fromJson(string $json): self;

    /**
     * 验证消息格式
     * @return bool
     */
    public function validate(): bool;

    /**
     * 获取验证错误
     * @return array
     */
    public function getValidationErrors(): array;

    /**
     * 克隆消息
     * @return self
     */
    public function clone(): self;

    /**
     * 检查消息是否过期
     * @param int $ttl 生存时间（秒）
     * @return bool
     */
    public function isExpired(int $ttl = 3600): bool;

    /**
     * 获取消息大小（字节）
     * @return int
     */
    public function getSize(): int;

    /**
     * 检查是否为系统消息
     * @return bool
     */
    public function isSystemMessage(): bool;

    /**
     * 设置为系统消息
     * @param bool $isSystem
     * @return self
     */
    public function setSystemMessage(bool $isSystem = true): self;

    /**
     * 获取消息优先级
     * @return int 1-10，数字越大优先级越高
     */
    public function getPriority(): int;

    /**
     * 设置消息优先级
     * @param int $priority
     * @return self
     */
    public function setPriority(int $priority): self;

    /**
     * 检查是否需要回执
     * @return bool
     */
    public function needsReceipt(): bool;

    /**
     * 设置是否需要回执
     * @param bool $needsReceipt
     * @return self
     */
    public function setNeedsReceipt(bool $needsReceipt): self;

    /**
     * 获取重试次数
     * @return int
     */
    public function getRetryCount(): int;

    /**
     * 增加重试次数
     * @return self
     */
    public function incrementRetryCount(): self;

    /**
     * 重置重试次数
     * @return self
     */
    public function resetRetryCount(): self;

    /**
     * 获取最大重试次数
     * @return int
     */
    public function getMaxRetries(): int;

    /**
     * 设置最大重试次数
     * @param int $maxRetries
     * @return self
     */
    public function setMaxRetries(int $maxRetries): self;

    /**
     * 检查是否可以重试
     * @return bool
     */
    public function canRetry(): bool;
}
