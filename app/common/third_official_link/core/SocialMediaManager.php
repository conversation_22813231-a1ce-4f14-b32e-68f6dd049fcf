<?php
/**
 * 新媒体平台管理器
 * 统一管理小红书、抖音、微博、快手等新媒体平台
 */

namespace app\common\third_official_link\core;

use think\facade\Config;
use think\facade\Log;
use app\common\third_official_link\platforms\social_media\xiaohongshu\XiaohongshuService;
use app\common\third_official_link\platforms\social_media\douyin\DouyinService;
use app\common\third_official_link\platforms\social_media\weibo\WeiboService;
use app\common\third_official_link\platforms\social_media\kuaishou\KuaishouService;

class SocialMediaManager
{
    /**
     * 平台服务实例
     * @var array
     */
    private array $platformServices = [];

    /**
     * 支持的平台列表
     * @var array
     */
    private array $supportedPlatforms = [
        'xiaohongshu' => XiaohongshuService::class,
        'douyin' => DouyinService::class,
        'weibo' => WeiboService::class,
        'kuaishou' => KuaishouService::class,
    ];

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->initializePlatforms();
    }

    /**
     * 初始化平台服务
     */
    private function initializePlatforms(): void
    {
        $platformConfigs = Config::get('vchat.third_party_integration.platforms', []);

        foreach ($this->supportedPlatforms as $platform => $serviceClass) {
            $config = $platformConfigs[$platform] ?? [];
            
            if ($config['enabled'] ?? false) {
                try {
                    $this->platformServices[$platform] = new $serviceClass($config);
                    Log::info("新媒体平台初始化成功: {$platform}");
                } catch (\Exception $e) {
                    Log::error("新媒体平台初始化失败: {$platform}", [
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }

    /**
     * 获取平台服务
     * @param string $platform 平台标识
     * @return object|null 平台服务实例
     */
    public function getPlatformService(string $platform): ?object
    {
        return $this->platformServices[$platform] ?? null;
    }

    /**
     * 检查平台是否可用
     * @param string $platform 平台标识
     * @return bool 是否可用
     */
    public function isPlatformAvailable(string $platform): bool
    {
        $service = $this->getPlatformService($platform);
        return $service ? $service->isPlatformAvailable() : false;
    }

    /**
     * 发送消息到指定平台
     * @param string $platform 平台标识
     * @param string $userId 用户ID
     * @param string $content 消息内容
     * @param string $type 消息类型
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendMessage(string $platform, string $userId, string $content, string $type = 'text', array $options = []): array
    {
        try {
            $service = $this->getPlatformService($platform);
            
            if (!$service) {
                throw new \Exception("Platform not available: {$platform}");
            }

            return $service->sendMessage($userId, $content, $type, $options);

        } catch (\Exception $e) {
            Log::error("新媒体平台消息发送失败: {$platform}", [
                'user_id' => $userId,
                'content' => $content,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => $platform
            ];
        }
    }

    /**
     * 处理来自平台的Webhook消息
     * @param string $platform 平台标识
     * @param array $webhookData Webhook数据
     * @return array 处理结果
     */
    public function handleWebhookMessage(string $platform, array $webhookData): array
    {
        try {
            $service = $this->getPlatformService($platform);
            
            if (!$service) {
                throw new \Exception("Platform not available: {$platform}");
            }

            return $service->handleIncomingMessage($webhookData);

        } catch (\Exception $e) {
            Log::error("新媒体平台Webhook处理失败: {$platform}", [
                'webhook_data' => $webhookData,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => $platform
            ];
        }
    }

    /**
     * 批量发送消息
     * @param string $platform 平台标识
     * @param array $messages 消息列表
     * @return array 发送结果
     */
    public function broadcastMessages(string $platform, array $messages): array
    {
        try {
            $service = $this->getPlatformService($platform);
            
            if (!$service) {
                throw new \Exception("Platform not available: {$platform}");
            }

            return $service->broadcast($messages);

        } catch (\Exception $e) {
            Log::error("新媒体平台批量发送失败: {$platform}", [
                'message_count' => count($messages),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => $platform
            ];
        }
    }

    /**
     * 获取用户信息
     * @param string $platform 平台标识
     * @param string $userId 用户ID
     * @return array 用户信息
     */
    public function getUserInfo(string $platform, string $userId): array
    {
        try {
            $service = $this->getPlatformService($platform);
            
            if (!$service) {
                throw new \Exception("Platform not available: {$platform}");
            }

            return $service->getUserInfo($userId);

        } catch (\Exception $e) {
            Log::error("新媒体平台获取用户信息失败: {$platform}", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => $platform
            ];
        }
    }

    /**
     * 上传媒体文件
     * @param string $platform 平台标识
     * @param string $filePath 文件路径
     * @param string $type 文件类型
     * @return array 上传结果
     */
    public function uploadMedia(string $platform, string $filePath, string $type): array
    {
        try {
            $service = $this->getPlatformService($platform);
            
            if (!$service) {
                throw new \Exception("Platform not available: {$platform}");
            }

            return $service->uploadMedia($filePath, $type);

        } catch (\Exception $e) {
            Log::error("新媒体平台媒体上传失败: {$platform}", [
                'file_path' => $filePath,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => $platform
            ];
        }
    }

    /**
     * 获取平台统计数据
     * @param string $platform 平台标识
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 统计数据
     */
    public function getPlatformStatistics(string $platform, string $startDate = '', string $endDate = ''): array
    {
        try {
            $service = $this->getPlatformService($platform);
            
            if (!$service) {
                throw new \Exception("Platform not available: {$platform}");
            }

            return $service->getPlatformStatistics($startDate, $endDate);

        } catch (\Exception $e) {
            Log::error("新媒体平台统计数据获取失败: {$platform}", [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'platform' => $platform
            ];
        }
    }

    /**
     * 获取所有可用平台列表
     * @return array 平台列表
     */
    public function getAvailablePlatforms(): array
    {
        $platforms = [];
        
        foreach ($this->platformServices as $platform => $service) {
            $platforms[] = [
                'platform' => $platform,
                'name' => $this->getPlatformName($platform),
                'icon' => $this->getPlatformIcon($platform),
                'color' => $this->getPlatformColor($platform),
                'available' => $service->isPlatformAvailable(),
                'webhook_url' => "/vchat/webhook/{$platform}"
            ];
        }

        return $platforms;
    }

    /**
     * 获取平台显示名称
     * @param string $platform 平台标识
     * @return string 显示名称
     */
    private function getPlatformName(string $platform): string
    {
        $names = [
            'xiaohongshu' => '小红书',
            'douyin' => '抖音',
            'weibo' => '微博',
            'kuaishou' => '快手'
        ];

        return $names[$platform] ?? $platform;
    }

    /**
     * 获取平台图标
     * @param string $platform 平台标识
     * @return string 图标名称
     */
    private function getPlatformIcon(string $platform): string
    {
        $icons = [
            'xiaohongshu' => 'xiaohongshu',
            'douyin' => 'douyin',
            'weibo' => 'weibo',
            'kuaishou' => 'kuaishou'
        ];

        return $icons[$platform] ?? 'default';
    }

    /**
     * 获取平台主题色
     * @param string $platform 平台标识
     * @return string 颜色代码
     */
    private function getPlatformColor(string $platform): string
    {
        $colors = [
            'xiaohongshu' => '#FF2442',
            'douyin' => '#000000',
            'weibo' => '#E6162D',
            'kuaishou' => '#FF6600'
        ];

        return $colors[$platform] ?? '#666666';
    }

    /**
     * 检查所有平台状态
     * @return array 平台状态
     */
    public function checkAllPlatformsStatus(): array
    {
        $status = [];
        
        foreach ($this->platformServices as $platform => $service) {
            $status[$platform] = [
                'platform' => $platform,
                'name' => $this->getPlatformName($platform),
                'available' => $service->isPlatformAvailable(),
                'last_check' => date('Y-m-d H:i:s')
            ];
        }

        return $status;
    }
}
