<?php

namespace app\common\third_official_link\core;

use app\common\third_official_link\contracts\MessageInterface;
use think\facade\Log;
use think\facade\Cache;

/**
 * 消息处理器
 * 统一处理消息的预处理和后处理逻辑
 */
class MessageProcessor
{
    /**
     * 消息过滤器
     * @var array
     */
    protected array $filters = [];

    /**
     * 消息中间件
     * @var array
     */
    protected array $middleware = [];

    /**
     * 消息转换器
     * @var array
     */
    protected array $transformers = [];

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->initializeDefaultFilters();
        $this->initializeDefaultMiddleware();
        $this->initializeDefaultTransformers();
    }

    /**
     * 预处理消息
     * @param array $message 原始消息
     * @param string $platform 平台标识
     * @return array 处理后的消息
     */
    public function preprocess(array $message, string $platform): array
    {
        // 1. 消息标准化
        $standardizedMessage = $this->standardizeMessage($message, $platform);
        
        // 2. 应用过滤器
        if (!$this->passesFilters($standardizedMessage)) {
            Log::info('Message filtered out', [
                'platform' => $platform,
                'message_id' => $standardizedMessage['id'] ?? 'unknown'
            ]);
            return [];
        }

        // 3. 应用中间件
        $processedMessage = $this->applyMiddleware($standardizedMessage, 'preprocess');

        // 4. 应用转换器
        $transformedMessage = $this->applyTransformers($processedMessage, $platform);

        // 5. 记录日志
        $this->logMessage($transformedMessage, 'preprocessed');

        return $transformedMessage;
    }

    /**
     * 后处理消息
     * @param array $result 处理结果
     * @param string $platform 平台标识
     * @return array 最终结果
     */
    public function postprocess(array $result, string $platform): array
    {
        // 1. 应用中间件
        $processedResult = $this->applyMiddleware($result, 'postprocess');

        // 2. 结果标准化
        $standardizedResult = $this->standardizeResult($processedResult, $platform);

        // 3. 缓存处理
        $this->cacheResult($standardizedResult, $platform);

        // 4. 记录日志
        $this->logMessage($standardizedResult, 'postprocessed');

        return $standardizedResult;
    }

    /**
     * 标准化消息格式
     * @param array $message 原始消息
     * @param string $platform 平台标识
     * @return array 标准化消息
     */
    protected function standardizeMessage(array $message, string $platform): array
    {
        $standardized = [
            'id' => $this->extractMessageId($message, $platform),
            'type' => $this->extractMessageType($message, $platform),
            'from_user_id' => $this->extractFromUserId($message, $platform),
            'to_user_id' => $this->extractToUserId($message, $platform),
            'content' => $this->extractContent($message, $platform),
            'timestamp' => $this->extractTimestamp($message, $platform),
            'platform' => $platform,
            'session_id' => $this->generateSessionId($message, $platform),
            'metadata' => $this->extractMetadata($message, $platform),
            'raw_data' => $message
        ];

        return $standardized;
    }

    /**
     * 标准化结果格式
     * @param array $result 处理结果
     * @param string $platform 平台标识
     * @return array 标准化结果
     */
    protected function standardizeResult(array $result, string $platform): array
    {
        return [
            'success' => $result['success'] ?? true,
            'message_id' => $result['message_id'] ?? null,
            'platform' => $platform,
            'timestamp' => time(),
            'data' => $result['data'] ?? $result,
            'error' => $result['error'] ?? null,
            'metadata' => $result['metadata'] ?? []
        ];
    }

    /**
     * 添加过滤器
     * @param callable $filter 过滤器
     * @return self
     */
    public function addFilter(callable $filter): self
    {
        $this->filters[] = $filter;
        return $this;
    }

    /**
     * 添加中间件
     * @param callable $middleware 中间件
     * @return self
     */
    public function addMiddleware(callable $middleware): self
    {
        $this->middleware[] = $middleware;
        return $this;
    }

    /**
     * 添加转换器
     * @param string $platform 平台标识
     * @param callable $transformer 转换器
     * @return self
     */
    public function addTransformer(string $platform, callable $transformer): self
    {
        $this->transformers[$platform] = $transformer;
        return $this;
    }

    /**
     * 检查消息是否通过过滤器
     * @param array $message 消息
     * @return bool
     */
    protected function passesFilters(array $message): bool
    {
        foreach ($this->filters as $filter) {
            if (!$filter($message)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 应用中间件
     * @param array $data 数据
     * @param string $stage 阶段
     * @return array
     */
    protected function applyMiddleware(array $data, string $stage): array
    {
        foreach ($this->middleware as $middleware) {
            $data = $middleware($data, $stage);
        }
        return $data;
    }

    /**
     * 应用转换器
     * @param array $message 消息
     * @param string $platform 平台标识
     * @return array
     */
    protected function applyTransformers(array $message, string $platform): array
    {
        if (isset($this->transformers[$platform])) {
            return $this->transformers[$platform]($message);
        }
        return $message;
    }

    /**
     * 初始化默认过滤器
     */
    protected function initializeDefaultFilters(): void
    {
        // 空消息过滤器
        $this->addFilter(function ($message) {
            return !empty($message['content']) || !empty($message['type']);
        });

        // 重复消息过滤器
        $this->addFilter(function ($message) {
            $cacheKey = 'message_duplicate_' . md5(json_encode($message));
            if (Cache::has($cacheKey)) {
                return false;
            }
            Cache::set($cacheKey, true, 60); // 1分钟内不允许重复
            return true;
        });
    }

    /**
     * 初始化默认中间件
     */
    protected function initializeDefaultMiddleware(): void
    {
        // 性能监控中间件
        $this->addMiddleware(function ($data, $stage) {
            $startTime = microtime(true);
            // 处理数据
            $endTime = microtime(true);
            $data['processing_time'] = $endTime - $startTime;
            return $data;
        });

        // 安全检查中间件
        $this->addMiddleware(function ($data, $stage) {
            if ($stage === 'preprocess') {
                // 检查恶意内容
                $data = $this->sanitizeMessage($data);
            }
            return $data;
        });
    }

    /**
     * 初始化默认转换器
     */
    protected function initializeDefaultTransformers(): void
    {
        // 微信平台转换器
        $this->addTransformer('wechat.miniprogram', function ($message) {
            // 微信小程序特殊处理
            return $this->transformWechatMessage($message);
        });

        $this->addTransformer('wechat.officialaccount', function ($message) {
            // 微信公众号特殊处理
            return $this->transformWechatMessage($message);
        });

        $this->addTransformer('wechat.work', function ($message) {
            // 企业微信特殊处理
            return $this->transformWechatWorkMessage($message);
        });
    }

    /**
     * 提取消息ID
     * @param array $message 消息
     * @param string $platform 平台
     * @return string
     */
    protected function extractMessageId(array $message, string $platform): string
    {
        switch ($platform) {
            case 'wechat.miniprogram':
            case 'wechat.officialaccount':
                return $message['MsgId'] ?? uniqid();
            case 'wechat.work':
                return $message['MsgId'] ?? uniqid();
            default:
                return $message['id'] ?? uniqid();
        }
    }

    /**
     * 提取消息类型
     * @param array $message 消息
     * @param string $platform 平台
     * @return string
     */
    protected function extractMessageType(array $message, string $platform): string
    {
        switch ($platform) {
            case 'wechat.miniprogram':
            case 'wechat.officialaccount':
            case 'wechat.work':
                return strtolower($message['MsgType'] ?? 'text');
            default:
                return $message['type'] ?? 'text';
        }
    }

    /**
     * 提取发送者ID
     * @param array $message 消息
     * @param string $platform 平台
     * @return string
     */
    protected function extractFromUserId(array $message, string $platform): string
    {
        switch ($platform) {
            case 'wechat.miniprogram':
            case 'wechat.officialaccount':
                return $message['FromUserName'] ?? '';
            case 'wechat.work':
                return $message['FromUserName'] ?? '';
            default:
                return $message['from_user_id'] ?? '';
        }
    }

    /**
     * 提取接收者ID
     * @param array $message 消息
     * @param string $platform 平台
     * @return string
     */
    protected function extractToUserId(array $message, string $platform): string
    {
        switch ($platform) {
            case 'wechat.miniprogram':
            case 'wechat.officialaccount':
                return $message['ToUserName'] ?? '';
            case 'wechat.work':
                return $message['ToUserName'] ?? '';
            default:
                return $message['to_user_id'] ?? '';
        }
    }

    /**
     * 提取消息内容
     * @param array $message 消息
     * @param string $platform 平台
     * @return mixed
     */
    protected function extractContent(array $message, string $platform)
    {
        switch ($platform) {
            case 'wechat.miniprogram':
            case 'wechat.officialaccount':
            case 'wechat.work':
                return $message['Content'] ?? $message['MediaId'] ?? '';
            default:
                return $message['content'] ?? '';
        }
    }

    /**
     * 提取时间戳
     * @param array $message 消息
     * @param string $platform 平台
     * @return int
     */
    protected function extractTimestamp(array $message, string $platform): int
    {
        switch ($platform) {
            case 'wechat.miniprogram':
            case 'wechat.officialaccount':
            case 'wechat.work':
                return $message['CreateTime'] ?? time();
            default:
                return $message['timestamp'] ?? time();
        }
    }

    /**
     * 生成会话ID
     * @param array $message 消息
     * @param string $platform 平台
     * @return string
     */
    protected function generateSessionId(array $message, string $platform): string
    {
        $fromUser = $this->extractFromUserId($message, $platform);
        $toUser = $this->extractToUserId($message, $platform);
        return md5($platform . '_' . $fromUser . '_' . $toUser);
    }

    /**
     * 提取元数据
     * @param array $message 消息
     * @param string $platform 平台
     * @return array
     */
    protected function extractMetadata(array $message, string $platform): array
    {
        $metadata = [
            'platform' => $platform,
            'original_format' => true
        ];

        // 平台特定元数据
        switch ($platform) {
            case 'wechat.miniprogram':
            case 'wechat.officialaccount':
            case 'wechat.work':
                $metadata['app_id'] = $message['ToUserName'] ?? '';
                $metadata['msg_type'] = $message['MsgType'] ?? '';
                break;
        }

        return $metadata;
    }

    /**
     * 转换微信消息
     * @param array $message 消息
     * @return array
     */
    protected function transformWechatMessage(array $message): array
    {
        // 微信特殊字段处理
        if ($message['type'] === 'image') {
            $message['media_id'] = $message['content'];
            $message['content'] = '[图片]';
        } elseif ($message['type'] === 'voice') {
            $message['media_id'] = $message['content'];
            $message['content'] = '[语音]';
        }

        return $message;
    }

    /**
     * 转换企业微信消息
     * @param array $message 消息
     * @return array
     */
    protected function transformWechatWorkMessage(array $message): array
    {
        // 企业微信特殊处理
        return $this->transformWechatMessage($message);
    }

    /**
     * 消息安全检查
     * @param array $message 消息
     * @return array
     */
    protected function sanitizeMessage(array $message): array
    {
        if (isset($message['content']) && is_string($message['content'])) {
            // 过滤恶意脚本
            $message['content'] = strip_tags($message['content']);
            // 过滤特殊字符
            $message['content'] = htmlspecialchars($message['content'], ENT_QUOTES, 'UTF-8');
        }

        return $message;
    }

    /**
     * 记录消息日志
     * @param array $data 数据
     * @param string $stage 阶段
     */
    protected function logMessage(array $data, string $stage): void
    {
        Log::info("Message {$stage}", [
            'message_id' => $data['id'] ?? 'unknown',
            'platform' => $data['platform'] ?? 'unknown',
            'type' => $data['type'] ?? 'unknown',
            'stage' => $stage
        ]);
    }

    /**
     * 缓存结果
     * @param array $result 结果
     * @param string $platform 平台
     */
    protected function cacheResult(array $result, string $platform): void
    {
        if (isset($result['message_id'])) {
            $cacheKey = "message_result_{$platform}_{$result['message_id']}";
            Cache::set($cacheKey, $result, 3600); // 缓存1小时
        }
    }
}
