<?php

namespace app\common\third_official_link\core;

use app\common\third_official_link\contracts\CustomerServiceInterface;
use app\common\third_official_link\config\PlatformConfig;
use app\common\third_official_link\exceptions\PlatformNotFoundException;
use app\common\third_official_link\exceptions\ConfigurationException;
use think\facade\Log;
use think\facade\Cache;

/**
 * 客服管理器
 * 统一管理所有第三方平台的客服功能
 */
class CustomerServiceManager
{
    /**
     * 平台实例缓存
     * @var array
     */
    protected array $platforms = [];

    /**
     * 配置管理器
     * @var PlatformConfig
     */
    protected PlatformConfig $config;

    /**
     * 默认平台
     * @var string
     */
    protected string $defaultPlatform;

    /**
     * AI集成状态
     * @var bool
     */
    protected bool $aiEnabled = false;

    /**
     * AI配置
     * @var array
     */
    protected array $aiConfig = [];

    /**
     * 消息处理器
     * @var MessageProcessor
     */
    protected MessageProcessor $messageProcessor;

    /**
     * 构造函数
     * @param array $config 配置数组
     */
    public function __construct(array $config = [])
    {
        $this->config = new PlatformConfig($config);
        $this->defaultPlatform = $this->config->getDefaultPlatform();
        $this->messageProcessor = new MessageProcessor();
        
        $this->initializePlatforms();
    }

    /**
     * 初始化平台
     */
    protected function initializePlatforms(): void
    {
        $platforms = $this->config->getAllPlatforms();
        
        foreach ($platforms as $platformKey => $platformConfig) {
            try {
                $this->registerPlatform($platformKey, $platformConfig);
            } catch (\Exception $e) {
                Log::error("Failed to initialize platform: {$platformKey}", [
                    'error' => $e->getMessage(),
                    'config' => $platformConfig
                ]);
            }
        }
    }

    /**
     * 注册平台
     * @param string $platformKey 平台标识
     * @param array $config 平台配置
     */
    public function registerPlatform(string $platformKey, array $config): void
    {
        $className = $this->getPlatformClassName($platformKey);
        
        if (!class_exists($className)) {
            throw new PlatformNotFoundException("Platform class not found: {$className}");
        }

        $this->platforms[$platformKey] = [
            'class' => $className,
            'config' => $config,
            'instance' => null
        ];
    }

    /**
     * 获取平台实例
     * @param string $platformKey 平台标识
     * @return CustomerServiceInterface
     */
    public function platform(string $platformKey = null): CustomerServiceInterface
    {
        $platformKey = $platformKey ?: $this->defaultPlatform;
        
        if (!isset($this->platforms[$platformKey])) {
            throw new PlatformNotFoundException("Platform not found: {$platformKey}");
        }

        // 懒加载平台实例
        if ($this->platforms[$platformKey]['instance'] === null) {
            $className = $this->platforms[$platformKey]['class'];
            $config = $this->platforms[$platformKey]['config'];
            
            $this->platforms[$platformKey]['instance'] = new $className($config);
        }

        return $this->platforms[$platformKey]['instance'];
    }

    /**
     * 发送消息
     * @param string $platformKey 平台标识
     * @param string $userId 用户ID
     * @param string $message 消息内容
     * @param string $type 消息类型
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendMessage(
        string $platformKey,
        string $userId,
        string $message,
        string $type = 'text',
        array $options = []
    ): array {
        $platform = $this->platform($platformKey);
        
        // AI处理
        if ($this->aiEnabled && $type === 'text') {
            $message = $this->processWithAI($message, $userId, $platformKey);
        }

        // 根据类型发送消息
        switch ($type) {
            case 'text':
                return $platform->sendTextMessage($userId, $message, $options);
            case 'image':
                return $platform->sendImageMessage($userId, $message, $options);
            case 'voice':
                return $platform->sendVoiceMessage($userId, $message, $options);
            case 'video':
                return $platform->sendVideoMessage($userId, $message, $options);
            case 'card':
                return $platform->sendCardMessage($userId, $message, $options);
            case 'template':
                $templateId = $options['template_id'] ?? '';
                return $platform->sendTemplateMessage($userId, $templateId, $message, $options);
            default:
                throw new \InvalidArgumentException("Unsupported message type: {$type}");
        }
    }

    /**
     * 批量发送消息
     * @param array $config 批量发送配置
     * @return array 发送结果
     */
    public function broadcast(array $config): array
    {
        $platformKey = $config['platform'] ?? $this->defaultPlatform;
        $users = $config['users'] ?? [];
        $message = $config['message'] ?? '';
        $type = $config['type'] ?? 'text';
        $options = $config['options'] ?? [];

        $results = [];
        $platform = $this->platform($platformKey);

        // 检查平台是否支持批量发送
        if (method_exists($platform, 'batchSendMessages')) {
            $messages = [];
            foreach ($users as $userId) {
                $messages[] = [
                    'user_id' => $userId,
                    'message' => $message,
                    'type' => $type,
                    'options' => $options
                ];
            }
            return $platform->batchSendMessages($messages);
        }

        // 逐个发送
        foreach ($users as $userId) {
            try {
                $result = $this->sendMessage($platformKey, $userId, $message, $type, $options);
                $results[$userId] = $result;
            } catch (\Exception $e) {
                $results[$userId] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                
                Log::error("Broadcast message failed", [
                    'platform' => $platformKey,
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * 处理接收消息
     * @param string $platformKey 平台标识
     * @param array $message 消息数据
     * @return array 处理结果
     */
    public function handleMessage(string $platformKey, array $message): array
    {
        $platform = $this->platform($platformKey);
        
        // 预处理消息
        $processedMessage = $this->messageProcessor->preprocess($message, $platformKey);
        
        // 平台处理
        $result = $platform->handleIncomingMessage($processedMessage);
        
        // 后处理
        $finalResult = $this->messageProcessor->postprocess($result, $platformKey);
        
        // AI自动回复
        if ($this->aiEnabled && $this->shouldAutoReply($processedMessage)) {
            $this->handleAutoReply($platformKey, $processedMessage);
        }

        return $finalResult;
    }

    /**
     * 启用AI集成
     * @param array $config AI配置
     * @return self
     */
    public function enableAI(array $config = []): self
    {
        $this->aiEnabled = true;
        $this->aiConfig = array_merge($this->aiConfig, $config);
        return $this;
    }

    /**
     * 禁用AI集成
     * @return self
     */
    public function disableAI(): self
    {
        $this->aiEnabled = false;
        return $this;
    }

    /**
     * 设置AI配置
     * @param array $config AI配置
     * @return self
     */
    public function setAIConfig(array $config): self
    {
        $this->aiConfig = $config;
        return $this;
    }

    /**
     * 设置自动回复
     * @param bool $enabled 是否启用
     * @return self
     */
    public function autoReply(bool $enabled = true): self
    {
        $this->aiConfig['auto_reply'] = $enabled;
        return $this;
    }

    /**
     * 获取平台列表
     * @return array 平台列表
     */
    public function getPlatforms(): array
    {
        return array_keys($this->platforms);
    }

    /**
     * 检查平台是否可用
     * @param string $platformKey 平台标识
     * @return bool 是否可用
     */
    public function isPlatformAvailable(string $platformKey): bool
    {
        if (!isset($this->platforms[$platformKey])) {
            return false;
        }

        try {
            $platform = $this->platform($platformKey);
            $status = $platform->checkConnection();
            return $status['connected'] ?? false;
        } catch (\Exception $e) {
            Log::error("Platform availability check failed", [
                'platform' => $platformKey,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取平台统计
     * @param string $platformKey 平台标识
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 统计数据
     */
    public function getPlatformStatistics(string $platformKey, string $startDate, string $endDate): array
    {
        $platform = $this->platform($platformKey);
        return $platform->getStatistics($startDate, $endDate);
    }

    /**
     * 获取所有平台统计
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 统计数据
     */
    public function getAllStatistics(string $startDate, string $endDate): array
    {
        $statistics = [];
        
        foreach ($this->getPlatforms() as $platformKey) {
            try {
                $statistics[$platformKey] = $this->getPlatformStatistics($platformKey, $startDate, $endDate);
            } catch (\Exception $e) {
                $statistics[$platformKey] = [
                    'error' => $e->getMessage()
                ];
            }
        }

        return $statistics;
    }

    /**
     * 获取平台类名
     * @param string $platformKey 平台标识
     * @return string 类名
     */
    protected function getPlatformClassName(string $platformKey): string
    {
        $parts = explode('.', $platformKey);
        $namespace = 'app\\common\\third_official_link\\platforms\\';
        
        if (count($parts) === 1) {
            // 单级平台，如 wechat
            return $namespace . $parts[0] . '\\' . ucfirst($parts[0]) . 'CustomerService';
        } else {
            // 多级平台，如 wechat.miniprogram
            return $namespace . $parts[0] . '\\' . $parts[1] . '\\' . ucfirst($parts[1]) . 'CustomerService';
        }
    }

    /**
     * AI处理消息
     * @param string $message 消息内容
     * @param string $userId 用户ID
     * @param string $platformKey 平台标识
     * @return string 处理后的消息
     */
    protected function processWithAI(string $message, string $userId, string $platformKey): string
    {
        // TODO: 集成AI服务
        return $message;
    }

    /**
     * 检查是否应该自动回复
     * @param array $message 消息数据
     * @return bool 是否自动回复
     */
    protected function shouldAutoReply(array $message): bool
    {
        return $this->aiConfig['auto_reply'] ?? false;
    }

    /**
     * 处理自动回复
     * @param string $platformKey 平台标识
     * @param array $message 消息数据
     */
    protected function handleAutoReply(string $platformKey, array $message): void
    {
        // TODO: 实现自动回复逻辑
    }
}
