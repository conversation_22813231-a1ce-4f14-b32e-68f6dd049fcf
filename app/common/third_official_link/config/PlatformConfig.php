<?php

namespace app\common\third_official_link\config;

use app\common\third_official_link\exceptions\ConfigurationException;
use think\facade\Config;

/**
 * 平台配置管理器
 * 统一管理所有第三方平台的配置信息
 */
class PlatformConfig
{
    /**
     * 配置数据
     * @var array
     */
    protected array $config;

    /**
     * 默认配置
     * @var array
     */
    protected array $defaultConfig = [
        'default' => 'wechat.miniprogram',
        'timeout' => 30,
        'retry_times' => 3,
        'cache_ttl' => 3600,
        'log_enabled' => true,
        'platforms' => []
    ];

    /**
     * 构造函数
     * @param array $config 配置数组
     */
    public function __construct(array $config = [])
    {
        // 优先使用传入的配置，如果为空则从VChat统一配置中读取
        if (empty($config)) {
            $vchatConfig = Config::get('vchat.third_party_integration', []);
            $config = [
                'platforms' => $vchatConfig['platforms'] ?? [],
                'timeout' => $vchatConfig['timeout'] ?? 30,
                'retry_times' => $vchatConfig['retry_times'] ?? 3,
            ];
        }

        $this->config = array_merge($this->defaultConfig, $config);

        // 如果平台配置不为空，则验证配置
        if (!empty($this->config['platforms'])) {
            $this->validateConfig();
        }
    }

    /**
     * 获取默认平台
     * @return string
     */
    public function getDefaultPlatform(): string
    {
        return $this->config['default'];
    }

    /**
     * 设置默认平台
     * @param string $platform 平台标识
     * @return self
     */
    public function setDefaultPlatform(string $platform): self
    {
        $this->config['default'] = $platform;
        return $this;
    }

    /**
     * 获取所有平台配置
     * @return array
     */
    public function getAllPlatforms(): array
    {
        return $this->config['platforms'] ?? [];
    }

    /**
     * 获取平台配置
     * @param string $platformKey 平台标识
     * @return array
     */
    public function getPlatformConfig(string $platformKey): array
    {
        $platforms = $this->getAllPlatforms();
        
        if (!isset($platforms[$platformKey])) {
            throw new ConfigurationException("Platform configuration not found: {$platformKey}");
        }

        return $platforms[$platformKey];
    }

    /**
     * 设置平台配置
     * @param string $platformKey 平台标识
     * @param array $config 配置数据
     * @return self
     */
    public function setPlatformConfig(string $platformKey, array $config): self
    {
        $this->config['platforms'][$platformKey] = $config;
        return $this;
    }

    /**
     * 检查平台是否存在
     * @param string $platformKey 平台标识
     * @return bool
     */
    public function hasPlatform(string $platformKey): bool
    {
        return isset($this->config['platforms'][$platformKey]);
    }

    /**
     * 移除平台配置
     * @param string $platformKey 平台标识
     * @return self
     */
    public function removePlatform(string $platformKey): self
    {
        unset($this->config['platforms'][$platformKey]);
        return $this;
    }

    /**
     * 获取微信小程序配置
     * @return array
     */
    public function getWechatMiniprogramConfig(): array
    {
        return $this->getPlatformConfig('wechat.miniprogram');
    }

    /**
     * 获取微信公众号配置
     * @return array
     */
    public function getWechatOfficialAccountConfig(): array
    {
        return $this->getPlatformConfig('wechat.officialaccount');
    }

    /**
     * 获取企业微信配置
     * @return array
     */
    public function getWechatWorkConfig(): array
    {
        return $this->getPlatformConfig('wechat.work');
    }

    /**
     * 获取全局配置
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    public function get(string $key, $default = null)
    {
        return $this->config[$key] ?? $default;
    }

    /**
     * 设置全局配置
     * @param string $key 配置键
     * @param mixed $value 配置值
     * @return self
     */
    public function set(string $key, $value): self
    {
        $this->config[$key] = $value;
        return $this;
    }

    /**
     * 获取超时时间
     * @return int
     */
    public function getTimeout(): int
    {
        return $this->config['timeout'];
    }

    /**
     * 获取重试次数
     * @return int
     */
    public function getRetryTimes(): int
    {
        return $this->config['retry_times'];
    }

    /**
     * 获取缓存TTL
     * @return int
     */
    public function getCacheTtl(): int
    {
        return $this->config['cache_ttl'];
    }

    /**
     * 检查是否启用日志
     * @return bool
     */
    public function isLogEnabled(): bool
    {
        return $this->config['log_enabled'];
    }

    /**
     * 获取完整配置
     * @return array
     */
    public function toArray(): array
    {
        return $this->config;
    }

    /**
     * 从数组加载配置
     * @param array $config 配置数组
     * @return self
     */
    public function fromArray(array $config): self
    {
        $this->config = array_merge($this->config, $config);
        $this->validateConfig();
        return $this;
    }

    /**
     * 验证配置
     * @throws ConfigurationException
     */
    protected function validateConfig(): void
    {
        // 验证默认平台
        if (empty($this->config['default'])) {
            throw new ConfigurationException('Default platform is required');
        }

        // 验证平台配置
        $platforms = $this->config['platforms'] ?? [];
        if (empty($platforms)) {
            throw new ConfigurationException('At least one platform configuration is required');
        }

        // 验证默认平台是否存在
        if (!isset($platforms[$this->config['default']])) {
            throw new ConfigurationException("Default platform configuration not found: {$this->config['default']}");
        }

        // 验证各平台配置
        foreach ($platforms as $platformKey => $platformConfig) {
            $this->validatePlatformConfig($platformKey, $platformConfig);
        }
    }

    /**
     * 验证平台配置
     * @param string $platformKey 平台标识
     * @param array $config 平台配置
     * @throws ConfigurationException
     */
    protected function validatePlatformConfig(string $platformKey, array $config): void
    {
        $parts = explode('.', $platformKey);
        
        switch ($parts[0]) {
            case 'wechat':
                $this->validateWechatConfig($platformKey, $config);
                break;
            case 'qq':
                $this->validateQQConfig($platformKey, $config);
                break;
            case 'dingtalk':
                $this->validateDingtalkConfig($platformKey, $config);
                break;
            default:
                // 通用验证
                if (empty($config['app_id'])) {
                    throw new ConfigurationException("app_id is required for platform: {$platformKey}");
                }
        }
    }

    /**
     * 验证微信配置
     * @param string $platformKey 平台标识
     * @param array $config 配置
     * @throws ConfigurationException
     */
    protected function validateWechatConfig(string $platformKey, array $config): void
    {
        $requiredFields = ['app_id', 'secret'];
        
        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                throw new ConfigurationException("{$field} is required for WeChat platform: {$platformKey}");
            }
        }

        // 企业微信需要额外字段
        if (str_contains($platformKey, 'work')) {
            if (empty($config['corp_id'])) {
                throw new ConfigurationException("corp_id is required for WeChat Work: {$platformKey}");
            }
        }
    }

    /**
     * 验证QQ配置
     * @param string $platformKey 平台标识
     * @param array $config 配置
     * @throws ConfigurationException
     */
    protected function validateQQConfig(string $platformKey, array $config): void
    {
        $requiredFields = ['app_id', 'app_key'];
        
        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                throw new ConfigurationException("{$field} is required for QQ platform: {$platformKey}");
            }
        }
    }

    /**
     * 验证钉钉配置
     * @param string $platformKey 平台标识
     * @param array $config 配置
     * @throws ConfigurationException
     */
    protected function validateDingtalkConfig(string $platformKey, array $config): void
    {
        $requiredFields = ['app_key', 'app_secret'];
        
        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                throw new ConfigurationException("{$field} is required for DingTalk platform: {$platformKey}");
            }
        }
    }

    /**
     * 获取环境变量配置
     * @param string $key 环境变量键
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function env(string $key, $default = null)
    {
        return $_ENV[$key] ?? $default;
    }

    /**
     * 合并环境变量配置
     * @return self
     */
    public function mergeEnvConfig(): self
    {
        // 微信小程序
        if ($this->env('WECHAT_MINIPROGRAM_APP_ID')) {
            $this->config['platforms']['wechat.miniprogram'] = [
                'app_id' => $this->env('WECHAT_MINIPROGRAM_APP_ID'),
                'secret' => $this->env('WECHAT_MINIPROGRAM_SECRET'),
                'token' => $this->env('WECHAT_MINIPROGRAM_TOKEN'),
                'aes_key' => $this->env('WECHAT_MINIPROGRAM_AES_KEY'),
            ];
        }

        // 微信公众号
        if ($this->env('WECHAT_OFFICIAL_ACCOUNT_APP_ID')) {
            $this->config['platforms']['wechat.officialaccount'] = [
                'app_id' => $this->env('WECHAT_OFFICIAL_ACCOUNT_APP_ID'),
                'secret' => $this->env('WECHAT_OFFICIAL_ACCOUNT_SECRET'),
                'token' => $this->env('WECHAT_OFFICIAL_ACCOUNT_TOKEN'),
                'aes_key' => $this->env('WECHAT_OFFICIAL_ACCOUNT_AES_KEY'),
            ];
        }

        // 企业微信
        if ($this->env('WECHAT_WORK_CORP_ID')) {
            $this->config['platforms']['wechat.work'] = [
                'corp_id' => $this->env('WECHAT_WORK_CORP_ID'),
                'agent_id' => $this->env('WECHAT_WORK_AGENT_ID'),
                'secret' => $this->env('WECHAT_WORK_SECRET'),
                'token' => $this->env('WECHAT_WORK_TOKEN'),
                'aes_key' => $this->env('WECHAT_WORK_AES_KEY'),
            ];
        }

        return $this;
    }
}
