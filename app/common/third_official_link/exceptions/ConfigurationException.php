<?php

namespace app\common\third_official_link\exceptions;

/**
 * 配置异常
 */
class ConfigurationException extends \Exception
{
    /**
     * 构造函数
     * @param string $message 异常消息
     * @param int $code 异常代码
     * @param \Throwable|null $previous 上一个异常
     */
    public function __construct(string $message = "Configuration error", int $code = 500, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
