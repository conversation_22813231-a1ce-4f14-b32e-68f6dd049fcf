<?php

namespace app\common\third_official_link\exceptions;

/**
 * 平台未找到异常
 */
class PlatformNotFoundException extends \Exception
{
    /**
     * 构造函数
     * @param string $message 异常消息
     * @param int $code 异常代码
     * @param \Throwable|null $previous 上一个异常
     */
    public function __construct(string $message = "Platform not found", int $code = 404, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
