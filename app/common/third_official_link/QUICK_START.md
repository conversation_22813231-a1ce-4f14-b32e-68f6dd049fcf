# 第三方平台客服对接系统 - 快速开始

## 🚀 快速安装

### 1. 安装依赖
```bash
# 已自动安装 EasyWeChat 6.x
composer require w7corp/easywechat:^6.0
```

### 2. 环境配置
在 `.env` 文件中添加配置：

```env
# 微信小程序
WECHAT_MINIPROGRAM_APP_ID=your_miniprogram_app_id
WECHAT_MINIPROGRAM_SECRET=your_miniprogram_secret
WECHAT_MINIPROGRAM_TOKEN=your_miniprogram_token
WECHAT_MINIPROGRAM_AES_KEY=your_miniprogram_aes_key

# 微信公众号
WECHAT_OFFICIAL_ACCOUNT_APP_ID=your_official_account_app_id
WECHAT_OFFICIAL_ACCOUNT_SECRET=your_official_account_secret
WECHAT_OFFICIAL_ACCOUNT_TOKEN=your_official_account_token
WECHAT_OFFICIAL_ACCOUNT_AES_KEY=your_official_account_aes_key

# 企业微信
WECHAT_WORK_CORP_ID=your_work_corp_id
WECHAT_WORK_AGENT_ID=your_work_agent_id
WECHAT_WORK_SECRET=your_work_secret
WECHAT_WORK_TOKEN=your_work_token
WECHAT_WORK_AES_KEY=your_work_aes_key

# 注意：AI和自动回复功能已统一到VChat系统
# 请在VChat配置中设置AI和自动回复功能
# 第三方平台消息将自动享受VChat的AI和自动回复功能
```

## 📝 基础使用

### 1. 初始化客服管理器
```php
use app\common\third_official_link\core\CustomerServiceManager;

$manager = new CustomerServiceManager();
```

### 2. 发送消息
```php
// 发送文本消息
$result = $manager->sendMessage(
    'wechat.miniprogram',           // 平台
    'user_openid_123',              // 用户ID
    '您好，欢迎使用我们的服务！',      // 消息内容
    'text'                          // 消息类型
);

// 发送图片消息
$result = $manager->sendMessage(
    'wechat.officialaccount',
    'user_openid_456',
    'media_id_12345',               // 媒体ID
    'image'
);

// 发送卡片消息
$cardData = [
    'title' => '产品介绍',
    'description' => '查看我们的最新产品',
    'url' => 'https://example.com/product'
];

$result = $manager->sendMessage(
    'wechat.work',
    'user_id_789',
    $cardData,
    'card'
);
```

### 3. 批量发送
```php
$config = [
    'platform' => 'wechat.miniprogram',
    'users' => ['user1', 'user2', 'user3'],
    'message' => '系统维护通知',
    'type' => 'text'
];

$results = $manager->broadcast($config);
```

### 4. 处理接收消息
```php
// 在控制器中处理Webhook
public function webhook()
{
    $message = [
        'type' => 'text',
        'from_user_id' => 'user_openid_123',
        'content' => '用户发送的消息',
        'timestamp' => time()
    ];

    $result = $manager->handleMessage('wechat.miniprogram', $message);
    
    return json($result);
}
```

## 🤖 VChat集成说明

### AI和自动回复功能
```php
// 注意：AI和自动回复功能已统一到VChat系统中处理
// 第三方平台客服系统专注于消息格式转换和平台适配

// AI和自动回复功能请在VChat系统中配置：
// - 自动回复：config/vchat.php 中的 auto_reply 配置
// - AI客服：config/vchat.php 中的 ai 配置
// - 所有第三方平台消息将自动享受VChat的AI和自动回复功能

// 第三方平台只需要专注于消息发送
$result = $manager->sendMessage(
    'wechat.miniprogram',
    'user_openid_123',
    '客服回复内容',
    'text'
);
```

## 🔧 高级功能

### 1. 平台状态检查
```php
// 检查平台是否可用
$isAvailable = $manager->isPlatformAvailable('wechat.miniprogram');

// 获取平台限制信息
$limits = $manager->platform('wechat.miniprogram')->getRateLimits();
```

### 2. 会话管理
```php
$platform = $manager->platform('wechat.miniprogram');

// 获取会话列表
$sessions = $platform->getSessionList(['status' => 'active']);

// 获取会话历史
$history = $platform->getSessionHistory('session_id_123');

// 转接会话
$platform->transferSession('session_id_123', 'service_002', '需要技术支持');

// 结束会话
$platform->endSession('session_id_123', '问题已解决');
```

### 3. 自动回复功能说明
```php
// 注意：自动回复功能已移至VChat系统
// 请在VChat配置中设置自动回复规则：
// config/vchat.php -> auto_reply 配置

// 第三方平台消息将自动享受VChat的自动回复功能
// 无需在第三方平台中单独设置自动回复规则
```

### 4. 统计数据
```php
// 获取单个平台统计
$stats = $manager->getPlatformStatistics(
    'wechat.miniprogram',
    '2024-01-01',
    '2024-01-31'
);

// 获取所有平台统计
$allStats = $manager->getAllStatistics('2024-01-01', '2024-01-31');
```

## 🌐 Webhook配置

### 1. 路由配置
在 `route/app.php` 中添加：

```php
use think\facade\Route;

// 微信小程序Webhook
Route::any('/webhook/wechat/miniprogram', 'webhook/wechat/miniprogram');

// 微信公众号Webhook  
Route::any('/webhook/wechat/officialaccount', 'webhook/wechat/officialaccount');

// 企业微信Webhook
Route::any('/webhook/wechat/work', 'webhook/wechat/work');
```

### 2. 控制器实现
```php
<?php

namespace app\controller\webhook\wechat;

use app\common\third_official_link\core\CustomerServiceManager;
use think\Request;

class Miniprogram
{
    public function index(Request $request)
    {
        $manager = new CustomerServiceManager();
        
        // 验证签名
        $signature = $request->get('signature');
        $timestamp = $request->get('timestamp');
        $nonce = $request->get('nonce');
        
        $platform = $manager->platform('wechat.miniprogram');
        
        if (!$platform->verifyWebhookSignature([
            'timestamp' => $timestamp,
            'nonce' => $nonce
        ], $signature)) {
            return 'Invalid signature';
        }
        
        // 处理事件
        $data = $request->getContent();
        $event = json_decode($data, true);
        
        $result = $platform->handleWebhookEvent($event);
        
        return json($result);
    }
}
```

## 📊 监控和日志

### 查看日志
```bash
# 查看系统日志
tail -f runtime/logs/third_official_link/info.log

# 查看错误日志
tail -f runtime/logs/third_official_link/error.log
```

### 性能监控
```php
// 获取平台连接状态
foreach ($manager->getPlatforms() as $platform) {
    $status = $manager->platform($platform)->checkConnection();
    echo "{$platform}: " . ($status['connected'] ? '✅' : '❌') . "\n";
}
```

## 🔍 故障排除

### 常见问题

1. **配置错误**
   - 检查 `.env` 文件中的配置是否正确
   - 确认微信平台的AppID、Secret等信息

2. **网络问题**
   - 检查服务器网络连接
   - 确认防火墙设置

3. **权限问题**
   - 检查缓存目录权限
   - 确认日志目录可写

4. **API限制**
   - 查看平台限制信息
   - 避免频繁调用API

### 调试模式
```php
// 启用调试模式
$config = [
    'debug' => [
        'enabled' => true,
        'log_requests' => true,
        'log_responses' => true
    ]
];

$manager = new CustomerServiceManager($config);
```

## 📚 更多资源

- [完整API文档](./README.md)
- [使用示例](./examples/BasicUsageExample.php)
- [配置说明](../../../config/third_official_link.php)
- [EasyWeChat文档](https://easywechat.com/)

## 🆘 获取帮助

如果遇到问题，请：

1. 查看日志文件
2. 检查配置是否正确
3. 参考使用示例
4. 联系技术支持

---

**祝您使用愉快！** 🎉
