<?php

namespace app\common\third_official_link\platforms\wechat\work;

use app\common\third_official_link\contracts\CustomerServiceInterface;
use app\common\third_official_link\platforms\wechat\BaseWechatCustomerService;
use EasyWeChat\Work\Application;
use think\facade\Log;
use think\facade\Cache;

/**
 * 企业微信客服服务
 * 基于EasyWeChat 6.x实现企业微信客服功能
 */
class WorkCustomerService extends BaseWechatCustomerService implements CustomerServiceInterface
{
    /**
     * 企业微信应用实例
     * @var Application
     */
    protected Application $app;

    /**
     * 平台标识
     * @var string
     */
    protected string $platformIdentifier = 'wechat.work';

    /**
     * 构造函数
     * @param array $config 配置数组
     */
    public function __construct(array $config)
    {
        parent::__construct($config);
        $this->initializeApp();
    }

    /**
     * 初始化企业微信应用
     */
    protected function initializeApp(): void
    {
        $this->app = new Application([
            'corp_id' => $this->config['corp_id'],
            'agent_id' => $this->config['agent_id'],
            'secret' => $this->config['secret'],
            'token' => $this->config['token'] ?? '',
            'aes_key' => $this->config['aes_key'] ?? '',
            
            // HTTP配置
            'http' => [
                'timeout' => $this->config['timeout'] ?? 30,
                'retry' => $this->config['retry_times'] ?? 3,
            ],
            
            // 日志配置
            'log' => [
                'level' => $this->config['log_level'] ?? 'info',
                'file' => $this->config['log_file'] ?? null,
            ],
            
            // 缓存配置
            'cache' => [
                'default' => 'file',
                'stores' => [
                    'file' => [
                        'driver' => 'file',
                        'path' => $this->config['cache_path'] ?? runtime_path('cache/wechat'),
                    ],
                ],
            ],
        ]);
    }

    /**
     * 发送文本消息
     * @param string $userId 用户ID
     * @param string $content 消息内容
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendTextMessage(string $userId, string $content, array $options = []): array
    {
        try {
            $message = [
                'touser' => $userId,
                'msgtype' => 'text',
                'agentid' => $this->config['agent_id'],
                'text' => [
                    'content' => $content
                ],
                'safe' => $options['safe'] ?? 0,
                'enable_id_trans' => $options['enable_id_trans'] ?? 0,
                'enable_duplicate_check' => $options['enable_duplicate_check'] ?? 0,
                'duplicate_check_interval' => $options['duplicate_check_interval'] ?? 1800
            ];

            $response = $this->app->getClient()->postJson('cgi-bin/message/send', $message);
            
            return $this->formatResponse($response, [
                'user_id' => $userId,
                'message_type' => 'text',
                'content' => $content
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'sendTextMessage', [
                'user_id' => $userId,
                'content' => $content
            ]);
        }
    }

    /**
     * 发送图片消息
     * @param string $userId 用户ID
     * @param string $mediaId 媒体ID
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendImageMessage(string $userId, string $mediaId, array $options = []): array
    {
        try {
            $message = [
                'touser' => $userId,
                'msgtype' => 'image',
                'agentid' => $this->config['agent_id'],
                'image' => [
                    'media_id' => $mediaId
                ],
                'safe' => $options['safe'] ?? 0
            ];

            $response = $this->app->getClient()->postJson('cgi-bin/message/send', $message);
            
            return $this->formatResponse($response, [
                'user_id' => $userId,
                'message_type' => 'image',
                'media_id' => $mediaId
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'sendImageMessage', [
                'user_id' => $userId,
                'media_id' => $mediaId
            ]);
        }
    }

    /**
     * 发送卡片消息
     * @param string $userId 用户ID
     * @param array $cardData 卡片数据
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendCardMessage(string $userId, array $cardData, array $options = []): array
    {
        try {
            $message = [
                'touser' => $userId,
                'msgtype' => 'textcard',
                'agentid' => $this->config['agent_id'],
                'textcard' => [
                    'title' => $cardData['title'] ?? '',
                    'description' => $cardData['description'] ?? '',
                    'url' => $cardData['url'] ?? '',
                    'btntxt' => $cardData['btntxt'] ?? '详情'
                ]
            ];

            $response = $this->app->getClient()->postJson('cgi-bin/message/send', $message);
            
            return $this->formatResponse($response, [
                'user_id' => $userId,
                'message_type' => 'textcard',
                'card_data' => $cardData
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'sendCardMessage', [
                'user_id' => $userId,
                'card_data' => $cardData
            ]);
        }
    }

    /**
     * 发送模板消息（企业微信使用应用消息）
     * @param string $userId 用户ID
     * @param string $templateId 模板ID（企业微信中不使用）
     * @param array $data 消息数据
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendTemplateMessage(string $userId, string $templateId, array $data, array $options = []): array
    {
        // 企业微信使用markdown消息作为模板消息的替代
        try {
            $content = $this->buildMarkdownContent($data);
            
            $message = [
                'touser' => $userId,
                'msgtype' => 'markdown',
                'agentid' => $this->config['agent_id'],
                'markdown' => [
                    'content' => $content
                ]
            ];

            $response = $this->app->getClient()->postJson('cgi-bin/message/send', $message);
            
            return $this->formatResponse($response, [
                'user_id' => $userId,
                'message_type' => 'markdown',
                'template_id' => $templateId,
                'data' => $data
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'sendTemplateMessage', [
                'user_id' => $userId,
                'template_id' => $templateId,
                'data' => $data
            ]);
        }
    }

    /**
     * 处理接收到的消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    public function handleIncomingMessage(array $message): array
    {
        try {
            $msgType = $message['type'] ?? 'text';
            $fromUser = $message['from_user_id'] ?? '';
            $content = $message['content'] ?? '';

            // 记录接收消息
            Log::info('Received work wechat message', [
                'from_user' => $fromUser,
                'msg_type' => $msgType,
                'content' => $content
            ]);

            // 根据消息类型处理
            switch ($msgType) {
                case 'text':
                    return $this->handleTextMessage($message);
                case 'image':
                    return $this->handleImageMessage($message);
                case 'voice':
                    return $this->handleVoiceMessage($message);
                case 'video':
                    return $this->handleVideoMessage($message);
                case 'file':
                    return $this->handleFileMessage($message);
                case 'location':
                    return $this->handleLocationMessage($message);
                case 'event':
                    return $this->handleEventMessage($message);
                default:
                    return $this->handleUnknownMessage($message);
            }
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'handleIncomingMessage', $message);
        }
    }

    /**
     * 获取用户信息
     * @param string $userId 用户ID
     * @return array 用户信息
     */
    public function getUserInfo(string $userId): array
    {
        try {
            $response = $this->app->getClient()->get('cgi-bin/user/get', [
                'userid' => $userId
            ]);

            return $this->formatResponse($response, [
                'user_id' => $userId,
                'action' => 'get_user_info'
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'getUserInfo', ['user_id' => $userId]);
        }
    }

    /**
     * 上传媒体文件
     * @param string $filePath 文件路径
     * @param string $type 文件类型
     * @return array 上传结果
     */
    public function uploadMedia(string $filePath, string $type): array
    {
        try {
            $response = $this->app->getClient()->upload('cgi-bin/media/upload', [
                'type' => $type,
                'media' => fopen($filePath, 'r')
            ]);

            return $this->formatResponse($response, [
                'file_path' => $filePath,
                'media_type' => $type
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'uploadMedia', [
                'file_path' => $filePath,
                'type' => $type
            ]);
        }
    }

    /**
     * 下载媒体文件
     * @param string $mediaId 媒体ID
     * @return array 下载结果
     */
    public function downloadMedia(string $mediaId): array
    {
        try {
            $response = $this->app->getClient()->get('cgi-bin/media/get', [
                'media_id' => $mediaId
            ]);

            return [
                'success' => true,
                'data' => [
                    'media_id' => $mediaId,
                    'content' => $response->getBody()->getContents(),
                    'content_type' => $response->getHeaderLine('Content-Type')
                ]
            ];
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'downloadMedia', ['media_id' => $mediaId]);
        }
    }

    /**
     * 检查连接状态
     * @return array 连接状态
     */
    public function checkConnection(): array
    {
        try {
            // 通过获取access_token来检查连接
            $token = $this->app->getAccessToken()->getToken();
            
            return [
                'success' => true,
                'connected' => !empty($token),
                'platform' => $this->platformIdentifier,
                'timestamp' => time()
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'connected' => false,
                'platform' => $this->platformIdentifier,
                'error' => $e->getMessage(),
                'timestamp' => time()
            ];
        }
    }

    /**
     * 获取平台限制信息
     * @return array 限制信息
     */
    public function getRateLimits(): array
    {
        return [
            'platform' => $this->platformIdentifier,
            'limits' => [
                'message_per_minute' => 200,
                'message_per_day' => 200000,
                'media_upload_size' => '20MB',
                'supported_media_types' => ['image', 'voice', 'video', 'file', 'textcard', 'news', 'markdown']
            ]
        ];
    }

    /**
     * 发送自定义消息
     * @param array $message 消息数据
     * @return mixed 发送结果
     */
    protected function sendCustomMessage(array $message)
    {
        return $this->app->getClient()->postJson('cgi-bin/message/send', $message);
    }

    /**
     * 构建Markdown内容
     * @param array $data 数据
     * @return string Markdown内容
     */
    protected function buildMarkdownContent(array $data): string
    {
        $content = '';
        
        foreach ($data as $key => $value) {
            if (is_array($value) && isset($value['value'])) {
                $content .= "**{$key}**: {$value['value']}\n";
            } else {
                $content .= "**{$key}**: {$value}\n";
            }
        }
        
        return $content;
    }

    /**
     * 处理文本消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    protected function handleTextMessage(array $message): array
    {
        return [
            'success' => true,
            'message_type' => 'text',
            'processed' => true,
            'data' => $message
        ];
    }

    /**
     * 处理图片消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    protected function handleImageMessage(array $message): array
    {
        return [
            'success' => true,
            'message_type' => 'image',
            'processed' => true,
            'data' => $message
        ];
    }

    /**
     * 处理语音消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    protected function handleVoiceMessage(array $message): array
    {
        return [
            'success' => true,
            'message_type' => 'voice',
            'processed' => true,
            'data' => $message
        ];
    }

    /**
     * 处理视频消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    protected function handleVideoMessage(array $message): array
    {
        return [
            'success' => true,
            'message_type' => 'video',
            'processed' => true,
            'data' => $message
        ];
    }

    /**
     * 处理文件消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    protected function handleFileMessage(array $message): array
    {
        return [
            'success' => true,
            'message_type' => 'file',
            'processed' => true,
            'data' => $message
        ];
    }

    /**
     * 处理位置消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    protected function handleLocationMessage(array $message): array
    {
        return [
            'success' => true,
            'message_type' => 'location',
            'processed' => true,
            'data' => $message
        ];
    }

    /**
     * 处理事件消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    protected function handleEventMessage(array $message): array
    {
        $event = $message['metadata']['Event'] ?? 'unknown';
        
        Log::info('Work wechat event received', [
            'event' => $event,
            'from_user' => $message['from_user_id'] ?? ''
        ]);

        return [
            'success' => true,
            'message_type' => 'event',
            'event_type' => $event,
            'processed' => true,
            'data' => $message
        ];
    }

    /**
     * 处理未知消息
     * @param array $message 消息数据
     * @return array 处理结果
     */
    protected function handleUnknownMessage(array $message): array
    {
        Log::warning('Unknown message type received', [
            'platform' => $this->platformIdentifier,
            'message' => $message
        ]);

        return [
            'success' => true,
            'message_type' => 'unknown',
            'processed' => false,
            'data' => $message
        ];
    }

    // 实现抽象方法
    protected function fetchSessionsFromStorage(array $filters): array
    {
        return [];
    }

    protected function fetchMessagesFromStorage(string $sessionId, int $limit, int $offset): array
    {
        return [];
    }

    protected function saveTransferRecord(array $data): void
    {
        Log::info('Session transfer recorded', $data);
    }

    protected function saveSessionEndRecord(array $data): void
    {
        Log::info('Session end recorded', $data);
    }

    protected function clearSessionCache(string $sessionId): void
    {
        Cache::clear();
    }

    protected function calculateStatistics(string $startDate, string $endDate, array $metrics): array
    {
        return [
            'message_count' => 0,
            'user_count' => 0,
            'session_count' => 0,
            'response_time_avg' => 0
        ];
    }
}
