<?php

namespace app\common\third_official_link\platforms\wechat;

use app\common\third_official_link\contracts\CustomerServiceInterface;
use think\facade\Log;
use think\facade\Cache;

/**
 * 微信基础客服服务
 * 提供微信平台通用的客服功能实现
 */
abstract class BaseWechatCustomerService
{
    /**
     * 配置信息
     * @var array
     */
    protected array $config;

    /**
     * 平台标识
     * @var string
     */
    protected string $platformIdentifier;

    /**
     * 会话缓存前缀
     * @var string
     */
    protected string $sessionCachePrefix = 'wechat_session_';

    /**
     * 统计缓存前缀
     * @var string
     */
    protected string $statsCachePrefix = 'wechat_stats_';

    /**
     * 构造函数
     * @param array $config 配置数组
     */
    public function __construct(array $config)
    {
        $this->config = $config;
        $this->validateConfig();
    }

    /**
     * 发送语音消息
     * @param string $userId 用户ID
     * @param string $mediaId 媒体ID
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendVoiceMessage(string $userId, string $mediaId, array $options = []): array
    {
        try {
            $message = [
                'touser' => $userId,
                'msgtype' => 'voice',
                'voice' => [
                    'media_id' => $mediaId
                ]
            ];

            $response = $this->sendCustomMessage($message);
            
            return $this->formatResponse($response, [
                'user_id' => $userId,
                'message_type' => 'voice',
                'media_id' => $mediaId
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'sendVoiceMessage', [
                'user_id' => $userId,
                'media_id' => $mediaId
            ]);
        }
    }

    /**
     * 发送视频消息
     * @param string $userId 用户ID
     * @param string $mediaId 媒体ID
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendVideoMessage(string $userId, string $mediaId, array $options = []): array
    {
        try {
            $message = [
                'touser' => $userId,
                'msgtype' => 'video',
                'video' => [
                    'media_id' => $mediaId,
                    'thumb_media_id' => $options['thumb_media_id'] ?? '',
                    'title' => $options['title'] ?? '',
                    'description' => $options['description'] ?? ''
                ]
            ];

            $response = $this->sendCustomMessage($message);
            
            return $this->formatResponse($response, [
                'user_id' => $userId,
                'message_type' => 'video',
                'media_id' => $mediaId
            ]);
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'sendVideoMessage', [
                'user_id' => $userId,
                'media_id' => $mediaId
            ]);
        }
    }

    /**
     * 获取会话列表
     * @param array $filters 过滤条件
     * @return array 会话列表
     */
    public function getSessionList(array $filters = []): array
    {
        try {
            $cacheKey = $this->sessionCachePrefix . 'list_' . md5(json_encode($filters));
            $cached = Cache::get($cacheKey);
            
            if ($cached !== null) {
                return $cached;
            }

            // 从数据库或其他存储获取会话列表
            $sessions = $this->fetchSessionsFromStorage($filters);
            
            $result = [
                'success' => true,
                'data' => $sessions,
                'total' => count($sessions),
                'platform' => $this->platformIdentifier
            ];

            Cache::set($cacheKey, $result, 300); // 缓存5分钟
            return $result;
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'getSessionList', $filters);
        }
    }

    /**
     * 获取会话历史消息
     * @param string $sessionId 会话ID
     * @param array $options 选项
     * @return array 历史消息
     */
    public function getSessionHistory(string $sessionId, array $options = []): array
    {
        try {
            $limit = $options['limit'] ?? 50;
            $offset = $options['offset'] ?? 0;
            
            $cacheKey = $this->sessionCachePrefix . "history_{$sessionId}_{$limit}_{$offset}";
            $cached = Cache::get($cacheKey);
            
            if ($cached !== null) {
                return $cached;
            }

            // 从数据库获取历史消息
            $messages = $this->fetchMessagesFromStorage($sessionId, $limit, $offset);
            
            $result = [
                'success' => true,
                'data' => $messages,
                'session_id' => $sessionId,
                'total' => count($messages),
                'platform' => $this->platformIdentifier
            ];

            Cache::set($cacheKey, $result, 600); // 缓存10分钟
            return $result;
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'getSessionHistory', [
                'session_id' => $sessionId,
                'options' => $options
            ]);
        }
    }

    /**
     * 设置客服状态
     * @param string $status 状态
     * @return array 设置结果
     */
    public function setServiceStatus(string $status): array
    {
        try {
            $validStatuses = ['online', 'offline', 'busy'];
            
            if (!in_array($status, $validStatuses)) {
                throw new \InvalidArgumentException("Invalid status: {$status}");
            }

            // 更新状态到缓存
            $cacheKey = $this->sessionCachePrefix . 'service_status';
            Cache::set($cacheKey, $status, 3600);

            return [
                'success' => true,
                'status' => $status,
                'platform' => $this->platformIdentifier,
                'timestamp' => time()
            ];
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'setServiceStatus', ['status' => $status]);
        }
    }

    /**
     * 转接会话
     * @param string $sessionId 会话ID
     * @param string $targetServiceId 目标客服ID
     * @param string $reason 转接原因
     * @return array 转接结果
     */
    public function transferSession(string $sessionId, string $targetServiceId, string $reason = ''): array
    {
        try {
            // 记录转接信息
            $transferData = [
                'session_id' => $sessionId,
                'target_service_id' => $targetServiceId,
                'reason' => $reason,
                'timestamp' => time(),
                'platform' => $this->platformIdentifier
            ];

            // 保存转接记录
            $this->saveTransferRecord($transferData);

            return [
                'success' => true,
                'data' => $transferData
            ];
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'transferSession', [
                'session_id' => $sessionId,
                'target_service_id' => $targetServiceId,
                'reason' => $reason
            ]);
        }
    }

    /**
     * 结束会话
     * @param string $sessionId 会话ID
     * @param string $reason 结束原因
     * @return array 结束结果
     */
    public function endSession(string $sessionId, string $reason = ''): array
    {
        try {
            $endData = [
                'session_id' => $sessionId,
                'reason' => $reason,
                'end_time' => time(),
                'platform' => $this->platformIdentifier
            ];

            // 保存结束记录
            $this->saveSessionEndRecord($endData);

            // 清除会话缓存
            $this->clearSessionCache($sessionId);

            return [
                'success' => true,
                'data' => $endData
            ];
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'endSession', [
                'session_id' => $sessionId,
                'reason' => $reason
            ]);
        }
    }

    /**
     * 获取平台配置
     * @return array 配置信息
     */
    public function getConfig(): array
    {
        return [
            'platform' => $this->platformIdentifier,
            'config' => array_merge($this->config, [
                'secret' => '***', // 隐藏敏感信息
                'aes_key' => '***'
            ])
        ];
    }

    /**
     * 验证Webhook签名
     * @param array $data 请求数据
     * @param string $signature 签名
     * @return bool 验证结果
     */
    public function verifyWebhookSignature(array $data, string $signature): bool
    {
        try {
            $token = $this->config['token'] ?? '';
            $timestamp = $data['timestamp'] ?? '';
            $nonce = $data['nonce'] ?? '';

            $tmpArr = [$token, $timestamp, $nonce];
            sort($tmpArr, SORT_STRING);
            $tmpStr = implode($tmpArr);
            $tmpStr = sha1($tmpStr);

            return $tmpStr === $signature;
            
        } catch (\Exception $e) {
            Log::error('Webhook signature verification failed', [
                'platform' => $this->platformIdentifier,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 处理Webhook事件
     * @param array $event 事件数据
     * @return array 处理结果
     */
    public function handleWebhookEvent(array $event): array
    {
        try {
            $eventType = $event['MsgType'] ?? $event['Event'] ?? 'unknown';
            
            Log::info('Webhook event received', [
                'platform' => $this->platformIdentifier,
                'event_type' => $eventType,
                'from_user' => $event['FromUserName'] ?? ''
            ]);

            return [
                'success' => true,
                'event_type' => $eventType,
                'processed' => true,
                'platform' => $this->platformIdentifier
            ];
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'handleWebhookEvent', $event);
        }
    }

    /**
     * 获取平台标识
     * @return string 平台标识
     */
    public function getPlatformIdentifier(): string
    {
        return $this->platformIdentifier;
    }

    /**
     * 批量发送消息
     * @param array $messages 消息列表
     * @return array 发送结果
     */
    public function batchSendMessages(array $messages): array
    {
        $results = [];
        
        foreach ($messages as $index => $messageData) {
            try {
                $userId = $messageData['user_id'] ?? '';
                $message = $messageData['message'] ?? '';
                $type = $messageData['type'] ?? 'text';
                $options = $messageData['options'] ?? [];

                switch ($type) {
                    case 'text':
                        $result = $this->sendTextMessage($userId, $message, $options);
                        break;
                    case 'image':
                        $result = $this->sendImageMessage($userId, $message, $options);
                        break;
                    default:
                        $result = ['success' => false, 'error' => 'Unsupported message type'];
                }

                $results[$index] = $result;
                
                // 避免频率限制
                if ($index > 0 && $index % 10 === 0) {
                    usleep(100000); // 休眠100ms
                }
                
            } catch (\Exception $e) {
                $results[$index] = $this->handleError($e, 'batchSendMessages', $messageData);
            }
        }

        return [
            'success' => true,
            'total' => count($messages),
            'results' => $results,
            'platform' => $this->platformIdentifier
        ];
    }

    /**
     * 注意：自动回复功能已移至VChat系统
     * 第三方平台不再提供独立的自动回复功能
     * 请在VChat配置中设置自动回复规则
     */

    /**
     * 获取统计数据
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param array $metrics 统计指标
     * @return array 统计数据
     */
    public function getStatistics(string $startDate, string $endDate, array $metrics = []): array
    {
        try {
            $cacheKey = $this->statsCachePrefix . md5($startDate . $endDate . implode(',', $metrics));
            $cached = Cache::get($cacheKey);
            
            if ($cached !== null) {
                return $cached;
            }

            // 获取统计数据
            $stats = $this->calculateStatistics($startDate, $endDate, $metrics);
            
            $result = [
                'success' => true,
                'data' => $stats,
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ],
                'platform' => $this->platformIdentifier
            ];

            Cache::set($cacheKey, $result, 1800); // 缓存30分钟
            return $result;
            
        } catch (\Exception $e) {
            return $this->handleError($e, 'getStatistics', [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'metrics' => $metrics
            ]);
        }
    }

    /**
     * 格式化响应
     * @param mixed $response API响应
     * @param array $context 上下文信息
     * @return array 格式化后的响应
     */
    protected function formatResponse($response, array $context = []): array
    {
        $responseArray = is_array($response) ? $response : $response->toArray();
        
        $success = ($responseArray['errcode'] ?? 0) === 0;
        
        return [
            'success' => $success,
            'data' => $success ? $responseArray : null,
            'error' => $success ? null : ($responseArray['errmsg'] ?? 'Unknown error'),
            'error_code' => $responseArray['errcode'] ?? 0,
            'context' => $context,
            'platform' => $this->platformIdentifier,
            'timestamp' => time()
        ];
    }

    /**
     * 处理错误
     * @param \Exception $e 异常
     * @param string $method 方法名
     * @param array $context 上下文
     * @return array 错误响应
     */
    protected function handleError(\Exception $e, string $method, array $context = []): array
    {
        Log::error("WeChat customer service error in {$method}", [
            'platform' => $this->platformIdentifier,
            'error' => $e->getMessage(),
            'context' => $context,
            'trace' => $e->getTraceAsString()
        ]);

        return [
            'success' => false,
            'error' => $e->getMessage(),
            'method' => $method,
            'context' => $context,
            'platform' => $this->platformIdentifier,
            'timestamp' => time()
        ];
    }

    /**
     * 验证配置
     * @throws \InvalidArgumentException
     */
    protected function validateConfig(): void
    {
        $requiredFields = ['app_id', 'secret'];
        
        foreach ($requiredFields as $field) {
            if (empty($this->config[$field])) {
                throw new \InvalidArgumentException("Missing required config field: {$field}");
            }
        }
    }

    /**
     * 发送自定义消息（抽象方法，由子类实现）
     * @param array $message 消息数据
     * @return mixed 发送结果
     */
    abstract protected function sendCustomMessage(array $message);

    /**
     * 从存储获取会话列表（抽象方法，由子类实现）
     * @param array $filters 过滤条件
     * @return array 会话列表
     */
    abstract protected function fetchSessionsFromStorage(array $filters): array;

    /**
     * 从存储获取消息列表（抽象方法，由子类实现）
     * @param string $sessionId 会话ID
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array 消息列表
     */
    abstract protected function fetchMessagesFromStorage(string $sessionId, int $limit, int $offset): array;

    /**
     * 保存转接记录（抽象方法，由子类实现）
     * @param array $data 转接数据
     * @return void
     */
    abstract protected function saveTransferRecord(array $data): void;

    /**
     * 保存会话结束记录（抽象方法，由子类实现）
     * @param array $data 结束数据
     * @return void
     */
    abstract protected function saveSessionEndRecord(array $data): void;

    /**
     * 清除会话缓存（抽象方法，由子类实现）
     * @param string $sessionId 会话ID
     * @return void
     */
    abstract protected function clearSessionCache(string $sessionId): void;

    /**
     * 计算统计数据（抽象方法，由子类实现）
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param array $metrics 统计指标
     * @return array 统计数据
     */
    abstract protected function calculateStatistics(string $startDate, string $endDate, array $metrics): array;
}
