<?php
/**
 * 小红书平台客服服务
 * 实现小红书私信、评论回复等客服功能
 */

namespace app\common\third_official_link\platforms\social_media\xiaohongshu;

use app\common\third_official_link\platforms\social_media\BaseSocialMediaService;
use think\facade\Log;

class XiaohongshuService extends BaseSocialMediaService
{
    /**
     * HTTP客户端
     * @var \GuzzleHttp\Client
     */
    private $httpClient;

    /**
     * 获取平台标识
     */
    protected function getPlatformIdentifier(): string
    {
        return 'xiaohongshu';
    }

    /**
     * 获取API基础URL
     */
    protected function getApiBaseUrl(): string
    {
        return 'https://api.xiaohongshu.com/v1/';
    }

    /**
     * 获取必需配置字段
     */
    protected function getRequiredConfigFields(): array
    {
        return ['app_id', 'app_secret', 'access_token'];
    }

    /**
     * 初始化API客户端
     */
    protected function initializeApiClient(): void
    {
        $this->httpClient = new \GuzzleHttp\Client([
            'base_uri' => $this->apiBaseUrl,
            'timeout' => 30,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
                'User-Agent' => 'VChat-XiaohongshuBot/1.0'
            ]
        ]);
    }

    /**
     * 构建消息数据
     */
    protected function buildMessageData(string $userId, string $content, string $type, array $options): array
    {
        $messageData = [
            'to_user_id' => $userId,
            'message_type' => $type,
            'timestamp' => time()
        ];

        switch ($type) {
            case 'text':
                $messageData['text'] = [
                    'content' => $content
                ];
                break;

            case 'image':
                $messageData['image'] = [
                    'media_id' => $options['media_id'] ?? '',
                    'caption' => $content
                ];
                break;

            case 'video':
                $messageData['video'] = [
                    'media_id' => $options['media_id'] ?? '',
                    'caption' => $content,
                    'duration' => $options['duration'] ?? 0
                ];
                break;

            case 'link':
                $messageData['link'] = [
                    'url' => $options['url'] ?? '',
                    'title' => $content,
                    'description' => $options['description'] ?? '',
                    'thumbnail' => $options['thumbnail'] ?? ''
                ];
                break;

            default:
                throw new \InvalidArgumentException("Unsupported message type: {$type}");
        }

        return $messageData;
    }

    /**
     * 发送到API
     */
    protected function sendToApi(array $messageData): array
    {
        try {
            $response = $this->httpClient->post('messages/send', [
                'json' => $messageData
            ]);

            $body = $response->getBody()->getContents();
            $data = json_decode($body, true);

            if ($response->getStatusCode() === 200 && ($data['success'] ?? false)) {
                return [
                    'success' => true,
                    'message_id' => $data['data']['message_id'] ?? '',
                    'timestamp' => $data['data']['timestamp'] ?? time()
                ];
            } else {
                throw new \Exception($data['message'] ?? 'API request failed');
            }

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            throw new \Exception('HTTP request failed: ' . $e->getMessage());
        }
    }

    /**
     * 解析Webhook消息
     */
    protected function parseWebhookMessage(array $webhookData): array
    {
        // 小红书Webhook消息格式解析
        return [
            'message_id' => $webhookData['message_id'] ?? '',
            'from_user_id' => $webhookData['from_user']['user_id'] ?? '',
            'from_user_name' => $webhookData['from_user']['nickname'] ?? '',
            'message_type' => $webhookData['message']['type'] ?? 'text',
            'content' => $this->extractMessageContent($webhookData['message'] ?? []),
            'timestamp' => $webhookData['timestamp'] ?? time(),
            'conversation_id' => $webhookData['conversation_id'] ?? '',
            'platform_data' => $webhookData
        ];
    }

    /**
     * 提取消息内容
     */
    private function extractMessageContent(array $message): string
    {
        switch ($message['type'] ?? 'text') {
            case 'text':
                return $message['text']['content'] ?? '';
            case 'image':
                return $message['image']['caption'] ?? '[图片]';
            case 'video':
                return $message['video']['caption'] ?? '[视频]';
            case 'link':
                return $message['link']['title'] ?? '[链接]';
            default:
                return '[未知消息类型]';
        }
    }

    /**
     * 标准化消息格式
     */
    protected function standardizeMessage(array $messageData): array
    {
        return [
            'platform' => 'xiaohongshu',
            'platform_name' => '小红书',
            'platform_icon' => 'xiaohongshu',
            'platform_color' => '#FF2442',
            'message_id' => $messageData['message_id'],
            'from_user_id' => $messageData['from_user_id'],
            'from_user_name' => $messageData['from_user_name'],
            'content' => $messageData['content'],
            'content_type' => $this->mapMessageType($messageData['message_type']),
            'timestamp' => $messageData['timestamp'],
            'extra' => [
                'conversation_id' => $messageData['conversation_id'],
                'platform_data' => $messageData['platform_data']
            ]
        ];
    }

    /**
     * 映射消息类型
     */
    private function mapMessageType(string $platformType): string
    {
        $mapping = [
            'text' => 'text',
            'image' => 'image',
            'video' => 'video',
            'link' => 'link'
        ];

        return $mapping[$platformType] ?? 'text';
    }

    /**
     * 从API获取用户信息
     */
    protected function fetchUserInfoFromApi(string $userId): array
    {
        try {
            $response = $this->httpClient->get("users/{$userId}");
            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['success'] ?? false) {
                $user = $data['data']['user'] ?? [];
                return [
                    'success' => true,
                    'data' => [
                        'user_id' => $user['user_id'] ?? $userId,
                        'nickname' => $user['nickname'] ?? '',
                        'avatar' => $user['avatar'] ?? '',
                        'gender' => $user['gender'] ?? '',
                        'location' => $user['location'] ?? '',
                        'follower_count' => $user['follower_count'] ?? 0,
                        'following_count' => $user['following_count'] ?? 0,
                        'note_count' => $user['note_count'] ?? 0,
                        'platform' => 'xiaohongshu'
                    ]
                ];
            } else {
                throw new \Exception($data['message'] ?? 'Failed to fetch user info');
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => [
                    'user_id' => $userId,
                    'platform' => 'xiaohongshu'
                ]
            ];
        }
    }

    /**
     * 验证Webhook签名
     */
    protected function verifyWebhookSignature(array $webhookData): bool
    {
        // 小红书Webhook签名验证逻辑
        $signature = $_SERVER['HTTP_X_XHS_SIGNATURE'] ?? '';
        $timestamp = $_SERVER['HTTP_X_XHS_TIMESTAMP'] ?? '';
        
        if (empty($signature) || empty($timestamp)) {
            return false;
        }

        // 验证时间戳（5分钟内有效）
        if (abs(time() - intval($timestamp)) > 300) {
            return false;
        }

        // 计算签名
        $payload = json_encode($webhookData);
        $expectedSignature = hash_hmac('sha256', $timestamp . $payload, $this->config['app_secret']);
        
        return hash_equals($signature, $expectedSignature);
    }

    /**
     * 获取健康检查端点
     */
    protected function getHealthCheckEndpoint(): string
    {
        return 'health';
    }

    /**
     * 从API获取统计数据
     */
    protected function fetchStatisticsFromApi(string $startDate, string $endDate): array
    {
        try {
            $response = $this->httpClient->get('analytics/messages', [
                'query' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'data' => [
                    'message_count' => $data['data']['message_count'] ?? 0,
                    'user_count' => $data['data']['user_count'] ?? 0,
                    'response_rate' => $data['data']['response_rate'] ?? 0,
                    'avg_response_time' => $data['data']['avg_response_time'] ?? 0,
                    'platform' => 'xiaohongshu',
                    'period' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate
                    ]
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 验证媒体文件
     */
    protected function validateMediaFile(string $filePath, string $type): void
    {
        $fileSize = filesize($filePath);
        $maxSizes = [
            'image' => 10 * 1024 * 1024, // 10MB
            'video' => 100 * 1024 * 1024, // 100MB
        ];

        if (isset($maxSizes[$type]) && $fileSize > $maxSizes[$type]) {
            throw new \InvalidArgumentException("File size exceeds limit for type: {$type}");
        }

        $allowedTypes = [
            'image' => ['jpg', 'jpeg', 'png', 'gif'],
            'video' => ['mp4', 'mov', 'avi']
        ];

        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        if (isset($allowedTypes[$type]) && !in_array($extension, $allowedTypes[$type])) {
            throw new \InvalidArgumentException("Invalid file type: {$extension} for {$type}");
        }
    }

    /**
     * 上传到API
     */
    protected function uploadToApi(string $filePath, string $type): array
    {
        try {
            $response = $this->httpClient->post('media/upload', [
                'multipart' => [
                    [
                        'name' => 'file',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => basename($filePath)
                    ],
                    [
                        'name' => 'type',
                        'contents' => $type
                    ]
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['success'] ?? false) {
                return [
                    'media_id' => $data['data']['media_id'] ?? '',
                    'media_url' => $data['data']['media_url'] ?? ''
                ];
            } else {
                throw new \Exception($data['message'] ?? 'Upload failed');
            }

        } catch (\Exception $e) {
            throw new \Exception('Media upload failed: ' . $e->getMessage());
        }
    }

    /**
     * 发起API请求
     */
    protected function makeApiRequest(string $method, string $endpoint, array $data = []): array
    {
        try {
            $options = [];
            if (!empty($data)) {
                $options['json'] = $data;
            }

            $response = $this->httpClient->request($method, $endpoint, $options);
            $body = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => $response->getStatusCode() === 200,
                'data' => $body,
                'status_code' => $response->getStatusCode()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 处理API响应
     */
    protected function handleApiResponse(array $response, array $messageData): array
    {
        if ($response['success']) {
            Log::info('小红书消息发送成功', [
                'message_id' => $response['message_id'] ?? '',
                'to_user' => $messageData['to_user_id'] ?? '',
                'type' => $messageData['message_type'] ?? ''
            ]);

            return [
                'success' => true,
                'message_id' => $response['message_id'] ?? '',
                'timestamp' => $response['timestamp'] ?? time(),
                'platform' => 'xiaohongshu'
            ];
        } else {
            return [
                'success' => false,
                'error' => $response['error'] ?? 'Unknown error',
                'platform' => 'xiaohongshu'
            ];
        }
    }
}
