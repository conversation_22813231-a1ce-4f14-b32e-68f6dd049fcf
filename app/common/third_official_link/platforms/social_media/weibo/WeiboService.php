<?php
/**
 * 微博平台客服服务
 * 实现微博私信、评论回复等客服功能
 */

namespace app\common\third_official_link\platforms\social_media\weibo;

use app\common\third_official_link\platforms\social_media\BaseSocialMediaService;
use think\facade\Log;

class WeiboService extends BaseSocialMediaService
{
    /**
     * HTTP客户端
     * @var \GuzzleHttp\Client
     */
    private $httpClient;

    /**
     * 获取平台标识
     */
    protected function getPlatformIdentifier(): string
    {
        return 'weibo';
    }

    /**
     * 获取API基础URL
     */
    protected function getApiBaseUrl(): string
    {
        return 'https://api.weibo.com/2/';
    }

    /**
     * 获取必需配置字段
     */
    protected function getRequiredConfigFields(): array
    {
        return ['app_key', 'app_secret', 'access_token'];
    }

    /**
     * 初始化API客户端
     */
    protected function initializeApiClient(): void
    {
        $this->httpClient = new \GuzzleHttp\Client([
            'base_uri' => $this->apiBaseUrl,
            'timeout' => 30,
            'headers' => [
                'Authorization' => 'OAuth2 ' . $this->accessToken,
                'Content-Type' => 'application/x-www-form-urlencoded',
                'User-Agent' => 'VChat-WeiboBot/1.0'
            ]
        ]);
    }

    /**
     * 构建消息数据
     */
    protected function buildMessageData(string $userId, string $content, string $type, array $options): array
    {
        $messageData = [
            'uid' => $userId,
            'access_token' => $this->accessToken
        ];

        switch ($type) {
            case 'text':
                $messageData['text'] = $content;
                break;

            case 'image':
                $messageData['text'] = $content;
                $messageData['pic'] = $options['pic_url'] ?? '';
                break;

            case 'link':
                $messageData['text'] = $content . ' ' . ($options['url'] ?? '');
                break;

            default:
                throw new \InvalidArgumentException("Unsupported message type: {$type}");
        }

        return $messageData;
    }

    /**
     * 发送到API
     */
    protected function sendToApi(array $messageData): array
    {
        try {
            $response = $this->httpClient->post('direct_messages/new.json', [
                'form_params' => $messageData
            ]);

            $body = $response->getBody()->getContents();
            $data = json_decode($body, true);

            if (isset($data['id'])) {
                return [
                    'success' => true,
                    'message_id' => $data['id'],
                    'created_at' => $data['created_at'] ?? ''
                ];
            } else {
                throw new \Exception($data['error'] ?? 'API request failed');
            }

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            throw new \Exception('HTTP request failed: ' . $e->getMessage());
        }
    }

    /**
     * 解析Webhook消息
     */
    protected function parseWebhookMessage(array $webhookData): array
    {
        // 微博Webhook消息格式解析
        return [
            'message_id' => $webhookData['id'] ?? '',
            'from_user_id' => $webhookData['sender']['id'] ?? '',
            'from_user_name' => $webhookData['sender']['screen_name'] ?? '',
            'message_type' => 'text',
            'content' => $webhookData['text'] ?? '',
            'timestamp' => strtotime($webhookData['created_at'] ?? 'now'),
            'conversation_id' => $webhookData['recipient']['id'] ?? '',
            'platform_data' => $webhookData
        ];
    }

    /**
     * 标准化消息格式
     */
    protected function standardizeMessage(array $messageData): array
    {
        return [
            'platform' => 'weibo',
            'platform_name' => '微博',
            'platform_icon' => 'weibo',
            'platform_color' => '#E6162D',
            'message_id' => $messageData['message_id'],
            'from_user_id' => $messageData['from_user_id'],
            'from_user_name' => $messageData['from_user_name'],
            'content' => $messageData['content'],
            'content_type' => 'text',
            'timestamp' => $messageData['timestamp'],
            'extra' => [
                'conversation_id' => $messageData['conversation_id'],
                'platform_data' => $messageData['platform_data']
            ]
        ];
    }

    /**
     * 从API获取用户信息
     */
    protected function fetchUserInfoFromApi(string $userId): array
    {
        try {
            $response = $this->httpClient->get('users/show.json', [
                'query' => [
                    'uid' => $userId,
                    'access_token' => $this->accessToken
                ]
            ]);
            
            $data = json_decode($response->getBody()->getContents(), true);

            if (isset($data['id'])) {
                return [
                    'success' => true,
                    'data' => [
                        'user_id' => $data['id'],
                        'nickname' => $data['screen_name'] ?? '',
                        'avatar' => $data['profile_image_url'] ?? '',
                        'gender' => $data['gender'] ?? '',
                        'location' => $data['location'] ?? '',
                        'followers_count' => $data['followers_count'] ?? 0,
                        'friends_count' => $data['friends_count'] ?? 0,
                        'statuses_count' => $data['statuses_count'] ?? 0,
                        'platform' => 'weibo'
                    ]
                ];
            } else {
                throw new \Exception($data['error'] ?? 'Failed to fetch user info');
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => [
                    'user_id' => $userId,
                    'platform' => 'weibo'
                ]
            ];
        }
    }

    /**
     * 验证Webhook签名
     */
    protected function verifyWebhookSignature(array $webhookData): bool
    {
        // 微博Webhook签名验证逻辑
        $signature = $_SERVER['HTTP_X_WEIBO_SIGNATURE'] ?? '';
        
        if (empty($signature)) {
            return false;
        }

        $payload = json_encode($webhookData);
        $expectedSignature = hash_hmac('sha1', $payload, $this->config['app_secret']);
        
        return hash_equals($signature, $expectedSignature);
    }

    /**
     * 获取健康检查端点
     */
    protected function getHealthCheckEndpoint(): string
    {
        return 'account/verify_credentials.json';
    }

    /**
     * 从API获取统计数据
     */
    protected function fetchStatisticsFromApi(string $startDate, string $endDate): array
    {
        // 微博API统计数据获取
        return [
            'success' => true,
            'data' => [
                'message_count' => 0,
                'user_count' => 0,
                'platform' => 'weibo',
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ]
        ];
    }

    /**
     * 验证媒体文件
     */
    protected function validateMediaFile(string $filePath, string $type): void
    {
        $fileSize = filesize($filePath);
        $maxSizes = [
            'image' => 5 * 1024 * 1024, // 5MB
        ];

        if (isset($maxSizes[$type]) && $fileSize > $maxSizes[$type]) {
            throw new \InvalidArgumentException("File size exceeds limit for type: {$type}");
        }
    }

    /**
     * 上传到API
     */
    protected function uploadToApi(string $filePath, string $type): array
    {
        try {
            $response = $this->httpClient->post('statuses/upload_pic.json', [
                'multipart' => [
                    [
                        'name' => 'pic',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => basename($filePath)
                    ],
                    [
                        'name' => 'access_token',
                        'contents' => $this->accessToken
                    ]
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (isset($data['pic_id'])) {
                return [
                    'media_id' => $data['pic_id'],
                    'media_url' => $data['pic_url'] ?? ''
                ];
            } else {
                throw new \Exception($data['error'] ?? 'Upload failed');
            }

        } catch (\Exception $e) {
            throw new \Exception('Media upload failed: ' . $e->getMessage());
        }
    }

    /**
     * 发起API请求
     */
    protected function makeApiRequest(string $method, string $endpoint, array $data = []): array
    {
        try {
            $options = [];
            if (!empty($data)) {
                $options['form_params'] = array_merge($data, ['access_token' => $this->accessToken]);
            } else {
                $options['query'] = ['access_token' => $this->accessToken];
            }

            $response = $this->httpClient->request($method, $endpoint, $options);
            $body = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => $response->getStatusCode() === 200 && !isset($body['error']),
                'data' => $body,
                'status_code' => $response->getStatusCode()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 处理API响应
     */
    protected function handleApiResponse(array $response, array $messageData): array
    {
        if ($response['success']) {
            Log::info('微博消息发送成功', [
                'message_id' => $response['message_id'] ?? '',
                'to_user' => $messageData['uid'] ?? ''
            ]);

            return [
                'success' => true,
                'message_id' => $response['message_id'] ?? '',
                'created_at' => $response['created_at'] ?? '',
                'platform' => 'weibo'
            ];
        } else {
            return [
                'success' => false,
                'error' => $response['error'] ?? 'Unknown error',
                'platform' => 'weibo'
            ];
        }
    }
}
