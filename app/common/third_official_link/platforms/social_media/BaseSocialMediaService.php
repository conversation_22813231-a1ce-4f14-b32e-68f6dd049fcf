<?php
/**
 * 新媒体平台基础服务类
 * 为小红书、抖音、微博、快手等新媒体平台提供统一的基础功能
 */

namespace app\common\third_official_link\platforms\social_media;

use app\common\third_official_link\contracts\CustomerServiceInterface;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Config;

abstract class BaseSocialMediaService implements CustomerServiceInterface
{
    /**
     * 平台标识
     * @var string
     */
    protected string $platformIdentifier;

    /**
     * 平台配置
     * @var array
     */
    protected array $config;

    /**
     * API基础URL
     * @var string
     */
    protected string $apiBaseUrl;

    /**
     * 访问令牌
     * @var string
     */
    protected string $accessToken;

    /**
     * 缓存前缀
     * @var string
     */
    protected string $cachePrefix;

    /**
     * 支持的消息类型
     * @var array
     */
    protected array $supportedMessageTypes = [
        'text', 'image', 'video', 'link'
    ];

    /**
     * 构造函数
     * @param array $config 平台配置
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->platformIdentifier = $this->getPlatformIdentifier();
        $this->cachePrefix = "social_media:{$this->platformIdentifier}:";
        
        $this->initializePlatform();
    }

    /**
     * 初始化平台
     */
    protected function initializePlatform(): void
    {
        $this->loadConfig();
        $this->validateConfig();
        $this->initializeApiClient();
    }

    /**
     * 加载配置
     */
    protected function loadConfig(): void
    {
        $platformConfig = Config::get("vchat.third_party_integration.platforms.{$this->platformIdentifier}", []);
        $this->config = array_merge($this->config, $platformConfig);
        
        $this->apiBaseUrl = $this->getApiBaseUrl();
        $this->accessToken = $this->config['access_token'] ?? '';
    }

    /**
     * 验证配置
     */
    protected function validateConfig(): void
    {
        $requiredFields = $this->getRequiredConfigFields();
        
        foreach ($requiredFields as $field) {
            if (empty($this->config[$field])) {
                throw new \InvalidArgumentException("Missing required config field: {$field}");
            }
        }
    }

    /**
     * 发送消息
     * @param string $userId 用户ID
     * @param string $content 消息内容
     * @param string $type 消息类型
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public function sendMessage(string $userId, string $content, string $type = 'text', array $options = []): array
    {
        try {
            // 验证消息类型
            if (!in_array($type, $this->supportedMessageTypes)) {
                throw new \InvalidArgumentException("Unsupported message type: {$type}");
            }

            // 构建消息数据
            $messageData = $this->buildMessageData($userId, $content, $type, $options);
            
            // 发送到平台API
            $response = $this->sendToApi($messageData);
            
            // 处理响应
            return $this->handleApiResponse($response, $messageData);

        } catch (\Exception $e) {
            return $this->handleError($e, 'sendMessage', [
                'user_id' => $userId,
                'content' => $content,
                'type' => $type
            ]);
        }
    }

    /**
     * 批量发送消息
     * @param array $messages 消息列表
     * @return array 发送结果
     */
    public function broadcast(array $messages): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($messages as $index => $message) {
            $result = $this->sendMessage(
                $message['user_id'],
                $message['content'],
                $message['type'] ?? 'text',
                $message['options'] ?? []
            );

            $results[] = [
                'index' => $index,
                'message' => $message,
                'result' => $result
            ];

            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }

            // 避免API限流
            if (isset($message['delay'])) {
                usleep($message['delay'] * 1000);
            }
        }

        return [
            'success' => $failureCount === 0,
            'total' => count($messages),
            'success_count' => $successCount,
            'failure_count' => $failureCount,
            'results' => $results
        ];
    }

    /**
     * 处理接收到的消息
     * @param array $webhookData Webhook数据
     * @return array 处理结果
     */
    public function handleIncomingMessage(array $webhookData): array
    {
        try {
            // 验证Webhook签名
            if (!$this->verifyWebhookSignature($webhookData)) {
                throw new \Exception('Invalid webhook signature');
            }

            // 解析消息数据
            $messageData = $this->parseWebhookMessage($webhookData);
            
            // 标准化消息格式
            $standardMessage = $this->standardizeMessage($messageData);
            
            return [
                'success' => true,
                'message' => $standardMessage,
                'platform' => $this->platformIdentifier
            ];

        } catch (\Exception $e) {
            return $this->handleError($e, 'handleIncomingMessage', $webhookData);
        }
    }

    /**
     * 获取用户信息
     * @param string $userId 用户ID
     * @return array 用户信息
     */
    public function getUserInfo(string $userId): array
    {
        try {
            $cacheKey = $this->cachePrefix . "user_info:{$userId}";
            $cached = Cache::get($cacheKey);
            
            if ($cached !== null) {
                return $cached;
            }

            // 从API获取用户信息
            $userInfo = $this->fetchUserInfoFromApi($userId);
            
            // 缓存用户信息
            Cache::set($cacheKey, $userInfo, 3600); // 缓存1小时
            
            return $userInfo;

        } catch (\Exception $e) {
            return $this->handleError($e, 'getUserInfo', ['user_id' => $userId]);
        }
    }

    /**
     * 检查平台可用性
     * @return bool 是否可用
     */
    public function isPlatformAvailable(): bool
    {
        try {
            $response = $this->makeApiRequest('GET', $this->getHealthCheckEndpoint());
            return $response['success'] ?? false;
        } catch (\Exception $e) {
            Log::error("Platform availability check failed for {$this->platformIdentifier}", [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取平台统计数据
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 统计数据
     */
    public function getPlatformStatistics(string $startDate = '', string $endDate = ''): array
    {
        try {
            if (empty($startDate)) {
                $startDate = date('Y-m-d', strtotime('-7 days'));
            }
            if (empty($endDate)) {
                $endDate = date('Y-m-d');
            }

            return $this->fetchStatisticsFromApi($startDate, $endDate);

        } catch (\Exception $e) {
            return $this->handleError($e, 'getPlatformStatistics', [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);
        }
    }

    /**
     * 上传媒体文件
     * @param string $filePath 文件路径
     * @param string $type 文件类型
     * @return array 上传结果
     */
    public function uploadMedia(string $filePath, string $type): array
    {
        try {
            if (!file_exists($filePath)) {
                throw new \InvalidArgumentException("File not found: {$filePath}");
            }

            // 验证文件类型和大小
            $this->validateMediaFile($filePath, $type);
            
            // 上传到平台
            $uploadResult = $this->uploadToApi($filePath, $type);
            
            return [
                'success' => true,
                'media_id' => $uploadResult['media_id'] ?? '',
                'media_url' => $uploadResult['media_url'] ?? '',
                'type' => $type,
                'platform' => $this->platformIdentifier
            ];

        } catch (\Exception $e) {
            return $this->handleError($e, 'uploadMedia', [
                'file_path' => $filePath,
                'type' => $type
            ]);
        }
    }

    /**
     * 处理错误
     * @param \Exception $e 异常
     * @param string $method 方法名
     * @param array $context 上下文数据
     * @return array 错误结果
     */
    protected function handleError(\Exception $e, string $method, array $context = []): array
    {
        $errorData = [
            'success' => false,
            'error' => $e->getMessage(),
            'method' => $method,
            'platform' => $this->platformIdentifier,
            'context' => $context
        ];

        Log::error("Social media platform error: {$this->platformIdentifier}", $errorData);

        return $errorData;
    }

    // 抽象方法 - 由具体平台实现
    abstract protected function getPlatformIdentifier(): string;
    abstract protected function getApiBaseUrl(): string;
    abstract protected function getRequiredConfigFields(): array;
    abstract protected function initializeApiClient(): void;
    abstract protected function buildMessageData(string $userId, string $content, string $type, array $options): array;
    abstract protected function sendToApi(array $messageData): array;
    abstract protected function parseWebhookMessage(array $webhookData): array;
    abstract protected function standardizeMessage(array $messageData): array;
    abstract protected function fetchUserInfoFromApi(string $userId): array;
    abstract protected function verifyWebhookSignature(array $webhookData): bool;
    abstract protected function getHealthCheckEndpoint(): string;
    abstract protected function fetchStatisticsFromApi(string $startDate, string $endDate): array;
    abstract protected function validateMediaFile(string $filePath, string $type): void;
    abstract protected function uploadToApi(string $filePath, string $type): array;
    abstract protected function makeApiRequest(string $method, string $endpoint, array $data = []): array;
    abstract protected function handleApiResponse(array $response, array $messageData): array;

    // 废弃的会话管理方法（已统一到VChat）
    public function getSessionList(array $filters = []): array
    {
        return [
            'success' => false,
            'message' => '会话管理功能已统一到VChat系统，请使用VChat的SessionService',
            'redirect' => 'app\\vchat\\services\\SessionService::getSessionList'
        ];
    }

    public function getSessionHistory(string $sessionId, array $options = []): array
    {
        return [
            'success' => false,
            'message' => '会话历史功能已统一到VChat系统，请使用VChat的MessageService',
            'redirect' => 'app\\vchat\\services\\MessageService::getSessionMessages'
        ];
    }

    public function transferSession(string $sessionId, string $targetServiceId, string $reason = ''): array
    {
        return [
            'success' => false,
            'message' => '会话转接功能已统一到VChat系统，请使用VChat的SessionService',
            'redirect' => 'app\\vchat\\services\\SessionService::transferSession'
        ];
    }

    public function endSession(string $sessionId, string $reason = ''): array
    {
        return [
            'success' => false,
            'message' => '结束会话功能已统一到VChat系统，请使用VChat的SessionService',
            'redirect' => 'app\\vchat\\services\\SessionService::endSession'
        ];
    }
}
