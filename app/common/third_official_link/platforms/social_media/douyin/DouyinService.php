<?php
/**
 * 抖音平台客服服务
 * 实现抖音私信、评论回复等客服功能
 */

namespace app\common\third_official_link\platforms\social_media\douyin;

use app\common\third_official_link\platforms\social_media\BaseSocialMediaService;
use think\facade\Log;

class DouyinService extends BaseSocialMediaService
{
    /**
     * HTTP客户端
     * @var \GuzzleHttp\Client
     */
    private $httpClient;

    /**
     * 获取平台标识
     */
    protected function getPlatformIdentifier(): string
    {
        return 'douyin';
    }

    /**
     * 获取API基础URL
     */
    protected function getApiBaseUrl(): string
    {
        return 'https://open.douyin.com/api/v1/';
    }

    /**
     * 获取必需配置字段
     */
    protected function getRequiredConfigFields(): array
    {
        return ['client_key', 'client_secret', 'access_token'];
    }

    /**
     * 初始化API客户端
     */
    protected function initializeApiClient(): void
    {
        $this->httpClient = new \GuzzleHttp\Client([
            'base_uri' => $this->apiBaseUrl,
            'timeout' => 30,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
                'User-Agent' => 'VChat-DouyinBot/1.0'
            ]
        ]);
    }

    /**
     * 构建消息数据
     */
    protected function buildMessageData(string $userId, string $content, string $type, array $options): array
    {
        $messageData = [
            'to_user_id' => $userId,
            'msg_type' => $type,
            'client_msg_id' => uniqid('dy_msg_'),
            'create_time' => time()
        ];

        switch ($type) {
            case 'text':
                $messageData['content'] = [
                    'text' => $content
                ];
                break;

            case 'image':
                $messageData['content'] = [
                    'media_id' => $options['media_id'] ?? '',
                    'media_type' => 'image'
                ];
                break;

            case 'video':
                $messageData['content'] = [
                    'media_id' => $options['media_id'] ?? '',
                    'media_type' => 'video',
                    'duration' => $options['duration'] ?? 0
                ];
                break;

            case 'link':
                $messageData['content'] = [
                    'title' => $content,
                    'description' => $options['description'] ?? '',
                    'url' => $options['url'] ?? '',
                    'pic_url' => $options['thumbnail'] ?? ''
                ];
                break;

            default:
                throw new \InvalidArgumentException("Unsupported message type: {$type}");
        }

        return $messageData;
    }

    /**
     * 发送到API
     */
    protected function sendToApi(array $messageData): array
    {
        try {
            $response = $this->httpClient->post('im/message/send/', [
                'json' => $messageData
            ]);

            $body = $response->getBody()->getContents();
            $data = json_decode($body, true);

            if ($data['error_code'] === 0) {
                return [
                    'success' => true,
                    'message_id' => $data['data']['message_id'] ?? '',
                    'create_time' => $data['data']['create_time'] ?? time()
                ];
            } else {
                throw new \Exception($data['description'] ?? 'API request failed');
            }

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            throw new \Exception('HTTP request failed: ' . $e->getMessage());
        }
    }

    /**
     * 解析Webhook消息
     */
    protected function parseWebhookMessage(array $webhookData): array
    {
        // 抖音Webhook消息格式解析
        $message = $webhookData['content'] ?? [];
        
        return [
            'message_id' => $webhookData['msg_id'] ?? '',
            'from_user_id' => $webhookData['from_user_id'] ?? '',
            'from_user_name' => $webhookData['from_user_name'] ?? '',
            'message_type' => $webhookData['msg_type'] ?? 'text',
            'content' => $this->extractMessageContent($message, $webhookData['msg_type'] ?? 'text'),
            'timestamp' => $webhookData['create_time'] ?? time(),
            'conversation_id' => $webhookData['conversation_id'] ?? '',
            'platform_data' => $webhookData
        ];
    }

    /**
     * 提取消息内容
     */
    private function extractMessageContent(array $content, string $msgType): string
    {
        switch ($msgType) {
            case 'text':
                return $content['text'] ?? '';
            case 'image':
                return '[图片]';
            case 'video':
                return '[视频]';
            case 'audio':
                return '[语音]';
            case 'file':
                return '[文件]';
            default:
                return '[未知消息类型]';
        }
    }

    /**
     * 标准化消息格式
     */
    protected function standardizeMessage(array $messageData): array
    {
        return [
            'platform' => 'douyin',
            'platform_name' => '抖音',
            'platform_icon' => 'douyin',
            'platform_color' => '#000000',
            'message_id' => $messageData['message_id'],
            'from_user_id' => $messageData['from_user_id'],
            'from_user_name' => $messageData['from_user_name'],
            'content' => $messageData['content'],
            'content_type' => $this->mapMessageType($messageData['message_type']),
            'timestamp' => $messageData['timestamp'],
            'extra' => [
                'conversation_id' => $messageData['conversation_id'],
                'platform_data' => $messageData['platform_data']
            ]
        ];
    }

    /**
     * 映射消息类型
     */
    private function mapMessageType(string $platformType): string
    {
        $mapping = [
            'text' => 'text',
            'image' => 'image',
            'video' => 'video',
            'audio' => 'voice',
            'file' => 'file'
        ];

        return $mapping[$platformType] ?? 'text';
    }

    /**
     * 从API获取用户信息
     */
    protected function fetchUserInfoFromApi(string $userId): array
    {
        try {
            $response = $this->httpClient->get('user/info/', [
                'query' => ['open_id' => $userId]
            ]);
            
            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['error_code'] === 0) {
                $user = $data['data'] ?? [];
                return [
                    'success' => true,
                    'data' => [
                        'user_id' => $user['open_id'] ?? $userId,
                        'nickname' => $user['nickname'] ?? '',
                        'avatar' => $user['avatar'] ?? '',
                        'gender' => $user['gender'] ?? 0,
                        'city' => $user['city'] ?? '',
                        'province' => $user['province'] ?? '',
                        'country' => $user['country'] ?? '',
                        'platform' => 'douyin'
                    ]
                ];
            } else {
                throw new \Exception($data['description'] ?? 'Failed to fetch user info');
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => [
                    'user_id' => $userId,
                    'platform' => 'douyin'
                ]
            ];
        }
    }

    /**
     * 验证Webhook签名
     */
    protected function verifyWebhookSignature(array $webhookData): bool
    {
        // 抖音Webhook签名验证逻辑
        $signature = $_SERVER['HTTP_X_DOUYIN_SIGNATURE'] ?? '';
        $timestamp = $_SERVER['HTTP_X_DOUYIN_TIMESTAMP'] ?? '';
        $nonce = $_SERVER['HTTP_X_DOUYIN_NONCE'] ?? '';
        
        if (empty($signature) || empty($timestamp) || empty($nonce)) {
            return false;
        }

        // 验证时间戳（5分钟内有效）
        if (abs(time() - intval($timestamp)) > 300) {
            return false;
        }

        // 计算签名
        $payload = json_encode($webhookData);
        $stringToSign = $timestamp . $nonce . $payload;
        $expectedSignature = hash_hmac('sha256', $stringToSign, $this->config['client_secret']);
        
        return hash_equals($signature, $expectedSignature);
    }

    /**
     * 获取健康检查端点
     */
    protected function getHealthCheckEndpoint(): string
    {
        return 'oauth/client_token/';
    }

    /**
     * 从API获取统计数据
     */
    protected function fetchStatisticsFromApi(string $startDate, string $endDate): array
    {
        try {
            $response = $this->httpClient->get('data/external/item/', [
                'query' => [
                    'date_type' => 7, // 最近7天
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['error_code'] === 0) {
                $stats = $data['data']['result_list'][0] ?? [];
                return [
                    'success' => true,
                    'data' => [
                        'message_count' => $stats['comment_count'] ?? 0,
                        'user_count' => $stats['share_count'] ?? 0,
                        'view_count' => $stats['play_count'] ?? 0,
                        'like_count' => $stats['digg_count'] ?? 0,
                        'platform' => 'douyin',
                        'period' => [
                            'start_date' => $startDate,
                            'end_date' => $endDate
                        ]
                    ]
                ];
            } else {
                throw new \Exception($data['description'] ?? 'Failed to fetch statistics');
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 验证媒体文件
     */
    protected function validateMediaFile(string $filePath, string $type): void
    {
        $fileSize = filesize($filePath);
        $maxSizes = [
            'image' => 20 * 1024 * 1024, // 20MB
            'video' => 128 * 1024 * 1024, // 128MB
        ];

        if (isset($maxSizes[$type]) && $fileSize > $maxSizes[$type]) {
            throw new \InvalidArgumentException("File size exceeds limit for type: {$type}");
        }

        $allowedTypes = [
            'image' => ['jpg', 'jpeg', 'png'],
            'video' => ['mp4']
        ];

        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        if (isset($allowedTypes[$type]) && !in_array($extension, $allowedTypes[$type])) {
            throw new \InvalidArgumentException("Invalid file type: {$extension} for {$type}");
        }
    }

    /**
     * 上传到API
     */
    protected function uploadToApi(string $filePath, string $type): array
    {
        try {
            $response = $this->httpClient->post('file/upload/', [
                'multipart' => [
                    [
                        'name' => 'file',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => basename($filePath)
                    ]
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['error_code'] === 0) {
                return [
                    'media_id' => $data['data']['media_id'] ?? '',
                    'media_url' => $data['data']['media_url'] ?? ''
                ];
            } else {
                throw new \Exception($data['description'] ?? 'Upload failed');
            }

        } catch (\Exception $e) {
            throw new \Exception('Media upload failed: ' . $e->getMessage());
        }
    }

    /**
     * 发起API请求
     */
    protected function makeApiRequest(string $method, string $endpoint, array $data = []): array
    {
        try {
            $options = [];
            if (!empty($data)) {
                $options['json'] = $data;
            }

            $response = $this->httpClient->request($method, $endpoint, $options);
            $body = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => ($body['error_code'] ?? -1) === 0,
                'data' => $body,
                'status_code' => $response->getStatusCode()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 处理API响应
     */
    protected function handleApiResponse(array $response, array $messageData): array
    {
        if ($response['success']) {
            Log::info('抖音消息发送成功', [
                'message_id' => $response['message_id'] ?? '',
                'to_user' => $messageData['to_user_id'] ?? '',
                'type' => $messageData['msg_type'] ?? ''
            ]);

            return [
                'success' => true,
                'message_id' => $response['message_id'] ?? '',
                'create_time' => $response['create_time'] ?? time(),
                'platform' => 'douyin'
            ];
        } else {
            return [
                'success' => false,
                'error' => $response['error'] ?? 'Unknown error',
                'platform' => 'douyin'
            ];
        }
    }
}
