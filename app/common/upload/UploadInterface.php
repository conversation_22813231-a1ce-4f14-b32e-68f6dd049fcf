<?php

namespace app\common\upload;

/**
 * 文件上传接口
 */
interface UploadInterface
{
    /**
     * 上传文件
     * @param string $file 文件路径或文件对象
     * @param string $savePath 保存路径
     * @param array $options 上传选项
     * @return array 上传结果
     */
    public function upload($file, $savePath = '', array $options = []);
    
    /**
     * 删除文件
     * @param string $filePath 文件路径
     * @return bool 删除结果
     */
    public function delete($filePath);
    
    /**
     * 获取文件访问URL
     * @param string $filePath 文件路径
     * @param int $expires 过期时间（秒）
     * @return string 文件URL
     */
    public function getUrl($filePath, $expires = 0);
    
    /**
     * 获取上传服务名称
     * @return string
     */
    public function getServiceName();
    
    /**
     * 获取上传服务提供商
     * @return string
     */
    public function getServiceProvider();
}