<?php

namespace app\common\upload;

use think\facade\Config;

/**
 * 上传服务工厂类
 */
class UploadFactory
{
    /**
     * 上传服务实例缓存
     * @var array
     */
    protected static $instances = [];
    
    /**
     * 获取上传服务实例
     * @param string $type 上传类型，如 'local', 'oss', 'cos'
     * @param string $provider 服务提供商，如 'default', 'aliyun', 'tencent'
     * @return UploadInterface
     * @throws \Exception 当找不到对应的上传服务实现时抛出异常
     */
    public static function getInstance($type = null, $provider = null)
    {
        // 如果未指定类型，则使用配置中的默认类型
        if (is_null($type)) {
            $type = Config::get('upload.default_type', 'local');
        }
        
        // 如果未指定提供商，则使用配置中对应类型的默认提供商
        if (is_null($provider)) {
            $provider = Config::get("upload.{$type}.default_provider", 'default');
        }
        
        // 生成缓存键
        $key = "{$type}:{$provider}";
        
        // 如果实例已存在，则直接返回
        if (isset(self::$instances[$key])) {
            return self::$instances[$key];
        }
        
        // 获取上传服务类
        $class = self::getUploadClass($type, $provider);
        
        // 获取配置
        $config = Config::get("upload.{$type}.providers.{$provider}", []);
        
        // 创建实例
        $instance = new $class($config);
        
        // 缓存实例
        self::$instances[$key] = $instance;
        
        return $instance;
    }
    
    /**
     * 获取上传服务类
     * @param string $type 上传类型
     * @param string $provider 服务提供商
     * @return string 类名
     * @throws \Exception 当找不到对应的上传服务类时抛出异常
     */
    protected static function getUploadClass($type, $provider)
    {
        // 尝试从配置中获取类名
        $class = Config::get("upload.{$type}.providers.{$provider}.class", '');
        
        if (!empty($class) && class_exists($class)) {
            return $class;
        }
        
        // 根据类型和提供商构建类名
        $className = '';
        
        // 处理提供商名称
        if ($provider == 'default') {
            $className = "\\app\\common\\upload\\{$type}\\Default" . ucfirst($type) . "Upload";
        } else {
            $className = "\\app\\common\\upload\\{$type}\\" . ucfirst($provider) . ucfirst($type) . "Upload";
        }
        
        // 检查类是否存在
        if (class_exists($className)) {
            return $className;
        }
        
        // 如果找不到对应的类，抛出异常
        throw new \Exception("Upload service not found: {$type}/{$provider}");
    }
}