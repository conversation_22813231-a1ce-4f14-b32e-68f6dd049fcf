<?php

namespace app\common\upload;

/**
 * 上传功能示例类
 */
class UploadExample
{
    /**
     * 使用默认上传方式（由配置文件upload.php中的default_type决定）
     * @return array|bool 上传结果
     */
    public static function uploadWithDefaultType()
    {
        // 假设有一个表单上传的文件
        $file = request()->file('file');
        
        // 使用系统默认上传方式（由配置文件upload.php中的default_type决定）
        $result = UploadService::upload($file, 'uploads/images');
        
        if ($result === false) {
            return ['code' => 1, 'msg' => UploadService::getError()];
        }
        
        return ['code' => 0, 'msg' => '上传成功', 'data' => $result];
    }
    
    /**
     * 使用本地存储上传
     * @return array|bool 上传结果
     */
    public static function uploadWithLocalStorage()
    {
        // 假设有一个表单上传的文件
        $file = request()->file('file');
        
        // 指定使用本地存储
        $result = UploadService::upload($file, 'uploads/documents', [], 'local');
        
        if ($result === false) {
            return ['code' => 1, 'msg' => UploadService::getError()];
        }
        
        return ['code' => 0, 'msg' => '上传成功', 'data' => $result];
    }
    
    /**
     * 使用阿里云OSS上传
     * @return array|bool 上传结果
     */
    public static function uploadWithAliyunOss()
    {
        // 假设有一个表单上传的文件
        $file = request()->file('file');
        
        // 指定使用阿里云OSS存储
        $result = UploadService::upload($file, 'uploads/images', [], 'oss', 'aliyun');
        
        if ($result === false) {
            return ['code' => 1, 'msg' => UploadService::getError()];
        }
        
        return ['code' => 0, 'msg' => '上传成功', 'data' => $result];
    }
    
    /**
     * 上传本地文件
     * @param string $filePath 本地文件路径
     * @return array|bool 上传结果
     */
    public static function uploadLocalFile($filePath)
    {
        // 上传本地文件
        $result = UploadService::upload($filePath, 'uploads/files');
        
        if ($result === false) {
            return ['code' => 1, 'msg' => UploadService::getError()];
        }
        
        return ['code' => 0, 'msg' => '上传成功', 'data' => $result];
    }
    
    /**
     * 使用自定义选项上传
     * @return array|bool 上传结果
     */
    public static function uploadWithCustomOptions()
    {
        // 假设有一个表单上传的文件
        $file = request()->file('file');
        
        // 自定义选项
        $options = [
            'save_name' => 'custom_' . time() . '.jpg', // 自定义文件名
            'oss_options' => [                          // OSS特有选项（仅在使用OSS时有效）
                'Content-Disposition' => 'inline',
                'x-oss-object-acl' => 'public-read',
            ],
        ];
        
        // 上传文件
        $result = UploadService::upload($file, 'uploads/custom', $options);
        
        if ($result === false) {
            return ['code' => 1, 'msg' => UploadService::getError()];
        }
        
        return ['code' => 0, 'msg' => '上传成功', 'data' => $result];
    }
    
    /**
     * 删除文件示例
     * @param string $filePath 文件路径
     * @return array 删除结果
     */
    public static function deleteFile($filePath)
    {
        // 删除文件
        $result = UploadService::delete($filePath);
        
        if ($result === false) {
            return ['code' => 1, 'msg' => UploadService::getError()];
        }
        
        return ['code' => 0, 'msg' => '删除成功'];
    }
    
    /**
     * 获取文件URL示例
     * @param string $filePath 文件路径
     * @param int $expires 过期时间（秒）
     * @return array 获取结果
     */
    public static function getFileUrl($filePath, $expires = 0)
    {
        // 获取文件URL
        $url = UploadService::getUrl($filePath, $expires);
        
        if ($url === false) {
            return ['code' => 1, 'msg' => UploadService::getError()];
        }
        
        return ['code' => 0, 'msg' => '获取成功', 'data' => ['url' => $url]];
    }
    
    /**
     * 切换上传方式示例
     * @return array 上传结果
     */
    public static function switchUploadType()
    {
        // 假设有一个表单上传的文件
        $file = request()->file('file');
        
        // 根据条件选择不同的上传方式
        $fileSize = $file->getSize();
        
        if ($fileSize > 10 * 1024 * 1024) { // 大于10MB的文件使用OSS
            $result = UploadService::upload($file, 'uploads/large', [], 'oss', 'aliyun');
        } else { // 小文件使用本地存储
            $result = UploadService::upload($file, 'uploads/small', [], 'local');
        }
        
        if ($result === false) {
            return ['code' => 1, 'msg' => UploadService::getError()];
        }
        
        return ['code' => 0, 'msg' => '上传成功', 'data' => $result];
    }
}