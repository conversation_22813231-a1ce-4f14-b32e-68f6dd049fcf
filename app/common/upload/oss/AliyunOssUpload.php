<?php

namespace app\common\upload\oss;

use app\common\upload\AbstractUpload;
use OSS\OssClient;
use OSS\Core\OssException;
use think\File;

/**
 * 阿里云OSS上传实现
 */
class AliyunOssUpload extends AbstractUpload
{
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'oss';
    
    /**
     * 服务提供商
     * @var string
     */
    protected $provider = 'aliyun';
    
    /**
     * OSS客户端
     * @var \OSS\OssClient
     */
    protected $client = null;
    
    /**
     * Bucket名称
     * @var string
     */
    protected $bucket = '';
    
    /**
     * 访问域名
     * @var string
     */
    protected $domain = '';
    
    /**
     * 构造函数
     * @param array $config 配置信息
     * @throws \Exception 初始化失败时抛出异常
     */
    public function __construct(array $config = [])
    {
        parent::__construct($config);
        
        // 检查必要配置
        if (!isset($config['access_key_id']) || !isset($config['access_key_secret']) || !isset($config['endpoint']) || !isset($config['bucket'])) {
            throw new \Exception('Missing required configuration for Aliyun OSS');
        }
        
        $this->bucket = $config['bucket'];
        
        // 设置自定义域名
        if (isset($config['domain'])) {
            $this->domain = rtrim($config['domain'], '/');
        }
        
        try {
            // 初始化OSS客户端
            $this->client = new OssClient(
                $config['access_key_id'],
                $config['access_key_secret'],
                $config['endpoint'],
                isset($config['is_cname']) ? $config['is_cname'] : false
            );
        } catch (OssException $e) {
            throw new \Exception('Initialize Aliyun OSS client failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 上传文件
     * @param string|\think\File $file 文件路径或文件对象
     * @param string $savePath 保存路径
     * @param array $options 上传选项
     * @return array 上传结果
     * @throws \Exception 上传失败时抛出异常
     */
    public function upload($file, $savePath = '', array $options = [])
    {
        try {
            // 处理文件对象
            $fileContent = null;
            $fileInfo = [];
            
            if (is_string($file)) {
                if (!file_exists($file)) {
                    throw new \Exception('File not found: ' . $file);
                }
                $fileContent = file_get_contents($file);
                $fileInfo['name'] = basename($file);
                $fileInfo['size'] = filesize($file);
                $fileInfo['extension'] = pathinfo($file, PATHINFO_EXTENSION);
                $fileInfo['type'] = mime_content_type($file);
            } else {
                $fileContent = file_get_contents($file->getRealPath());
                $fileInfo = $file->getInfo();
            }
            
            // 处理保存路径
            $savePath = trim($savePath, '/');
            if (!empty($savePath)) {
                $savePath .= '/';
            }
            
            // 生成保存文件名
            $saveFileName = isset($options['save_name']) ? $options['save_name'] : '';
            if (empty($saveFileName)) {
                $saveFileName = $this->generateFileName($fileInfo);
            }
            
            // 完整的保存路径
            $fullSavePath = $savePath . $saveFileName;
            
            // 设置OSS选项
            $ossOptions = [];
            
            // 设置Content-Type
            if (!empty($fileInfo['type'])) {
                $ossOptions['Content-Type'] = $fileInfo['type'];
            }
            
            // 合并自定义选项
            if (isset($options['oss_options']) && is_array($options['oss_options'])) {
                $ossOptions = array_merge($ossOptions, $options['oss_options']);
            }
            
            // 执行上传
            $result = $this->client->putObject($this->bucket, $fullSavePath, $fileContent, $ossOptions);
            
            // 返回结果
            return [
                'name' => $fileInfo['name'] ?? basename($fullSavePath),
                'save_name' => $saveFileName,
                'size' => $fileInfo['size'] ?? strlen($fileContent),
                'mime_type' => $fileInfo['type'] ?? ($result['oss-requestheaders']['Content-Type'] ?? ''),
                'extension' => $fileInfo['extension'] ?? pathinfo($fullSavePath, PATHINFO_EXTENSION),
                'path' => $fullSavePath,
                'url' => $this->getUrl($fullSavePath),
                'oss_info' => $result,
            ];
            
        } catch (OssException $e) {
            $this->setError('Aliyun OSS upload failed: ' . $e->getMessage());
            throw new \Exception($this->getError());
        } catch (\Exception $e) {
            $this->setError($e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 删除文件
     * @param string $filePath 文件路径
     * @return bool 删除结果
     * @throws \Exception 删除失败时抛出异常
     */
    public function delete($filePath)
    {
        try {
            $this->client->deleteObject($this->bucket, $filePath);
            return true;
        } catch (OssException $e) {
            $this->setError('Aliyun OSS delete failed: ' . $e->getMessage());
            throw new \Exception($this->getError());
        } catch (\Exception $e) {
            $this->setError($e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取文件访问URL
     * @param string $filePath 文件路径
     * @param int $expires 过期时间（秒），0表示永久
     * @return string 文件URL
     */
    public function getUrl($filePath, $expires = 0)
    {
        // 如果设置了自定义域名，则使用自定义域名
        if (!empty($this->domain)) {
            return $this->domain . '/' . ltrim($filePath, '/');
        }
        
        // 否则生成签名URL
        try {
            if ($expires > 0) {
                // 生成带有签名的临时URL
                $signedUrl = $this->client->signUrl($this->bucket, $filePath, $expires);
                return $signedUrl;
            } else {
                // 生成不带签名的URL
                $endpoint = $this->config['endpoint'];
                // 去除http://或https://前缀
                if (strpos($endpoint, 'http://') === 0) {
                    $endpoint = substr($endpoint, 7);
                } elseif (strpos($endpoint, 'https://') === 0) {
                    $endpoint = substr($endpoint, 8);
                }
                
                return 'https://' . $this->bucket . '.' . $endpoint . '/' . ltrim($filePath, '/');
            }
        } catch (OssException $e) {
            $this->setError('Generate URL failed: ' . $e->getMessage());
            return '';
        }
    }
    
    /**
     * 生成文件名
     * @param array $fileInfo 文件信息
     * @return string 生成的文件名
     */
    protected function generateFileName($fileInfo)
    {
        // 默认使用日期+随机字符串+扩展名的方式生成文件名
        $extension = isset($fileInfo['extension']) ? $fileInfo['extension'] : '';
        $fileName = date('Ymd') . '/' . md5(uniqid(mt_rand(), true));
        
        if (!empty($extension)) {
            $fileName .= '.' . $extension;
        }
        
        return $fileName;
    }
}