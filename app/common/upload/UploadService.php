<?php

namespace app\common\upload;

/**
 * 上传服务管理类
 */
class UploadService
{
    /**
     * 错误信息
     * @var string
     */
    protected static $error = '';
    
    /**
     * 执行上传
     * @param string $file 文件路径或文件对象
     * @param string $savePath 保存路径
     * @param array $options 上传选项
     * @param string $type 上传类型，如 'local', 'oss', 'cos'
     * @param string $provider 服务提供商，如 'default', 'aliyun', 'tencent'
     * @return array|bool 上传结果，成功返回文件信息数组，失败返回false
     */
    public static function upload($file, $savePath = '', array $options = [], $type = null, $provider = null)
    {
        try {
            // 获取上传服务实例
            $service = UploadFactory::getInstance($type, $provider);
            
            try {
                // 执行上传
                $result = $service->upload($file, $savePath, $options);
                return $result;
            } catch (\Exception $e) {
                // 上传失败
                self::$error = $e->getMessage();
                return false;
            }
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }
    
    /**
     * 删除文件
     * @param string $filePath 文件路径
     * @param string $type 上传类型
     * @param string $provider 服务提供商
     * @return bool 删除结果
     */
    public static function delete($filePath, $type = null, $provider = null)
    {
        try {
            // 获取上传服务实例
            $service = UploadFactory::getInstance($type, $provider);
            
            try {
                // 执行删除
                $result = $service->delete($filePath);
                return $result;
            } catch (\Exception $e) {
                // 删除失败
                self::$error = $e->getMessage();
                return false;
            }
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }
    
    /**
     * 获取文件URL
     * @param string $filePath 文件路径
     * @param int $expires 过期时间（秒）
     * @param string $type 上传类型
     * @param string $provider 服务提供商
     * @return string|bool 文件URL，失败返回false
     */
    public static function getUrl($filePath, $expires = 0, $type = null, $provider = null)
    {
        try {
            // 获取上传服务实例
            $service = UploadFactory::getInstance($type, $provider);
            
            try {
                // 获取URL
                $result = $service->getUrl($filePath, $expires);
                return $result;
            } catch (\Exception $e) {
                // 获取失败
                self::$error = $e->getMessage();
                return false;
            }
            
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }
    
    /**
     * 获取错误信息
     * @return string
     */
    public static function getError()
    {
        return self::$error;
    }
}