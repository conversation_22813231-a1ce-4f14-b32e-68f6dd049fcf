<?php

namespace app\common\upload;

/**
 * 文件上传抽象基类
 */
abstract class AbstractUpload implements UploadInterface
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = '';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = '';
    
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];
    
    /**
     * 错误信息
     * @var string
     */
    protected $error = '';
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }
    
    /**
     * 获取上传服务名称
     * @return string
     */
    public function getServiceName()
    {
        return $this->serviceName;
    }
    
    /**
     * 获取上传服务提供商
     * @return string
     */
    public function getServiceProvider()
    {
        return $this->provider;
    }
    
    /**
     * 获取错误信息
     * @return string
     */
    public function getError()
    {
        return $this->error;
    }
    
    /**
     * 设置错误信息
     * @param string $error 错误信息
     * @return $this
     */
    protected function setError($error)
    {
        $this->error = $error;
        return $this;
    }
}