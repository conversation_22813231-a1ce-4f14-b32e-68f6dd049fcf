<?php

namespace app\common\upload\local;

use app\common\upload\AbstractUpload;
use think\facade\Filesystem;
use think\File;
use think\facade\Validate;

/**
 * 默认本地上传实现
 */
class DefaultLocalUpload extends AbstractUpload
{
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'local';
    
    /**
     * 服务提供商
     * @var string
     */
    protected $provider = 'default';
    
    /**
     * 磁盘名称
     * @var string
     */
    protected $disk = 'public';
    
    /**
     * 默认验证规则
     * @var array
     */
    protected $rules = [
        'size' => 10485760, // 10MB
        'ext' => 'jpg,jpeg,png,gif,bmp,webp,svg'
    ];
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        parent::__construct($config);
        
        // 设置磁盘
        if (isset($config['disk'])) {
            $this->disk = $config['disk'];
        }
    }
    
    /**
     * 上传文件
     * @param string|\think\File $file 文件路径或文件对象
     * @param string $savePath 保存路径
     * @param array $options 上传选项
     * @return array 上传结果
     * @throws \Exception 上传失败时抛出异常
     */
    public function upload($file, $savePath = '', array $options = [])
    {
        try {
            // 处理文件对象
            if (is_string($file)) {
                if (!file_exists($file)) {
                    throw new \Exception('File not found: ' . $file);
                }
                $file = new File($file);
            }
            
            // 验证文件
            $rules = array_merge($this->rules, $options['rules'] ?? []);
            $validate = Validate::rule([
                'file' => [
                    'fileSize' => $rules['size'],
                    'fileExt'  => $rules['ext']
                ]
            ]);
            
            if (!$validate->check(['file' => $file])) {
                throw new \Exception($validate->getError());
            }
            
            // 处理保存路径
            $savePath = trim($savePath, '/');

            // 执行上传
            $filesystem = Filesystem::disk($this->disk);
            $saveName = $filesystem->putFile($savePath, $file);

            if (!$saveName) {
                throw new \Exception('Upload failed');
            }
            
            // 获取文件信息
            $fileInfo = [
                'name' => $file->getOriginalName(),
                'save_name' => $saveName,
                'size' => $file->getSize(),
                'mime_type' => $file->getMime(),
                'extension' => $file->getExtension(),
                'path' => $saveName,
                'url' => $this->getUrl($saveName),
            ];

            return $fileInfo;
            
        } catch (\Exception $e) {
            $this->setError($e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 删除文件
     * @param string $filePath 文件路径
     * @return bool 删除结果
     * @throws \Exception 删除失败时抛出异常
     */
    public function delete($filePath)
    {
        try {
            $filesystem = Filesystem::disk($this->disk);
            $filesystem->delete($filePath);
            
            return true;
            
        } catch (\Exception $e) {
            $this->setError($e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取文件访问URL
     * @param string $filePath 文件路径
     * @param int $expires 过期时间（秒），本地存储忽略此参数
     * @return string 文件URL
     */
    public function getUrl($filePath, $expires = 0)
    {
        $filesystem = Filesystem::disk($this->disk);
        $url = $filesystem->url($filePath);

        return $url;
    }
    
    /**
     * 生成文件名
     * @param \think\File $file 文件对象
     * @return string 生成的文件名
     */
    protected function generateFileName($file)
    {
        // 默认使用日期+随机字符串+扩展名的方式生成文件名
        $extension = $file->getExtension();
        $fileName = date('Ymd') . '/' . md5(uniqid(mt_rand(), true));
        
        if (!empty($extension)) {
            $fileName .= '.' . $extension;
        }
        
        return $fileName;
    }
}