<?php

namespace app\common;

/**
 * API返回状态码枚举类
 * 
 * 用于统一管理API返回的状态码和消息
 * 状态码规则：
 * - 200: 成功
 * - 400-499: 客户端错误
 * - 500-599: 服务器错误
 * - 600+: 业务逻辑错误
 */
class ApiCode
{
    // 成功状态码
    const SUCCESS = 200;                  // 操作成功
    const UN_LOGIN = -1;                  // 未登入

    const SYSTEM_ERROR = 1000;          // 系统错误
    const PARAM_EMPTY = 1002;              // 参数为空
    const PARAM_INVALID_FORMAT = 1004;     // 参数格式错误
    const PARAM_INVALID_RANGE = 1005;      // 参数范围错误
    const PARAM_INVALID_LENGTH = 1006;     // 参数长度错误
    
    // 客户端错误 (400-499)
    const BAD_REQUEST = 4000;              // 请求参数错误
    const UNAUTHORIZED = 4001;             // 未授权（未登录）
    const FORBIDDEN = 4003;                // 禁止访问（无权限）
    const NOT_FOUND = 4004;                // 资源不存在
    const METHOD_NOT_ALLOWED = 4005;       // 请求方法不允许
    const REQUEST_TIMEOUT = 4008;          // 请求超时
    const CONFLICT = 4009;                 // 资源冲突
    const UNPROCESSABLE_ENTITY = 4022;     // 请求格式正确，但语义错误
    const TOO_MANY_REQUESTS = 4029;        // 请求过于频繁
    
    // 服务器错误 (500-599)
    const SERVER_ERROR = 5000;             // 服务器内部错误
    const NOT_IMPLEMENTED = 5001;          // 功能未实现
    const BAD_GATEWAY = 5002;              // 网关错误
    const SERVICE_UNAVAILABLE = 5003;      // 服务不可用
    const GATEWAY_TIMEOUT = 5004;          // 网关超时
    
    // 业务逻辑错误 (6000+)
    const USER_NOT_EXIST = 6000;           // 用户不存在
    const PASSWORD_ERROR = 6001;           // 密码错误
    const USERNAME_EMPTY = 6002;           // 用户名为空
    const PASSWORD_EMPTY = 6003;           // 密码为空
    const ACCOUNT_DISABLED = 6004;         // 账号已禁用
    const VERIFICATION_CODE_ERROR = 6005;  // 验证码错误或已过期
    const AUTO_REGISTER_FAILED = 6006;     // 自动注册失败
    const ACCOUNT_NOT_BOUND = 6007;        // 账号未绑定第三方账号
    const SEND_CODE_FAILED = 6008;         // 验证码发送失败
    const LOGIN_TYPE_NOT_SUPPORTED = 6009; // 不支持的登录类型
    const CODE_EMPTY = 6010;              // 验证码为空
    const CODE_ERROR = 6015;              //验证码错误或过期
    const MOBILE_EMPTY = 6011;            // 手机号为空
    const MOBILE_EXIST = 6012;            // 手机号已存在
    const EMAIL_EMPTY = 6013;             // 邮箱为空
    const EMAIL_EXIST = 6014;             // 邮箱已存在
    const USERNAME_EXIST = 6016;          //用户名已存在
    const CODE_ABANDONED = 6017;          //验证码发送被拦截
    const CODE_SEND_FAILED = 6018;          //验证码发送失败
    const LOGIN_FAILURE_TIMES_ERROR = 6019;     // 登入验证超过最大次数
    const ACCESS_TOKEN_INVALID = 6020;     // token无效
    const PARAM_ERROR = 6021;             //参数错误
    const CONFIG_ERROR = 6022;
    const THIRD_PARTY_ERROR = 6023;
    const PARAM_INVALID = 6024;
    const ACCESS_TOKEN_EXPIRED = 6025;

    
    // 消息映射
    const OPERATION_FAILED = 7001;          // 操作失败
    const OPERATION_SUCCESS = 7002;        // 操作成功
    const OPERATION_FAILED_WITH_MSG = 7003; // 操作失败
    const OPERATION_SUCCESS_WITH_MSG = 7004; // 操作成功
    const OPERATION_FAILED_WITH_DATA = 7005; // 操作失败
    const OPERATION_SUCCESS_WITH_DATA = 7006; // 操作成功
    const OPERATION_FAILED_WITH_MSG_AND_DATA = 7007; // 操作失败
    const OPERATION_SUCCESS_WITH_MSG_AND_DATA = 7008; // 操作成功
    const OPERATION_FAILED_WITH_CODE = 7009; // 操作失败
    const OPERATION_SUCCESS_WITH_CODE = 7010; // 操作成功
    const OPERATION_FAILED_WITH_MSG_AND_CODE = 7011; // 操作失败
    const OPERATION_SUCCESS_WITH_MSG_AND_CODE = 7012; // 操作成功
    const OPERATION_FAILED_WITH_DATA_AND_CODE = 7013; // 操作失败
    const OPERATION_SUCCESS_WITH_DATA_AND_CODE = 7014; // 操作成功
    private static $messages = [
        self::SUCCESS => '操作成功',
        self::UN_LOGIN => '未登入',
        
        self::BAD_REQUEST => '请求参数错误',
        self::UNAUTHORIZED => '未授权，请先登录',
        self::FORBIDDEN => '无权限访问',
        self::NOT_FOUND => '请求的资源不存在',
        self::METHOD_NOT_ALLOWED => '请求方法不允许',
        self::REQUEST_TIMEOUT => '请求超时',
        self::CONFLICT => '资源冲突',
        self::UNPROCESSABLE_ENTITY => '请求格式正确，但语义错误',
        self::TOO_MANY_REQUESTS => '请求过于频繁，请稍后再试',
        
        self::SERVER_ERROR => '服务器内部错误',
        self::NOT_IMPLEMENTED => '功能未实现',
        self::BAD_GATEWAY => '网关错误',
        self::SERVICE_UNAVAILABLE => '服务不可用',
        self::GATEWAY_TIMEOUT => '网关超时',
        
        self::USER_NOT_EXIST => '用户不存在',
        self::PASSWORD_ERROR => '密码错误',
        self::USERNAME_EMPTY => '用户名不能为空',
        self::PASSWORD_EMPTY => '密码不能为空',
        self::ACCOUNT_DISABLED => '账号已禁用',
        self::VERIFICATION_CODE_ERROR => '验证码错误或已过期',
        self::AUTO_REGISTER_FAILED => '自动注册失败',
        self::ACCOUNT_NOT_BOUND => '账号未绑定第三方账号',
        self::SEND_CODE_FAILED => '验证码发送失败',
        self::LOGIN_TYPE_NOT_SUPPORTED => '不支持的登录类型',
        self::CODE_EMPTY => '验证码不能为空',
        self::CODE_ERROR => '验证码错误或过期',
        self::MOBILE_EMPTY => '手机号不能为空',
        self::MOBILE_EXIST => '手机号已注册',
        self::EMAIL_EMPTY => '邮箱不能为空',
        self::EMAIL_EXIST => '邮箱已注册',
        self::USERNAME_EXIST => '用户名已存在',
        self::CODE_ABANDONED => '验证码发送被拦截',
        self::CODE_SEND_FAILED => '验证码发送失败',
        self::LOGIN_FAILURE_TIMES_ERROR => '登入验证超过最大次数',
        self::ACCESS_TOKEN_INVALID => 'token无效',
        self::PARAM_ERROR => '参数错误',
        self::CONFIG_ERROR => '配置错误',
        self::THIRD_PARTY_ERROR  => '第三方错误',
        self::PARAM_INVALID => '参数无效',
    ];
    
    /**
     * 获取状态码对应的消息
     * 
     * @param int $code 状态码
     * @return string 消息
     */
    public static function getMessage(int $code): string
    {
        return self::$messages[$code] ?? '未知错误';
    }

}