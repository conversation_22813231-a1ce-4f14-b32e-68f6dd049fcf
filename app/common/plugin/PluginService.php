<?php
namespace app\common\plugin;

use app\plugin\constant\AppsName;
use think\facade\Config;

class PluginService
{
    /**
     * 插件实例缓存
     * @var array
     */
    protected static $instances = [];
    
    /**
     * 获取插件实例
     * @param string $name 插件名称
     * @return mixed|null 插件实例
     */
    public static function getInstance($name)
    {
        if (isset(self::$instances[$name])) {
            return self::$instances[$name];
        }
        
        $pluginClass = "\\" . AppsName::APPS_NAME . "\\{$name}\\App";
        if (!class_exists($pluginClass)) {
            return null;
        }
        
        $instance = new $pluginClass();
        self::$instances[$name] = $instance;
        
        // 加载插件配置
        $configFile = root_path() . AppsName::APPS_NAME . "/{$name}/config.php";
        if (file_exists($configFile)) {
            $config = include $configFile;
            Config::set($config, "plugin.{$name}");
        }
        
        return $instance;
    }
}