<?php

namespace app\common;

use app\common\message\MessageService;

/**
 * 消息服务门面类
 */
class Message
{
    /**
     * 发送消息
     * @param string $to 接收者
     * @param string $content 消息内容
     * @param array $params 额外参数
     * @param string $type 消息类型，如 'sms', 'email'
     * @param string $provider 服务提供商，如 'aliyun', 'tencent', 'smtp'
     * @return mixed 发送结果
     */
    public static function send($to, $content, array $params = [], $type = null, $provider = null)
    {
        return MessageService::send($to, $content, $params, $type, $provider);
    }
    
    /**
     * 发送短信
     * @param string $to 手机号码
     * @param string|array $content 短信内容或模板参数
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return mixed 发送结果
     */
    public static function sendSms($to, $content, array $params = [], $provider = null)
    {
        return MessageService::sendSms($to, $content, $params, $provider);
    }
    
    /**
     * 发送邮件
     * @param string $to 收件人邮箱
     * @param string $content 邮件内容
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return mixed 发送结果
     */
    public static function sendEmail($to, $content, array $params = [], $provider = null)
    {
        return MessageService::sendEmail($to, $content, $params, $provider);
    }
}