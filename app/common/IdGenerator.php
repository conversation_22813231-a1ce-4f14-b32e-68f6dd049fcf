<?php

namespace app\common;

/**
 * ID生成器类
 * 用于生成类似QQ号的唯一数字ID
 */
class IdGenerator
{
    /**
     * 起始时间戳（2023-01-01 00:00:00）
     * @var int
     */
    protected static $startTimestamp = 1672502400;
    
    /**
     * 序列号位数
     * @var int
     */
    protected static $sequenceBits = 3;
    
    /**
     * 随机数位数
     * @var int
     */
    protected static $randomBits = 2;
    
    /**
     * 最后生成ID的时间戳
     * @var int
     */
    protected static $lastTimestamp = 0;
    
    /**
     * 序列号
     * @var int
     */
    protected static $sequence = 0;
    
    /**
     * 生成类似唯一ID
     * 
     * @param int $length ID长度，默认为10位
     * @param string $prefix ID前缀，默认为空
     * @return string 生成的唯一ID
     */
    public static function generate($length = 10, $prefix = '')
    {
        // 获取当前时间戳（毫秒级）
        $timestamp = self::getCurrentTimestamp();
        
        // 如果在同一毫秒内，增加序列号
        if ($timestamp == self::$lastTimestamp) {
            self::$sequence = (self::$sequence + 1) % (1 << self::$sequenceBits);
            // 如果序列号溢出，则等待下一毫秒
            if (self::$sequence == 0) {
                $timestamp = self::waitNextMillisecond(self::$lastTimestamp);
            }
        } else {
            // 不同毫秒内，重置序列号
            self::$sequence = 0;
        }
        
        // 更新最后时间戳
        self::$lastTimestamp = $timestamp;
        
        // 计算时间差值（相对于起始时间戳）
        $timeDiff = $timestamp - self::$startTimestamp;
        
        // 生成随机数部分
        $random = mt_rand(0, (1 << self::$randomBits) - 1);
        
        // 组合ID：时间差值 + 序列号 + 随机数
        $id = $timeDiff . str_pad(self::$sequence, self::$sequenceBits, '0', STR_PAD_LEFT) . str_pad($random, self::$randomBits, '0', STR_PAD_LEFT);
        
        // 如果ID长度不足，在前面补0
        if (strlen($id) < $length) {
            $id = str_pad($id, $length, '0', STR_PAD_LEFT);
        }
        // 如果ID长度超过要求，截取后面部分
        elseif (strlen($id) > $length) {
            $id = substr($id, -$length);
        }
        
        // 添加前缀
        return $prefix . $id;
    }
    
    /**
     * 生成指定长度的数字ID
     * 
     * @param int $length ID长度，默认为10位
     * @return string 生成的数字ID
     */
    public static function generateNumericId($length = 10)
    {
        return self::generate($length);
    }
    
    /**
     * 生成雪花算法ID（适用于高并发场景）
     * 
     * @param int $workerId 工作机器ID (0-31)
     * @param int $dataCenterId 数据中心ID (0-31)
     * @return string 生成的雪花算法ID
     */
    public static function generateSnowflakeId($workerId = 0, $dataCenterId = 0)
    {
        // 参数校验
        $workerId = $workerId & 0x1F;  // 限制为0-31
        $dataCenterId = $dataCenterId & 0x1F;  // 限制为0-31
        
        // 获取当前时间戳（毫秒级）
        $timestamp = self::getCurrentTimestamp();
        
        // 如果在同一毫秒内，增加序列号
        if ($timestamp == self::$lastTimestamp) {
            self::$sequence = (self::$sequence + 1) & 0xFFF; // 限制为0-4095
            // 如果序列号溢出，则等待下一毫秒
            if (self::$sequence == 0) {
                $timestamp = self::waitNextMillisecond(self::$lastTimestamp);
            }
        } else {
            // 不同毫秒内，重置序列号
            self::$sequence = 0;
        }
        
        // 更新最后时间戳
        self::$lastTimestamp = $timestamp;
        
        // 计算时间差值（相对于起始时间戳）
        $timeDiff = $timestamp - self::$startTimestamp;
        
        // 组合ID：时间差值(41位) + 数据中心ID(5位) + 工作机器ID(5位) + 序列号(12位)
        // 由于PHP整数限制，这里使用字符串拼接
        $id = ($timeDiff << 22) | ($dataCenterId << 17) | ($workerId << 12) | self::$sequence;
        
        return (string)$id;
    }
    
    /**
     * 获取当前时间戳（毫秒级）
     * 
     * @return int 当前时间戳（毫秒级）
     */
    protected static function getCurrentTimestamp()
    {
        return (int)(microtime(true) * 1000);
    }
    
    /**
     * 等待下一毫秒
     * 
     * @param int $lastTimestamp 上一次的时间戳
     * @return int 新的时间戳
     */
    protected static function waitNextMillisecond($lastTimestamp)
    {
        $timestamp = self::getCurrentTimestamp();
        while ($timestamp <= $lastTimestamp) {
            $timestamp = self::getCurrentTimestamp();
        }
        return $timestamp;
    }
    
    /**
     * 从数据库生成自增ID
     * 
     * @param string $counterKey 计数器键名，用于区分不同用途的ID
     * @param int $length ID长度，默认为9位
     * @param int $startId 起始ID，默认为100000000
     * @param string $table 表名，默认为sys_counter
     * @return string 生成的ID
     */
    public static function generateIdFromDb($counterKey = 'user_id', $length = 9, $startId = 100000000, $table = 'sys_counter')
    {
        // 获取上一次生成的ID
        $lastId = $startId;
        try {
            $record = \think\facade\Db::table($table)
                ->where('counter_key', $counterKey)
                ->find();
                
            if ($record && isset($record['last_id']) && $record['last_id'] >= $startId) {
                $lastId = $record['last_id'];
            }
        } catch (\Exception $e) {
            // 数据库读取失败，使用起始ID
        }
        
        // 获取步长，默认为随机1-10
        $step = 1;
        if (isset($record['step']) && $record['step'] > 0) {
            $step = $record['step'];
            // 如果步长设置为负数，则使用随机步长
            if ($step < 0) {
                $step = mt_rand(1, abs($step));
            }
        } else {
            $step = mt_rand(1, 10);
        }
        
        // 生成新ID
        $newId = $lastId + $step;
        
        // 确保ID长度符合要求
        $idStr = (string)$newId;
        if (strlen($idStr) < $length) {
            $idStr = str_pad($idStr, $length, '0', STR_PAD_LEFT);
        } elseif (strlen($idStr) > $length) {
            // 如果超出长度限制，则从起始ID重新开始
            $newId = $startId + $step;
            $idStr = (string)$newId;
        }
        
        // 更新数据库
        try {
            $exists = \think\facade\Db::table($table)
                ->where('counter_key', $counterKey)
                ->find();
                
            if ($exists) {
                \think\facade\Db::table($table)
                    ->where('counter_key', $counterKey)
                    ->update(['last_id' => $newId, 'updatetime' => date('Y-m-d H:i:s')]);
            } else {
                \think\facade\Db::table($table)->insert([
                    'counter_key' => $counterKey,
                    'last_id' => $newId,
                    'step' => $step > 0 ? $step : 1,
                    'description' => $counterKey . '自增ID',
                    'createtime' => date('Y-m-d H:i:s'),
                    'updatetime' => date('Y-m-d H:i:s')
                ]);
            }
        } catch (\Exception $e) {
            // 数据库更新失败，忽略

        }
        
        return $idStr;
    }
}