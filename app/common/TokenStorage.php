<?php

namespace app\common;

use app\common\token\MySQLTokenStorage;
use app\common\token\RedisTokenStorage;
use app\common\token\EncryptedTokenStorage;
use app\common\token\TokenStorageInterface;
use think\facade\Config;
use think\facade\Log;

/**
 * Token存储类
 * 支持Redis和MySQL两种存储方式
 */
class TokenStorage
{
    /**
     * 存储驱动实例
     * @var TokenStorageInterface
     */
    protected static $instance = null;
    
    /**
     * 获取存储驱动实例
     * @return TokenStorageInterface
     */
    public static function getInstance()
    {
        if (is_null(self::$instance)) {
            // 从配置中获取存储方式，默认为redis
            $driver = Config::get('token.driver', 'mysql');
            $enableEncryption = Config::get('token.encryption.enabled', false);
            
            // 创建基础存储驱动
            switch ($driver) {
                case 'mysql':
                    $baseDriver = new MySQLTokenStorage();
                    break;
                case 'redis':
                default:
                    $baseDriver = new RedisTokenStorage();
                    break;
            }
            
            // 如果启用加密且支持加密功能，则使用加密存储
            if ($enableEncryption && EncryptedTokenStorage::isEncryptionAvailable()) {
                self::$instance = new EncryptedTokenStorage($baseDriver);
                
                // 测试加密功能
//                if (!self::$instance->testEncryption()) {
//                    Log::warning('[TOKEN_STORAGE] 加密功能测试失败，回退到普通存储');
//                    self::$instance = $baseDriver;
//                }
            } else {
                self::$instance = $baseDriver;
                
                if ($enableEncryption) {
                    Log::warning('[TOKEN_STORAGE] 加密功能不可用，使用普通存储');
                }
            }
        }
        
        return self::$instance;
    }

    public static function setTable($table)
    {
        return self::getInstance()->setTable($table);
    }

    public static function setRefreshTable($table)
    {
        return self::getInstance()->setRefreshTable($table);
    }

    public static function getError()
    {
        return self::getInstance()->getError();
    }
    
    /**
     * 存储令牌
     * @param array $token 令牌
     * @param mixed $data 令牌关联的数据
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public static function store($token, $data, $expire = 7200)
    {
        return self::getInstance()->store($token, $data, $expire);
    }
    
    /**
     * 获取令牌关联的数据
     * @param string $token 令牌
     * @return mixed|null 成功返回数据，失败返回null
     */
    public static function get($token, $refresh = false)
    {
        return self::getInstance()->get($token, $refresh);
    }
    
    /**
     * 刷新令牌过期时间
     * @param string $token 令牌
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public static function refresh($token, $expire = 7200)
    {
        return self::getInstance()->refresh($token, $expire);
    }
    
    /**
     * 删除令牌
     * @param string $token 令牌
     * @return bool
     */
    public static function remove($token, $refresh = false)
    {
        return self::getInstance()->remove($token, $refresh);
    }
    
    /**
     * 刷新令牌并生成新的token
     * @param string $token 旧令牌
     * @param int $expire 过期时间（秒）
     * @return string|false 成功返回新令牌，失败返回false
     */
    public static function refreshWithNewToken($accessToken, $data, $refreshToken, $expireTime, $expireRefreshTime)
    {
        return self::getInstance()->refreshWithNewToken($accessToken, $data, $refreshToken, $expireTime, $expireRefreshTime);
    }
}