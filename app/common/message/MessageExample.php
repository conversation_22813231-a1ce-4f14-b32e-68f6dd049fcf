<?php

namespace app\common\message;

/**
 * 消息服务使用示例
 */
class MessageExample
{
    /**
     * 发送短信示例
     */
    public static function sendSmsExample()
    {
        // 示例1：使用默认短信服务提供商发送短信
        $to = '13800138000';
        $templateParam = ['code' => '123456', 'product' => '测试系统'];
        $result = MessageService::sendSms($to, $templateParam);
        
        // 示例2：指定使用腾讯云短信服务
        $result = MessageService::sendSms($to, $templateParam, [], 'tencent');
        
        // 示例3：直接使用消息服务实例
        $smsService = MessageFactory::getInstance('sms', 'aliyun');
        $result = $smsService->send($to, json_encode($templateParam, JSON_UNESCAPED_UNICODE));
        
        return $result;
    }
    
    /**
     * 发送邮件示例
     */
    public static function sendEmailExample()
    {
        // 示例1：使用默认邮件服务提供商发送邮件
        $to = '<EMAIL>';
        $content = '<h1>测试邮件</h1><p>这是一封测试邮件</p>';
        $params = [
            'subject' => '测试邮件',
            'is_html' => true
        ];
        $result = MessageService::sendEmail($to, $content, $params);
        
        // 示例2：指定使用PHP内置mail函数发送邮件
        $result = MessageService::sendEmail($to, $content, $params, 'php');
        
        // 示例3：直接使用消息服务实例
        $emailService = MessageFactory::getInstance('email', 'smtp');
        $result = $emailService->send($to, $content, $params);
        
        return $result;
    }
    
    /**
     * 动态切换消息服务示例
     */
    public static function switchServiceExample()
    {
        // 根据业务需求动态选择消息类型和服务提供商
        $messageType = 'sms'; // 或 'email'
        $provider = 'aliyun'; // 或其他提供商
        
        // 根据消息类型准备参数
        if ($messageType == 'sms') {
            $to = '13800138000';
            $content = ['code' => '123456'];
            $params = [
                'template_code' => 'SMS_123456',
                'sign_name' => '测试签名'
            ];
        } else {
            $to = '<EMAIL>';
            $content = '这是一封测试邮件';
            $params = [
                'subject' => '测试邮件'
            ];
        }
        
        // 发送消息
        $result = MessageService::send($to, $content, $params, $messageType, $provider);
        
        return $result;
    }
}