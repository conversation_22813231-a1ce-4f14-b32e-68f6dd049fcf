<?php

namespace app\common\message;

/**
 * 消息服务管理类
 */
class MessageService
{
    /**
     * 发送消息
     * @param string $to 接收者
     * @param string $content 消息内容
     * @param array $params 额外参数
     * @param string $type 消息类型，如 'sms', 'email'
     * @param string $provider 服务提供商，如 'aliyun', 'tencent', 'smtp'
     * @return mixed 发送结果
     */
    public static function send($to, $content, array $params = [], $type = null, $provider = null)
    {
        // 获取消息服务实例
        $service = MessageFactory::getInstance($type, $provider);
        
        // 发送消息
        return $service->send($to, $content, $params);
    }
    
    /**
     * 发送短信
     * @param string $to 手机号码
     * @param string|array $content 短信内容或模板参数
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return mixed 发送结果
     */
    public static function sendSms($to, $content, array $params = [], $provider = null)
    {
        // 如果内容是数组，则转换为JSON字符串
        if (is_array($content)) {
            $content = json_encode($content, JSON_UNESCAPED_UNICODE);
        }
        
        return self::send($to, $content, $params, 'sms', $provider);
    }
    
    /**
     * 发送邮件
     * @param string $to 收件人邮箱
     * @param string $content 邮件内容
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return mixed 发送结果
     */
    public static function sendEmail($to, $content, array $params = [], $provider = null)
    {
        return self::send($to, $content, $params, 'email', $provider);
    }
}