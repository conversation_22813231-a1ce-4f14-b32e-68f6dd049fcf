<?php

namespace app\common\message;

use think\facade\Config;

/**
 * 消息服务工厂类
 */
class MessageFactory
{
    /**
     * 消息服务实例缓存
     * @var array
     */
    protected static $instances = [];
    
    /**
     * 获取消息服务实例
     * @param string $type 消息类型，如 'sms', 'email'
     * @param string $provider 服务提供商，如 'aliyun', 'tencent', 'smtp'
     * @return MessageInterface
     * @throws \Exception 当找不到对应的消息服务实现时抛出异常
     */
    public static function getInstance($type = null, $provider = null)
    {
        // 如果未指定类型，则使用配置中的默认类型
        if (is_null($type)) {
            $type = Config::get('message.default_type', 'sms');
        }
        
        // 如果未指定提供商，则使用配置中对应类型的默认提供商
        if (is_null($provider)) {
            $provider = Config::get("message.{$type}.default_provider", '');
        }

        // 生成缓存键
        $key = "{$type}:{$provider}";
        
        // 如果实例已存在，则直接返回
        if (isset(self::$instances[$key])) {
            return self::$instances[$key];
        }
        
        // 获取消息服务类
        $class = self::getMessageClass($type, $provider);
        
        // 获取配置
        $config = Config::get("message.{$type}.providers.{$provider}", []);
        
        // 创建实例
        self::$instances[$key] = new $class($config);
        
        return self::$instances[$key];
    }
    
    /**
     * 获取消息服务类名
     * @param string $type 消息类型
     * @param string $provider 服务提供商
     * @return string 完整的类名
     * @throws \Exception 当找不到对应的消息服务实现时抛出异常
     */
    protected static function getMessageClass($type, $provider)
    {
        // 尝试从配置中获取类名
        $class = Config::get("message.{$type}.providers.{$provider}.class", '');
        
        // 如果配置中没有指定类名，则使用默认命名规则
        if (empty($class)) {
            // 首字母大写
            $type = ucfirst($type);
            $provider = ucfirst($provider);
            
            // 构建类名
            $class = "\\app\\common\\message\\" . strtolower($type) . "\\{$provider}{$type}Message";
        }
        
        // 检查类是否存在
        if (!class_exists($class)) {
            throw new \Exception("消息服务类 {$class} 不存在");
        }
        
        return $class;
    }
}