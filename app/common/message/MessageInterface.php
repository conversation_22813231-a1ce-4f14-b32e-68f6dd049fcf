<?php

namespace app\common\message;

/**
 * 消息发送接口
 */
interface MessageInterface
{
    /**
     * 发送消息
     * @param string $to 接收者
     * @param string $content 消息内容
     * @param array $params 额外参数
     * @return mixed 发送结果
     */
    public function send($to, $content, array $params = []);
    
    /**
     * 获取发送服务名称
     * @return string
     */
    public function getServiceName();
    
    /**
     * 获取发送服务提供商
     * @return string
     */
    public function getServiceProvider();
}