<?php

namespace app\common\message\sms;

use app\common\message\AbstractMessage;
use liugene\alisms\Sms;

/**
 * 阿里云短信服务实现
 */
class AliyunSmsMessage extends AbstractMessage
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'aliyun';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'sms';
    
    /**
     * 发送短信
     * @param string $to 手机号码
     * @param string $content 短信内容（模板参数JSON字符串）
     * @param array $params 额外参数，可包含：
     *                      - template_code: 短信模板编号
     *                      - sign_name: 短信签名
     * @return mixed 发送结果
     */
    public function send($to, $content, array $params = [])
    {
        // 获取配置信息
        $accessKeyId = $params['access_key_id'] ?? ($this->config['access_key_id'] ?? '');
        $accessKeySecret = $params['access_key_secret'] ?? ($this->config['access_key_secret'] ?? '');
        $signName = $params['sign_name'] ?? ($this->config['sign_name'] ?? '');
        $templateCode = $params['template_code'] ?? ($this->config['template_code'] ?? '');

        try {
            // 获取短信发送实例（使用单例模式）
            $sms = Sms::getInstance();

            // 设置访问密钥
            $sms->setAccessKeyId($accessKeyId);
            $sms->setAccessKeyToken($accessKeySecret);

            // 设置短信服务
            $service = $sms->setService('SmsSend');

            // 解析内容为模板参数
            $templateParam = $content;
            if (!is_array($templateParam) && !empty($templateParam)) {
                // 如果不是数组，尝试解析JSON
                $templateParam = json_decode($templateParam, true) ?: [];
            }

            // 将模板参数转为JSON字符串
            if (is_array($templateParam)) {
                $templateParam = json_encode($templateParam, JSON_UNESCAPED_UNICODE);
            }

            // 配置短信参数
            $service->setPhoneNumbers($to)
                ->setSignName($signName)
                ->setSignatureNonce(uniqid())
                ->setTemplateCode($templateCode)
                ->setTemplateParam($templateParam);

            // 发送短信
            $result = $sms->send();
            $response = json_decode($result, true);

            if ($response['Message'] != 'OK') {
                // 返回错误结果
                $response = [
                    'success' => false,
                    'message' => '邮件发送失败: ' . $response['Message'],
                    'provider' => $this->provider,
                    'to' => $to,
                    'error' => $response['Message']
                ];
            } else {
                // 返回成功结果
                $response = [
                    'success' => true,
                    'message' => 'Sms发送成功',
                    'provider' => $this->provider,
                    'to' => $to
                ];
            }

        } catch (\Exception $e) {
            // 返回错误结果
            $response = [
                'success' => false,
                'message' => '邮件发送失败: ' . $e->getMessage(),
                'provider' => $this->provider,
                'to' => $to,
                'error' => $e->getMessage()
            ];
        }

        return $response;

    }
}