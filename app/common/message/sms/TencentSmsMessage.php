<?php

namespace app\common\message\sms;

use app\common\message\AbstractMessage;

/**
 * 腾讯云短信服务实现
 */
class TencentSmsMessage extends AbstractMessage
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'tencent';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'sms';
    
    /**
     * 发送短信
     * @param string $to 手机号码
     * @param string $content 短信内容（模板参数JSON字符串）
     * @param array $params 额外参数，可包含：
     *                      - template_id: 短信模板ID
     *                      - sign_name: 短信签名
     * @return mixed 发送结果
     */
    public function send($to, $content, array $params = [])
    {
        // 获取配置信息
        $secretId = $params['secret_id'] ?? ($this->config['secret_id'] ?? '');
        $secretKey = $params['secret_key'] ?? ($this->config['secret_key'] ?? '');
        $appId = $params['app_id'] ?? ($this->config['app_id'] ?? '');
        $signName = $params['sign_name'] ?? ($this->config['sign_name'] ?? '');
        $templateId = $params['template_id'] ?? ($this->config['template_id'] ?? '');
        
        // 解析内容为模板参数
        $templateParam = $content;
        if (!is_array($templateParam) && !empty($templateParam)) {
            // 如果不是数组，尝试解析JSON
            $templateParam = json_decode($templateParam, true) ?: [];
        }
        
        // 这里是腾讯云短信SDK的调用逻辑
        // 注意：这里只是一个示例框架，实际使用时需要引入腾讯云短信SDK并实现具体逻辑
        // 例如：使用 composer require tencentcloud/tencentcloud-sdk-php
        
        // 模拟返回结果
        $response = [
            'success' => true,
            'message' => '短信发送成功（模拟）',
            'provider' => $this->provider,
            'to' => $to
        ];
        
        return $response;
    }
}