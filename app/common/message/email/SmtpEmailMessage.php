<?php

namespace app\common\message\email;

use app\common\message\AbstractMessage;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\SMTP;

/**
 * SMTP邮件服务实现
 */
class SmtpEmailMessage extends AbstractMessage
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'smtp';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'email';
    
    /**
     * 发送邮件
     * @param string $to 收件人邮箱
     * @param string $content 邮件内容
     * @param array $params 额外参数，可包含：
     *                      - subject: 邮件主题
     *                      - from: 发件人
     *                      - from_name: 发件人名称
     *                      - cc: 抄送
     *                      - bcc: 密送
     *                      - is_html: 是否HTML内容
     *                      - attachments: 附件数组
     * @return mixed 发送结果
     */
    public function send($to, $content, array $params = [])
    {
        // 获取SMTP配置
        $host = $params['host'] ?? ($this->config['host'] ?? '');
        $port = $params['port'] ?? ($this->config['port'] ?? 25);
        $username = $params['username'] ?? ($this->config['username'] ?? '');
        $password = $params['password'] ?? ($this->config['password'] ?? '');
        $secure = $params['secure'] ?? ($this->config['secure'] ?? ''); // tls 或 ssl
        
        // 获取邮件参数
        $subject = $params['subject'] ?? '系统通知';
        $from = $params['from'] ?? ($this->config['from'] ?? $username);
        $fromName = $params['from_name'] ?? ($this->config['from_name'] ?? '');
        $isHtml = $params['is_html'] ?? false;
        $cc = $params['cc'] ?? [];
        $bcc = $params['bcc'] ?? [];
        $attachments = $params['attachments'] ?? [];

        // 创建PHPMailer实例
        $mail = new PHPMailer(true); // true启用异常处理
        
        try {
            // 服务器设置
            $mail->SMTPDebug = 0; // 调试输出级别：0=关闭，1=客户端消息，2=客户端和服务器消息
            $mail->isSMTP(); // 使用SMTP
            $mail->Host = $host; // SMTP服务器地址
            $mail->SMTPAuth = true; // 启用SMTP认证
            $mail->Username = $username; // SMTP用户名
            $mail->Password = $password; // SMTP密码
            
            // 设置加密方式
            if ($secure === 'tls') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // 启用TLS加密
            } elseif ($secure === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // 启用SSL加密
            }
            
            $mail->Port = $port; // TCP端口
            $mail->CharSet = 'UTF-8'; // 设置字符集
            
            // 发件人
            $mail->setFrom($from, $fromName);
            
            // 收件人
            if (is_array($to)) {
                foreach ($to as $email => $name) {
                    if (is_numeric($email)) {
                        $mail->addAddress($name); // 只有邮箱地址
                    } else {
                        $mail->addAddress($email, $name); // 邮箱和名称
                    }
                }
            } else {
                $mail->addAddress($to); // 添加收件人
            }
            
            // 添加抄送
            if (!empty($cc)) {
                if (is_array($cc)) {
                    foreach ($cc as $email => $name) {
                        if (is_numeric($email)) {
                            $mail->addCC($name);
                        } else {
                            $mail->addCC($email, $name);
                        }
                    }
                } else {
                    $mail->addCC($cc);
                }
            }
            
            // 添加密送
            if (!empty($bcc)) {
                if (is_array($bcc)) {
                    foreach ($bcc as $email => $name) {
                        if (is_numeric($email)) {
                            $mail->addBCC($name);
                        } else {
                            $mail->addBCC($email, $name);
                        }
                    }
                } else {
                    $mail->addBCC($bcc);
                }
            }
            
            // 添加附件
            if (!empty($attachments)) {
                foreach ($attachments as $attachment) {
                    if (is_array($attachment) && isset($attachment['path'])) {
                        $filename = $attachment['name'] ?? '';
                        $mail->addAttachment($attachment['path'], $filename);
                    } else if (is_string($attachment)) {
                        $mail->addAttachment($attachment);
                    }
                }
            }
            
            // 邮件内容
            $mail->Subject = $subject; // 邮件主题

            // 处理邮件内容
            if ($isHtml) {
                // 设置邮件格式为HTML（必须在设置Body前调用）
                $mail->isHTML(true);
                // 设置HTML内容，确保内容类型正确
                $mail->Body = $content;
                // 自动生成纯文本版本的内容作为备用
                $mail->AltBody = strip_tags($content);
                // 确保内容类型头部正确设置
                $mail->ContentType = 'text/html; charset=utf-8';
            } else {
                // 设置为纯文本格式
                $mail->isHTML(false);
                $mail->Body = $content;
                $mail->ContentType = 'text/plain';
            }
            
            // 发送邮件
            $mail->send();

            // 返回成功结果
            $response = [
                'success' => true,
                'message' => 'SMTP邮件发送成功',
                'provider' => $this->provider,
                'to' => $to,
                'subject' => $subject
            ];
            
        } catch (Exception $e) {
            // 返回错误结果
            $response = [
                'success' => false,
                'message' => '邮件发送失败: ' . $mail->ErrorInfo,
                'provider' => $this->provider,
                'to' => $to,
                'subject' => $subject,
                'error' => $e->getMessage()
            ];
        }
        
        return $response;
    }
}