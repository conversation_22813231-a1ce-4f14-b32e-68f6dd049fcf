<?php

namespace app\common\message\email;

/**
 * Dynadot风格邮件发送示例
 * 展示如何使用DynadotEmailTemplate发送通知邮件
 */
class NoticeEmailExample
{
    /**
     * 生成域名到期通知邮件示例
     * 
     * @return string 生成的HTML邮件内容
     */
    public static function domainExpirationNotice()
    {
        // 设置邮件参数
        $params = [
            'name' => 'Liu Jun Jun',
            'email' => '<EMAIL>',
            'title' => '域名即将到期通知',
            'content' => '您的域名example.com将在30天内到期。请及时续费以避免服务中断。',
            'changes' => [
                '续费后，域名有效期将延长一年。',
                '如不续费，域名将在到期后进入赎回期，赎回期内续费将产生额外费用。',
                '赎回期结束后，域名将被释放，任何人都可以注册。'
            ],
            'steps' => [
                '登录到您的Dynadot账户。',
                '进入"我的域名"页面。',
                '选择需要续费的域名并完成支付。'
            ],
            'help_url' => 'https://help.dynadot.com',
            'support_email' => '<EMAIL>'
        ];
        
        // 获取邮件HTML内容
        $html = NoticeEmailTemplate::getTemplate($params);
        
        return $html;
    }
    
    /**
     * 生成隐私设置更新通知邮件示例
     * 
     * @return string 生成的HTML邮件内容
     */
    public static function privacySettingsUpdate()
    {
        // 设置邮件参数
        $params = [
            'name' => 'Liu Jun Jun',
            'email' => '<EMAIL>',
            'title' => 'Whois隐私设置更新通知',
            'content' => '我们将于2025年3月31日更新我们的Whois隐私设置。',
            'changes' => [
                '不再使用Whois隐私开关。将改为使用下拉菜单来设置隐私级别。',
                '您选择的隐私级别将应用于与注册商共享的信息，除非受到注册局限制。',
                '您现在可以为TLD选择隐私级别，这些TLD之前由于注册局限制而无法设置。请花点时间查看适用的TLD。'
            ],
            'steps' => [
                '登录到您的Dynadot账户。',
                '选择您希望更新的域名。',
                '点击"操作"按钮，然后选择"隐私设置"。',
                '使用下拉菜单为这些域名选择隐私级别。'
            ],
            'help_url' => 'https://help.dynadot.com',
            'support_email' => '<EMAIL>'
        ];
        
        // 获取邮件HTML内容
        $html = NoticeEmailTemplate::getTemplate($params);
        
        return $html;
    }
}