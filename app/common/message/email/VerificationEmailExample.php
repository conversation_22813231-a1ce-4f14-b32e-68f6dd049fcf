<?php

namespace app\common\message\email;

/**
 * 验证邮件发送示例
 * 展示如何使用VerificationEmailTemplate发送验证码邮件
 */
class VerificationEmailExample
{
    /**
     * 发送验证码邮件
     * 
     * @param string $to 收件人邮箱
     * @param string $code 验证码
     * @param array $config SMTP配置
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public static function sendVerificationEmail($to, $code, array $config = [], array $options = [])
    {
        // 默认选项
        $defaultOptions = [
            'expire_minutes' => 10,
            'support_url' => 'https://support.example.com',
            'subject' => '验证您的账户电子邮件地址',
            'from_name' => '系统通知'
        ];
        
        // 合并选项
        $options = array_merge($defaultOptions, $options);
        
        // 生成邮件内容
        $params = [
            'email' => $to,
            'code' => $code,
            'expire_minutes' => $options['expire_minutes'],
            'support_url' => $options['support_url']
        ];
        
        // 获取邮件HTML内容
        $content = VerificationEmailTemplate::getTemplate($params);
        
        // 创建SMTP邮件发送实例
        $emailSender = new SmtpEmailMessage($config);
        
        // 设置发送参数
        $sendParams = [
            'subject' => $options['subject'],
            'from_name' => $options['from_name'],
            'is_html' => true
        ];
        
        // 发送邮件
        return $emailSender->send($to, $content, $sendParams);
    }
    
    /**
     * 使用示例
     */
    public static function example()
    {
        // SMTP配置
        $config = [
            'host' => 'smtp.example.com',
            'port' => 465,
            'username' => '<EMAIL>',
            'password' => 'password',
            'secure' => 'ssl',
            'from' => '<EMAIL>',
            'from_name' => '系统通知'
        ];
        
        // 生成6位数验证码
        $code = sprintf('%06d', mt_rand(0, 999999));
        
        // 发送验证邮件
        $result = self::sendVerificationEmail('<EMAIL>', $code, $config, [
            'subject' => '您的账户验证码',
            'expire_minutes' => 15
        ]);
        
        // 返回结果
        return $result;
    }
}