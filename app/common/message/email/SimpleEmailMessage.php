<?php

namespace app\common\message\email;

use app\common\message\AbstractMessage;

/**
 * 简单邮件服务实现（基于PHP mail函数）
 */
class SimpleEmailMessage extends AbstractMessage
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'php';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'email';
    
    /**
     * 发送邮件
     * @param string $to 收件人邮箱
     * @param string $content 邮件内容
     * @param array $params 额外参数，可包含：
     *                      - subject: 邮件主题
     *                      - from: 发件人
     *                      - cc: 抄送
     *                      - bcc: 密送
     *                      - is_html: 是否HTML内容
     * @return mixed 发送结果
     */
    public function send($to, $content, array $params = [])
    {
        // 获取参数
        $subject = $params['subject'] ?? '系统通知';
        $from = $params['from'] ?? ($this->config['from'] ?? '<EMAIL>');
        $isHtml = $params['is_html'] ?? false;
        
        // 设置邮件头
        $headers = [];
        $headers[] = "From: {$from}";
        
        // 抄送
        if (!empty($params['cc'])) {
            $headers[] = "Cc: {$params['cc']}";
        }
        
        // 密送
        if (!empty($params['bcc'])) {
            $headers[] = "Bcc: {$params['bcc']}";
        }
        
        // 内容类型
        if ($isHtml) {
            $headers[] = "MIME-Version: 1.0";
            $headers[] = "Content-type: text/html; charset=utf-8";
        } else {
            $headers[] = "Content-type: text/plain; charset=utf-8";
        }

        // 发送邮件
        $result = mail($to, $subject, $content, implode("\r\n", $headers));

        return [
            'success' => $result,
            'message' => $result ? '邮件发送成功' : '邮件发送失败',
            'provider' => $this->provider,
            'to' => $to
        ];
    }
}