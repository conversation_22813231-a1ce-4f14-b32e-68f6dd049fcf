<?php

namespace app\common\message\email;

/**
 * 通知风格的邮件模板
 * 用于发送通知邮件，Notice邮件的样式
 */
class NoticeEmailTemplate implements EmailTemplateInterface
{
    /**
     * 获取Dynadot风格邮件HTML模板
     * 
     * @param array $params 模板参数，可包含：
     *                      - name: 收件人姓名
     *                      - email: 用户邮箱
     *                      - title: 邮件标题
     *                      - content: 邮件正文内容（可以是HTML格式）
     *                      - changes: 变更内容（数组格式，每个元素为一条变更信息）
     *                      - steps: 操作步骤（数组格式，每个元素为一个步骤）
     *                      - help_url: 帮助链接
     *                      - support_email: 支持邮箱
     * @return string 渲染后的HTML内容
     */
    public static function getTemplate(array $params = []) :string
    {
        // 提取参数，设置默认值
        $name = $params['name'] ?? '';
        $email = $params['email'] ?? '';
        $title = $params['title'] ?? '重要通知';
        $content = $params['content'] ?? '';
        $changes = $params['changes'] ?? [];
        $steps = $params['steps'] ?? [];
        $hostname = $params['hostname'] ?? '';
        $helpUrl = $params['help_url'] ?? 'https://help.example.com';
        $supportEmail = $params['support_email'] ?? '<EMAIL>';
        
        // 构建变更内容HTML
        $changesHtml = '';
        if (!empty($changes)) {
            $changesHtml .= '<ul style="margin-top: 10px; margin-bottom: 15px; padding-left: 20px;">';
            foreach ($changes as $change) {
                $changesHtml .= '<li style="margin-bottom: 8px;">' . $change . '</li>';
            }
            $changesHtml .= '</ul>';
        }
        
        // 构建操作步骤HTML
        $stepsHtml = '';
        if (!empty($steps)) {
            $stepsHtml .= '<ol style="margin-top: 15px; margin-bottom: 20px; padding-left: 20px;">';
            foreach ($steps as $index => $step) {
                $stepsHtml .= '<li style="margin-bottom: 8px;">' . $step . '</li>';
            }
            $stepsHtml .= '</ol>';
        }

        // 构建HTML模板
        $html = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0;">
    <div style="padding: 10px;background: #f2f3f5;">
        <div style="">
        <!-- 顶部Logo区域 -->
        <div style="text-align: center; padding: 20px 0; text-align: center;color: #333333;font-size: 25px;font-weight: bold;">
        {$hostname}
        </div>
        
        <!-- 邮件内容区域 -->
        <div style="max-width:760px;margin: 0 auto;padding: 20px 30px;background-color: #ffffff;border-radius: 12px;line-height: 30px;font-size: 14px;color: #474747;box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);border-top: 5px solid #328ffa;">
            <p>Hi {$name},</p>
            
            <p>{$content}</p>
            
            <!-- 变更内容区域 -->
            {$changesHtml}
            
            <!-- 操作步骤区域 -->
            <div style="margin-top: 20px;">
                <p style="font-weight: bold; margin-bottom: 10px;">操作步骤：</p>
                {$stepsHtml}
            </div>
            
            <!-- 联系支持区域 -->
            <p style="margin-top: 25px;">如果您有任何问题，请随时联系我们的支持团队：<a href="mailto:{$supportEmail}" style="color: #0066cc; text-decoration: none;">{$supportEmail}</a></p>
            
            <p style="margin-top: 20px;">
                此致，<br>
                {$hostname}
            </p>
        </div>
        
        <!-- 底部区域 -->
        <div style="text-align: center; padding: 15px; font-size: 12px; color: #666;">
            <p style="margin: 5px 0;">Sign In | <a href="$helpUrl">Help & Support | Contact Us</a></p>
            <p style="margin: 5px 0;">© {$hostname} LLC. All rights reserved.</p>
        </div>
        </div>
    </div>
</body>
</html>
HTML;
        
        return $html;
    }
}