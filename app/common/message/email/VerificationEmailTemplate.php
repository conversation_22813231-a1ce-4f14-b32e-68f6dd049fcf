<?php

namespace app\common\message\email;

/**
 * Google风格的验证邮件模板
 * 用于发送账号验证邮件，模仿Google邮件的样式
 */
class VerificationEmailTemplate implements EmailTemplateInterface
{
    /**
     * 获取验证邮件HTML模板
     * 
     * @param array $params 模板参数，可包含：
     *                      - email: 用户邮箱
     *                      - code: 验证码
     *                      - expire_minutes: 验证码有效期（分钟）
     *                      - support_url: 支持链接
     *                      - email: 主邮箱
     *                      - backup_email: 辅助邮箱
     *                      - expire_date: 过期日期
     *                      - hostname: 站点域名
     * @return string 渲染后的HTML内容
     */
    public static function getTemplate(array $params = []) :string
    {
        // 提取参数，设置默认值
        $email = $params['email'] ?? '';
        $code = $params['code'] ?? '';
        $backupEmail = $params['backup_email'] ?? '';
        $expireDate = $params['expire_date'] ?? '';
        $hostname = $params['hostname'] ?? '';
        $expireMinutes = $params['expire_minutes']/60 ?? 10;
        $supportUrl = $params['support_url'] ?? 'https://support.example.com';
        $downloadUrl = $params['download_url'] ?? '#';

        // 构建HTML模板
        $html = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请登录您的 {$hostname} 账号</title>
</head>
<body style="font-family: 'Google Sans',Roboto,Arial,sans-serif; line-height: 1.6; color: #202124; margin: 0; padding: 20px;">
    <div style="max-width: 600px; margin: 0 auto; background: #fff; border-radius: 8px; border: 1px solid #dadce0;">
        <!-- 邮件图标 -->
        <div style="text-align: center; padding: 40px 20px 20px;">
            <img src="https://www.google.com/images/branding/googlelogo/2x/googlelogo_color_92x30dp.png" alt="Google" style="height: 30px;">
        </div>
        
        <!-- 主要内容 -->
        <div style="padding: 0 40px;">
            <h1 style="font-size: 24px; font-weight: 400; text-align: center; margin-bottom: 30px;">请验证您的 {$hostname} 账号</h1>
            
            <div style="margin-bottom: 20px;">
                <img src="https://www.google.com/images/icons/profile_avatar.png" alt="Profile" style="width: 20px; height: 20px; vertical-align: middle;">
                <span style="margin-left: 10px; color: #202124;">{$email}</span>
            </div>

            <p style="color: #5f6368; margin-bottom: 30px;">
                您的 {$hostname} 账号已连续 2 年无人用过。<br>
                如果想保留您的 {$hostname} 账号，请在 {$expireDate} 之前<a href="#" style="color: #1a73e8; text-decoration: none;">登录</a>。
            </p>

            <p style="color: #5f6368; margin-bottom: 30px;">
                为了保护用户隐私和账号数据，{$hostname} 会删除长期无人使用的 {$hostname} 账号。
                <a href="#" style="color: #1a73e8; text-decoration: none;">了解详情</a>
            </p>

            <p style="color: #5f6368; margin-bottom: 30px;">
                了解如何<a href="{$downloadUrl}" style="color: #1a73e8; text-decoration: none;">下载您的 {$hostname} 数据</a>。
            </p>
        </div>

        <!-- 辅助邮箱信息 -->
        <div style="background-color: #f8f9fa; padding: 20px 40px; margin-top: 20px; color: #5f6368; font-size: 12px;">
            <p style="margin: 0;">这是发送给 {$email} 的安全提醒的副本。验证码：{$code}</p>
            <p style="margin: 5px 0 0;">{$backupEmail} 是该账号的辅助邮箱。如果不认识此账号，请<a href="#" style="color: #1a73e8; text-decoration: none;">移除您的电子邮件地址</a>。</p>
        </div>

        <!-- 页脚 -->
        <div style="padding: 20px 40px; text-align: center; color: #5f6368; font-size: 11px;">
            <p>我们向您发送这封电子邮件，目的是让您了解关于您的 {$hostname} 账号和服务的重大变化。</p>
            <p style="margin-top: 15px;">© 2025 {$hostname} LLC, 1600 Amphitheatre Parkway, Mountain View, CA 94043, USA</p>
        </div>
    </div>
</body>
</html>
HTML;
        
        return $html;
    }
}