<?php

namespace app\common\message;

/**
 * 消息发送抽象基类
 */
abstract class AbstractMessage implements MessageInterface
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = '';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = '';
    
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }
    
    /**
     * 获取发送服务名称
     * @return string
     */
    public function getServiceName()
    {
        return $this->serviceName;
    }
    
    /**
     * 获取发送服务提供商
     * @return string
     */
    public function getServiceProvider()
    {
        return $this->provider;
    }
    
    /**
     * 发送消息
     * @param string $to 接收者
     * @param string $content 消息内容
     * @param array $params 额外参数
     * @return mixed 发送结果
     */
    abstract public function send($to, $content, array $params = []);
}