<?php

namespace app\common;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Config;

class Auth
{

    protected static $prefix = 'oB10uD5vj0y00B3J';
    
    // 操作日志前缀
    protected static $logPrefix = '[AUTH]';
    
    /**
     * 获取缓存前缀
     * 
     * @param string $type 缓存类型
     * @return string
     */
    private static function getCachePrefix($type = 'token')
    {
        switch ($type) {
            case 'blacklist':
                return 'token_blacklist:';
            case 'rate_limit':
                return 'rate_limit:';
            case 'suspicious':
                return 'suspicious:';
            case 'redis':
                return self::getTokenConfig('redis.prefix', 'token:');
            default:
                return 'token:';
        }
    }
    
    /**
     * 获取Token配置
     * 
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    private static function getTokenConfig($key = null, $default = null)
    {
        if ($key === null) {
            return Config::get('token', []);
        }
        return Config::get('token.' . $key, $default);
    }
    
    /**
     * 获取安全配置状态
     * 
     * @return array
     */
    public static function getSecurityStatus()
    {
        return [
            'blacklist_enabled' => self::getTokenConfig('security.blacklist.enabled', true),
            'logging_enabled' => self::getTokenConfig('security.logging.enabled', true),
            'rate_limit_enabled' => true, // 速率限制总是启用
            'suspicious_detection_enabled' => self::getTokenConfig('security.suspicious_detection.enabled', true),
            'encryption_enabled' => self::getTokenConfig('encryption.enabled', true),
            'rate_limit_max_attempts' => self::getTokenConfig('security.rate_limit.max_attempts', 60),
            'rate_limit_time_window' => self::getTokenConfig('security.rate_limit.time_window', 3600),
            'suspicious_ip_threshold' => self::getTokenConfig('security.suspicious_detection.ip_failure_threshold', 20),
            'suspicious_detection_window' => self::getTokenConfig('security.suspicious_detection.detection_window', 3600),
            'blacklist_default_expire' => self::getTokenConfig('security.blacklist.default_expire', 86400),
            'jwt_access_expire' => self::getTokenConfig('jwt.access_expire', 7200),
            'jwt_refresh_expire' => self::getTokenConfig('jwt.refresh_expire', 604800)
        ];
    }
    
    /**
     * 验证配置完整性
     * 
     * @return array
     */
    public static function validateConfig()
    {
        $errors = [];
        $config = self::getTokenConfig();
        
        // 检查必要的配置项
        $requiredKeys = [
            'driver',
            'security.rate_limit.max_attempts',
            'security.rate_limit.time_window',
            'jwt.access_expire',
            'jwt.refresh_expire'
        ];
        
        foreach ($requiredKeys as $key) {
            if (self::getTokenConfig($key) === null) {
                $errors[] = "缺少必要配置项: {$key}";
            }
        }
        
        // 检查数值配置的合理性
        $maxAttempts = self::getTokenConfig('security.rate_limit.max_attempts', 60);
        if ($maxAttempts <= 0) {
            $errors[] = '速率限制最大尝试次数必须大于0';
        }
        
        $timeWindow = self::getTokenConfig('security.rate_limit.time_window', 3600);
        if ($timeWindow <= 0) {
            $errors[] = '速率限制时间窗口必须大于0';
        }
        
        $accessExpire = self::getTokenConfig('jwt.access_expire', 7200);
        if ($accessExpire <= 0) {
            $errors[] = '访问令牌过期时间必须大于0';
        }
        
        $refreshExpire = self::getTokenConfig('jwt.refresh_expire', 604800);
        if ($refreshExpire <= 0) {
            $errors[] = '刷新令牌过期时间必须大于0';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'config' => $config
        ];
    }
    
    /**
     * 生成更安全的JWT密钥
     * 
     * @return string
     */
    private static function getSecureKey()
    {
        // 使用更强的哈希算法和盐值
        $salt = Config::get('app.app_key', 'default_salt');
        return hash('sha256', self::$prefix . $salt . 'jwt_secret');
    }
    
    /**
     * 记录Token操作日志
     * 
     * @param string $action 操作类型
     * @param int $userId 用户ID
     * @param string $token Token
     * @param array $extra 额外信息
     */
    private static function logTokenAction($action, $userId = null, $token = null, $extra = [])
    {
        // 检查是否启用日志记录
        if (!self::getTokenConfig('security.logging.enabled', true)) {
            return;
        }
        
        $logData = [
            'action' => $action,
            'user_id' => $userId,
            'token' => $token ? substr($token, 0, 8) . '...' : null, // 只记录前8位
            'ip' => request()->ip(),
            'user_agent' => request()->header('user-agent'),
            'timestamp' => date('Y-m-d H:i:s'),
            'extra' => $extra
        ];
        
        // 检查是否记录敏感操作
        if (!self::getTokenConfig('security.logging.log_sensitive', false)) {
            unset($logData['token']);
        }
        
        Log::info(self::$logPrefix . ' ' . $action, $logData);
    }
    
    /**
     * 检查Token是否在黑名单中
     * 
     * @param string $token Token
     * @return bool
     */
    private static function isTokenBlacklisted($token)
    {
        // 检查是否启用黑名单功能
        if (!self::getTokenConfig('security.blacklist.enabled', true)) {
            return false;
        }

        $cache = Cache::has(self::getCachePrefix('blacklist') . $token);

        if ($cache) {
            return true;
        }

        $result = \app\model\TokenBlacklist::isBlacklisted($token);

        if ($result !== false) {
            return true;
        }

        return false;
    }
    
    /**
     * 将Token加入黑名单
     * 
     * @param string $token Token
     * @param int $expireTime 过期时间（秒）
     * @return bool
     */
    private static function addToBlacklist($token, $userId = 0, $reason = '', $jwtId = '', $expireTime = null)
    {
        // 检查是否启用黑名单功能
        if (!self::getTokenConfig('security.blacklist.enabled', true)) {
            return false;
        }
        
        // 使用配置中的默认过期时间
        if ($expireTime === null) {
            $expireTime = self::getTokenConfig('security.blacklist.default_expire', 86400);
        }
        
        // 使用TokenBlacklist模型添加到黑名单
        $blacklistResult = \app\model\TokenBlacklist::addToBlacklist(
            $token,
            $userId,
            $reason,
            $jwtId
        );

        Cache::set(self::getCachePrefix('blacklist') . $token, true, $expireTime);

        return $blacklistResult;
    }

    public static function generateToken($type, $userId, $customData, $expireTime, $prefix = '')
    {
        try {
            // 使用更安全的密钥生成方式
            $key = self::getSecureKey();

            $jti = uniqid('', true); // JWT ID，用于唯一标识

            // 基础token数据
            $token = [
                "iss" => self::getTokenConfig('jwt.issuer', 'anchor_system'),  // 签发者
                "iat" => time(),    // 签发时间
                "nbf" => time(),    // 该时间之前不接收处理该Token
                "exp" => time() + $expireTime, // 过期时间
                "aud" => self::getTokenConfig('jwt.audience', 'anchor_client'), // 接收者
                "sub" => self::getTokenConfig('jwt.subject', 'user_auth'),  // 主题
                "jti" => $jti,
                "data" => [         // 自定义信息
                    'userId' => $userId,
                    'type' => $type
                ]
            ];

            // 合并自定义数据到data字段
            if (!empty($customData) && is_array($customData)) {
                $token['data'] = array_merge($token['data'], $customData);
            }

            $jwt = JWT::encode($token, $key, 'HS256');
            // 将JWT转换为UUID格式
            $accessToken = self::jwtToUuid($jwt);
            
            // 记录Token创建日志
            self::logTokenAction('TOKEN_CREATED', $userId, $accessToken, [
                'type' => $type,
                'expire_time' => $expireTime
            ]);
            
            // 使用TokenLog模型记录操作日志
            \app\model\TokenLog::logGenerate($userId, $type, true, '', [
                'type' => $type,
                'expire_time' => $expireTime,
                'token' => $accessToken,
                'jwt_id' => $jti,
                'custom_data' => $customData
            ]);
            
            // 记录安全事件
            \app\model\SecurityEvent::record(
                \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                "生成新Token: 类型={$type}, 用户ID={$userId}",
                \app\model\SecurityEvent::SEVERITY_LOW,
                [
                    'token' => substr($accessToken, 0, 8) . '***',
                    'type' => $type,
                    'jwt_id' => $jti,
                    'expire_time' => $expireTime,
                    'action' => 'token_generate'
                ],
                $userId
            );

            return ['access_token' => $accessToken, 'jwt' => $jwt, 'jti' => $jti, 'user_id' => $userId];
        } catch (\Exception $e) {
            self::logTokenAction('TOKEN_CREATE_FAILED', $userId, null, [
                'error' => $e->getMessage()
            ]);

            // 使用TokenLog模型记录操作日志
            \app\model\TokenLog::logGenerate($userId, $type, false, '', [
                'type' => $type,
                'expire_time' => $expireTime,
                'token' => '',
                'jwt_id' => '',
                'custom_data' => $customData
            ]);
            
            // 记录异常到安全事件
            \app\model\SecurityEvent::record(
                \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                "Token生成失败: " . $e->getMessage(),
                \app\model\SecurityEvent::SEVERITY_HIGH,
                [
                    'user_id' => $userId,
                    'type' => $type,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ],
                $userId
            );
            
            throw new \Exception('Token生成失败: ' . $e->getMessage(), ApiCode::SYSTEM_ERROR);
        }
    }

    /**
     * 创建JWT Token
     * 
     * @param int $userId 用户ID
     * @param array $customData 自定义数据，可选
     * @param int $expireTime 过期时间(秒)，默认7200秒
     * @param bool $withRefreshToken 是否同时返回刷新令牌，默认false
     * @return string|array JWT Token (UUID格式)，或者包含访问令牌和刷新令牌的数组
     */
    public static function createToken($userId, $customData = [], $expireTime = null, $withRefreshToken = false, $expireRefreshTime = null)
    {
        try {
            // 使用配置中的默认过期时间
            if ($expireTime === null) {
                $expireTime = self::getTokenConfig('jwt.access_expire', 7200);
            }
            if ($expireRefreshTime === null) {
                $expireRefreshTime = self::getTokenConfig('jwt.refresh_expire', 604800);
            }
            
            $accessToken = self::generateToken('access_token', $userId, $customData, $expireTime);

            // 如果需要刷新令牌，则创建并返回包含访问令牌和刷新令牌的数组
            if ($withRefreshToken) {
                $refreshToken = self::createRefreshToken($userId, $customData);

                $data = array_merge([
                    'user_id' => $userId
                    , 'jwt' => $accessToken['jwt']
                    , 'access_token' => $accessToken['access_token'],
                    'jti' => $accessToken['jti'],
                    ], $customData
                );

                $result = TokenStorage::refreshWithNewToken($accessToken, $data, $refreshToken, $expireTime, $expireRefreshTime);

                if(!$result) {
                    // 记录存储失败事件
                    \app\model\SecurityEvent::record(
                        \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                        "Token存储失败: 用户ID={$userId}",
                        \app\model\SecurityEvent::SEVERITY_HIGH,
                        [
                            'user_id' => $userId,
                            'with_refresh_token' => true,
                            'action' => 'token_storage_failed'
                        ],
                        $userId
                    );
                    return false;
                }
                
                // 记录成功创建带刷新Token的事件
                \app\model\SecurityEvent::record(
                    \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                    "成功创建访问Token和刷新Token: 用户ID={$userId}",
                    \app\model\SecurityEvent::SEVERITY_LOW,
                    [
                        'user_id' => $userId,
                        'access_token' => substr($accessToken['access_token'], 0, 8) . '***',
                        'refresh_token' => substr($refreshToken['access_token'], 0, 8) . '***',
                        'expires_in' => $expireTime,
                        'refresh_expires_in' => $expireRefreshTime
                    ],
                    $userId
                );
                
                return [
                    'access_token' => $accessToken['access_token'],
                    'refresh_token' => $refreshToken['access_token'],
                    'expires_in' => $expireTime,
                    'refresh_expires_in' => $expireRefreshTime
                ];
            }

            TokenStorage::store($accessToken, array_merge(
                [
                    'user_id' => $userId
                    , 'jwt' => $accessToken['jwt']
                    , 'jti' => $accessToken['jti'],
                ], $customData), $expireTime
            );

            return [
                'access_token' => $accessToken['access_token'],
                'expires_in' => $expireTime,
            ];

        } catch (\Exception $e) {
            // 记录创建Token异常
            \app\model\SecurityEvent::record(
                \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                "创建Token过程中发生异常: " . $e->getMessage(),
                \app\model\SecurityEvent::SEVERITY_HIGH,
                [
                    'user_id' => $userId,
                    'with_refresh_token' => $withRefreshToken,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ],
                $userId
            );
            
            throw $e;
        }
    }

    /**
     * 验证JWT Token
     * 
     * @param string $token UUID格式的Token
     * @return array|false 成功返回解码后的数据，失败返回false
     */
    public static function checkToken($token)
    {
        try {
            // 检查Token是否在黑名单中
            if (self::isTokenBlacklisted($token)) {
                self::logTokenAction('TOKEN_BLACKLISTED', null, $token);
                
                // 记录黑名单Token访问事件
                \app\model\SecurityEvent::recordBlacklistAccess($token);
                
                throw new \Exception('Token已被撤销', ApiCode::ACCESS_TOKEN_INVALID);
            }
            
            // 将UUID格式转换回JWT
            $jwt = self::uuidToJwt($token, false);

            // 使用更安全的密钥验证
            $key = new Key(self::getSecureKey(), 'HS256');
            $decoded = JWT::decode($jwt['jwt'], $key);

            // 验证JWT ID是否存在（防止重放攻击）
            if (!isset($decoded->jti)) {
                // 记录Token格式无效事件
                \app\model\SecurityEvent::recordInvalidToken($token, 'JWT ID缺失');
                
                throw new \Exception('Token格式无效', ApiCode::ACCESS_TOKEN_INVALID);
            }
            
            $tokenData = (array)$decoded->data;
            $userId = $tokenData['userId'] ?? 0;

            // 记录Token验证成功日志
            self::logTokenAction('TOKEN_VERIFIED', $userId, $token);
            
            // 使用TokenLog模型记录验证日志
            \app\model\TokenLog::logValidate($token, $userId, true, '', [
                'jwt_id' => $decoded->jti,
                'token_type' => $tokenData['type'] ?? 'access_token'
            ]);

            // 返回解码后的数据
            return $tokenData;
        } catch (\Firebase\JWT\ExpiredException $e) {
            // token已过期
            self::logTokenAction('TOKEN_EXPIRED', null, $token);
            
            // 记录Token过期事件
            \app\model\SecurityEvent::recordInvalidToken($token, 'Token已过期');
            
            // 记录验证失败日志
            \app\model\TokenLog::logValidate($token, 0, false, 'Token已过期');
            
            throw new \Exception('Token已过期', ApiCode::ACCESS_TOKEN_EXPIRED);
        } catch (\Firebase\JWT\SignatureInvalidException $e) {
            // 签名无效
            self::logTokenAction('TOKEN_SIGNATURE_INVALID', null, $token);
            
            // 记录签名无效事件
            \app\model\SecurityEvent::recordInvalidToken($token, '签名无效');
            
            // 记录验证失败日志
            \app\model\TokenLog::logValidate($token, 0, false, '签名无效');
            
            throw new \Exception('Token签名无效', ApiCode::ACCESS_TOKEN_INVALID);
        } catch (\Firebase\JWT\BeforeValidException $e) {
            // token在当前服务器时间之前
            self::logTokenAction('TOKEN_NOT_VALID_YET', null, $token);
            
            // 记录Token尚未生效事件
            \app\model\SecurityEvent::recordInvalidToken($token, 'Token尚未生效');
            
            // 记录验证失败日志
            \app\model\TokenLog::logValidate($token, 0, false, 'Token尚未生效');
            
            throw new \Exception('Token尚未生效', ApiCode::ACCESS_TOKEN_INVALID);
        } catch (\Exception $e) {
            // 其他错误
            self::logTokenAction('TOKEN_VERIFY_FAILED', null, $token, [
                'error' => $e->getMessage()
            ]);
            
            // 记录其他验证失败事件
            \app\model\SecurityEvent::recordInvalidToken($token, $e->getMessage());
            
            // 记录验证失败日志
            \app\model\TokenLog::logValidate($token, 0, false, $e->getMessage());
            
            throw new \Exception($e->getMessage(), $e->getCode() ?: ApiCode::ACCESS_TOKEN_INVALID);
        }
    }
    
    /**
     * 创建刷新令牌
     * 
     * @param int $userId 用户ID
     * @param array $customData 自定义数据，可选
     * @param int $expireTime 过期时间(秒)，默认604800秒(7天)
     * @return array 刷新令牌 (UUID格式)
     */
    public static function createRefreshToken($userId, $customData = [], $expireTime = null)
    {
        try {
            // 使用配置中的默认刷新令牌过期时间
            if ($expireTime === null) {
                $expireTime = self::getTokenConfig('jwt.refresh_expire', 604800);
            }
            
            // 将JWT转换为UUID格式，使用refresh_前缀区分
            $refreshToken = self::generateToken('refresh_token', $userId, $customData, $expireTime, 'refresh_');
            
            // 记录刷新Token创建事件
            \app\model\SecurityEvent::record(
                \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                "创建刷新Token: 用户ID={$userId}",
                \app\model\SecurityEvent::SEVERITY_LOW,
                [
                    'user_id' => $userId,
                    'token' => substr($refreshToken['access_token'], 0, 8) . '***',
                    'jwt_id' => $refreshToken['jti'],
                    'expire_time' => $expireTime,
                    'action' => 'refresh_token_create'
                ],
                $userId
            );
            
            return $refreshToken;
        } catch (\Exception $e) {
            // 记录刷新Token创建失败事件
            \app\model\SecurityEvent::record(
                \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                "创建刷新Token失败: " . $e->getMessage(),
                \app\model\SecurityEvent::SEVERITY_HIGH,
                [
                    'user_id' => $userId,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ],
                $userId
            );
            
            throw $e;
        }
    }
    
    /**
     * 刷新访问令牌
     * 
     * @param string $refreshToken 刷新令牌
     * @param int $accessTokenExpireTime 新访问令牌的过期时间(秒)，默认7200秒
     * @return array 包含新的访问令牌的数组
     */
    public static function refreshToken($refreshToken, $accessTokenExpireTime = null)
    {
        try {
            // 使用配置中的默认访问令牌过期时间
            if ($accessTokenExpireTime === null) {
                $accessTokenExpireTime = self::getTokenConfig('jwt.access_expire', 7200);
            }

            // 检查刷新令牌是否在黑名单中
            if (self::isTokenBlacklisted($refreshToken)) {
                self::logTokenAction('REFRESH_TOKEN_BLACKLISTED', null, $refreshToken);
                
                // 记录黑名单刷新Token访问事件
                \app\model\SecurityEvent::recordBlacklistAccess($refreshToken);
                
                throw new \Exception('刷新令牌已被撤销', ApiCode::ACCESS_TOKEN_INVALID);
            }
            
            // 验证刷新令牌
            $jwt = self::uuidToJwt($refreshToken, true);
            $key = new Key(self::getSecureKey(), 'HS256');
            $decoded = JWT::decode($jwt['jwt'], $key);

            // 检查是否为刷新令牌
            if (!isset($decoded->data->type) || $decoded->data->type !== 'refresh_token') {
                // 记录无效刷新Token类型事件
                \app\model\SecurityEvent::recordInvalidToken($refreshToken, '无效的刷新令牌类型');
                
                throw new \Exception('无效的刷新令牌类型', ApiCode::ACCESS_TOKEN_INVALID);
            }
            
            // 获取用户ID和自定义数据
            $userId = $decoded->data->userId;
            $customData = (array)$decoded->data;
            
            // 移除类型标识，避免污染访问令牌
            unset($customData['type']);
            
            // 创建新的访问令牌
            $accessToken = self::createToken($userId, $customData, $accessTokenExpireTime);

            // 记录令牌刷新日志
            self::logTokenAction('TOKEN_REFRESHED', $userId, $refreshToken, [
                'new_access_token' => substr($accessToken['access_token'], 0, 8) . '...',
                'expires_in' => $accessTokenExpireTime
            ]);
            
            // 使用TokenLog模型记录刷新操作
            \app\model\TokenLog::logRefresh($refreshToken, $accessToken, $userId, true, '', [
                'new_access_token' => substr($accessToken['access_token'], 0, 8) . '***',
                'expires_in' => $accessTokenExpireTime,
                'jwt_id' => $decoded->jti
            ]);
            
            // 记录Token刷新成功事件
            \app\model\SecurityEvent::record(
                \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                "Token刷新成功: 用户ID={$userId}",
                \app\model\SecurityEvent::SEVERITY_LOW,
                [
                    'user_id' => $userId,
                    'refresh_token' => substr($refreshToken, 0, 8) . '***',
                    'new_access_token' => substr($accessToken['access_token'], 0, 8) . '***',
                    'expires_in' => $accessTokenExpireTime,
                    'jwt_id' => $decoded->jti,
                    'action' => 'token_refresh'
                ],
                $userId
            );
            
            return [
                'access_token' => $accessToken['access_token'],
                'expires_in' => $accessTokenExpireTime
            ];
        } catch (\Firebase\JWT\ExpiredException $e) {
            self::logTokenAction('REFRESH_TOKEN_EXPIRED', null, $refreshToken);
            
            // 记录刷新Token过期事件
            \app\model\SecurityEvent::recordInvalidToken($refreshToken, '刷新令牌已过期');
            
            // 记录刷新失败日志
            \app\model\TokenLog::logRefresh($refreshToken, 0, 0,  false, '刷新令牌已过期');
            
            throw new \Exception('刷新令牌已过期', ApiCode::ACCESS_TOKEN_EXPIRED);
        } catch (\Exception $e) {
            self::logTokenAction('TOKEN_REFRESH_FAILED', null, $refreshToken, [
                'error' => $e->getMessage()
            ]);
            
            // 记录刷新Token失败事件
            \app\model\SecurityEvent::record(
                \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                "Token刷新失败: " . $e->getMessage(),
                \app\model\SecurityEvent::SEVERITY_MEDIUM,
                [
                    'refresh_token' => substr($refreshToken, 0, 8) . '***',
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );
            
            // 记录刷新失败日志
            \app\model\TokenLog::logRefresh($refreshToken, 0, 0, false, $e->getMessage());
            
            throw new \Exception('令牌刷新失败: ' . $e->getMessage(), $e->getCode() ?: ApiCode::SYSTEM_ERROR);
        }
    }
    
    /**
     * 将JWT转换为UUID格式
     * 
     * @param string $jwt JWT Token
     * @return string UUID格式的Token
     */
    private static function jwtToUuid($jwt)
    {
        // 对JWT进行哈希处理，确保长度足够
        $hash = hash('sha256', $jwt);
        
        // 按照UUID格式重新组织: 8-4-4-4-12
        $uuid = substr($hash, 0, 8) . '-' . 
                substr($hash, 8, 4) . '-' . 
                substr($hash, 12, 4) . '-' . 
                substr($hash, 16, 4) . '-' . 
                substr($hash, 20, 12);

        return $uuid;
    }
    
    /**
     * 将UUID格式转换回JWT
     * 
     * @param string $uuid UUID格式的Token
     * @return string 原始JWT Token
     */
    private static function uuidToJwt($uuid, $refresh)
    {
        // 使用ThinkPHP的缓存系统获取映射关系
        $jwt = TokenStorage::get($uuid, $refresh);

        if ($jwt) {
            return $jwt;
        }
        
        // 如果找不到映射关系，抛出异常
        throw new \Exception('InvalidToken', ApiCode::ACCESS_TOKEN_INVALID);
    }

    /**
     * 移除Token（增强版）
     * @param string $token Token值
     * @param bool $addToBlacklist 是否加入黑名单
     * @param string $reason 撤销原因
     * @return bool
     */
    public static function removeToken($token, $addToBlacklist = true, $reason = 'manual_revoke')
    {
        try {
            // 记录撤销日志
            trace("Token撤销开始: " . substr($token, 0, 8) . "***, 加入黑名单: " . ($addToBlacklist ? '是' : '否') . ", 原因: {$reason}", 'info');
            
            // 解析Token获取用户信息
            $jwt = self::uuidToJwt($token, false);
            $key = new Key(self::getSecureKey(), 'HS256');
            $decoded = JWT::decode($jwt['jwt'], $key);
            $tokenData = (array)$decoded->data;
            $userId = $tokenData['userId'] ?? null;
            $jwtId = $tokenData['jti'] ?? '';
            
            // 如果需要加入黑名单
            if ($addToBlacklist) {
                // 使用TokenBlacklist模型添加到黑名单
                $blacklistResult = self::addToBlacklist(
                    $token,
                    $userId,
                    $reason,
                    $jwtId
                );
                
                if ($blacklistResult) {
                    trace("Token已加入黑名单: " . substr($token, 0, 8) . "***", 'info');
                    
                    // 记录安全事件
                    \app\model\SecurityEvent::record(
                        \app\model\SecurityEvent::EVENT_TYPE_TOKEN_REPLAY,
                        "Token被手动撤销并加入黑名单: " . substr($token, 0, 8) . "***",
                        \app\model\SecurityEvent::SEVERITY_MEDIUM,
                        [
                            'token' => substr($token, 0, 8) . '***',
                            'reason' => $reason,
                            'jwt_id' => $jwtId,
                            'action' => 'manual_revoke'
                        ],
                        $userId
                    );
                } else {
                    trace("Token加入黑名单失败: " . substr($token, 0, 8) . "***", 'error');
                }
            }

            // 记录Token创建日志
            self::logTokenAction('TOKEN_REMOVE', $userId, $token, [
                'reason' => $reason
            ]);
            
            // 记录Token操作日志
            \app\model\TokenLog::logRevoke($token, $userId, $addToBlacklist, $reason, [
                'action' => 'revoke_tokens',
                'reason' => $reason
            ]);
            
            // 从存储中移除Token
            $storage = TokenStorage::getInstance();
            $result = $storage->remove($token);
            
            trace("Token撤销" . ($result ? '成功' : '失败') . ": " . substr($token, 0, 8) . "***", $result ? 'info' : 'error');
            
            return $result;
        } catch (\Exception $e) {
            trace("Token撤销异常: " . $e->getMessage(), 'error');
            
            // 记录异常到安全事件
            \app\model\SecurityEvent::record(
                \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                "Token撤销过程中发生异常: " . $e->getMessage(),
                \app\model\SecurityEvent::SEVERITY_HIGH,
                [
                    'token' => substr($token, 0, 8) . '***',
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );
            
            return false;
        }
    }
    
    /**
     * 批量撤销用户的所有Token
     * @param int $userId 用户ID
     * @param string $reason 撤销原因
     * @return bool
     */
    public static function revokeAllUserTokens($userId, $reason = 'security_cleanup')
    {
        try {
            trace("开始批量撤销用户Token: 用户ID={$userId}, 原因={$reason}", 'info');
            
            // 使用TokenBlacklist模型批量撤销用户Token
            $result = \app\model\TokenBlacklist::revokeAllUserTokens($userId, $reason);
            
            if ($result) {
                // 记录安全事件
                \app\model\SecurityEvent::record(
                    \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                    "批量撤销用户所有Token: 用户ID={$userId}",
                    \app\model\SecurityEvent::SEVERITY_MEDIUM,
                    [
                        'user_id' => $userId,
                        'reason' => $reason,
                        'action' => 'revoke_all_tokens',
                        'revoked_count' => $result
                    ],
                    $userId
                );
                
                // 记录操作日志
                \app\model\TokenLog::logRevoke('', $userId, true, $reason, [
                    'action' => 'revoke_all_tokens',
                    'reason' => $reason,
                    'revoked_count' => $result
                ]);
                
                trace("用户Token批量撤销完成: 用户ID={$userId}, 撤销数量={$result}", 'info');
            } else {
                trace("用户Token批量撤销失败: 用户ID={$userId}", 'error');
            }
            
            return $result > 0;
        } catch (\Exception $e) {
            trace("批量撤销Token异常: " . $e->getMessage(), 'error');
            
            // 记录异常到安全事件
            \app\model\SecurityEvent::record(
                \app\model\SecurityEvent::EVENT_TYPE_UNUSUAL_ACTIVITY,
                "批量撤销Token过程中发生异常: " . $e->getMessage(),
                \app\model\SecurityEvent::SEVERITY_HIGH,
                [
                    'user_id' => $userId,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );
            
            return false;
        }
    }
}