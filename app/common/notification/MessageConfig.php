<?php

namespace app\common\notification;

/**
 * 消息配置类
 * 用于管理通知相关的消息发送配置
 */
class MessageConfig
{
    /**
     * 获取通知类型对应的配置
     * 
     * @param int $notificationType 通知类型
     * @return array 配置数组
     */
    public static function getNotificationConfig($notificationType)
    {
        $config = config('notification.');
        $commonConfig = $config['common'] ?? [];
        
        // 根据通知类型获取对应配置
        switch ($notificationType) {
            case NotificationService::TYPE_SYSTEM:
                $typeConfig = $config['system'] ?? [];
                return [
                    // 优先使用新结构，然后是旧结构，最后是通用配置
                    'email_enabled' => isset($typeConfig['email']['enabled']) ? $typeConfig['email']['enabled'] : 
                                      ($config['system_email_enabled'] ?? 
                                       (isset($commonConfig['email']['enabled']) ? $commonConfig['email']['enabled'] : 
                                        $config['email_enabled'] ?? false)),
                    'sms_enabled' => isset($typeConfig['sms']['enabled']) ? $typeConfig['sms']['enabled'] : 
                                    ($config['system_sms_enabled'] ?? 
                                     (isset($commonConfig['sms']['enabled']) ? $commonConfig['sms']['enabled'] : 
                                      $config['sms_enabled'] ?? false)),
                ];
            case NotificationService::TYPE_ORDER:
                $typeConfig = $config['order'] ?? [];
                return [
                    'email_enabled' => isset($typeConfig['email']['enabled']) ? $typeConfig['email']['enabled'] : 
                                      ($config['order_email_enabled'] ?? 
                                       (isset($commonConfig['email']['enabled']) ? $commonConfig['email']['enabled'] : 
                                        $config['email_enabled'] ?? false)),
                    'sms_enabled' => isset($typeConfig['sms']['enabled']) ? $typeConfig['sms']['enabled'] : 
                                    ($config['order_sms_enabled'] ?? 
                                     (isset($commonConfig['sms']['enabled']) ? $commonConfig['sms']['enabled'] : 
                                      $config['sms_enabled'] ?? false)),
                ];
            case NotificationService::TYPE_ACTIVITY:
                $typeConfig = $config['activity'] ?? [];
                return [
                    'email_enabled' => isset($typeConfig['email']['enabled']) ? $typeConfig['email']['enabled'] : 
                                      ($config['activity_email_enabled'] ?? 
                                       (isset($commonConfig['email']['enabled']) ? $commonConfig['email']['enabled'] : 
                                        $config['email_enabled'] ?? false)),
                    'sms_enabled' => isset($typeConfig['sms']['enabled']) ? $typeConfig['sms']['enabled'] : 
                                    ($config['activity_sms_enabled'] ?? 
                                     (isset($commonConfig['sms']['enabled']) ? $commonConfig['sms']['enabled'] : 
                                      $config['sms_enabled'] ?? false)),
                ];
            case NotificationService::TYPE_COUPON:
                $typeConfig = $config['coupon'] ?? [];
                return [
                    'email_enabled' => isset($typeConfig['email']['enabled']) ? $typeConfig['email']['enabled'] : 
                                      ($config['coupon_email_enabled'] ?? 
                                       (isset($commonConfig['email']['enabled']) ? $commonConfig['email']['enabled'] : 
                                        $config['email_enabled'] ?? false)),
                    'sms_enabled' => isset($typeConfig['sms']['enabled']) ? $typeConfig['sms']['enabled'] : 
                                    ($config['coupon_sms_enabled'] ?? 
                                     (isset($commonConfig['sms']['enabled']) ? $commonConfig['sms']['enabled'] : 
                                      $config['sms_enabled'] ?? false)),
                ];
            default:
                return [
                    'email_enabled' => isset($commonConfig['email']['enabled']) ? $commonConfig['email']['enabled'] : 
                                      ($config['email_enabled'] ?? false),
                    'sms_enabled' => isset($commonConfig['sms']['enabled']) ? $commonConfig['sms']['enabled'] : 
                                    ($config['sms_enabled'] ?? false),
                ];
        }
    }
    
    /**
     * 检查是否需要发送邮件
     * 
     * @param int $notificationType 通知类型
     * @return bool 是否需要发送邮件
     */
    public static function shouldSendEmail($notificationType)
    {
        $config = self::getNotificationConfig($notificationType);
        return $config['email_enabled'];
    }
    
    /**
     * 检查是否需要发送短信
     * 
     * @param int $notificationType 通知类型
     * @return bool 是否需要发送短信
     */
    public static function shouldSendSms($notificationType)
    {
        $config = self::getNotificationConfig($notificationType);
        return $config['sms_enabled'];
    }
}