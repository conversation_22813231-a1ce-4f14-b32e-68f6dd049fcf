<?php

namespace app\common\notification;

use think\facade\Db;

/**
 * 通知服务类
 * 用于创建和管理通知
 */
class NotificationService
{
    // 通知类型常量
    const TYPE_SYSTEM = 1;    // 系统通知
    const TYPE_ORDER = 2;     // 订单通知
    const TYPE_ACTIVITY = 3;  // 活动通知
    const TYPE_COUPON = 4;    // 优惠券通知
    
    /**
     * 创建单条通知
     * 
     * @param int $userId 用户ID
     * @param int $type 通知类型
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @return int|bool 成功返回通知ID，失败返回false
     */
    public function create($userId, $type, $title, $content)
    {
        // 参数验证
        if (empty($userId) || empty($title) || empty($content)) {
            return false;
        }
        
        // 验证通知类型
        if (!in_array($type, [self::TYPE_SYSTEM, self::TYPE_ORDER, self::TYPE_ACTIVITY, self::TYPE_COUPON])) {
            $type = self::TYPE_SYSTEM; // 默认为系统通知
        }
        
        // 准备数据
        $data = [
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'content' => $content,
            'is_read' => 0,
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ];
        
        // 插入数据
        try {
            $notificationId = Db::table('notification')->insertGetId($data);
            return $notificationId;
        } catch (\Exception $e) {
            // 记录日志
            // \think\facade\Log::error('创建通知失败：' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量创建通知
     * 
     * @param array $notifications 通知数据数组，每个元素包含user_id, type, title, content
     * @return int 成功创建的通知数量
     */
    public function batchCreate(array $notifications)
    {
        if (empty($notifications)) {
            return 0;
        }
        
        $insertData = [];
        $now = time();
        
        foreach ($notifications as $notification) {
            // 参数验证
            if (empty($notification['user_id']) || empty($notification['title']) || empty($notification['content'])) {
                continue;
            }
            
            // 验证通知类型
            $type = isset($notification['type']) ? $notification['type'] : self::TYPE_SYSTEM;
            if (!in_array($type, [self::TYPE_SYSTEM, self::TYPE_ORDER, self::TYPE_ACTIVITY, self::TYPE_COUPON])) {
                $type = self::TYPE_SYSTEM; // 默认为系统通知
            }
            
            // 准备数据
            $insertData[] = [
                'user_id' => $notification['user_id'],
                'type' => $type,
                'title' => $notification['title'],
                'content' => $notification['content'],
                'is_read' => 0,
                'status' => 1,
                'create_time' => $now,
                'update_time' => $now
            ];
        }
        
        // 批量插入数据
        if (!empty($insertData)) {
            try {
                $affectedRows = Db::table('sys_notification')->insertAll($insertData);
                return $affectedRows;
            } catch (\Exception $e) {
                // 记录日志
                // \think\facade\Log::error('批量创建通知失败：' . $e->getMessage());
                return 0;
            }
        }
        
        return 0;
    }
    
    /**
     * 创建系统通知
     * 
     * @param int $userId 用户ID
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @return int|bool 成功返回通知ID，失败返回false
     */
    public function createSystemNotification($userId, $title, $content)
    {
        return $this->create($userId, self::TYPE_SYSTEM, $title, $content);
    }
    
    /**
     * 创建订单通知
     * 
     * @param int $userId 用户ID
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @return int|bool 成功返回通知ID，失败返回false
     */
    public function createOrderNotification($userId, $title, $content)
    {
        return $this->create($userId, self::TYPE_ORDER, $title, $content);
    }
    
    /**
     * 创建活动通知
     * 
     * @param int $userId 用户ID
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @return int|bool 成功返回通知ID，失败返回false
     */
    public function createActivityNotification($userId, $title, $content)
    {
        return $this->create($userId, self::TYPE_ACTIVITY, $title, $content);
    }
    
    /**
     * 创建优惠券通知
     * 
     * @param int $userId 用户ID
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @return int|bool 成功返回通知ID，失败返回false
     */
    public function createCouponNotification($userId, $title, $content)
    {
        return $this->create($userId, self::TYPE_COUPON, $title, $content);
    }
    
    /**
     * 批量创建系统通知
     * 
     * @param array $userIds 用户ID数组
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @return int 成功创建的通知数量
     */
    public function batchCreateSystemNotification(array $userIds, $title, $content)
    {
        if (empty($userIds) || empty($title) || empty($content)) {
            return 0;
        }
        
        $notifications = [];
        foreach ($userIds as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'type' => self::TYPE_SYSTEM,
                'title' => $title,
                'content' => $content
            ];
        }
        
        return $this->batchCreate($notifications);
    }
}