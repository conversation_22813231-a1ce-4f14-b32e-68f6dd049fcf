<?php

namespace app\common\goee_segmenta;

/**
 * 西班牙文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class SpanishSegmentationService
{
    /**
     * 西班牙文停用词列表
     * @var array
     */
    protected array $stopWords = [
        'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas', 'y', 'o', 'pero', 'sino', 'aunque',
        'en', 'de', 'a', 'por', 'para', 'con', 'sin', 'sobre', 'bajo', 'entre', 'desde', 'hasta',
        'yo', 'tú', 'él', 'ella', 'nosotros', 'vosotros', 'ellos', 'ellas', 'me', 'te', 'se', 'nos', 'os',
        'qué', 'cuándo', 'dónde', 'por qué', 'cómo', 'cuál', 'quién', 'cuánto', 'cuán<PERSON>', 'cuántas',
        'por favor', 'gracias', 'hola', 'perdón', 'disculpe', 'adiós', 'buenos días', 'buenas tardes',
        'este', 'esta', 'esto', 'estos', 'estas', 'ese', 'esa', 'eso', 'esos', 'esas', 'aquel', 'aquella',
        'ser', 'estar', 'tener', 'hacer', 'poder', 'deber', 'querer', 'saber', 'ir', 'venir', 'dar', 'ver'
    ];

    /**
     * 西班牙文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'amazon' => 10, 'mercadolibre' => 10, 'aliexpress' => 9, 'ebay' => 8, 'linio' => 8,
        'falabella' => 8, 'ripley' => 7, 'liverpool' => 7, 'palacio de hierro' => 7,
        
        // 支付方式
        'paypal' => 10, 'visa' => 9, 'mastercard' => 9, 'american express' => 8, 'mercadopago' => 9,
        'tarjeta de crédito' => 9, 'tarjeta de débito' => 7, 'transferencia bancaria' => 8, 'efectivo' => 6,
        
        // 核心业务动作
        'comprar' => 10, 'compra' => 10, 'pago' => 10, 'pagar' => 10, 'recarga' => 9,
        'reembolso' => 10, 'devolver' => 9, 'cambio' => 8, 'pedido' => 10, 'envío' => 9,
        'entrega' => 8, 'producto' => 9, 'artículo' => 8, 'precio' => 8, 'costo' => 7,
        'descuento' => 8, 'oferta' => 8, 'cupón' => 7, 'promoción' => 7, 'puntos' => 7,
        
        // 账户相关
        'iniciar sesión' => 9, 'registro' => 9, 'suscripción' => 8, 'cerrar sesión' => 7, 'cuenta' => 9,
        'perfil' => 7, 'contraseña' => 9, 'correo' => 8, 'usuario' => 8, 'verificación' => 8,
        
        // 客户服务
        'soporte' => 9, 'ayuda' => 8, 'servicio al cliente' => 10, 'consulta' => 8, 'chat' => 7,
        'teléfono' => 7, 'soporte por correo' => 8, 'ticket' => 7, 'queja' => 8, 'comentario' => 7,
        
        // 产品类别
        'electrónicos' => 8, 'ropa' => 7, 'libros' => 7, 'hogar' => 6, 'deportes' => 7,
        'juguetes' => 7, 'belleza' => 7, 'salud' => 7, 'automóvil' => 7, 'comida' => 7
    ];

    /**
     * 西班牙文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'navegar' => 6, 'buscar' => 7, 'filtrar' => 6, 'ordenar' => 5, 'comparar' => 6,
        'lista de deseos' => 6, 'carrito' => 7, 'cesta' => 6, 'favoritos' => 6, 'guardar' => 5,
        
        // 订单状态
        'pendiente' => 6, 'procesando' => 7, 'enviado' => 7, 'entregado' => 7, 'cancelado' => 7,
        'confirmado' => 6, 'completado' => 6, 'fallido' => 6, 'expirado' => 5,
        
        // 产品属性
        'marca' => 6, 'modelo' => 6, 'tamaño' => 6, 'color' => 6, 'peso' => 5,
        'material' => 5, 'calidad' => 6, 'condición' => 6, 'nuevo' => 5, 'usado' => 5,
        
        // 时间相关
        'hoy' => 5, 'mañana' => 5, 'ayer' => 5, 'semana' => 5, 'mes' => 5,
        'días laborables' => 6, 'fin de semana' => 5, 'feriado' => 5, 'horas' => 5,
        
        // 数量和测量
        'cantidad' => 6, 'monto' => 6, 'total' => 6, 'subtotal' => 6, 'impuesto' => 6,
        'comisión' => 6, 'tarifa' => 6, 'tasa' => 5, 'porcentaje' => 5
    ];

    /**
     * 西班牙文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'servicio al cliente', 'tarjeta de crédito', 'tarjeta de regalo', 'carrito de compras', 'lista de deseos',
        'cuenta de usuario', 'dirección de correo', 'número de teléfono', 'código postal', 'dirección',
        'días laborables', 'horario de trabajo', 'política de devolución', 'política de privacidad', 'términos de servicio',
        'envío gratis', 'entrega rápida', 'entrega el mismo día', 'entrega al día siguiente',
        'compras en línea', 'aplicación móvil', 'navegador web', 'motor de búsqueda', 'redes sociales',
        'reseña del producto', 'reseña del cliente', 'calificación con estrellas', 'descripción del producto',
        'historial de pedidos', 'historial de compras', 'método de pago', 'dirección de facturación', 'dirección de envío'
    ];

    /**
     * 提取西班牙文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 西班牙文词汇分词
        $wordKeywords = $this->spanishWordSegmentation($cleanText);
        $keywords = array_merge($keywords, $wordKeywords);
        
        // 策略5: 词干提取
        $stemKeywords = $this->spanishStemming($cleanText);
        $keywords = array_merge($keywords, $stemKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理西班牙文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);
        
        // 处理西班牙文特殊字符
        $text = $this->normalizeSpanishText($text);
        
        // 移除特殊符号，保留字母、数字、空格
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 规范化西班牙文文本
     * @param string $text
     * @return string
     */
    protected function normalizeSpanishText(string $text): string
    {
        // 保留重音符号，但统一某些变体
        $normalizations = [
            'ñ' => 'ñ', // 保持ñ
            'á' => 'á', 'é' => 'é', 'í' => 'í', 'ó' => 'ó', 'ú' => 'ú', // 保持重音
            'ü' => 'u', // 分音符转换
        ];
        
        foreach ($normalizations as $from => $to) {
            $text = str_replace($from, $to, $text);
        }
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (mb_stripos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 西班牙文词汇分词
     * @param string $text
     * @return array
     */
    protected function spanishWordSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格和标点分割
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 3 && preg_match('/^[a-záéíóúñü]+$/iu', $word)) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 西班牙文词干提取
     * @param string $text
     * @return array
     */
    protected function spanishStemming(string $text): array
    {
        $keywords = [];
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 4) {
                $stem = $this->spanishStem($word);
                if ($stem !== $word && mb_strlen($stem) >= 3) {
                    $keywords[] = $stem;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 西班牙文词干提取算法
     * @param string $word
     * @return string
     */
    protected function spanishStem(string $word): string
    {
        $word = mb_strtolower($word);
        
        // 去除常见后缀
        $suffixes = [
            // 动词后缀
            'ando', 'iendo', 'ado', 'ido', 'ar', 'er', 'ir',
            // 名词后缀
            'ción', 'sión', 'dad', 'tad', 'idad', 'ancia', 'encia',
            // 形容词后缀
            'oso', 'osa', 'ivo', 'iva', 'able', 'ible',
            // 复数形式
            'os', 'as', 'es',
            // 其他常见后缀
            'mente', 'ito', 'ita', 'illo', 'illa'
        ];
        
        foreach ($suffixes as $suffix) {
            if (mb_strlen($word) > mb_strlen($suffix) + 2 && 
                mb_substr($word, -mb_strlen($suffix)) === $suffix) {
                return mb_substr($word, 0, -mb_strlen($suffix));
            }
        }
        
        return $word;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidSpanishKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateSpanishWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证西班牙文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidSpanishKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 20) {
            return false;
        }
        
        // 停用词检查
        if (in_array(mb_strtolower($keyword), $this->stopWords)) {
            return false;
        }
        
        // 必须包含字母
        if (!preg_match('/[a-záéíóúñü]/iu', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算西班牙文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateSpanishWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count(mb_strtolower($text), mb_strtolower($keyword));
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->coreBusinessDict[mb_strtolower($keyword)] * 0.1;
        } elseif (isset($this->extendedBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->extendedBusinessDict[mb_strtolower($keyword)] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array(mb_strtolower($keyword), $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 重音符号权重（西班牙文特色）
        if (preg_match('/[áéíóúñ]/iu', $keyword)) {
            $weight += 0.2;
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示西班牙文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'normalized' => $this->normalizeSpanishText($text),
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'word_segmentation' => $this->spanishWordSegmentation($cleanText),
            'stemming' => $this->spanishStemming($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
