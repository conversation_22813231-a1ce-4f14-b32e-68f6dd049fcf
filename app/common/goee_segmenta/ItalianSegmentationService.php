<?php

namespace app\common\goee_segmenta;

/**
 * 意大利文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class ItalianSegmentationService
{
    /**
     * 意大利文停用词列表
     * @var array
     */
    protected array $stopWords = [
        'il', 'la', 'lo', 'le', 'gli', 'i', 'un', 'una', 'uno', 'e', 'o', 'ma', 'però', 'tuttavia',
        'in', 'di', 'a', 'da', 'per', 'con', 'su', 'tra', 'fra', 'sopra', 'sotto', 'dentro', 'fuori',
        'io', 'tu', 'lui', 'lei', 'noi', 'voi', 'loro', 'mi', 'ti', 'si', 'ci', 'vi', 'gli', 'le',
        'che', 'quando', 'dove', 'perché', 'come', 'quale', 'chi', 'quanto', 'quanti', 'quante',
        'per favore', 'grazie', 'ciao', 'scusa', 'scusi', 'arrivederci', 'buongiorno', 'buonasera',
        'questo', 'questa', 'questi', 'queste', 'quello', 'quella', 'quelli', 'quelle', 'qui', 'qua', 'lì', 'là',
        'essere', 'avere', 'fare', 'andare', 'venire', 'vedere', 'sapere', 'potere', 'dovere', 'volere', 'dire'
    ];

    /**
     * 意大利文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'amazon' => 10, 'ebay' => 9, 'zalando' => 8, 'unieuro' => 8, 'mediaworld' => 8,
        'euronics' => 7, 'yoox' => 7, 'privalia' => 7, 'groupon' => 7, 'subito' => 7,
        
        // 支付方式
        'paypal' => 10, 'visa' => 9, 'mastercard' => 9, 'american express' => 8, 'postepay' => 9,
        'carta di credito' => 9, 'carta di debito' => 7, 'bonifico bancario' => 8, 'contanti' => 6,
        'satispay' => 8, 'apple pay' => 8, 'google pay' => 8,
        
        // 核心业务动作
        'comprare' => 10, 'acquisto' => 10, 'pagamento' => 10, 'pagare' => 10, 'ricarica' => 9,
        'rimborso' => 10, 'restituire' => 9, 'cambio' => 8, 'ordine' => 10, 'consegna' => 9,
        'ricevimento' => 8, 'prodotto' => 9, 'articolo' => 8, 'prezzo' => 8, 'costo' => 7,
        'sconto' => 8, 'offerta' => 8, 'coupon' => 7, 'promozione' => 7, 'punti' => 7,
        
        // 账户相关
        'accesso' => 9, 'registrazione' => 9, 'abbonamento' => 8, 'uscita' => 7, 'account' => 9,
        'profilo' => 7, 'password' => 9, 'email' => 8, 'utente' => 8, 'verifica' => 8,
        
        // 客户服务
        'supporto' => 9, 'aiuto' => 8, 'servizio clienti' => 10, 'domanda' => 8, 'chat' => 7,
        'telefono' => 7, 'supporto email' => 8, 'ticket' => 7, 'reclamo' => 8, 'feedback' => 7,
        
        // 产品类别
        'elettronica' => 8, 'abbigliamento' => 7, 'libri' => 7, 'casa' => 6, 'sport' => 7,
        'giocattoli' => 7, 'bellezza' => 7, 'salute' => 7, 'auto' => 7, 'cibo' => 7
    ];

    /**
     * 意大利文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'navigare' => 6, 'cercare' => 7, 'filtrare' => 6, 'ordinare' => 5, 'confrontare' => 6,
        'lista desideri' => 6, 'carrello' => 7, 'cestino' => 6, 'preferiti' => 6, 'salvare' => 5,
        
        // 订单状态
        'in attesa' => 6, 'elaborazione' => 7, 'spedito' => 7, 'consegnato' => 7, 'annullato' => 7,
        'confermato' => 6, 'completato' => 6, 'fallito' => 6, 'scaduto' => 5,
        
        // 产品属性
        'marca' => 6, 'modello' => 6, 'taglia' => 6, 'colore' => 6, 'peso' => 5,
        'materiale' => 5, 'qualità' => 6, 'condizione' => 6, 'nuovo' => 5, 'usato' => 5,
        
        // 时间相关
        'oggi' => 5, 'domani' => 5, 'ieri' => 5, 'settimana' => 5, 'mese' => 5,
        'giorni lavorativi' => 6, 'fine settimana' => 5, 'festivo' => 5, 'ore' => 5,
        
        // 数量和测量
        'quantità' => 6, 'importo' => 6, 'totale' => 6, 'subtotale' => 6, 'tassa' => 6,
        'commissione' => 6, 'costo' => 6, 'tariffa' => 5, 'percentuale' => 5
    ];

    /**
     * 意大利文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'servizio clienti', 'carta di credito', 'carta regalo', 'carrello della spesa', 'lista dei desideri',
        'account utente', 'indirizzo email', 'numero di telefono', 'codice postale', 'indirizzo',
        'giorni lavorativi', 'orario di lavoro', 'politica di reso', 'politica sulla privacy', 'termini di servizio',
        'spedizione gratuita', 'consegna veloce', 'consegna in giornata', 'consegna il giorno successivo',
        'shopping online', 'app mobile', 'browser web', 'motore di ricerca', 'social media',
        'recensione prodotto', 'recensione cliente', 'valutazione stelle', 'descrizione prodotto',
        'cronologia ordini', 'cronologia acquisti', 'metodo di pagamento', 'indirizzo di fatturazione', 'indirizzo di spedizione'
    ];

    /**
     * 提取意大利文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 意大利文词汇分词
        $wordKeywords = $this->italianWordSegmentation($cleanText);
        $keywords = array_merge($keywords, $wordKeywords);
        
        // 策略5: 词干提取
        $stemKeywords = $this->italianStemming($cleanText);
        $keywords = array_merge($keywords, $stemKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理意大利文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);
        
        // 处理意大利文缩写
        $text = $this->expandItalianContractions($text);
        
        // 移除特殊符号，保留字母、数字、空格、撇号
        $text = preg_replace('/[^\p{L}\p{N}\s\']/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 展开意大利文缩写
     * @param string $text
     * @return string
     */
    protected function expandItalianContractions(string $text): string
    {
        $contractions = [
            'dell\'', 'dello', 'della', 'dei', 'degli', 'delle' => 'di',
            'nell\'', 'nello', 'nella', 'nei', 'negli', 'nelle' => 'in',
            'dall\'', 'dallo', 'dalla', 'dai', 'dagli', 'dalle' => 'da',
            'sull\'', 'sullo', 'sulla', 'sui', 'sugli', 'sulle' => 'su',
            'all\'', 'allo', 'alla', 'ai', 'agli', 'alle' => 'a',
            'coll\'', 'collo', 'colla', 'coi', 'cogli', 'colle' => 'con'
        ];
        
        foreach ($contractions as $contraction => $expansion) {
            if (is_array($contraction)) {
                foreach ($contraction as $variant) {
                    $text = str_replace($variant, $expansion, $text);
                }
            } else {
                $text = str_replace($contraction, $expansion, $text);
            }
        }
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (mb_stripos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 意大利文词汇分词
     * @param string $text
     * @return array
     */
    protected function italianWordSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格和标点分割
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 3 && preg_match('/^[a-zàáèéìíîòóùú]+$/iu', $word)) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 意大利文词干提取
     * @param string $text
     * @return array
     */
    protected function italianStemming(string $text): array
    {
        $keywords = [];
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 4) {
                $stem = $this->italianStem($word);
                if ($stem !== $word && mb_strlen($stem) >= 3) {
                    $keywords[] = $stem;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 意大利文词干提取算法
     * @param string $word
     * @return string
     */
    protected function italianStem(string $word): string
    {
        $word = mb_strtolower($word);
        
        // 去除常见后缀
        $suffixes = [
            // 动词后缀
            'ando', 'endo', 'ato', 'uto', 'ito', 'are', 'ere', 'ire',
            // 名词后缀
            'zione', 'sione', 'anza', 'enza', 'ità', 'mento', 'aggio',
            // 形容词后缀
            'oso', 'osa', 'ivo', 'iva', 'ale', 'ile', 'abile', 'ibile',
            // 复数形式
            'i', 'e', 'hi', 'he',
            // 其他常见后缀
            'mente', 'ino', 'ina', 'etto', 'etta'
        ];
        
        foreach ($suffixes as $suffix) {
            if (mb_strlen($word) > mb_strlen($suffix) + 2 && 
                mb_substr($word, -mb_strlen($suffix)) === $suffix) {
                return mb_substr($word, 0, -mb_strlen($suffix));
            }
        }
        
        return $word;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidItalianKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateItalianWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证意大利文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidItalianKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 20) {
            return false;
        }
        
        // 停用词检查
        if (in_array(mb_strtolower($keyword), $this->stopWords)) {
            return false;
        }
        
        // 必须包含字母
        if (!preg_match('/[a-zàáèéìíîòóùú]/iu', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算意大利文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateItalianWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count(mb_strtolower($text), mb_strtolower($keyword));
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->coreBusinessDict[mb_strtolower($keyword)] * 0.1;
        } elseif (isset($this->extendedBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->extendedBusinessDict[mb_strtolower($keyword)] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array(mb_strtolower($keyword), $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 重音符号权重（意大利文特色）
        if (preg_match('/[àáèéìíîòóùú]/iu', $keyword)) {
            $weight += 0.2;
        }
        
        // 6. 意大利特色词汇权重
        $italianTerms = ['postepay', 'satispay', 'unieuro', 'mediaworld'];
        foreach ($italianTerms as $term) {
            if (mb_stripos($keyword, $term) !== false) {
                $weight += 0.3;
                break;
            }
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示意大利文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'expanded_contractions' => $this->expandItalianContractions($text),
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'word_segmentation' => $this->italianWordSegmentation($cleanText),
            'stemming' => $this->italianStemming($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
