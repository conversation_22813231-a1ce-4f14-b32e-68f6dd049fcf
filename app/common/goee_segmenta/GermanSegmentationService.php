<?php

namespace app\common\goee_segmenta;

/**
 * 德文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class GermanSegmentationService
{
    /**
     * 德文停用词列表
     * @var array
     */
    protected array $stopWords = [
        'der', 'die', 'das', 'den', 'dem', 'des', 'ein', 'eine', 'einer', 'eines', 'einem', 'einen',
        'und', 'oder', 'aber', 'doch', 'sondern', 'denn', 'weil', 'wenn', 'als', 'wie', 'dass',
        'in', 'an', 'auf', 'zu', 'von', 'mit', 'bei', 'nach', 'vor', 'über', 'unter', 'zwischen', 'durch',
        'ich', 'du', 'er', 'sie', 'es', 'wir', 'ihr', 'sie', 'mich', 'dich', 'sich', 'uns', 'euch',
        'was', 'wann', 'wo', 'warum', 'wie', 'welcher', 'welche', 'welches', 'wer', 'wen', 'wem', 'wessen',
        'bitte', 'danke', 'hallo', 'entschuldigung', 'verzeihung', 'auf wiedersehen', 'guten tag', 'guten morgen',
        'dieser', 'diese', 'dieses', 'jener', 'jene', 'jenes', 'hier', 'dort', 'da', 'hier',
        'sein', 'haben', 'werden', 'können', 'müssen', 'sollen', 'wollen', 'dürfen', 'mögen', 'lassen'
    ];

    /**
     * 德文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'amazon' => 10, 'otto' => 9, 'zalando' => 8, 'ebay' => 8, 'mediamarkt' => 8,
        'saturn' => 8, 'real' => 7, 'kaufland' => 7, 'lidl' => 7, 'aldi' => 7,
        
        // 支付方式
        'paypal' => 10, 'visa' => 9, 'mastercard' => 9, 'american express' => 8, 'sofortüberweisung' => 9,
        'kreditkarte' => 9, 'ec-karte' => 7, 'banküberweisung' => 8, 'bar' => 6, 'rechnung' => 7,
        
        // 核心业务动作
        'kaufen' => 10, 'kauf' => 10, 'zahlung' => 10, 'bezahlen' => 10, 'aufladen' => 9,
        'rückerstattung' => 10, 'zurückgeben' => 9, 'umtausch' => 8, 'bestellung' => 10, 'lieferung' => 9,
        'zustellung' => 8, 'produkt' => 9, 'artikel' => 8, 'preis' => 8, 'kosten' => 7,
        'rabatt' => 8, 'angebot' => 8, 'gutschein' => 7, 'aktion' => 7, 'punkte' => 7,
        
        // 账户相关
        'anmelden' => 9, 'registrierung' => 9, 'abonnement' => 8, 'abmelden' => 7, 'konto' => 9,
        'profil' => 7, 'passwort' => 9, 'email' => 8, 'benutzername' => 8, 'verifizierung' => 8,
        
        // 客户服务
        'support' => 9, 'hilfe' => 8, 'kundenservice' => 10, 'anfrage' => 8, 'chat' => 7,
        'telefon' => 7, 'email-support' => 8, 'ticket' => 7, 'beschwerde' => 8, 'feedback' => 7,
        
        // 产品类别
        'elektronik' => 8, 'kleidung' => 7, 'bücher' => 7, 'haushalt' => 6, 'sport' => 7,
        'spielzeug' => 7, 'schönheit' => 7, 'gesundheit' => 7, 'auto' => 7, 'lebensmittel' => 7
    ];

    /**
     * 德文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'durchsuchen' => 6, 'suchen' => 7, 'filtern' => 6, 'sortieren' => 5, 'vergleichen' => 6,
        'wunschliste' => 6, 'warenkorb' => 7, 'favoriten' => 6, 'speichern' => 5,
        
        // 订单状态
        'wartend' => 6, 'bearbeitung' => 7, 'versandt' => 7, 'geliefert' => 7, 'storniert' => 7,
        'bestätigt' => 6, 'abgeschlossen' => 6, 'fehlgeschlagen' => 6, 'abgelaufen' => 5,
        
        // 产品属性
        'marke' => 6, 'modell' => 6, 'größe' => 6, 'farbe' => 6, 'gewicht' => 5,
        'material' => 5, 'qualität' => 6, 'zustand' => 6, 'neu' => 5, 'gebraucht' => 5,
        
        // 时间相关
        'heute' => 5, 'morgen' => 5, 'gestern' => 5, 'woche' => 5, 'monat' => 5,
        'werktage' => 6, 'wochenende' => 5, 'feiertag' => 5, 'stunden' => 5,
        
        // 数量和测量
        'menge' => 6, 'betrag' => 6, 'gesamt' => 6, 'zwischensumme' => 6, 'steuer' => 6,
        'gebühr' => 6, 'kosten' => 6, 'rate' => 5, 'prozent' => 5
    ];

    /**
     * 德文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'kundenservice', 'kreditkarte', 'geschenkkarte', 'warenkorb', 'wunschliste',
        'benutzerkonto', 'email-adresse', 'telefonnummer', 'postleitzahl', 'adresse',
        'werktage', 'arbeitszeiten', 'rückgaberichtlinie', 'datenschutzrichtlinie', 'nutzungsbedingungen',
        'kostenlose lieferung', 'schnelle lieferung', 'lieferung am selben tag', 'lieferung am nächsten tag',
        'online-shopping', 'mobile app', 'webbrowser', 'suchmaschine', 'soziale medien',
        'produktbewertung', 'kundenbewertung', 'sterne-bewertung', 'produktbeschreibung',
        'bestellhistorie', 'kaufhistorie', 'zahlungsmethode', 'rechnungsadresse', 'lieferadresse'
    ];

    /**
     * 提取德文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 德文词汇分词
        $wordKeywords = $this->germanWordSegmentation($cleanText);
        $keywords = array_merge($keywords, $wordKeywords);
        
        // 策略5: 德文复合词分解
        $compoundSplitKeywords = $this->germanCompoundSplitting($cleanText);
        $keywords = array_merge($keywords, $compoundSplitKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理德文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);
        
        // 处理德文特殊字符
        $text = $this->normalizeGermanText($text);
        
        // 移除特殊符号，保留字母、数字、空格、连字符
        $text = preg_replace('/[^\p{L}\p{N}\s\-]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 规范化德文文本
     * @param string $text
     * @return string
     */
    protected function normalizeGermanText(string $text): string
    {
        // 德文特殊字符处理
        $normalizations = [
            'ä' => 'ae', 'ö' => 'oe', 'ü' => 'ue', 'ß' => 'ss',
            'Ä' => 'ae', 'Ö' => 'oe', 'Ü' => 'ue'
        ];
        
        // 保留原始字符，同时添加标准化版本
        $normalized = $text;
        foreach ($normalizations as $from => $to) {
            if (strpos($text, $from) !== false) {
                $normalized .= ' ' . str_replace($from, $to, $text);
            }
        }
        
        return $normalized;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (mb_stripos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 德文词汇分词
     * @param string $text
     * @return array
     */
    protected function germanWordSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格和标点分割
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 3 && preg_match('/^[a-zäöüß]+$/iu', $word)) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 德文复合词分解
     * @param string $text
     * @return array
     */
    protected function germanCompoundSplitting(string $text): array
    {
        $keywords = [];
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 8) { // 长词可能是复合词
                $parts = $this->splitGermanCompound($word);
                $keywords = array_merge($keywords, $parts);
            }
        }
        
        return $keywords;
    }

    /**
     * 分解德文复合词
     * @param string $word
     * @return array
     */
    protected function splitGermanCompound(string $word): array
    {
        $parts = [];
        $word = mb_strtolower($word);
        
        // 简单的德文复合词分解
        $commonParts = [
            'kunden', 'service', 'waren', 'korb', 'geschenk', 'karte', 'kredit',
            'bestellung', 'lieferung', 'zahlung', 'produkt', 'artikel', 'preis',
            'rabatt', 'angebot', 'gutschein', 'konto', 'profil', 'passwort',
            'telefon', 'nummer', 'adresse', 'post', 'leitzahl', 'email'
        ];
        
        foreach ($commonParts as $part) {
            if (strpos($word, $part) !== false && mb_strlen($part) >= 4) {
                $parts[] = $part;
            }
        }
        
        return $parts;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidGermanKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateGermanWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证德文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidGermanKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 25) { // 德文词汇可能很长
            return false;
        }
        
        // 停用词检查
        if (in_array(mb_strtolower($keyword), $this->stopWords)) {
            return false;
        }
        
        // 必须包含字母
        if (!preg_match('/[a-zäöüß]/iu', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算德文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateGermanWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count(mb_strtolower($text), mb_strtolower($keyword));
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->coreBusinessDict[mb_strtolower($keyword)] * 0.1;
        } elseif (isset($this->extendedBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->extendedBusinessDict[mb_strtolower($keyword)] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array(mb_strtolower($keyword), $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 复合词权重（德文特色）
        if ($length >= 8) {
            $weight += 0.3; // 长复合词通常更重要
        }
        
        // 6. 德文特殊字符权重
        if (preg_match('/[äöüß]/iu', $keyword)) {
            $weight += 0.2;
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示德文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'normalized' => $this->normalizeGermanText($text),
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'word_segmentation' => $this->germanWordSegmentation($cleanText),
            'compound_splitting' => $this->germanCompoundSplitting($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
