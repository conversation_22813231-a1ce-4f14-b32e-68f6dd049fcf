<?php

namespace app\common\goee_segmenta;

/**
 * 印地语分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class HindiSegmentationService
{
    /**
     * 印地语停用词列表
     * @var array
     */
    protected array $stopWords = [
        'में', 'पर', 'से', 'को', 'का', 'की', 'के', 'और', 'या', 'लेकिन', 'परंतु', 'तथा',
        'मैं', 'आप', 'वह', 'वे', 'हम', 'तुम', 'यह', 'वो', 'इस', 'उस', 'इन', 'उन',
        'क्या', 'कब', 'कहाँ', 'क्यों', 'कैसे', 'कौन', 'कितना', 'कितने', 'कितनी',
        'नमस्ते', 'धन्यवाद', 'माफ करें', 'अलविदा', 'कोई बात नहीं', 'ठीक है',
        'यहाँ', 'वहाँ', 'जहाँ', 'तहाँ', 'इधर', 'उधर',
        'है', 'हैं', 'था', 'थे', 'थी', 'होगा', 'होंगे', 'होगी', 'कर', 'करना', 'किया', 'करते'
    ];

    /**
     * 印地语核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'अमेज़न' => 10, 'फ्लिपकार्ट' => 10, 'स्नैपडील' => 9, 'पेटीएम' => 9, 'मिंत्रा' => 8,
        'जबॉन्ग' => 8, 'शॉपक्लूज' => 7, 'नायका' => 7, 'बिग बास्केट' => 7,
        
        // 支付方式
        'पेटीएम' => 10, 'गूगल पे' => 9, 'फोनपे' => 9, 'वीज़ा' => 8, 'मास्टरकार्ड' => 8,
        'क्रेडिट कार्ड' => 9, 'डेबिट कार्ड' => 7, 'बैंक ट्रांसफर' => 8, 'नकद' => 6,
        
        // 核心业务动作
        'खरीदना' => 10, 'खरीदारी' => 10, 'भुगतान' => 10, 'पैसे देना' => 10, 'रिचार्ज' => 9,
        'रिफंड' => 10, 'वापसी' => 9, 'अदला-बदली' => 8, 'ऑर्डर' => 10, 'डिलीवरी' => 9,
        'प्राप्ति' => 8, 'उत्पाद' => 9, 'सामान' => 8, 'कीमत' => 8, 'लागत' => 7,
        'छूट' => 8, 'ऑफर' => 8, 'कूपन' => 7, 'प्रमोशन' => 7, 'पॉइंट्स' => 7,
        
        // 账户相关
        'लॉगिन' => 9, 'रजिस्ट्रेशन' => 9, 'सदस्यता' => 8, 'लॉगआउट' => 7, 'खाता' => 9,
        'प्रोफाइल' => 7, 'पासवर्ड' => 9, 'ईमेल' => 8, 'यूजरनेम' => 8, 'वेरिफिकेशन' => 8,
        
        // 客户服务
        'सहायता' => 9, 'मदद' => 8, 'कस्टमर केयर' => 10, 'पूछताछ' => 8, 'चैट' => 7,
        'फोन' => 7, 'ईमेल सपोर्ट' => 8, 'टिकट' => 7, 'शिकायत' => 8, 'फीडबैक' => 7,
        
        // 产品类别
        'इलेक्ट्रॉनिक्स' => 8, 'कपड़े' => 7, 'किताबें' => 7, 'घर' => 6, 'खेल' => 7,
        'खिलौने' => 7, 'सुंदरता' => 7, 'स्वास्थ्य' => 7, 'कार' => 7, 'खाना' => 7
    ];

    /**
     * 印地语扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'ब्राउज़' => 6, 'खोज' => 7, 'फिल्टर' => 6, 'सॉर्ट' => 5, 'तुलना' => 6,
        'विशलिस्ट' => 6, 'कार्ट' => 7, 'पसंदीदा' => 6, 'सेव' => 5,
        
        // 订单状态
        'पेंडिंग' => 6, 'प्रोसेसिंग' => 7, 'शिप्ड' => 7, 'डिलीवर्ड' => 7, 'कैंसल' => 7,
        'कन्फर्म' => 6, 'कम्प्लीट' => 6, 'फेल' => 6, 'एक्सपायर' => 5,
        
        // 产品属性
        'ब्रांड' => 6, 'मॉडल' => 6, 'साइज़' => 6, 'रंग' => 6, 'वजन' => 5,
        'मैटेरियल' => 5, 'क्वालिटी' => 6, 'कंडीशन' => 6, 'नया' => 5, 'पुराना' => 5,
        
        // 时间相关
        'आज' => 5, 'कल' => 5, 'कल' => 5, 'हफ्ता' => 5, 'महीना' => 5,
        'कार्य दिवस' => 6, 'वीकेंड' => 5, 'छुट्टी' => 5, 'घंटे' => 5,
        
        // 数量和测量
        'मात्रा' => 6, 'राशि' => 6, 'कुल' => 6, 'सब टोटल' => 6, 'टैक्स' => 6,
        'फीस' => 6, 'चार्ज' => 6, 'रेट' => 5, 'प्रतिशत' => 5
    ];

    /**
     * 印地语组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'कस्टमर केयर', 'क्रेडिट कार्ड', 'गिफ्ट कार्ड', 'शॉपिंग कार्ट', 'विश लिस्ट',
        'यूजर अकाउंट', 'ईमेल एड्रेस', 'फोन नंबर', 'पिन कोड', 'एड्रेस',
        'कार्य दिवस', 'कार्य समय', 'रिटर्न पॉलिसी', 'प्राइवेसी पॉलिसी', 'सर्विस टर्म्स',
        'फ्री डिलीवरी', 'फास्ट डिलीवरी', 'सेम डे डिलीवरी', 'नेक्स्ट डे डिलीवरी',
        'ऑनलाइन शॉपिंग', 'मोबाइल ऐप', 'वेब ब्राउज़र', 'सर्च इंजन', 'सोशल मीडिया',
        'प्रोडक्ट रिव्यू', 'कस्टमर रिव्यू', 'स्टार रेटिंग', 'प्रोडक्ट डिस्क्रिप्शन',
        'ऑर्डर हिस्ट्री', 'परचेज हिस्ट्री', 'पेमेंट मेथड', 'बिलिंग एड्रेस', 'शिपिंग एड्रेस'
    ];

    /**
     * 提取印地语关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 印地语词汇分词
        $wordKeywords = $this->hindiWordSegmentation($cleanText);
        $keywords = array_merge($keywords, $wordKeywords);
        
        // 策略5: 梵文字符处理
        $devanagariKeywords = $this->devanagariProcessing($cleanText);
        $keywords = array_merge($keywords, $devanagariKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理印地语文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 移除特殊符号，但保留梵文字符
        $text = preg_replace('/[^\p{Devanagari}\p{N}\s]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (strpos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 印地语词汇分词
     * @param string $text
     * @return array
     */
    protected function hindiWordSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格分割
        $words = preg_split('/\s+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 2 && preg_match('/[\p{Devanagari}]/u', $word)) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 梵文字符处理
     * @param string $text
     * @return array
     */
    protected function devanagariProcessing(string $text): array
    {
        $keywords = [];
        
        // 提取梵文词汇（2-8字符）
        preg_match_all('/[\p{Devanagari}]{2,8}/u', $text, $matches);
        if (!empty($matches[0])) {
            foreach ($matches[0] as $word) {
                // 去除常见后缀
                $stemmed = $this->removeHindiSuffixes($word);
                if ($stemmed !== $word && mb_strlen($stemmed) >= 2) {
                    $keywords[] = $stemmed;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 去除印地语后缀
     * @param string $word
     * @return string
     */
    protected function removeHindiSuffixes(string $word): string
    {
        // 常见的印地语后缀
        $suffixes = ['ना', 'ता', 'ते', 'ती', 'या', 'ये', 'यी', 'ों', 'ें', 'ीं', 'ाँ', 'ियों'];
        
        foreach ($suffixes as $suffix) {
            if (mb_strlen($word) > mb_strlen($suffix) + 1 && 
                mb_substr($word, -mb_strlen($suffix)) === $suffix) {
                return mb_substr($word, 0, -mb_strlen($suffix));
            }
        }
        
        return $word;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidHindiKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateHindiWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证印地语关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidHindiKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 15) {
            return false;
        }
        
        // 停用词检查
        if (in_array($keyword, $this->stopWords)) {
            return false;
        }
        
        // 必须包含梵文字符
        if (!preg_match('/[\p{Devanagari}]/u', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算印地语关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateHindiWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count($text, $keyword);
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[$keyword])) {
            $weight += $this->coreBusinessDict[$keyword] * 0.1;
        } elseif (isset($this->extendedBusinessDict[$keyword])) {
            $weight += $this->extendedBusinessDict[$keyword] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array($keyword, $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 梵文字符权重
        if (preg_match('/[\p{Devanagari}]/', $keyword)) {
            $weight += 0.3;
        }
        
        // 6. 业务相关性权重
        if ($this->hasHindiBusinessRelevance($keyword)) {
            $weight += 0.5;
        }
        
        return $weight;
    }

    /**
     * 判断是否有印地语业务相关性
     * @param string $text
     * @return bool
     */
    protected function hasHindiBusinessRelevance(string $text): bool
    {
        // 检查是否包含业务相关字符
        $businessChars = ['खरी', 'भुग', 'ऑर्ड', 'डिली', 'उत्पा', 'कीम', 'छूट', 'ऑफ', 'खा', 'सहा'];
        
        foreach ($businessChars as $char) {
            if (strpos($text, $char) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 调试信息：显示印地语分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'word_segmentation' => $this->hindiWordSegmentation($cleanText),
            'devanagari_processing' => $this->devanagariProcessing($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
