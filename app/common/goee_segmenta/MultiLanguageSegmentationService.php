<?php

namespace app\common\goee_segmenta;

/**
 * 多语言分词服务 - 统一分发器
 * 使用专门的分词服务处理不同语言，统一分发和管理
 */
class MultiLanguageSegmentationService
{
    /**
     * 专门的分词服务实例
     * @var array
     */
    protected array $segmentationServices = [];

    /**
     * 支持的语言列表
     * @var array
     */
    protected array $supportedLanguages = [
        'zh' => '中文',
        'en' => 'English',
        'ja' => '日本語',
        'ko' => '한국어',
        'ar' => 'العربية',
        'ru' => 'Русский',
        'es' => 'Español',
        'fr' => 'Français',
        'de' => 'Deutsch',
        'pt' => 'Português',
        'it' => 'Italiano',
        'th' => 'ไทย',
        'vi' => 'Tiếng Việt',
        'hi' => 'हिन्दी'
    ];

    /**
     * 语言到分词服务的映射
     * @var array
     */
    protected array $serviceMapping = [
        // 专门分词服务
        'zh' => ChineseSegmentationService::class,
        'en' => EnglishSegmentationService::class,
        'ja' => JapaneseSegmentationService::class,
        'ko' => KoreanSegmentationService::class,
        'ar' => ArabicSegmentationService::class,
        'ru' => RussianSegmentationService::class,
        'es' => SpanishSegmentationService::class,
        'fr' => FrenchSegmentationService::class,
        'de' => GermanSegmentationService::class,
        'th' => ThaiSegmentationService::class,
        'vi' => VietnameseSegmentationService::class,
        'hi' => HindiSegmentationService::class,
        'pt' => PortugueseSegmentationService::class,
        'it' => ItalianSegmentationService::class
    ];

    /**
     * 语言检测正则表达式
     * @var array
     */
    protected array $languagePatterns = [
        'zh' => '/[\x{4e00}-\x{9fff}]/u',           // 中文字符
        'ja' => '/[\x{3040}-\x{309f}\x{30a0}-\x{30ff}\x{4e00}-\x{9fff}]/u', // 日文字符
        'ko' => '/[\x{ac00}-\x{d7af}\x{1100}-\x{11ff}\x{3130}-\x{318f}]/u', // 韩文字符
        'ar' => '/[\x{0600}-\x{06ff}\x{0750}-\x{077f}]/u', // 阿拉伯文字符
        'ru' => '/[\x{0400}-\x{04ff}]/u',           // 俄文字符
        'es' => '/[ñáéíóúüÑÁÉÍÓÚÜ]/u',             // 西班牙文特殊字符
        'fr' => '/[àâäéèêëïîôöùûüÿçÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ]/u', // 法文特殊字符
        'de' => '/[äöüßÄÖÜ]/u',                     // 德文特殊字符
        'pt' => '/[ãõáéíóúâêôàçÃÕÁÉÍÓÚÂÊÔÀÇ]/u',   // 葡萄牙文特殊字符
        'it' => '/[àèéìíîòóùúÀÈÉÌÍÎÒÓÙÚ]/u',       // 意大利文特殊字符
        'th' => '/[\x{0e00}-\x{0e7f}]/u',           // 泰文字符
        'vi' => '/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/u', // 越南文特殊字符
        'hi' => '/[\x{0900}-\x{097f}]/u',           // 印地文字符
        'en' => '/^[a-zA-Z\s\d\p{P}]+$/u'           // 英文字符（最后检测）
    ];

    /**
     * 构造函数 - 初始化分词服务
     */
    public function __construct()
    {
        // 延迟加载分词服务
    }

    /**
     * 提取多语言关键词（统一分发入口）
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 检测语言
        $detectedLanguages = $this->detectLanguages($text);
        $primaryLanguage = array_key_first($detectedLanguages);
        $primaryConfidence = $detectedLanguages[$primaryLanguage] ?? 0;

        // 2. 根据主要语言选择分词策略
        if ($primaryConfidence > 0.3) {
            // 使用专门的分词服务
            $keywords = $this->segmentWithSpecializedService($text, $primaryLanguage, $maxWords);

            // 如果专门服务返回的关键词不足，补充通用分词
            if (count($keywords) < $maxWords * 0.7) {
                $universalKeywords = $this->universalSegmentation($text);
                $keywords = array_merge($keywords, $universalKeywords);
                $keywords = array_unique($keywords);
                $keywords = array_slice($keywords, 0, $maxWords);
            }
        } else {
            // 混合语言或未知语言，使用通用分词
            $keywords = $this->universalSegmentation($text);
            $keywords = array_slice($keywords, 0, $maxWords);
        }

        return $keywords;
    }

    /**
     * 使用专门的分词服务
     * @param string $text
     * @param string $language
     * @param int $maxWords
     * @return array
     */
    protected function segmentWithSpecializedService(string $text, string $language, int $maxWords): array
    {
        // 获取对应的分词服务
        $service = $this->getSegmentationService($language);

        if ($service) {
            return $service->extractKeywords($text, $maxWords);
        }

        // 降级到通用分词
        return $this->universalSegmentation($text);
    }

    /**
     * 获取指定语言的分词服务
     * @param string $language
     * @return object|null
     */
    protected function getSegmentationService(string $language)
    {
        // 如果已经实例化，直接返回
        if (isset($this->segmentationServices[$language])) {
            return $this->segmentationServices[$language];
        }

        // 检查是否有专门的分词服务
        if (!isset($this->serviceMapping[$language])) {
            return null;
        }

        $serviceClass = $this->serviceMapping[$language];

        // 如果是通用分词，返回null
        if ($serviceClass === 'universal') {
            return null;
        }

        // 实例化专门的分词服务
        try {
            $this->segmentationServices[$language] = new $serviceClass();
            return $this->segmentationServices[$language];
        } catch (\Exception $e) {
            // 实例化失败，记录错误
            error_log("Failed to instantiate segmentation service for {$language}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 检测文本中的语言
     * @param string $text
     * @return array 语言代码 => 置信度
     */
    public function detectLanguages(string $text): array
    {
        $languages = [];
        $totalChars = mb_strlen($text);
        
        if ($totalChars === 0) {
            return $languages;
        }
        
        foreach ($this->languagePatterns as $lang => $pattern) {
            preg_match_all($pattern, $text, $matches);
            $matchCount = count($matches[0]);
            $confidence = $matchCount / $totalChars;
            
            if ($confidence > 0) {
                $languages[$lang] = $confidence;
            }
        }
        
        // 如果没有检测到特定语言，默认为英文
        if (empty($languages)) {
            $languages['en'] = 1.0;
        }
        
        // 按置信度排序
        arsort($languages);
        
        return $languages;
    }

    /**
     * 预处理多语言文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        // 移除特殊符号，但保留语言特定字符
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        
        // 再次规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 通用分词（处理混合语言和未知语言）
     * @param string $text
     * @return array
     */
    protected function universalSegmentation(string $text): array
    {
        $keywords = [];

        // 1. 按空格分割
        $words = preg_split('/\s+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        foreach ($words as $word) {
            if (mb_strlen($word) >= 2) {
                $keywords[] = $word;
            }
        }

        // 2. 提取数字
        preg_match_all('/\d{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        // 3. 提取英文单词
        preg_match_all('/[a-zA-Z]{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        return $keywords;
    }

    /**
     * 获取支持的语言列表
     * @return array
     */
    public function getSupportedLanguages(): array
    {
        return $this->supportedLanguages;
    }

    /**
     * 调试信息：显示多语言分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $detectedLanguages = $this->detectLanguages($text);
        $primaryLanguage = array_key_first($detectedLanguages);

        $debug = [
            'original' => $text,
            'detected_languages' => $detectedLanguages,
            'primary_language' => $primaryLanguage,
            'specialized_service' => null,
            'specialized_result' => [],
            'universal_segmentation' => $this->universalSegmentation($text),
            'final_keywords' => $this->extractKeywords($text)
        ];

        // 如果有专门的分词服务，显示其结果
        $service = $this->getSegmentationService($primaryLanguage);
        if ($service) {
            $debug['specialized_service'] = get_class($service);
            if (method_exists($service, 'debugSegmentation')) {
                $debug['specialized_result'] = $service->debugSegmentation($text);
            } else {
                $debug['specialized_result'] = $service->extractKeywords($text);
            }
        }

        return $debug;
    }

    /**
     * 获取所有可用的分词服务信息
     * @return array
     */
    public function getAvailableServices(): array
    {
        $services = [];

        foreach ($this->serviceMapping as $language => $serviceClass) {
            $services[$language] = [
                'language_name' => $this->supportedLanguages[$language] ?? $language,
                'service_class' => $serviceClass,
                'is_specialized' => $serviceClass !== 'universal',
                'is_loaded' => isset($this->segmentationServices[$language])
            ];
        }

        return $services;
    }

    /**
     * 预加载指定语言的分词服务
     * @param array $languages
     * @return array 成功加载的语言列表
     */
    public function preloadServices(array $languages): array
    {
        $loaded = [];

        foreach ($languages as $language) {
            $service = $this->getSegmentationService($language);
            if ($service) {
                $loaded[] = $language;
            }
        }

        return $loaded;
    }

    /**
     * 清理未使用的分词服务实例
     */
    public function cleanupServices(): void
    {
        $this->segmentationServices = [];
    }
}