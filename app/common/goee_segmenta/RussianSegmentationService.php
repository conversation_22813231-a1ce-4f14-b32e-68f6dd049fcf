<?php

namespace app\common\goee_segmenta;

/**
 * 俄文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class RussianSegmentationService
{
    /**
     * 俄文停用词列表
     * @var array
     */
    protected array $stopWords = [
        'в', 'на', 'с', 'по', 'для', 'от', 'до', 'за', 'под', 'над', 'между', 'через', 'без', 'при', 'о', 'об',
        'я', 'ты', 'он', 'она', 'мы', 'вы', 'они', 'меня', 'тебя', 'его', 'её', 'нас', 'вас', 'их',
        'что', 'когда', 'где', 'почему', 'как', 'какой', 'кто', 'чей', 'сколько', 'куда', 'откуда',
        'пожалуйста', 'спасибо', 'привет', 'извините', 'простите', 'здравствуйте', 'до свидания',
        'этот', 'эта', 'это', 'эти', 'тот', 'та', 'то', 'те', 'здесь', 'там', 'туда', 'сюда',
        'быть', 'есть', 'был', 'была', 'было', 'были', 'будет', 'будут', 'может', 'можно', 'нужно', 'надо'
    ];

    /**
     * 俄文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'озон' => 10, 'вайлдберриз' => 10, 'яндекс' => 9, 'авито' => 9, 'алиэкспресс' => 9,
        'амазон' => 8, 'ебей' => 8, 'ламода' => 8, 'мвидео' => 8, 'эльдорадо' => 7,
        
        // 支付方式
        'сбербанк' => 9, 'тинькофф' => 8, 'яндекс деньги' => 9, 'киви' => 7, 'вебмани' => 7,
        'виза' => 8, 'мастеркард' => 8, 'мир' => 7, 'наличные' => 6, 'безналичный' => 7,
        
        // 核心业务动作
        'покупка' => 10, 'купить' => 10, 'платеж' => 10, 'платить' => 10, 'пополнение' => 9,
        'возврат' => 10, 'вернуть' => 9, 'обмен' => 8, 'заказ' => 10, 'доставка' => 9,
        'получение' => 8, 'товар' => 9, 'продукт' => 8, 'цена' => 8, 'стоимость' => 7,
        'скидка' => 8, 'распродажа' => 8, 'купон' => 7, 'акция' => 7, 'бонус' => 7,
        
        // 账户相关
        'вход' => 9, 'регистрация' => 9, 'подписка' => 8, 'выход' => 7, 'аккаунт' => 9,
        'профиль' => 7, 'пароль' => 9, 'почта' => 8, 'логин' => 8, 'проверка' => 8,
        
        // 客户服务
        'поддержка' => 9, 'помощь' => 8, 'служба поддержки' => 10, 'вопрос' => 8, 'чат' => 7,
        'телефон' => 7, 'почтовая поддержка' => 8, 'тикет' => 7, 'жалоба' => 8, 'отзыв' => 7,
        
        // 产品类别
        'электроника' => 8, 'одежда' => 7, 'книги' => 7, 'дом' => 6, 'спорт' => 7,
        'игрушки' => 7, 'красота' => 7, 'здоровье' => 7, 'авто' => 7, 'еда' => 7
    ];

    /**
     * 俄文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'просмотр' => 6, 'поиск' => 7, 'фильтр' => 6, 'сортировка' => 5, 'сравнение' => 6,
        'избранное' => 6, 'корзина' => 7, 'список желаний' => 6, 'сохранить' => 5,
        
        // 订单状态
        'ожидание' => 6, 'обработка' => 7, 'отправлен' => 7, 'доставлен' => 7, 'отменен' => 7,
        'подтвержден' => 6, 'завершен' => 6, 'неудача' => 6, 'истек' => 5,
        
        // 产品属性
        'бренд' => 6, 'модель' => 6, 'размер' => 6, 'цвет' => 6, 'вес' => 5,
        'материал' => 5, 'качество' => 6, 'состояние' => 6, 'новый' => 5, 'б/у' => 5,
        
        // 时间相关
        'сегодня' => 5, 'завтра' => 5, 'вчера' => 5, 'неделя' => 5, 'месяц' => 5,
        'рабочие дни' => 6, 'выходные' => 5, 'праздник' => 5, 'часы' => 5,
        
        // 数量和测量
        'количество' => 6, 'сумма' => 6, 'итого' => 6, 'подытог' => 6, 'налог' => 6,
        'комиссия' => 6, 'плата' => 6, 'ставка' => 5, 'процент' => 5
    ];

    /**
     * 俄文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'служба поддержки', 'кредитная карта', 'подарочная карта', 'корзина покупок', 'список желаний',
        'учетная запись', 'адрес электронной почты', 'номер телефона', 'почтовый индекс', 'адрес',
        'рабочие дни', 'рабочие часы', 'политика возврата', 'политика конфиденциальности', 'условия обслуживания',
        'бесплатная доставка', 'быстрая доставка', 'доставка в тот же день', 'доставка на следующий день',
        'интернет-магазин', 'мобильное приложение', 'веб-браузер', 'поисковая система', 'социальные сети',
        'отзыв о товаре', 'отзыв клиента', 'звездный рейтинг', 'описание товара',
        'история заказов', 'история покупок', 'способ оплаты', 'адрес выставления счета', 'адрес доставки'
    ];

    /**
     * 提取俄文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 俄文词汇分词
        $wordKeywords = $this->russianWordSegmentation($cleanText);
        $keywords = array_merge($keywords, $wordKeywords);
        
        // 策略5: 词干提取
        $stemKeywords = $this->russianStemming($cleanText);
        $keywords = array_merge($keywords, $stemKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理俄文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);
        
        // 移除特殊符号，但保留俄文字符
        $text = preg_replace('/[^\p{Cyrillic}\p{N}\s]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (mb_stripos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 俄文词汇分词
     * @param string $text
     * @return array
     */
    protected function russianWordSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格分割
        $words = preg_split('/\s+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 3 && preg_match('/[\p{Cyrillic}]/u', $word)) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 俄文词干提取
     * @param string $text
     * @return array
     */
    protected function russianStemming(string $text): array
    {
        $keywords = [];
        $words = preg_split('/\s+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 4) {
                $stem = $this->russianStem($word);
                if ($stem !== $word && mb_strlen($stem) >= 3) {
                    $keywords[] = $stem;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 俄文词干提取算法
     * @param string $word
     * @return string
     */
    protected function russianStem(string $word): string
    {
        // 简化的俄文词干提取
        $word = mb_strtolower($word);
        
        // 去除常见后缀
        $suffixes = [
            // 动词后缀
            'ать', 'ить', 'еть', 'уть', 'ыть', 'ять', 'ти', 'чь',
            // 名词后缀
            'ость', 'ение', 'ание', 'ция', 'сия', 'тие', 'ние', 'ие',
            // 形容词后缀
            'ный', 'ной', 'ский', 'цкий', 'ческий', 'ический',
            // 其他常见后缀
            'ов', 'ев', 'ин', 'ын', 'ен', 'ан', 'ям', 'ах', 'ами', 'ях'
        ];
        
        foreach ($suffixes as $suffix) {
            if (mb_strlen($word) > mb_strlen($suffix) + 2 && 
                mb_substr($word, -mb_strlen($suffix)) === $suffix) {
                return mb_substr($word, 0, -mb_strlen($suffix));
            }
        }
        
        return $word;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidRussianKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateRussianWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证俄文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidRussianKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 20) {
            return false;
        }
        
        // 停用词检查
        if (in_array(mb_strtolower($keyword), $this->stopWords)) {
            return false;
        }
        
        // 必须包含俄文字符
        if (!preg_match('/[\p{Cyrillic}]/u', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算俄文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateRussianWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count(mb_strtolower($text), mb_strtolower($keyword));
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->coreBusinessDict[mb_strtolower($keyword)] * 0.1;
        } elseif (isset($this->extendedBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->extendedBusinessDict[mb_strtolower($keyword)] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array(mb_strtolower($keyword), $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 词长权重（俄文词汇较长）
        if ($length >= 6) {
            $weight += 0.3;
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示俄文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'word_segmentation' => $this->russianWordSegmentation($cleanText),
            'stemming' => $this->russianStemming($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
