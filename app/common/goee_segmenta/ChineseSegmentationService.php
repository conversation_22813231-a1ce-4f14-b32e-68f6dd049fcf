<?php

namespace app\common\goee_segmenta;

/**
 * 优化的中文分词服务
 * 支持多种分词策略，专门针对业务场景优化
 */
class ChineseSegmentationService
{
    /**
     * 停用词列表（扩展版）
     * @var array
     */
    protected array $stopWords = [
        // 基础停用词
        '的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那', '有', '没', '不', '要', '会', '能', '可以', '可能',
        // 疑问词
        '怎么', '什么', '为什么', '如何', '哪里', '哪个', '哪些', '什么时候', '多少', '几个', '多久', '多长时间',
        // 语气词
        '啊', '呢', '吧', '嘛', '呀', '哦', '额', '嗯', '哈', '呵', '嘿', '哟',
        // 连接词
        '和', '与', '或', '但是', '然后', '因为', '所以', '如果', '虽然', '但是', '而且', '或者', '以及', '还有',
        // 时间词
        '今天', '明天', '昨天', '现在', '以前', '以后', '刚才', '马上', '立即', '刚刚', '稍后', '随时',
        // 程度词
        '很', '非常', '特别', '比较', '相当', '十分', '极其', '最', '更', '太', '挺', '蛮', '超级',
        // 量词
        '一个', '一些', '一点', '一下', '一直', '一起', '一样', '几个', '多个', '少量', '大量',
        // 助词
        '还是', '就是', '只是', '而且', '或者', '以及', '还有', '包括', '除了', '另外',
        // 礼貌用语
        '请', '请问', '您好', '你好', '谢谢', '不好意思', '麻烦', '帮忙', '打扰', '抱歉',
        // 无意义词
        '知道', '告诉', '看看', '想要', '希望', '觉得', '感觉', '认为', '以为'
    ];

    /**
     * 业务核心词典（高权重）
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        '淘宝' => 10, '天猫' => 10, '京东' => 10, '拼多多' => 10, '苏宁' => 8, '唯品会' => 8,
        '小红书' => 8, '抖音' => 8, '快手' => 8, '微店' => 6, '有赞' => 6,

        // 支付相关
        '支付宝' => 10, '微信支付' => 10, '银联' => 8, '花呗' => 8, '借呗' => 8, '余额宝' => 8,
        '信用卡' => 8, '储蓄卡' => 6, '网银' => 6, '快捷支付' => 6,

        // 核心业务
        '购买' => 10, '支付' => 10, '充值' => 10, '退款' => 10, '订单' => 10, '商品' => 9,
        '价格' => 8, '优惠' => 8, '折扣' => 8, '会员' => 8, '积分' => 7, '红包' => 7,

        // 账户相关
        '登录' => 9, '注册' => 9, '密码' => 9, '账户' => 9, '个人中心' => 8, '实名认证' => 8,
        '绑定' => 7, '验证' => 7, '安全' => 7, '手机号' => 7, '邮箱' => 6,

        // 功能操作
        '测试' => 8, '功能' => 8, '使用' => 7, '操作' => 7, '设置' => 7, '配置' => 6,
        '教程' => 6, '说明' => 6, '帮助' => 8, '问题' => 7, '解决' => 6,

        // 服务相关
        '客服' => 9, '联系' => 7, '咨询' => 7, '反馈' => 6, '建议' => 6, '投诉' => 7,
        '处理' => 6, '回复' => 6, '服务' => 7, '售后' => 7
    ];

    /**
     * 扩展业务词典（中等权重）
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 系统相关
        '系统' => 6, '平台' => 6, '网站' => 6, '应用' => 6, '软件' => 5, '程序' => 5,
        '版本' => 5, '更新' => 5, '升级' => 5, '维护' => 5, '故障' => 6, '修复' => 5,

        // 商品相关
        '商品' => 7, '产品' => 6, '服务' => 6, '套餐' => 6, '方案' => 5, '计划' => 5,
        '类型' => 5, '种类' => 5, '规格' => 5, '型号' => 5, '品牌' => 6, '质量' => 5,

        // 时间相关
        '时间' => 5, '日期' => 5, '期限' => 5, '有效期' => 6, '到期' => 6, '过期' => 6,
        '营业时间' => 7, '工作日' => 5, '节假日' => 5, '周末' => 5,

        // 流程相关
        '流程' => 7, '步骤' => 7, '方法' => 6, '方式' => 6, '途径' => 5, '渠道' => 6,
        '条件' => 5, '要求' => 5, '限制' => 5, '规则' => 5, '政策' => 5,

        // 状态相关
        '状态' => 6, '进度' => 6, '结果' => 5, '成功' => 5, '失败' => 5, '完成' => 5,
        '处理中' => 6, '待审核' => 6, '已通过' => 5, '已拒绝' => 5
    ];

    /**
     * 常见组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        '自营小程序', '微信小程序', '支付宝小程序', '百度小程序',
        '实名认证', '身份验证', '手机验证', '邮箱验证',
        '在线客服', '人工客服', '智能客服', '客服热线',
        '会员等级', '会员权益', '会员积分', '会员卡',
        '订单状态', '订单详情', '订单查询', '订单取消',
        '支付方式', '支付密码', '支付限额', '支付记录',
        '充值记录', '充值方式', '充值金额', '充值卡',
        '退款申请', '退款原因', '退款进度', '退款到账',
        '营业时间', '工作时间', '服务时间', '客服时间',
        '使用教程', '操作指南', '帮助文档', '常见问题'
    ];

    /**
     * 提取关键词（优化主方法）
     * @param string $text
     * @param int $maxWords 最大关键词数量
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);

        // 2. 优先处理组合词（避免被拆分）
        $compoundKeywords = $this->extractCompoundWords($cleanText);

        // 3. 多种分词策略
        $keywords = [];

        // 策略1: 组合词优先
        $keywords = array_merge($keywords, $compoundKeywords);

        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);

        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);

        // 策略4: 智能规则分词
        $ruleKeywords = $this->intelligentRuleSegmentation($cleanText);
        $keywords = array_merge($keywords, $ruleKeywords);

        // 策略5: 优化的N-gram分词
        $ngramKeywords = $this->optimizedNgramSegmentation($cleanText);
        $keywords = array_merge($keywords, $ngramKeywords);

        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);

        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);

        // 移除特殊字符，保留中文、英文、数字
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);

        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));

        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];

        foreach ($this->compoundWords as $compound) {
            if (strpos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }

        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];

        foreach ($this->coreBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }

        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];

        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }

        return $keywords;
    }

    /**
     * 智能规则分词
     * @param string $text
     * @return array
     */
    protected function intelligentRuleSegmentation(string $text): array
    {
        $keywords = [];

        // 1. 提取连续的中文词汇（2-6个字符，更灵活）
        preg_match_all('/[\p{Han}]{2,6}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        // 2. 提取英文单词（2个字符以上）
        preg_match_all('/[a-zA-Z]{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        // 3. 提取有意义的数字（2位以上）
        preg_match_all('/\d{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }

        return $keywords;
    }

    /**
     * 优化的N-gram分词
     * @param string $text
     * @return array
     */
    protected function optimizedNgramSegmentation(string $text): array
    {
        $keywords = [];
        $length = mb_strlen($text);

        // 只对中文文本进行N-gram分词
        if (!preg_match('/[\p{Han}]/u', $text)) {
            return $keywords;
        }

        // 2-gram（优先）
        for ($i = 0; $i < $length - 1; $i++) {
            $gram = mb_substr($text, $i, 2);
            if ($this->isValidNgram($gram)) {
                $keywords[] = $gram;
            }
        }

        // 3-gram（选择性）
        for ($i = 0; $i < $length - 2; $i++) {
            $gram = mb_substr($text, $i, 3);
            if ($this->isValidNgram($gram) && $this->hasBusinessRelevance($gram)) {
                $keywords[] = $gram;
            }
        }

        return $keywords;
    }

    /**
     * 判断N-gram是否有效
     * @param string $gram
     * @return bool
     */
    protected function isValidNgram(string $gram): bool
    {
        // 必须包含中文字符
        if (!preg_match('/[\p{Han}]/u', $gram)) {
            return false;
        }

        // 不能全是停用词
        if (in_array($gram, $this->stopWords)) {
            return false;
        }

        // 不能包含过多重复字符
        $chars = preg_split('//u', $gram, -1, PREG_SPLIT_NO_EMPTY);
        $uniqueChars = array_unique($chars);
        if (count($uniqueChars) < count($chars) * 0.6) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否有业务相关性
     * @param string $text
     * @return bool
     */
    protected function hasBusinessRelevance(string $text): bool
    {
        // 检查是否包含业务相关字符
        $businessChars = ['购', '买', '支', '付', '充', '值', '退', '款', '订', '单', '商', '品',
                         '会', '员', '登', '录', '注', '册', '密', '码', '账', '户', '测', '试',
                         '功', '能', '客', '服', '帮', '助', '问', '题', '解', '决'];

        foreach ($businessChars as $char) {
            if (strpos($text, $char) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);

        // 2. 高级过滤
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isAdvancedValidKeyword($keyword, $originalText)) {
                $filtered[] = $keyword;
            }
        }

        // 3. 计算综合权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateAdvancedWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }

        // 按权重排序
        arsort($weighted);

        return array_keys($weighted);
    }

    /**
     * 高级关键词有效性检查
     * @param string $keyword
     * @param string $originalText
     * @return bool
     */
    protected function isAdvancedValidKeyword(string $keyword, string $originalText): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 10) {
            return false;
        }

        // 停用词检查
        if (in_array($keyword, $this->stopWords)) {
            return false;
        }

        // 纯空格检查
        if (trim($keyword) === '') {
            return false;
        }

        // 纯数字且长度小于3的过滤
        if (is_numeric($keyword) && $length < 3) {
            return false;
        }

        // 重复字符检查（避免"aaa"这样的无意义词）
        $chars = preg_split('//u', $keyword, -1, PREG_SPLIT_NO_EMPTY);
        $uniqueChars = array_unique($chars);
        if (count($uniqueChars) < count($chars) * 0.5) {
            return false;
        }

        return true;
    }

    /**
     * 计算高级权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateAdvancedWeight(string $keyword, string $text): float
    {
        $weight = 0.0;

        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += ($length - 1) * 0.1;

        // 2. 频率权重
        $frequency = substr_count($text, $keyword);
        $weight += $frequency * 0.2;

        // 3. 位置权重（出现在前面的权重更高）
        $position = strpos($text, $keyword);
        if ($position !== false) {
            $textLength = mb_strlen($text);
            $positionWeight = 1 - $position / $textLength;
            $weight += $positionWeight * 0.2;
        }

        // 4. 业务词典权重
        if (isset($this->coreBusinessDict[$keyword])) {
            $weight += $this->coreBusinessDict[$keyword] * 0.1;
        } elseif (isset($this->extendedBusinessDict[$keyword])) {
            $weight += $this->extendedBusinessDict[$keyword] * 0.05;
        }

        // 5. 组合词权重
        if (in_array($keyword, $this->compoundWords)) {
            $weight += 2.0;
        }

        // 6. 业务相关性权重
        if ($this->hasBusinessRelevance($keyword)) {
            $weight += 0.5;
        }

        // 7. 完整词汇权重（避免碎片化）
        if ($this->isCompleteWord($keyword)) {
            $weight += 0.3;
        }

        return $weight;
    }

    /**
     * 判断是否为完整词汇
     * @param string $word
     * @return bool
     */
    protected function isCompleteWord(string $word): bool
    {
        // 检查是否为常见的完整词汇
        $completeWords = [
            '购买', '支付', '充值', '退款', '订单', '商品', '会员', '登录', '注册', '密码',
            '账户', '测试', '功能', '客服', '帮助', '问题', '解决', '设置', '配置', '使用',
            '操作', '流程', '方法', '步骤', '时间', '价格', '优惠', '折扣', '积分', '红包'
        ];

        return in_array($word, $completeWords);
    }

    /**
     * 基于规则的分词（保留原方法兼容性）
     * @param string $text
     * @return array
     */
    protected function ruleBasedSegmentation(string $text): array
    {
        $keywords = [];
        
        // 1. 提取连续的中文词汇（2-4个字符）
        preg_match_all('/[\p{Han}]{2,4}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 2. 提取英文单词
        preg_match_all('/[a-zA-Z]{2,}/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        // 3. 提取数字
        preg_match_all('/\d+/u', $text, $matches);
        if (!empty($matches[0])) {
            $keywords = array_merge($keywords, $matches[0]);
        }
        
        return $keywords;
    }

    /**
     * N-gram 分词
     * @param string $text
     * @return array
     */
    protected function ngramSegmentation(string $text): array
    {
        $keywords = [];
        $length = mb_strlen($text);
        
        // 2-gram
        for ($i = 0; $i < $length - 1; $i++) {
            $gram = mb_substr($text, $i, 2);
            if ($this->isValidKeyword($gram)) {
                $keywords[] = $gram;
            }
        }
        
        // 3-gram
        for ($i = 0; $i < $length - 2; $i++) {
            $gram = mb_substr($text, $i, 3);
            if ($this->isValidKeyword($gram)) {
                $keywords[] = $gram;
            }
        }
        
        return $keywords;
    }

    /**
     * 基于词典的分词
     * @param string $text
     * @return array
     */
    protected function dictionaryBasedSegmentation(string $text): array
    {
        // 常见的业务相关词汇
        $businessDict = [
            '购买', '支付', '充值', '退款', '订单', '商品', '价格', '优惠', '折扣', '会员',
            '登录', '注册', '密码', '账户', '个人', '设置', '修改', '绑定', '验证', '安全',
            '测试', '功能', '使用', '操作', '步骤', '方法', '教程', '说明', '帮助', '问题',
            '客服', '联系', '咨询', '反馈', '建议', '投诉', '处理', '解决', '回复', '服务',
            '系统', '平台', '网站', '应用', '软件', '程序', '版本', '更新', '升级', '维护',
            '淘宝', '天猫', '京东', '拼多多', '微信', '支付宝', '银行', '信用卡', '花呗', '借呗'
        ];
        
        $keywords = [];
        
        foreach ($businessDict as $word) {
            if (strpos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 过滤和排序关键词
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function filterAndRankKeywords(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无意义词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateKeywordWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 判断是否为有效关键词
     * @param string $keyword
     * @return bool
     */
    protected function isValidKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 8) {
            return false;
        }
        
        // 停用词检查
        if (in_array($keyword, $this->stopWords)) {
            return false;
        }
        
        // 纯空格检查
        if (trim($keyword) === '') {
            return false;
        }
        
        // 纯数字且长度小于3的过滤
        if (is_numeric($keyword) && $length < 3) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateKeywordWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 频率权重
        $frequency = substr_count($text, $keyword);
        $weight += $frequency * 0.3;
        
        // 2. 长度权重（更长的词汇权重更高）
        $length = mb_strlen($keyword);
        $weight += ($length - 1) * 0.2;
        
        // 3. 位置权重（出现在前面的权重更高）
        $position = strpos($text, $keyword);
        if ($position !== false) {
            $textLength = mb_strlen($text);
            $positionWeight = 1 - ($position / $textLength);
            $weight += $positionWeight * 0.3;
        }
        
        // 4. 业务相关性权重
        $businessKeywords = ['购买', '支付', '充值', '退款', '测试', '功能', '登录', '注册', '客服', '帮助'];
        if (in_array($keyword, $businessKeywords)) {
            $weight += 0.5;
        }
        
        return $weight;
    }

    /**
     * 获取文本摘要关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function getSummaryKeywords(string $text, int $maxWords = 5): array
    {
        $keywords = $this->extractKeywords($text, $maxWords * 2);
        
        // 进一步筛选最重要的关键词
        $important = [];
        foreach ($keywords as $keyword) {
            $weight = $this->calculateKeywordWeight($keyword, $text);
            if ($weight > 0.5) { // 只保留高权重的关键词
                $important[] = $keyword;
            }
        }
        
        return array_slice($important, 0, $maxWords);
    }

    /**
     * 调试信息：显示优化的分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);

        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'rule_based' => $this->intelligentRuleSegmentation($cleanText),
            'ngram' => $this->optimizedNgramSegmentation($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
