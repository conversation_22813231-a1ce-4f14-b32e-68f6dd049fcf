<?php

namespace app\common\goee_segmenta;

/**
 * 法文分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class FrenchSegmentationService
{
    /**
     * 法文停用词列表
     * @var array
     */
    protected array $stopWords = [
        'le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or',
        'dans', 'sur', 'sous', 'avec', 'sans', 'pour', 'par', 'entre', 'vers', 'chez', 'depuis', 'jusqu',
        'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles', 'me', 'te', 'se', 'lui', 'leur', 'en', 'y',
        'que', 'quand', 'où', 'pourquoi', 'comment', 'qui', 'quoi', 'quel', 'quelle', 'combien',
        's\'il vous plaît', 'merci', 'bonjour', 'pardon', 'excusez-moi', 'au revoir', 'bonsoir', 'bonne nuit',
        'ce', 'cette', 'ces', 'cet', 'celui', 'celle', 'ceux', 'celles', 'ici', 'là', 'voici', 'voilà',
        'être', 'avoir', 'faire', 'aller', 'venir', 'voir', 'savoir', 'pouvoir', 'vouloir', 'devoir', 'dire'
    ];

    /**
     * 法文核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'amazon' => 10, 'cdiscount' => 9, 'fnac' => 8, 'darty' => 8, 'leclerc' => 8,
        'carrefour' => 8, 'auchan' => 7, 'but' => 7, 'conforama' => 7, 'ikea' => 7,
        
        // 支付方式
        'paypal' => 10, 'visa' => 9, 'mastercard' => 9, 'american express' => 8, 'cb' => 8,
        'carte de crédit' => 9, 'carte de débit' => 7, 'virement bancaire' => 8, 'espèces' => 6,
        
        // 核心业务动作
        'acheter' => 10, 'achat' => 10, 'paiement' => 10, 'payer' => 10, 'recharge' => 9,
        'remboursement' => 10, 'retourner' => 9, 'échange' => 8, 'commande' => 10, 'livraison' => 9,
        'réception' => 8, 'produit' => 9, 'article' => 8, 'prix' => 8, 'coût' => 7,
        'réduction' => 8, 'offre' => 8, 'coupon' => 7, 'promotion' => 7, 'points' => 7,
        
        // 账户相关
        'connexion' => 9, 'inscription' => 9, 'abonnement' => 8, 'déconnexion' => 7, 'compte' => 9,
        'profil' => 7, 'mot de passe' => 9, 'email' => 8, 'utilisateur' => 8, 'vérification' => 8,
        
        // 客户服务
        'support' => 9, 'aide' => 8, 'service client' => 10, 'question' => 8, 'chat' => 7,
        'téléphone' => 7, 'support email' => 8, 'ticket' => 7, 'plainte' => 8, 'commentaire' => 7,
        
        // 产品类别
        'électronique' => 8, 'vêtements' => 7, 'livres' => 7, 'maison' => 6, 'sport' => 7,
        'jouets' => 7, 'beauté' => 7, 'santé' => 7, 'automobile' => 7, 'nourriture' => 7
    ];

    /**
     * 法文扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'parcourir' => 6, 'rechercher' => 7, 'filtrer' => 6, 'trier' => 5, 'comparer' => 6,
        'liste de souhaits' => 6, 'panier' => 7, 'favoris' => 6, 'sauvegarder' => 5,
        
        // 订单状态
        'en attente' => 6, 'en cours' => 7, 'expédié' => 7, 'livré' => 7, 'annulé' => 7,
        'confirmé' => 6, 'terminé' => 6, 'échoué' => 6, 'expiré' => 5,
        
        // 产品属性
        'marque' => 6, 'modèle' => 6, 'taille' => 6, 'couleur' => 6, 'poids' => 5,
        'matériau' => 5, 'qualité' => 6, 'condition' => 6, 'neuf' => 5, 'occasion' => 5,
        
        // 时间相关
        'aujourd\'hui' => 5, 'demain' => 5, 'hier' => 5, 'semaine' => 5, 'mois' => 5,
        'jours ouvrables' => 6, 'week-end' => 5, 'vacances' => 5, 'heures' => 5,
        
        // 数量和测量
        'quantité' => 6, 'montant' => 6, 'total' => 6, 'sous-total' => 6, 'taxe' => 6,
        'commission' => 6, 'frais' => 6, 'taux' => 5, 'pourcentage' => 5
    ];

    /**
     * 法文组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'service client', 'carte de crédit', 'carte cadeau', 'panier d\'achat', 'liste de souhaits',
        'compte utilisateur', 'adresse email', 'numéro de téléphone', 'code postal', 'adresse',
        'jours ouvrables', 'heures de travail', 'politique de retour', 'politique de confidentialité', 'conditions de service',
        'livraison gratuite', 'livraison rapide', 'livraison le jour même', 'livraison le lendemain',
        'achat en ligne', 'application mobile', 'navigateur web', 'moteur de recherche', 'réseaux sociaux',
        'avis produit', 'avis client', 'notation étoiles', 'description produit',
        'historique commandes', 'historique achats', 'méthode de paiement', 'adresse de facturation', 'adresse de livraison'
    ];

    /**
     * 提取法文关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 法文词汇分词
        $wordKeywords = $this->frenchWordSegmentation($cleanText);
        $keywords = array_merge($keywords, $wordKeywords);
        
        // 策略5: 词干提取
        $stemKeywords = $this->frenchStemming($cleanText);
        $keywords = array_merge($keywords, $stemKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理法文文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);
        
        // 处理法文缩写
        $text = $this->expandFrenchContractions($text);
        
        // 移除特殊符号，保留字母、数字、空格、撇号、连字符
        $text = preg_replace('/[^\p{L}\p{N}\s\'\-]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 展开法文缩写
     * @param string $text
     * @return string
     */
    protected function expandFrenchContractions(string $text): string
    {
        $contractions = [
            'j\'ai' => 'je ai', 'j\'étais' => 'je étais', 'j\'irai' => 'je irai',
            'c\'est' => 'ce est', 'c\'était' => 'ce était', 'c\'était' => 'ce était',
            'n\'est' => 'ne est', 'n\'était' => 'ne était', 'n\'ai' => 'ne ai',
            'l\'ai' => 'le ai', 'l\'est' => 'le est', 'l\'était' => 'le était',
            'd\'un' => 'de un', 'd\'une' => 'de une', 'd\'autres' => 'de autres',
            's\'il' => 'si il', 's\'ils' => 'si ils', 's\'elle' => 'si elle',
            'qu\'il' => 'que il', 'qu\'elle' => 'que elle', 'qu\'on' => 'que on'
        ];
        
        foreach ($contractions as $contraction => $expansion) {
            $text = str_replace($contraction, $expansion, $text);
        }
        
        return $text;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (mb_stripos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 法文词汇分词
     * @param string $text
     * @return array
     */
    protected function frenchWordSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格和标点分割
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 3 && preg_match('/^[a-zàâäéèêëïîôöùûüÿç]+$/iu', $word)) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 法文词干提取
     * @param string $text
     * @return array
     */
    protected function frenchStemming(string $text): array
    {
        $keywords = [];
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 4) {
                $stem = $this->frenchStem($word);
                if ($stem !== $word && mb_strlen($stem) >= 3) {
                    $keywords[] = $stem;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 法文词干提取算法
     * @param string $word
     * @return string
     */
    protected function frenchStem(string $word): string
    {
        $word = mb_strtolower($word);
        
        // 去除常见后缀
        $suffixes = [
            // 动词后缀
            'aient', 'eront', 'erait', 'erais', 'erons', 'erez', 'ent', 'ant', 'er', 'ir', 're',
            // 名词后缀
            'tion', 'sion', 'ment', 'ance', 'ence', 'ité', 'age', 'isme', 'eur', 'euse',
            // 形容词后缀
            'able', 'ible', 'ique', 'eux', 'euse', 'ais', 'aise',
            // 复数形式
            'aux', 'eux', 'oux', 'es', 's',
            // 其他常见后缀
            'ment', 'ette', 'elle', 'eau', 'eau'
        ];
        
        foreach ($suffixes as $suffix) {
            if (mb_strlen($word) > mb_strlen($suffix) + 2 && 
                mb_substr($word, -mb_strlen($suffix)) === $suffix) {
                return mb_substr($word, 0, -mb_strlen($suffix));
            }
        }
        
        return $word;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidFrenchKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateFrenchWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证法文关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidFrenchKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 20) {
            return false;
        }
        
        // 停用词检查
        if (in_array(mb_strtolower($keyword), $this->stopWords)) {
            return false;
        }
        
        // 必须包含字母
        if (!preg_match('/[a-zàâäéèêëïîôöùûüÿç]/iu', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算法文关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateFrenchWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count(mb_strtolower($text), mb_strtolower($keyword));
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->coreBusinessDict[mb_strtolower($keyword)] * 0.1;
        } elseif (isset($this->extendedBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->extendedBusinessDict[mb_strtolower($keyword)] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array(mb_strtolower($keyword), $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 重音符号权重（法文特色）
        if (preg_match('/[àâäéèêëïîôöùûüÿç]/iu', $keyword)) {
            $weight += 0.2;
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示法文分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'expanded_contractions' => $this->expandFrenchContractions($text),
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'word_segmentation' => $this->frenchWordSegmentation($cleanText),
            'stemming' => $this->frenchStemming($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
