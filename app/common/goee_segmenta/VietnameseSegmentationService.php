<?php

namespace app\common\goee_segmenta;

/**
 * 越南语分词服务
 * 参照 ChineseSegmentationService 架构设计
 */
class VietnameseSegmentationService
{
    /**
     * 越南语停用词列表
     * @var array
     */
    protected array $stopWords = [
        'trong', 'trên', 'tại', 'từ', 'đến', 'với', 'bởi', 'cho', 'của', 'và', 'hoặc', 'nhưng', 'mà',
        'tôi', 'bạn', 'anh', 'chị', 'em', 'ông', 'bà', 'chúng tôi', 'họ', 'nó', 'này', 'đó', 'kia',
        'gì', 'khi nào', 'ở đâu', 'tại sao', 'như thế nào', 'ai', 'bao nhiêu', 'mấy',
        'xin chào', 'cảm ơn', 'xin lỗi', 'chào tạm biệt', 'không có gì', 'đ<PERSON><PERSON><PERSON> rồi',
        'đây', 'đó', 'ở đây', 'ở đó', 'tại đây', 'tại đó',
        'là', 'có', 'không', 'được', 'sẽ', 'đã', 'đang', 'phải', 'nên', 'có thể', 'cần'
    ];

    /**
     * 越南语核心业务词典
     * @var array
     */
    protected array $coreBusinessDict = [
        // 电商平台
        'shopee' => 10, 'lazada' => 10, 'tiki' => 9, 'sendo' => 8, 'thegioididong' => 8,
        'fpt shop' => 8, 'vinmart' => 7, 'big c' => 7, 'lotte mart' => 7, 'aeon' => 7,
        
        // 支付方式
        'momo' => 9, 'zalopay' => 9, 'vnpay' => 8, 'visa' => 8, 'mastercard' => 8,
        'thẻ tín dụng' => 9, 'thẻ ghi nợ' => 7, 'chuyển khoản' => 8, 'tiền mặt' => 6,
        
        // 核心业务动作
        'mua' => 10, 'mua hàng' => 10, 'thanh toán' => 10, 'trả tiền' => 10, 'nạp tiền' => 9,
        'hoàn tiền' => 10, 'trả hàng' => 9, 'đổi hàng' => 8, 'đặt hàng' => 10, 'giao hàng' => 9,
        'nhận hàng' => 8, 'sản phẩm' => 9, 'hàng hóa' => 8, 'giá' => 8, 'chi phí' => 7,
        'giảm giá' => 8, 'khuyến mãi' => 8, 'mã giảm giá' => 7, 'chương trình' => 7, 'điểm' => 7,
        
        // 账户相关
        'đăng nhập' => 9, 'đăng ký' => 9, 'thành viên' => 8, 'đăng xuất' => 7, 'tài khoản' => 9,
        'hồ sơ' => 7, 'mật khẩu' => 9, 'email' => 8, 'tên người dùng' => 8, 'xác minh' => 8,
        
        // 客户服务
        'hỗ trợ' => 9, 'giúp đỡ' => 8, 'chăm sóc khách hàng' => 10, 'hỏi đáp' => 8, 'chat' => 7,
        'điện thoại' => 7, 'hỗ trợ email' => 8, 'ticket' => 7, 'khiếu nại' => 8, 'phản hồi' => 7,
        
        // 产品类别
        'điện tử' => 8, 'quần áo' => 7, 'sách' => 7, 'gia đình' => 6, 'thể thao' => 7,
        'đồ chơi' => 7, 'làm đẹp' => 7, 'sức khỏe' => 7, 'ô tô' => 7, 'thực phẩm' => 7
    ];

    /**
     * 越南语扩展业务词典
     * @var array
     */
    protected array $extendedBusinessDict = [
        // 购物动作
        'duyệt' => 6, 'tìm kiếm' => 7, 'lọc' => 6, 'sắp xếp' => 5, 'so sánh' => 6,
        'danh sách yêu thích' => 6, 'giỏ hàng' => 7, 'yêu thích' => 6, 'lưu' => 5,
        
        // 订单状态
        'chờ xử lý' => 6, 'đang xử lý' => 7, 'đã gửi' => 7, 'đã giao' => 7, 'đã hủy' => 7,
        'đã xác nhận' => 6, 'hoàn thành' => 6, 'thất bại' => 6, 'hết hạn' => 5,
        
        // 产品属性
        'thương hiệu' => 6, 'mẫu' => 6, 'kích thước' => 6, 'màu sắc' => 6, 'trọng lượng' => 5,
        'chất liệu' => 5, 'chất lượng' => 6, 'tình trạng' => 6, 'mới' => 5, 'đã sử dụng' => 5,
        
        // 时间相关
        'hôm nay' => 5, 'ngày mai' => 5, 'hôm qua' => 5, 'tuần' => 5, 'tháng' => 5,
        'ngày làm việc' => 6, 'cuối tuần' => 5, 'ngày lễ' => 5, 'giờ' => 5,
        
        // 数量和测量
        'số lượng' => 6, 'số tiền' => 6, 'tổng cộng' => 6, 'tổng phụ' => 6, 'thuế' => 6,
        'phí' => 6, 'chi phí' => 6, 'tỷ lệ' => 5, 'phần trăm' => 5
    ];

    /**
     * 越南语组合词（避免被拆分）
     * @var array
     */
    protected array $compoundWords = [
        'chăm sóc khách hàng', 'thẻ tín dụng', 'thẻ quà tặng', 'giỏ hàng', 'danh sách yêu thích',
        'tài khoản người dùng', 'địa chỉ email', 'số điện thoại', 'mã bưu điện', 'địa chỉ',
        'ngày làm việc', 'giờ làm việc', 'chính sách đổi trả', 'chính sách bảo mật', 'điều khoản dịch vụ',
        'giao hàng miễn phí', 'giao hàng nhanh', 'giao hàng trong ngày', 'giao hàng ngày hôm sau',
        'mua sắm trực tuyến', 'ứng dụng di động', 'trình duyệt web', 'công cụ tìm kiếm', 'mạng xã hội',
        'đánh giá sản phẩm', 'đánh giá khách hàng', 'xếp hạng sao', 'mô tả sản phẩm',
        'lịch sử đơn hàng', 'lịch sử mua hàng', 'phương thức thanh toán', 'địa chỉ thanh toán', 'địa chỉ giao hàng'
    ];

    /**
     * 提取越南语关键词
     * @param string $text
     * @param int $maxWords
     * @return array
     */
    public function extractKeywords(string $text, int $maxWords = 10): array
    {
        if (empty(trim($text))) {
            return [];
        }

        // 1. 预处理文本
        $cleanText = $this->preprocessText($text);
        
        // 2. 多种分词策略
        $keywords = [];
        
        // 策略1: 组合词优先
        $compoundKeywords = $this->extractCompoundWords($cleanText);
        $keywords = array_merge($keywords, $compoundKeywords);
        
        // 策略2: 核心业务词典匹配
        $coreKeywords = $this->extractCoreBusinessWords($cleanText);
        $keywords = array_merge($keywords, $coreKeywords);
        
        // 策略3: 扩展词典匹配
        $extendedKeywords = $this->extractExtendedBusinessWords($cleanText);
        $keywords = array_merge($keywords, $extendedKeywords);
        
        // 策略4: 越南语词汇分词
        $wordKeywords = $this->vietnameseWordSegmentation($cleanText);
        $keywords = array_merge($keywords, $wordKeywords);
        
        // 策略5: 音调处理
        $toneKeywords = $this->vietnameseToneProcessing($cleanText);
        $keywords = array_merge($keywords, $toneKeywords);
        
        // 4. 高级过滤和排序
        $filteredKeywords = $this->advancedFilterAndRank($keywords, $cleanText);
        
        // 5. 返回前N个关键词
        return array_slice($filteredKeywords, 0, $maxWords);
    }

    /**
     * 预处理越南语文本
     * @param string $text
     * @return string
     */
    protected function preprocessText(string $text): string
    {
        // 转换为小写
        $text = mb_strtolower($text);
        
        // 规范化越南语音调
        $text = $this->normalizeVietnameseTones($text);
        
        // 移除特殊符号，保留字母、数字、空格
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        
        // 规范化空格
        $text = preg_replace('/\s+/u', ' ', trim($text));
        
        return $text;
    }

    /**
     * 规范化越南语音调
     * @param string $text
     * @return string
     */
    protected function normalizeVietnameseTones(string $text): string
    {
        // 越南语音调符号统一化
        $toneMap = [
            // a系列
            'à' => 'a', 'á' => 'a', 'ả' => 'a', 'ã' => 'a', 'ạ' => 'a',
            'ằ' => 'a', 'ắ' => 'a', 'ẳ' => 'a', 'ẵ' => 'a', 'ặ' => 'a',
            'ầ' => 'a', 'ấ' => 'a', 'ẩ' => 'a', 'ẫ' => 'a', 'ậ' => 'a',
            // e系列
            'è' => 'e', 'é' => 'e', 'ẻ' => 'e', 'ẽ' => 'e', 'ẹ' => 'e',
            'ề' => 'e', 'ế' => 'e', 'ể' => 'e', 'ễ' => 'e', 'ệ' => 'e',
            // i系列
            'ì' => 'i', 'í' => 'i', 'ỉ' => 'i', 'ĩ' => 'i', 'ị' => 'i',
            // o系列
            'ò' => 'o', 'ó' => 'o', 'ỏ' => 'o', 'õ' => 'o', 'ọ' => 'o',
            'ồ' => 'o', 'ố' => 'o', 'ổ' => 'o', 'ỗ' => 'o', 'ộ' => 'o',
            'ờ' => 'o', 'ớ' => 'o', 'ở' => 'o', 'ỡ' => 'o', 'ợ' => 'o',
            // u系列
            'ù' => 'u', 'ú' => 'u', 'ủ' => 'u', 'ũ' => 'u', 'ụ' => 'u',
            'ừ' => 'u', 'ứ' => 'u', 'ử' => 'u', 'ữ' => 'u', 'ự' => 'u',
            // y系列
            'ỳ' => 'y', 'ý' => 'y', 'ỷ' => 'y', 'ỹ' => 'y', 'ỵ' => 'y'
        ];
        
        // 保留原文，同时添加去音调版本
        $normalized = $text;
        foreach ($toneMap as $toned => $plain) {
            if (strpos($text, $toned) !== false) {
                $normalized .= ' ' . str_replace($toned, $plain, $text);
            }
        }
        
        return $normalized;
    }

    /**
     * 提取组合词
     * @param string $text
     * @return array
     */
    protected function extractCompoundWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->compoundWords as $compound) {
            if (mb_stripos($text, $compound) !== false) {
                $keywords[] = $compound;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取核心业务词
     * @param string $text
     * @return array
     */
    protected function extractCoreBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->coreBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 提取扩展业务词
     * @param string $text
     * @return array
     */
    protected function extractExtendedBusinessWords(string $text): array
    {
        $keywords = [];
        
        foreach ($this->extendedBusinessDict as $word => $weight) {
            if (mb_stripos($text, $word) !== false) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 越南语词汇分词
     * @param string $text
     * @return array
     */
    protected function vietnameseWordSegmentation(string $text): array
    {
        $keywords = [];
        
        // 按空格分割（越南语有空格分隔）
        $words = preg_split('/\s+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 2 && preg_match('/^[a-zàáảãạằắẳẵặầấẩẫậèéẻẽẹềếểễệìíỉĩịòóỏõọồốổỗộờớởỡợùúủũụừứửữựỳýỷỹỵ]+$/iu', $word)) {
                $keywords[] = $word;
            }
        }
        
        return $keywords;
    }

    /**
     * 越南语音调处理
     * @param string $text
     * @return array
     */
    protected function vietnameseToneProcessing(string $text): array
    {
        $keywords = [];
        $words = preg_split('/\s+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (mb_strlen($word) >= 3) {
                // 去除音调的版本
                $toneless = $this->removeTones($word);
                if ($toneless !== $word && mb_strlen($toneless) >= 2) {
                    $keywords[] = $toneless;
                }
            }
        }
        
        return $keywords;
    }

    /**
     * 去除越南语音调
     * @param string $word
     * @return string
     */
    protected function removeTones(string $word): string
    {
        $toneMap = [
            'à' => 'a', 'á' => 'a', 'ả' => 'a', 'ã' => 'a', 'ạ' => 'a',
            'ằ' => 'a', 'ắ' => 'a', 'ẳ' => 'a', 'ẵ' => 'a', 'ặ' => 'a',
            'ầ' => 'a', 'ấ' => 'a', 'ẩ' => 'a', 'ẫ' => 'a', 'ậ' => 'a',
            'è' => 'e', 'é' => 'e', 'ẻ' => 'e', 'ẽ' => 'e', 'ẹ' => 'e',
            'ề' => 'e', 'ế' => 'e', 'ể' => 'e', 'ễ' => 'e', 'ệ' => 'e',
            'ì' => 'i', 'í' => 'i', 'ỉ' => 'i', 'ĩ' => 'i', 'ị' => 'i',
            'ò' => 'o', 'ó' => 'o', 'ỏ' => 'o', 'õ' => 'o', 'ọ' => 'o',
            'ồ' => 'o', 'ố' => 'o', 'ổ' => 'o', 'ỗ' => 'o', 'ộ' => 'o',
            'ờ' => 'o', 'ớ' => 'o', 'ở' => 'o', 'ỡ' => 'o', 'ợ' => 'o',
            'ù' => 'u', 'ú' => 'u', 'ủ' => 'u', 'ũ' => 'u', 'ụ' => 'u',
            'ừ' => 'u', 'ứ' => 'u', 'ử' => 'u', 'ữ' => 'u', 'ự' => 'u',
            'ỳ' => 'y', 'ý' => 'y', 'ỷ' => 'y', 'ỹ' => 'y', 'ỵ' => 'y'
        ];
        
        foreach ($toneMap as $toned => $plain) {
            $word = str_replace($toned, $plain, $word);
        }
        
        return $word;
    }

    /**
     * 高级过滤和排序
     * @param array $keywords
     * @param string $originalText
     * @return array
     */
    protected function advancedFilterAndRank(array $keywords, string $originalText): array
    {
        // 1. 去重
        $keywords = array_unique($keywords);
        
        // 2. 过滤停用词和无效词
        $filtered = [];
        foreach ($keywords as $keyword) {
            if ($this->isValidVietnameseKeyword($keyword)) {
                $filtered[] = $keyword;
            }
        }
        
        // 3. 计算权重并排序
        $weighted = [];
        foreach ($filtered as $keyword) {
            $weight = $this->calculateVietnameseWeight($keyword, $originalText);
            if ($weight > 0) {
                $weighted[$keyword] = $weight;
            }
        }
        
        // 按权重排序
        arsort($weighted);
        
        return array_keys($weighted);
    }

    /**
     * 验证越南语关键词有效性
     * @param string $keyword
     * @return bool
     */
    protected function isValidVietnameseKeyword(string $keyword): bool
    {
        // 长度检查
        $length = mb_strlen($keyword);
        if ($length < 2 || $length > 20) {
            return false;
        }
        
        // 停用词检查
        if (in_array(mb_strtolower($keyword), $this->stopWords)) {
            return false;
        }
        
        // 必须包含字母
        if (!preg_match('/[a-zàáảãạằắẳẵặầấẩẫậèéẻẽẹềếểễệìíỉĩịòóỏõọồốổỗộờớởỡợùúủũụừứửữựỳýỷỹỵ]/iu', $keyword)) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算越南语关键词权重
     * @param string $keyword
     * @param string $text
     * @return float
     */
    protected function calculateVietnameseWeight(string $keyword, string $text): float
    {
        $weight = 0.0;
        
        // 1. 基础权重（长度）
        $length = mb_strlen($keyword);
        $weight += $length * 0.1;
        
        // 2. 频率权重
        $frequency = substr_count(mb_strtolower($text), mb_strtolower($keyword));
        $weight += $frequency * 0.3;
        
        // 3. 业务词典权重
        if (isset($this->coreBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->coreBusinessDict[mb_strtolower($keyword)] * 0.1;
        } elseif (isset($this->extendedBusinessDict[mb_strtolower($keyword)])) {
            $weight += $this->extendedBusinessDict[mb_strtolower($keyword)] * 0.05;
        }
        
        // 4. 组合词权重
        if (in_array(mb_strtolower($keyword), $this->compoundWords)) {
            $weight += 2.0;
        }
        
        // 5. 音调权重（越南语特色）
        if (preg_match('/[àáảãạằắẳẵặầấẩẫậèéẻẽẹềếểễệìíỉĩịòóỏõọồốổỗộờớởỡợùúủũụừứửữựỳýỷỹỵ]/u', $keyword)) {
            $weight += 0.2;
        }
        
        return $weight;
    }

    /**
     * 调试信息：显示越南语分词过程
     * @param string $text
     * @return array
     */
    public function debugSegmentation(string $text): array
    {
        $cleanText = $this->preprocessText($text);
        
        return [
            'original' => $text,
            'cleaned' => $cleanText,
            'normalized_tones' => $this->normalizeVietnameseTones($text),
            'compound_words' => $this->extractCompoundWords($cleanText),
            'core_business' => $this->extractCoreBusinessWords($cleanText),
            'extended_business' => $this->extractExtendedBusinessWords($cleanText),
            'word_segmentation' => $this->vietnameseWordSegmentation($cleanText),
            'tone_processing' => $this->vietnameseToneProcessing($cleanText),
            'final_keywords' => $this->extractKeywords($text)
        ];
    }
}
