<?php

namespace app\common;

/**
 * Token使用示例
 * 本文件仅作为示例，展示如何使用Token类
 */
class TokenExample
{
    /**
     * 存储令牌示例
     */
    public function storeTokenExample()
    {
        // 令牌内容，通常是JWT或其他格式的字符串
        $token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.example';
        
        // 令牌关联的数据
        $data = [
            'user_id' => 1,
            'username' => 'admin',
            'role' => 'administrator'
        ];
        
        // 存储令牌，过期时间为2小时
        $result = TokenStorage::store($token, $data, 7200);
        
        return $result;
    }
    
    /**
     * 获取令牌数据示例
     */
    public function getTokenExample($token)
    {
        // 获取令牌关联的数据
        $data = TokenStorage::get($token);
        
        if ($data === null) {
            // 令牌不存在或已过期
            return false;
        }
        
        // 令牌有效，返回关联数据
        return $data;
    }
    
    /**
     * 刷新令牌过期时间示例
     */
    public function refreshTokenExample($token)
    {
        // 刷新令牌过期时间为1小时
        $result = TokenStorage::refresh($token, 3600);
        
        return $result;
    }
    
    /**
     * 删除令牌示例
     */
    public function removeTokenExample($token)
    {
        // 删除令牌
        $result = TokenStorage::remove($token);
        
        return $result;
    }
    
    /**
     * 切换存储驱动示例
     * 
     * 注意：通常不需要在代码中切换驱动，而是通过配置文件设置
     * 这里仅作为演示如何在特殊情况下手动切换驱动
     */
    public function switchDriverExample()
    {
        // 方法1：修改配置文件 config/token.php 中的 'driver' 值
        
        // 方法2：在 .env 文件中设置环境变量
        // TOKEN_DRIVER=mysql
        
        // 方法3（不推荐）：在代码中临时修改配置
        // \think\facade\Config::set(['driver' => 'mysql'], 'token');
        
        // 使用Token类，此时会根据配置使用对应的驱动
        $token = 'example_token';
        $data = ['temp' => 'data'];
        return TokenStorage::store($token, $data, 1800);
    }
}