<?php

namespace app\common\pay;

/**
 * 支付工厂类
 * 负责创建不同的支付驱动实例
 */
class PayFactory
{
    /**
     * 创建支付驱动实例
     * 
     * @param string $type 驱动名称
     * @param string $configKey 配置键名
     * @return \app\common\pay\PayInterface
     * @throws \Exception
     */
    public static function create(string $type, string $configKey = 'default', array $config = [])
    {
        // 默认类型
        if (empty($type)) {
            $type = config('pay.default_type', 'alipay');
        }
        
        // 类名格式：{Type}Captcha
        $className = '\\app\\common\\pay\\' . strtolower($type) . '\\' . ucfirst($type) . 'Pay';
        
        // 如果类不存在，则使用默认类
        if (!class_exists($className)) {
            $className = '\\app\\common\\pay\\alipay\\AlipayPay';
        }

        // 合并配置
        $defaultConfig = config('pay.' . $type . '.' . $configKey, []);
        $mergedConfig = array_merge($defaultConfig, $config);
        
        // 创建实例
        return new $className($mergedConfig);
    }
    
    /**
     * 创建支付宝支付实例
     * 
     * @param string $configKey 配置键名
     * @return \app\common\pay\alipay\AlipayPay
     */
    public static function createAlipay(string $configKey = 'default', array $config = [])
    {
        return self::create('alipay', $configKey, $config);
    }
    
    /**
     * 创建微信支付实例
     * 
     * @param string $configKey 配置键名
     * @return \app\common\pay\wechat\WechatPay
     */
    public static function createWechat(string $configKey = 'default', array $config = [])
    {
        return self::create('wechat', $configKey, $config);
    }
}