<?php

namespace app\common\pay;

/**
 * 支付接口
 * 定义支付服务的基本方法
 */
interface PayInterface
{
    /**
     * 支付
     * 
     * @param array $order 订单信息
     * @return mixed
     */
    public function pay(array $order);
    
    /**
     * 查询订单
     * 
     * @param string $outTradeNo 商户订单号
     * @return mixed
     */
    public function find(string $outTradeNo);
    
    /**
     * 退款
     * 
     * @param array $order 订单信息
     * @return mixed
     */
    public function refund(array $order);
    
    /**
     * 关闭订单
     * 
     * @param string $outTradeNo 商户订单号
     * @return mixed
     */
    public function close(string $outTradeNo);
    
    /**
     * 取消订单
     * 
     * @param string $outTradeNo 商户订单号
     * @return mixed
     */
    public function cancel(string $outTradeNo);
    
    /**
     * 验证支付回调通知
     * 
     * @param array $data 通知数据
     * @return mixed
     */
    public function verify(array $data);
    
    /**
     * 支付通知处理
     * 
     * @return mixed
     */
    public function notify();
    
    /**
     * 获取配置
     * 
     * @return array
     */
    public function getConfig();
}