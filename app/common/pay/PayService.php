<?php

namespace app\common\pay;

/**
 * 支付服务类
 * 提供支付服务的统一入口
 */
class PayService
{
    /**
     * 获取支付宝支付实例
     * 
     * @param string $configKey 配置键名
     * @return \app\common\pay\alipay\AlipayPay
     */
    public static function alipay(string $configKey = 'default', array $config = [])
    {
        return PayFactory::createAlipay($configKey, $config);
    }
    
    /**
     * 获取微信支付实例
     * 
     * @param string $configKey 配置键名
     * @return \app\common\pay\wechat\WechatPay
     */
    public static function wechat(string $configKey = 'default', array $config = [])
    {
        return PayFactory::createWechat($configKey, $config);
    }
    
    /**
     * 获取支付驱动实例
     * 
     * @param string $type 驱动名称
     * @param string $configKey 配置键名
     * @return \app\common\pay\PayInterface
     * @throws \Exception
     */
    public static function pay(string $type, string $configKey = 'default', array $config = [])
    {
        return PayFactory::create($type, $configKey, $config);
    }
}