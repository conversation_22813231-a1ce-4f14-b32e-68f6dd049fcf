<?php

namespace app\common\pay\wechat;

use app\common\pay\AbstractPay;
use Yansongda\Pay\Exception\Exception;
use Yansongda\Pay\Exception\InvalidParamsException;

/**
 * 微信支付驱动
 */
class WechatPay extends AbstractPay
{
    /**
     * 支付驱动
     * 
     * @var string
     */
    protected $driver = 'wechat';
    
    /**
     * 支付
     * 
     * @param array $order 订单信息
     * @return mixed
     * @throws \Exception
     */
    public function pay(array $order)
    {
        try {
            // 根据支付场景选择支付方式
            $method = $order['method'] ?? 'mp';
            
            switch ($method) {
                case 'mp': // 公众号支付
                    return $this->pay->wechat()->mp($order);
                case 'mini': // 小程序支付
                    return $this->pay->wechat()->mini($order);
                case 'wap': // H5支付
                    return $this->pay->wechat()->wap($order);
                case 'scan': // 扫码支付
                    return $this->pay->wechat()->scan($order);
                case 'app': // APP支付
                    return $this->pay->wechat()->app($order);
                case 'pos': // 刷卡支付
                    return $this->pay->wechat()->pos($order);
                case 'transfer': // 转账
                    return $this->pay->wechat()->transfer($order);
                default:
                    throw new \Exception('不支持的支付方式：' . $method);
            }
        } catch (InvalidParamsException $e) {
            throw new \Exception('支付参数错误：' . $e->getMessage());
        } catch (Exception $e) {
            throw new \Exception('微信支付异常：' . $e->getMessage());
        }
    }
    
    /**
     * 查询订单
     * 
     * @param string $outTradeNo 商户订单号
     * @return mixed
     * @throws \Exception
     */
    public function find(string $outTradeNo)
    {
        try {
            return $this->pay->wechat()->find($outTradeNo);
        } catch (Exception $e) {
            throw new \Exception('查询订单失败：' . $e->getMessage());
        }
    }
    
    /**
     * 退款
     * 
     * @param array $order 订单信息
     * @return mixed
     * @throws \Exception
     */
    public function refund(array $order)
    {
        try {
            return $this->pay->wechat()->refund($order);
        } catch (Exception $e) {
            throw new \Exception('退款失败：' . $e->getMessage());
        }
    }
    
    /**
     * 关闭订单
     * 
     * @param string $outTradeNo 商户订单号
     * @return mixed
     * @throws \Exception
     */
    public function close(string $outTradeNo)
    {
        try {
            return $this->pay->wechat()->close($outTradeNo);
        } catch (Exception $e) {
            throw new \Exception('关闭订单失败：' . $e->getMessage());
        }
    }
    
    /**
     * 取消订单
     * 
     * @param string $outTradeNo 商户订单号
     * @return mixed
     * @throws \Exception
     */
    public function cancel(string $outTradeNo)
    {
        try {
            return $this->pay->wechat()->cancel($outTradeNo);
        } catch (Exception $e) {
            throw new \Exception('取消订单失败：' . $e->getMessage());
        }
    }
    
    /**
     * 验证支付回调通知
     * 
     * @param array $data 通知数据
     * @return mixed
     * @throws \Exception
     */
    public function verify(array $data = [])
    {
        try {
            return $this->pay->wechat()->callback($data);
        } catch (Exception $e) {
            throw new \Exception('验证支付通知失败：' . $e->getMessage());
        }
    }
    
    /**
     * 支付通知处理
     * 
     * @return mixed
     * @throws \Exception
     */
    public function notify()
    {
        try {
            return $this->pay->wechat()->callback();
        } catch (Exception $e) {
            throw new \Exception('处理支付通知失败：' . $e->getMessage());
        }
    }
}