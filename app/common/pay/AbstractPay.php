<?php

namespace app\common\pay;

use app\common\ApiCode;
use think\facade\Config;
use Yansongda\Pay\Pay;

/**
 * 支付抽象类
 * 实现支付接口的基本方法
 */
abstract class AbstractPay implements PayInterface
{
    /**
     * 支付实例
     * 
     * @var \Yansongda\Pay\Pay
     */
    protected $pay;
    
    /**
     * 支付驱动
     * 
     * @var string
     */
    protected $driver;
    
    /**
     * 支付配置
     * 
     * @var array
     */
    protected $config = [];
    
    /**
     * 构造函数
     * 
     * @param string $configKey 配置键名
     */
    public function __construct(string $configKey = 'default')
    {
        // 获取支付配置
        $config = Config::get('pay.' . $this->driver);
        
        if (empty($config)) {
            throw new \Exception(ApiCode::getMessage(ApiCode::SERVER_ERROR) . ': 支付配置不存在', ApiCode::SERVER_ERROR);
        }
        
        if (!isset($config[$configKey])) {
            throw new \Exception(ApiCode::getMessage(ApiCode::SERVER_ERROR) . ': 支付配置不存在', ApiCode::SERVER_ERROR);
        }
        
        $this->config = $config[$configKey];
        $this->pay = Pay::config([$this->driver => $this->config]);
    }
    
    /**
     * 获取配置
     * 
     * @return array
     */
    public function getConfig()
    {
        return $this->config;
    }
    
    /**
     * 获取支付实例
     * 
     * @return \Yansongda\Pay\Pay
     */
    public function getPay()
    {
        return $this->pay;
    }
}