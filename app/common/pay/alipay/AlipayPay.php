<?php

namespace app\common\pay\alipay;

use app\common\pay\AbstractPay;
use Yansongda\Pay\Exception\Exception;
use Yansongda\Pay\Exception\InvalidParamsException;

/**
 * 支付宝支付驱动
 */
class AlipayPay extends AbstractPay
{
    /**
     * 支付驱动
     * 
     * @var string
     */
    protected $driver = 'alipay';
    
    /**
     * 支付
     * 
     * @param array $order 订单信息
     * @return mixed
     * @throws \Exception
     */
    public function pay(array $order)
    {
        try {
            // 根据支付场景选择支付方式
            $method = $order['method'] ?? 'web';
            
            switch ($method) {
                case 'web': // 电脑支付
                    return $this->pay->alipay()->web($order);
                case 'wap': // 手机网站支付
                    return $this->pay->alipay()->wap($order);
                case 'app': // APP支付
                    return $this->pay->alipay()->app($order);
                case 'scan': // 扫码支付
                    return $this->pay->alipay()->scan($order);
                case 'pos': // 刷卡支付
                    return $this->pay->alipay()->pos($order);
                case 'mini': // 小程序支付
                    return $this->pay->alipay()->mini($order);
                case 'transfer': // 转账
                    return $this->pay->alipay()->transfer($order);
                default:
                    throw new \Exception('不支持的支付方式：' . $method);
            }
        } catch (InvalidParamsException $e) {
            throw new \Exception('支付参数错误：' . $e->getMessage());
        } catch (Exception $e) {
            throw new \Exception('支付宝支付异常：' . $e->getMessage());
        }
    }
    
    /**
     * 查询订单
     * 
     * @param string $outTradeNo 商户订单号
     * @return mixed
     * @throws \Exception
     */
    public function find(string $outTradeNo)
    {
        try {
            return $this->pay->alipay()->find($outTradeNo);
        } catch (Exception $e) {
            throw new \Exception('查询订单失败：' . $e->getMessage());
        }
    }
    
    /**
     * 退款
     * 
     * @param array $order 订单信息
     * @return mixed
     * @throws \Exception
     */
    public function refund(array $order)
    {
        try {
            return $this->pay->alipay()->refund($order);
        } catch (Exception $e) {
            throw new \Exception('退款失败：' . $e->getMessage());
        }
    }
    
    /**
     * 关闭订单
     * 
     * @param string $outTradeNo 商户订单号
     * @return mixed
     * @throws \Exception
     */
    public function close(string $outTradeNo)
    {
        try {
            return $this->pay->alipay()->close($outTradeNo);
        } catch (Exception $e) {
            throw new \Exception('关闭订单失败：' . $e->getMessage());
        }
    }
    
    /**
     * 取消订单
     * 
     * @param string $outTradeNo 商户订单号
     * @return mixed
     * @throws \Exception
     */
    public function cancel(string $outTradeNo)
    {
        try {
            return $this->pay->alipay()->cancel($outTradeNo);
        } catch (Exception $e) {
            throw new \Exception('取消订单失败：' . $e->getMessage());
        }
    }
    
    /**
     * 验证支付回调通知
     * 
     * @param array $data 通知数据
     * @return mixed
     * @throws \Exception
     */
    public function verify(array $data = [])
    {
        try {
            return $this->pay->alipay()->callback($data);
        } catch (Exception $e) {
            throw new \Exception('验证支付通知失败：' . $e->getMessage());
        }
    }
    
    /**
     * 支付通知处理
     * 
     * @return mixed
     * @throws \Exception
     */
    public function notify()
    {
        try {
            return $this->pay->alipay()->callback();
        } catch (Exception $e) {
            throw new \Exception('处理支付通知失败：' . $e->getMessage());
        }
    }
}