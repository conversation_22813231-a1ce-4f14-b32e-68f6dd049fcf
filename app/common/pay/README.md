# 支付服务使用说明

本支付服务基于 yansongda/pay 3.7 版本开发，支持支付宝、微信支付等多种支付方式的灵活切换。

## 配置说明

支付配置文件位于 `config/pay.php`，包含支付宝和微信支付的配置项。实际使用时，建议将敏感信息配置在环境变量中。

### 支付宝配置

```php
'alipay' => [
    'default' => [
        // 必填-支付宝分配的 app_id
        'app_id' => env('ALIPAY_APP_ID', ''),
        // 必填-应用私钥 字符串或路径
        'app_secret_cert' => env('ALIPAY_APP_SECRET_CERT', ''),
        // 必填-应用公钥证书 路径
        'app_public_cert_path' => env('ALIPAY_APP_PUBLIC_CERT_PATH', ''),
        // 必填-支付宝公钥证书 路径
        'alipay_public_cert_path' => env('ALIPAY_PUBLIC_CERT_PATH', ''),
        // 必填-支付宝根证书 路径
        'alipay_root_cert_path' => env('ALIPAY_ROOT_CERT_PATH', ''),
        // 选填-服务商模式下的服务商 id，当 mode 为 Pay::MODE_SERVICE 时使用该参数
        'service_provider_id' => env('ALIPAY_SERVICE_PROVIDER_ID', ''),
        // 选填-默认为正常模式。可选为： MODE_NORMAL, MODE_SANDBOX, MODE_SERVICE
        'mode' => env('ALIPAY_MODE', 'MODE_NORMAL'),
    ]
]
```

### 微信支付配置

```php
'wechat' => [
    'default' => [
        // 必填-商户号，服务商模式下为服务商商户号
        'mch_id' => env('WECHAT_MCH_ID', ''),
        // 必填-商户秘钥
        'mch_secret_key' => env('WECHAT_MCH_SECRET_KEY', ''),
        // 必填-商户私钥 字符串或路径
        'mch_secret_cert' => env('WECHAT_MCH_SECRET_CERT', ''),
        // 必填-商户公钥证书路径
        'mch_public_cert_path' => env('WECHAT_MCH_PUBLIC_CERT_PATH', ''),
        // 必填-微信公钥证书路径
        'wechat_public_cert_path' => env('WECHAT_PUBLIC_CERT_PATH', ''),
        // 必填-微信支付平台证书根路径, optional, 可选, 如需使用微信敏感接口时必填
        'wechat_cert_dir' => env('WECHAT_CERT_DIR', ''),
        // 必填-应用 app_id
        'app_id' => env('WECHAT_APP_ID', ''),
        // 选填-微信公众号 app_id
        'mp_app_id' => env('WECHAT_MP_APP_ID', ''),
        // 选填-微信小程序 app_id
        'mini_app_id' => env('WECHAT_MINI_APP_ID', ''),
        // 选填-服务商模式下，子商户id
        'sub_mch_id' => env('WECHAT_SUB_MCH_ID', ''),
        // 选填-服务商模式下，子商户app_id
        'sub_app_id' => env('WECHAT_SUB_APP_ID', ''),
        // 选填-服务商模式下，子商户公众号 app_id
        'sub_mp_app_id' => env('WECHAT_SUB_MP_APP_ID', ''),
        // 选填-服务商模式下，子商户小程序 app_id
        'sub_mini_app_id' => env('WECHAT_SUB_MINI_APP_ID', ''),
        // 选填-默认为正常模式。可选为： MODE_NORMAL, MODE_SERVICE
        'mode' => env('WECHAT_MODE', 'MODE_NORMAL'),
    ]
]
```

## 使用示例

### 创建支付订单

```php
// 使用支付宝支付
$payService = \app\common\pay\PayService::alipay();

// 构建支付订单参数
$order = [
    'out_trade_no' => '202405010001', // 商户订单号
    'amount' => '99.99', // 支付金额
    'subject' => '测试商品', // 商品标题
    'method' => 'web', // 支付方式：web-电脑支付，wap-手机网站支付，app-APP支付，scan-扫码支付，pos-刷卡支付，mini-小程序支付
];

// 创建支付订单
$result = $payService->pay($order);

// 使用微信支付
$payService = \app\common\pay\PayService::wechat();

// 构建支付订单参数
$order = [
    'out_trade_no' => '202405010001', // 商户订单号
    'amount' => '99.99', // 支付金额
    'subject' => '测试商品', // 商品标题
    'method' => 'mp', // 支付方式：mp-公众号支付，mini-小程序支付，wap-H5支付，scan-扫码支付，app-APP支付，pos-刷卡支付
];

// 创建支付订单
$result = $payService->pay($order);
```

### 查询订单

```php
// 使用支付宝查询订单
$payService = \app\common\pay\PayService::alipay();
$result = $payService->find('202405010001'); // 商户订单号

// 使用微信查询订单
$payService = \app\common\pay\PayService::wechat();
$result = $payService->find('202405010001'); // 商户订单号
```

### 关闭订单

```php
// 使用支付宝关闭订单
$payService = \app\common\pay\PayService::alipay();
$result = $payService->close('202405010001'); // 商户订单号

// 使用微信关闭订单
$payService = \app\common\pay\PayService::wechat();
$result = $payService->close('202405010001'); // 商户订单号
```

### 取消订单

```php
// 使用支付宝取消订单
$payService = \app\common\pay\PayService::alipay();
$result = $payService->cancel('202405010001'); // 商户订单号

// 使用微信取消订单
$payService = \app\common\pay\PayService::wechat();
$result = $payService->cancel('202405010001'); // 商户订单号
```

### 申请退款

```php
// 使用支付宝申请退款
$payService = \app\common\pay\PayService::alipay();

// 构建退款参数
$order = [
    'out_trade_no' => '202405010001', // 商户订单号
    'out_refund_no' => '202405010001R', // 商户退款单号
    'amount' => '99.99', // 退款金额
    'reason' => '商品退款', // 退款原因
];

// 申请退款
$result = $payService->refund($order);

// 使用微信申请退款
$payService = \app\common\pay\PayService::wechat();

// 构建退款参数
$order = [
    'out_trade_no' => '202405010001', // 商户订单号
    'out_refund_no' => '202405010001R', // 商户退款单号
    'amount' => '99.99', // 退款金额
    'reason' => '商品退款', // 退款原因
];

// 申请退款
$result = $payService->refund($order);
```

### 处理支付回调通知

```php
// 处理支付宝支付回调通知
$payService = \app\api\common\pay\PayService::alipay();

// 验证支付宝回调通知
$data = $payService->notify();

// 判断支付状态
$isPaid = isset($data['trade_status']) && in_array($data['trade_status'], ['TRADE_SUCCESS', 'TRADE_FINISHED']);

// 获取商户订单号
$outTradeNo = $data['out_trade_no'] ?? '';

// 处理业务逻辑
if ($isPaid && !empty($outTradeNo)) {
    // 更新订单状态为已支付
    // ...
}

// 返回成功响应
return $payService->getDriver()->getPay()->alipay()->success();

// 处理微信支付回调通知
$payService = \app\api\common\pay\PayService::wechat();

// 验证微信回调通知
$data = $payService->notify();

// 判断支付状态
$isPaid = isset($data['trade_state']) && $data['trade_state'] == 'SUCCESS';

// 获取商户订单号
$outTradeNo = $data['out_trade_no'] ?? '';

// 处理业务逻辑
if ($isPaid && !empty($outTradeNo)) {
    // 更新订单状态为已支付
    // ...
}

// 返回成功响应
return $payService->getDriver()->getPay()->wechat()->success();