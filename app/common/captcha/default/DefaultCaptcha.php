<?php

namespace app\common\captcha\default;

use app\common\captcha\AbstractCaptcha;

/**
 * 默认图片验证码实现类
 * 
 * 使用GD库生成图片验证码
 */
class DefaultCaptcha extends AbstractCaptcha
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'default';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'captcha';
    
    /**
     * 生成验证码
     * 
     * @param array $params 额外参数
     * @return array 包含验证码信息的数组
     */
    public function generate(array $params = [])
    {
        // 合并配置
        if (!empty($params)) {
            $this->config = array_merge($this->config, $params);
        }
        
        // 生成验证码
        $code = $this->generateCode($params);
        
        // 生成验证码标识
        $key = $this->generateKey($params);
        
        // 存储验证码
        $this->storeCode($key, $code, $params);
        
        // 生成图片
        $image = $this->createImage($code, $params);
        
        return [
            'key' => $key,
            'code' => $code, // 注意：实际应用中不应返回验证码值
            'image' => $image,
        ];
    }
    
    /**
     * 验证验证码
     * 
     * @param string $code 用户输入的验证码
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return bool 验证结果
     */
    public function verify($code, $key, array $params = [])
    {
        // 获取存储的验证码
        $storedCode = $this->getStoredCode($key);
        
        // 验证码不存在
        if ($storedCode === null) {
            return false;
        }
        
        // 是否区分大小写
        $caseSensitive = $params['case_sensitive'] ?? $this->config['case_sensitive'] ?? false;
        
        // 验证
        $result = $caseSensitive ? ($code === $storedCode) : (strtoupper($code) === strtoupper($storedCode));
        
        // 验证后是否删除验证码
        $removeAfterVerify = $params['remove_after_verify'] ?? $this->config['remove_after_verify'] ?? true;
        if ($removeAfterVerify) {
            $this->removeStoredCode($key);
        }
        
        return $result;
    }
    
    /**
     * 获取验证码图片
     * 
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return string 验证码图片数据（base64编码）
     */
    public function getImage($key, array $params = [])
    {
        // 获取存储的验证码
        $code = $this->getStoredCode($key);
        
        // 验证码不存在
        if ($code === null) {
            return '';
        }
        
        // 生成图片
        return $this->createImage($code, $params);
    }
    
    /**
     * 创建验证码图片
     * 
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return string base64编码的图片数据
     */
    protected function createImage($code, array $params = [])
    {
        // 图片宽度
        $width = $params['width'] ?? $this->config['width'] ?? 120;
        
        // 图片高度
        $height = $params['height'] ?? $this->config['height'] ?? 40;
        
        // 创建图片
        $image = imagecreatetruecolor($width, $height);
        
        // 填充背景色
        $bgColor = imagecolorallocate($image, 255, 255, 255);
        imagefilledrectangle($image, 0, 0, $width, $height, $bgColor);
        
        // 添加干扰线
        $lineNum = $params['line_num'] ?? $this->config['line_num'] ?? 3;
        for ($i = 0; $i < $lineNum; $i++) {
            $lineColor = imagecolorallocate($image, mt_rand(0, 200), mt_rand(0, 200), mt_rand(0, 200));
            imageline($image, mt_rand(0, $width), mt_rand(0, $height), mt_rand(0, $width), mt_rand(0, $height), $lineColor);
        }
        
        // 添加干扰点
        $pixelNum = $params['pixel_num'] ?? $this->config['pixel_num'] ?? 50;
        for ($i = 0; $i < $pixelNum; $i++) {
            $pixelColor = imagecolorallocate($image, mt_rand(0, 200), mt_rand(0, 200), mt_rand(0, 200));
            imagesetpixel($image, mt_rand(0, $width), mt_rand(0, $height), $pixelColor);
        }
        
        // 添加验证码文字
        $length = strlen($code);
        $fontSize = $params['font_size'] ?? $this->config['font_size'] ?? 18;
        
        for ($i = 0; $i < $length; $i++) {
            $textColor = imagecolorallocate($image, mt_rand(0, 100), mt_rand(0, 100), mt_rand(0, 100));
            $x = ($width / $length) * $i + mt_rand(5, 10);
            $y = $height / 2 + mt_rand(-10, 10);
            imagechar($image, $fontSize, $x, $y, $code[$i], $textColor);
        }
        
        // 输出图片
        ob_start();
        imagepng($image);
        $imageData = ob_get_clean();
        imagedestroy($image);
        
        // 返回base64编码的图片数据
        return 'data:image/png;base64,' . base64_encode($imageData);
    }
}