<?php

namespace app\common\captcha;

/**
 * 验证码工厂类
 * 
 * 用于创建不同类型的验证码实例
 */
class CaptchaFactory
{
    /**
     * 获取验证码实例
     * 
     * @param string $type 验证码类型，如 'default', 'third_party'
     * @param array $config 配置信息
     * @return CaptchaInterface 验证码实例
     */
    public static function getInstance($type = null, array $config = [])
    {
        // 默认类型
        if (empty($type)) {
            $type = config('captcha.default_type', 'default');
        }
        
        // 类名格式：{Type}Captcha
        $className = '\\app\\common\\captcha\\' . strtolower($type) . '\\' . ucfirst($type) . 'Captcha';
        
        // 如果类不存在，则使用默认类
        if (!class_exists($className)) {
            $className = '\\app\\common\\captcha\\default\\DefaultCaptcha';
        }
        
        // 如果默认类也不存在，则抛出异常
        if (!class_exists($className)) {
            throw new \Exception("Captcha type not found: {$type}");
        }
        
        // 合并配置
        $defaultConfig = config('captcha.types.' . $type, []);
        $mergedConfig = array_merge($defaultConfig, $config);
        
        // 创建实例
        return new $className($mergedConfig);
    }
}