<?php

namespace app\common\captcha\tencent;

use app\common\captcha\AbstractCaptcha;

/**
 * 腾讯云验证码实现类
 * 
 * 使用腾讯云验证码服务
 */
class TencentCaptcha extends AbstractCaptcha
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'tencent';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'captcha';
    
    /**
     * 生成验证码
     * 
     * @param array $params 额外参数
     * @return array 包含验证码信息的数组
     */
    public function generate(array $params = [])
    {
        // 合并配置
        if (!empty($params)) {
            $this->config = array_merge($this->config, $params);
        }
        
        // 第三方验证码通常不需要生成验证码，而是返回一个唯一标识
        $key = $this->generateKey($params);
        
        // 返回腾讯云验证码所需的参数
        return [
            'key' => $key,
            'app_id' => $this->config['app_id'] ?? '',
            'scene_id' => $this->config['scene_id'] ?? '',
            'extra_data' => $this->config['extra_data'] ?? '',
        ];
    }
    
    /**
     * 验证验证码
     * 
     * @param string $ticket 用户输入的验证码或腾讯云验证码返回的票据
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return bool 验证结果
     */
    public function verify($ticket, $key, array $params = [])
    {
        // 合并配置
        if (!empty($params)) {
            $this->config = array_merge($this->config, $params);
        }
        
        // 获取配置
        $appId = $params['app_id'] ?? $this->config['app_id'] ?? '';
        $appSecretKey = $params['app_secret_key'] ?? $this->config['app_secret_key'] ?? '';
        $randStr = $params['rand_str'] ?? '';
        $userIp = $params['user_ip'] ?? request()->ip();
        
        // 如果缺少必要参数，则验证失败
        if (empty($appId) || empty($appSecretKey) || empty($ticket) || empty($randStr)) {
            return false;
        }
        
        // 构造请求参数
        $requestParams = [
            'aid' => $appId,
            'AppSecretKey' => $appSecretKey,
            'Ticket' => $ticket,
            'Randstr' => $randStr,
            'UserIP' => $userIp,
        ];
        
        // 发送验证请求
        try {
            // 这里应该使用HTTP客户端发送请求到腾讯云验证码服务
            // 为了简化示例，这里只是模拟请求
            // 实际应用中应该使用HTTP客户端如Guzzle或cURL发送请求
            
            // 模拟请求结果
            $response = [
                'response' => 1, // 1: 验证成功，0: 验证失败
                'evil_level' => 0, // 恶意等级
                'err_msg' => '',
            ];
            
            // 验证结果
            return $response['response'] === 1;
        } catch (\Exception $e) {
            // 记录错误日志
            // log_error('验证腾讯云验证码失败：' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取验证码图片
     * 
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return string 验证码图片数据（通常为空，因为腾讯云验证码由前端直接调用）
     */
    public function getImage($key, array $params = [])
    {
        // 腾讯云验证码通常由前端直接调用，不需要后端生成图片
        return '';
    }
}