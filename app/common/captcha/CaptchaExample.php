<?php

namespace app\common\captcha;

/**
 * 验证码使用示例
 * 
 * 本文件展示了如何使用验证码系统，包括生成验证码、验证验证码以及如何切换不同的验证码提供商
 */
class CaptchaExample
{
    /**
     * 默认图片验证码示例
     */
    public static function defaultCaptchaExample()
    {
        // 生成验证码
        $result = CaptchaService::generate();
        
        if ($result) {
            // 获取验证码信息
            $key = $result['key'];
            $image = $result['image']; // base64编码的图片数据
            
            // 在页面中显示验证码图片
            echo '<img src="' . $image . '" alt="验证码">';
            echo '<input type="hidden" name="captcha_key" value="' . $key . '">';
            echo '<input type="text" name="captcha_code" placeholder="请输入验证码">';
            
            // 验证验证码（通常在表单提交后验证）
            $userInput = '1234'; // 用户输入的验证码
            $isValid = CaptchaService::verify($userInput, $key);
            
            if ($isValid) {
                echo '验证码正确';
            } else {
                echo '验证码错误';
            }
        } else {
            echo '生成验证码失败：' . CaptchaService::getError();
        }
    }
    
    /**
     * 第三方验证码示例（腾讯云验证码）
     */
    public static function thirdPartyCaptchaExample()
    {
        // 生成第三方验证码参数
        $result = CaptchaService::generate([], 'third_party');
        
        if ($result) {
            // 获取验证码信息
            $key = $result['key'];
            $appId = $result['app_id'];
            
            // 在页面中集成第三方验证码
            echo '<div id="TencentCaptcha" data-appid="' . $appId . '" data-cbfn="callback"></div>';
            echo '<script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>';
            echo '<script>
                window.callback = function(res) {
                    if (res.ret === 0) {
                        // 验证成功，将票据和随机字符串提交到服务端验证
                        var ticket = res.ticket;
                        var randstr = res.randstr;
                        
                        // 提交到服务端验证（示例）
                        console.log("验证成功，票据：" + ticket + "，随机字符串：" + randstr);
                    }
                }
            </script>';
            
            // 服务端验证（通常在接收到前端提交的票据后验证）
            $ticket = 'xxx'; // 前端提交的票据
            $randStr = 'xxx'; // 前端提交的随机字符串
            
            $isValid = CaptchaService::verify($ticket, $key, [
                'rand_str' => $randStr,
                'user_ip' => request()->ip(),
            ], 'third_party');
            
            if ($isValid) {
                echo '验证码正确';
            } else {
                echo '验证码错误';
            }
        } else {
            echo '生成验证码失败：' . CaptchaService::getError();
        }
    }
    
    /**
     * 自定义配置示例
     */
    public static function customConfigExample()
    {
        // 自定义配置
        $config = [
            'code_length' => 6,
            'code_type' => 'numeric',
            'width' => 150,
            'height' => 50,
            'line_num' => 5,
            'pixel_num' => 100,
        ];
        
        // 生成验证码
        $result = CaptchaService::generate($config);
        
        if ($result) {
            // 获取验证码信息
            $key = $result['key'];
            $image = $result['image'];
            
            // 在页面中显示验证码图片
            echo '<img src="' . $image . '" alt="验证码">';
        }
    }
    
    /**
     * 切换验证码类型示例
     */
    public static function switchCaptchaTypeExample()
    {
        // 根据配置或用户选择切换验证码类型
        $captchaType = request()->param('captcha_type', 'default');
        
        // 生成验证码
        $result = CaptchaService::generate([], $captchaType);
        
        if ($result) {
            // 根据不同的验证码类型处理结果
            if ($captchaType === 'default') {
                // 显示图片验证码
                echo '<img src="' . $result['image'] . '" alt="验证码">';
            } else if ($captchaType === 'third_party') {
                // 集成第三方验证码
                echo '<div id="TencentCaptcha" data-appid="' . $result['app_id'] . '" data-cbfn="callback"></div>';
            }
        }
    }
}