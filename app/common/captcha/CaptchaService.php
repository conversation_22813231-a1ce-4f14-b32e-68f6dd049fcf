<?php

namespace app\common\captcha;

/**
 * 验证码服务类
 * 
 * 作为验证码系统的主入口，提供统一的接口供应用调用
 */
class CaptchaService
{
    /**
     * 错误信息
     * @var string
     */
    protected static $error = '';

    /**
     * 错误码
     * @var int
     */
    protected static $code = 0;
    
    /**
     * 生成验证码
     * 
     * @param array $params 额外参数
     * @param string $type 验证码类型，如 'default', 'third_party'
     * @return array|bool 生成结果，成功返回结果数组，失败返回false
     */
    public static function generate(array $params = [], $type = null)
    {
        try {
            // 获取验证码实例
            $captcha = CaptchaFactory::getInstance($type, $params);
            
            // 生成验证码
            return $captcha->generate($params);
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            self::$code = $e->getCode();
            return false;
        }
    }
    
    /**
     * 验证验证码
     * 
     * @param string $code 用户输入的验证码
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @param string $type 验证码类型，如 'default', 'third_party'
     * @return bool 验证结果
     */
    public static function verify($code, $key, array $params = [], $type = null)
    {
        try {
            // 获取验证码实例
            $captcha = CaptchaFactory::getInstance($type, $params);
            
            // 验证验证码
            return $captcha->verify($code, $key, $params);
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            self::$code = $e->getCode();
            return false;
        }
    }
    
    /**
     * 获取验证码图片
     * 
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @param string $type 验证码类型，如 'default', 'third_party'
     * @return string 验证码图片数据
     */
    public static function getImage($key, array $params = [], $type = null)
    {
        try {
            // 获取验证码实例
            $captcha = CaptchaFactory::getInstance($type, $params);
            
            // 获取验证码图片
            return $captcha->getImage($key, $params);
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            self::$code = $e->getCode();
            return '';
        }
    }
    
    /**
     * 获取错误信息
     * 
     * @return string
     */
    public static function getError()
    {
        return self::$error;
    }
    
    /**
     * 获取错误码
     * 
     * @return int
     */
    public static function getCode()
    {
        return self::$code;
    }
}