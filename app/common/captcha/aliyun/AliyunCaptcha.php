<?php

namespace app\common\captcha\aliyun;

use app\common\captcha\AbstractCaptcha;

/**
 * 阿里云验证码实现类
 * 
 * 使用阿里云验证码服务
 */
class AliyunCaptcha extends AbstractCaptcha
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'aliyun';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'captcha';
    
    /**
     * 生成验证码
     * 
     * @param array $params 额外参数
     * @return array 包含验证码信息的数组
     */
    public function generate(array $params = [])
    {
        // 合并配置
        if (!empty($params)) {
            $this->config = array_merge($this->config, $params);
        }
        
        // 第三方验证码通常不需要生成验证码，而是返回一个唯一标识
        $key = $this->generateKey($params);
        
        // 返回阿里云验证码所需的参数
        return [
            'key' => $key,
            'access_key_id' => $this->config['access_key_id'] ?? '',
            'scene' => $this->config['scene'] ?? 'default',
            'extra_data' => $this->config['extra_data'] ?? '',
        ];
    }
    
    /**
     * 验证验证码
     * 
     * @param string $token 用户输入的验证码或阿里云验证码返回的token
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return bool 验证结果
     */
    public function verify($token, $key, array $params = [])
    {
        // 合并配置
        if (!empty($params)) {
            $this->config = array_merge($this->config, $params);
        }
        
        // 获取配置
        $accessKeyId = $params['access_key_id'] ?? $this->config['access_key_id'] ?? '';
        $accessKeySecret = $params['access_key_secret'] ?? $this->config['access_key_secret'] ?? '';
        $scene = $params['scene'] ?? $this->config['scene'] ?? 'default';
        
        // 如果缺少必要参数，则验证失败
        if (empty($accessKeyId) || empty($accessKeySecret) || empty($token)) {
            return false;
        }
        
        // 构造请求参数
        $requestParams = [
            'AccessKeyId' => $accessKeyId,
            'AccessKeySecret' => $accessKeySecret,
            'Token' => $token,
            'Scene' => $scene,
        ];
        
        // 发送验证请求
        try {
            // 这里应该使用HTTP客户端发送请求到阿里云验证码服务
            // 为了简化示例，这里只是模拟请求
            // 实际应用中应该使用HTTP客户端如Guzzle或cURL发送请求
            
            // 模拟请求结果
            $response = [
                'Code' => 'OK', // OK: 验证成功，其他: 验证失败
                'Message' => '',
            ];
            
            // 验证结果
            return $response['Code'] === 'OK';
        } catch (\Exception $e) {
            // 记录错误日志
            // log_error('验证阿里云验证码失败：' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取验证码图片
     * 
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return string 验证码图片数据（通常为空，因为阿里云验证码由前端直接调用）
     */
    public function getImage($key, array $params = [])
    {
        // 阿里云验证码通常由前端直接调用，不需要后端生成图片
        return '';
    }
}