<?php

namespace app\common\captcha\math;

use app\common\captcha\AbstractCaptcha;

/**
 * 数学计算验证码实现类
 * 
 * 生成简单的数学计算表达式作为验证码
 */
class MathCaptcha extends AbstractCaptcha
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'math';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'captcha';
    
    /**
     * 生成验证码
     * 
     * @param array $params 额外参数
     * @return array 包含验证码信息的数组
     */
    public function generate(array $params = [])
    {
        // 合并配置
        if (!empty($params)) {
            $this->config = array_merge($this->config, $params);
        }
        
        // 生成数学表达式
        $expression = $this->generateMathExpression($params);
        
        // 计算结果
        $result = $this->calculateExpression($expression);
        
        // 生成验证码标识
        $key = $this->generateKey($params);
        
        // 存储验证码（结果）
        $this->storeCode($key, $result, $params);
        
        // 生成图片
        $image = $this->createImage($expression, $params);
        
        return [
            'key' => $key,
            'expression' => $expression,
            'result' => $result, // 注意：实际应用中不应返回结果
            'image' => $image,
        ];
    }
    
    /**
     * 验证验证码
     * 
     * @param string $code 用户输入的验证码（计算结果）
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return bool 验证结果
     */
    public function verify($code, $key, array $params = [])
    {
        // 获取存储的验证码（计算结果）
        $storedCode = $this->getStoredCode($key);
        
        // 验证码不存在
        if ($storedCode === null) {
            return false;
        }
        
        // 验证（比较计算结果）
        $result = (string)$code === (string)$storedCode;
        
        // 验证后是否删除验证码
        $removeAfterVerify = $params['remove_after_verify'] ?? $this->config['remove_after_verify'] ?? true;
        if ($removeAfterVerify) {
            $this->removeStoredCode($key);
        }
        
        return $result;
    }
    
    /**
     * 获取验证码图片
     * 
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return string 验证码图片数据（base64编码）
     */
    public function getImage($key, array $params = [])
    {
        // 获取存储的验证码表达式
        $expression = cache('captcha_expression_' . $key);
        
        // 表达式不存在
        if ($expression === null) {
            return '';
        }
        
        // 生成图片
        return $this->createImage($expression, $params);
    }
    
    /**
     * 生成数学表达式
     * 
     * @param array $params 额外参数
     * @return string 数学表达式
     */
    protected function generateMathExpression(array $params = [])
    {
        // 获取难度级别
        $level = $params['level'] ?? $this->config['level'] ?? 'easy';
        
        // 根据难度级别生成表达式
        switch ($level) {
            case 'easy':
                // 简单：加减法，个位数
                $num1 = mt_rand(1, 9);
                $num2 = mt_rand(1, 9);
                $operator = mt_rand(0, 1) ? '+' : '-';
                
                // 确保结果为正数
                if ($operator === '-' && $num1 < $num2) {
                    list($num1, $num2) = [$num2, $num1];
                }
                
                return $num1 . ' ' . $operator . ' ' . $num2;
                
            case 'medium':
                // 中等：加减乘，两位数
                $num1 = mt_rand(10, 20);
                $num2 = mt_rand(1, 9);
                $operator = mt_rand(0, 2);
                
                if ($operator === 0) {
                    return $num1 . ' + ' . $num2;
                } elseif ($operator === 1) {
                    return $num1 . ' - ' . $num2;
                } else {
                    return $num1 . ' × ' . $num2;
                }
                
            case 'hard':
                // 困难：加减乘除，两位数
                $num1 = mt_rand(10, 50);
                $num2 = mt_rand(2, 10);
                $operator = mt_rand(0, 3);
                
                if ($operator === 0) {
                    return $num1 . ' + ' . $num2;
                } elseif ($operator === 1) {
                    return $num1 . ' - ' . $num2;
                } elseif ($operator === 2) {
                    return $num1 . ' × ' . $num2;
                } else {
                    // 确保能整除
                    $num1 = $num2 * mt_rand(1, 10);
                    return $num1 . ' ÷ ' . $num2;
                }
                
            default:
                // 默认简单
                $num1 = mt_rand(1, 9);
                $num2 = mt_rand(1, 9);
                return $num1 . ' + ' . $num2;
        }
    }
    
    /**
     * 计算表达式结果
     * 
     * @param string $expression 数学表达式
     * @return string 计算结果
     */
    protected function calculateExpression($expression)
    {
        // 替换乘除符号为PHP可识别的符号
        $expression = str_replace(['×', '÷'], ['*', '/'], $expression);
        
        // 使用eval计算表达式（注意：实际应用中应该使用更安全的方法）
        // 这里仅用于演示，因为我们的表达式是系统生成的，不存在安全风险
        return eval('return ' . $expression . ';');
    }
    
    /**
     * 创建验证码图片
     * 
     * @param string $expression 数学表达式
     * @param array $params 额外参数
     * @return string base64编码的图片数据
     */
    protected function createImage($expression, array $params = [])
    {
        // 图片宽度
        $width = $params['width'] ?? $this->config['width'] ?? 150;
        
        // 图片高度
        $height = $params['height'] ?? $this->config['height'] ?? 40;
        
        // 创建图片
        $image = imagecreatetruecolor($width, $height);
        
        // 填充背景色
        $bgColor = imagecolorallocate($image, 255, 255, 255);
        imagefilledrectangle($image, 0, 0, $width, $height, $bgColor);
        
        // 添加干扰线
        $lineNum = $params['line_num'] ?? $this->config['line_num'] ?? 3;
        for ($i = 0; $i < $lineNum; $i++) {
            $lineColor = imagecolorallocate($image, mt_rand(0, 200), mt_rand(0, 200), mt_rand(0, 200));
            imageline($image, mt_rand(0, $width), mt_rand(0, $height), mt_rand(0, $width), mt_rand(0, $height), $lineColor);
        }
        
        // 添加干扰点
        $pixelNum = $params['pixel_num'] ?? $this->config['pixel_num'] ?? 50;
        for ($i = 0; $i < $pixelNum; $i++) {
            $pixelColor = imagecolorallocate($image, mt_rand(0, 200), mt_rand(0, 200), mt_rand(0, 200));
            imagesetpixel($image, mt_rand(0, $width), mt_rand(0, $height), $pixelColor);
        }
        
        // 添加表达式文字
        $textColor = imagecolorallocate($image, mt_rand(0, 100), mt_rand(0, 100), mt_rand(0, 100));
        $fontSize = $params['font_size'] ?? $this->config['font_size'] ?? 5;
        $x = 10;
        $y = $height / 2;
        imagestring($image, $fontSize, $x, $y - 8, $expression . ' = ?', $textColor);
        
        // 输出图片
        ob_start();
        imagepng($image);
        $imageData = ob_get_clean();
        imagedestroy($image);
        
        // 返回base64编码的图片数据
        return 'data:image/png;base64,' . base64_encode($imageData);
    }
}