<?php

namespace app\common\captcha;

use app\common\ApiCode;

/**
 * 验证码抽象基类
 * 
 * 实现验证码接口的通用功能，具体验证码实现类可继承此类
 */
abstract class AbstractCaptcha implements CaptchaInterface
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = '';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = '';
    
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        // 合并默认配置
        $defaultConfig = config('captcha', []);
        $this->config = array_merge($defaultConfig, $config);
    }
    
    /**
     * 获取验证码服务名称
     * @return string
     */
    public function getServiceName()
    {
        return $this->serviceName;
    }
    
    /**
     * 获取验证码服务提供商
     * @return string
     */
    public function getServiceProvider()
    {
        return $this->provider;
    }
    
    /**
     * 生成随机验证码
     * 
     * @param array $params 额外参数
     * @return string 生成的验证码
     */
    protected function generateCode(array $params = [])
    {
        $length = $params['code_length'] ?? $this->config['code_length'] ?? 4;
        $type = $params['code_type'] ?? $this->config['code_type'] ?? 'alphanumeric';
        
        switch ($type) {
            case 'numeric':
                return str_pad(mt_rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
            case 'alpha':
                $characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ';
                $code = '';
                for ($i = 0; $i < $length; $i++) {
                    $code .= $characters[mt_rand(0, strlen($characters) - 1)];
                }
                return $code;
            case 'alphanumeric':
                $characters = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ';
                $code = '';
                for ($i = 0; $i < $length; $i++) {
                    $code .= $characters[mt_rand(0, strlen($characters) - 1)];
                }
                return $code;
            default:
                return str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);
        }
    }
    
    /**
     * 存储验证码
     * 
     * @param string $key 验证码标识
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 存储结果
     */
    protected function storeCode($key, $code, array $params = [])
    {
        // 获取验证码有效期
        $expire = $params['expire'] ?? $this->config['expire'] ?? 300;
        
        // 存储验证码到缓存
        return cache('captcha_' . $key, $code, $expire);
    }
    
    /**
     * 获取存储的验证码
     * 
     * @param string $key 验证码标识
     * @return string|null 验证码，不存在则返回null
     */
    protected function getStoredCode($key)
    {
        return cache('captcha_' . $key);
    }
    
    /**
     * 删除存储的验证码
     * 
     * @param string $key 验证码标识
     * @return bool 删除结果
     */
    protected function removeStoredCode($key)
    {
        return cache('captcha_' . $key, null);
    }
    
    /**
     * 生成验证码标识
     * 
     * @param array $params 额外参数
     * @return string 验证码标识
     */
    protected function generateKey(array $params = [])
    {
        return md5(uniqid(mt_rand(), true));
    }
}