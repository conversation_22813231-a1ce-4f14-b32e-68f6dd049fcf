<?php

namespace app\common\captcha;

/**
 * 验证码接口
 * 
 * 定义验证码系统的基本功能，包括生成验证码、验证验证码等
 */
interface CaptchaInterface
{
    /**
     * 生成验证码
     * 
     * @param array $params 额外参数
     * @return array 包含验证码信息的数组，如：['code' => '验证码值', 'image' => '图片数据', 'key' => '验证码标识']
     */
    public function generate(array $params = []);
    
    /**
     * 验证验证码
     * 
     * @param string $code 用户输入的验证码
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return bool 验证结果
     */
    public function verify($code, $key, array $params = []);
    
    /**
     * 获取验证码图片
     * 
     * @param string $key 验证码标识
     * @param array $params 额外参数
     * @return string 验证码图片数据（通常为base64编码的图片数据）
     */
    public function getImage($key, array $params = []);
    
    /**
     * 获取验证码服务名称
     * 
     * @return string
     */
    public function getServiceName();
    
    /**
     * 获取验证码服务提供商
     * 
     * @return string
     */
    public function getServiceProvider();
}