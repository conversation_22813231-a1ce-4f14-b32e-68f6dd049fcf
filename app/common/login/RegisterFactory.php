<?php

namespace app\common\login;

use think\facade\Config;

/**
 * 注册服务工厂类
 */
class RegisterFactory
{
    /**
     * 注册服务实例缓存
     * @var array
     */
    protected static $instances = [];
    
    /**
     * 获取注册服务实例
     * @param string $type 注册类型，如 'username', 'sms', 'email'
     * @param string $provider 服务提供商，如 'default', 'aliyun', 'tencent'
     * @return RegisterInterface
     * @throws \Exception 当找不到对应的注册服务实现时抛出异常
     */
    public static function getInstance($type = null, $provider = null)
    {
        // 如果未指定类型，则使用配置中的默认类型
        if (is_null($type)) {
            $type = Config::get('register.default_type', 'username');
        }
        
        // 如果未指定提供商，则使用配置中对应类型的默认提供商
        if (is_null($provider)) {
            $provider = Config::get("register.{$type}.default_provider", 'default');
        }
        
        // 生成缓存键
        $key = "{$type}:{$provider}";
        
        // 如果实例已存在，则直接返回
        if (isset(self::$instances[$key])) {
            return self::$instances[$key];
        }
        
        // 获取注册服务类
        $class = self::getRegisterClass($type, $provider);
        
        // 获取配置
        $config = Config::get("register.{$type}.providers.{$provider}", []);
        
        // 创建实例
        $instance = new $class($config);
        
        // 缓存实例
        self::$instances[$key] = $instance;
        
        return $instance;
    }
    
    /**
     * 获取注册服务类名
     * @param string $type 注册类型
     * @param string $provider 服务提供商
     * @return string 完整类名
     * @throws \Exception 当找不到对应的注册服务类时抛出异常
     */
    protected static function getRegisterClass($type, $provider)
    {
        // 尝试加载自定义类
        $customClass = Config::get("register.{$type}.providers.{$provider}.class", '');
        if ($customClass && class_exists($customClass)) {
            return $customClass;
        }
        
        // 构建默认类名
        $providerFormatted = ucfirst($provider);
        $typeFormatted = ucfirst($type);
        
        // 尝试加载内置类
        $class = "\\app\\common\\login\\{$type}\\{$providerFormatted}{$typeFormatted}Register";
        
        if (class_exists($class)) {
            return $class;
        }
        
        // 找不到对应的类，抛出异常
        throw new \Exception("Register service not found: {$type}/{$provider}");
    }
}