<?php

namespace app\common\login\password;

use app\common\ApiCode;
use app\common\login\AbstractLogin;
use app\common\login\LoginException;
use think\facade\Db;

/**
 * 默认密码登录实现类
 */
class DefaultPasswordLogin extends AbstractLogin
{
    /**
     * 执行密码登录
     * 
     * @param string $username 用户名
     * @param string $password 密码
     * @param array $params 额外参数
     * @return array 登录结果
     */
    public function login($username, $password, array $params = [])
    {

        if(is_array($params) && !is_null($params)) {
            $this->config = array_merge($params, $this->config);
        }
        // 验证参数
        if (empty($username)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::USERNAME_EMPTY),
                ApiCode::USERNAME_EMPTY
            );
        }
        
        if (empty($password)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::PASSWORD_EMPTY),
                ApiCode::PASSWORD_EMPTY
            );
        }
        
        // 查询用户
        $user = $this->getUserByUsername($username);
        
        if (empty($user)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::USER_NOT_EXIST),
                ApiCode::USER_NOT_EXIST
            );
        }

        if($user['loginfailure'] >= 10 && time() - $user['updatetime'] < 86400){
            throw new LoginException(
                '请于一天后重试',
                ApiCode::LOGIN_FAILURE_TIMES_ERROR
            );
        }

        // 验证密码
        if (!$this->verifyPassword($password, $user['password'], $user['salt'] ?? '')) {
            // 更新登录失败次数
            $table = $params['table'] ?? ($this->config['table'] ?? 'sys_admin');
            Db::table($table)
                ->where('id', $user['id'])
                ->update([
                    'loginfailure' => $user['loginfailure'] + 1,
                    'updatetime' => date("Y-m-d H:i:s", time())
                ]);
            throw new LoginException(
                ApiCode::getMessage(ApiCode::PASSWORD_ERROR),
                ApiCode::PASSWORD_ERROR
            );
        }

        // 更新登录信息
        $table = $params['table'] ?? ($this->config['table'] ?? 'sys_admin');
        Db::table($table)
            ->where('id', $user['id'])
            ->update([
                'loginfailure' => 0,
                'logintime' => date("Y-m-d H:i:s", time()),
                'updatetime' => date("Y-m-d H:i:s", time())
            ]);

        // 登录成功，返回用户信息
        return [
            'success' => true,
            'msg' => '登录成功',
            'user_id' => $user['id'],
            'user' => $user
        ];
    }
    
    /**
     * 根据用户名获取用户信息
     * 
     * @param string $username 用户名
     * @return array|null 用户信息
     */
    protected function getUserByUsername($username)
    {
        // 获取配置
        $table = $params['table'] ?? ($this->config['table'] ?? 'sys_admin');
        $usernameField = $params['username_field'] ?? ($this->config['username_field'] ?? 'username');
        $statusField = $params['status_field'] ?? ($this->config['status_field'] ?? 'enabled');
        $activeStatus = $params['active_status'] ?? ($this->config['active_status'] ?? 1);

        // 查询用户信息
        $user = Db::table($table)
            ->where($usernameField, '=', $username)
            ->find();

        if (!$user) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::USER_NOT_EXIST),
                ApiCode::USER_NOT_EXIST
            );
        }

        // 检查用户状态
        if (isset($user[$statusField]) && $user[$statusField] != $activeStatus) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::ACCOUNT_DISABLED),
                ApiCode::ACCOUNT_DISABLED
            );
        }

        // 登录成功，返回用户信息
        return $user;
    }
    
    /**
     * 验证密码
     * 
     * @param string $password 输入的密码
     * @param string $hashedPassword 数据库中的哈希密码
     * @param string $salt 盐值
     * @return bool 验证结果
     */
    protected function verifyPassword($password, $hashedPassword, $salt = '')
    {
        // 实际应用中应该使用安全的密码验证方法
        // 这里使用PHP内置的password_verify函数作为示例
        // 如果使用了盐值，应该在验证前将盐值添加到密码中
        if (!empty($salt)) {
//            $password = $password . $salt;
        }

        return password_verify($password, $hashedPassword);
    }
}