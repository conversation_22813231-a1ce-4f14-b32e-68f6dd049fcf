<?php

namespace app\common\login\password;

use app\common\ApiCode;
use app\common\IdGenerator;
use app\common\login\AbstractRegister;
use app\common\login\LoginException;
use app\common\login\validator\PasswordValidator;
use app\common\login\validator\UsernameValidator;
use think\facade\Db;

/**
 * 默认用户名注册实现类
 */
class DefaultPasswordRegister extends AbstractRegister
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'default';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'username';
    
    /**
     * 执行用户名注册
     * 
     * @param string $username 用户名
     * @param string $password 密码
     * @param array $params 额外参数
     * @return array 注册结果
     */
    public function register($username, $password, array $params = [])
    {
        if(is_array($params) && !is_null($params)) {
            $this->config = array_merge($params, $this->config);
        }
        
        // 验证参数
        if (empty($username)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::USERNAME_EMPTY),
                ApiCode::USERNAME_EMPTY
            );
        }
        
        // 验证用户名是否符合规范（禁用词检测等）
        try {
            // 引入用户名验证器
            $validator = new UsernameValidator();
            $validator::validate($username);
        } catch (LoginException $e) {
            // 用户名验证失败，直接抛出异常
            throw new LoginException(
                $e->getMessage(),
                $e->getCode()
            );
        }
        
        if (empty($password)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::PASSWORD_EMPTY),
                ApiCode::PASSWORD_EMPTY
            );
        }
        
        // 验证密码是否符合规范（长度、复杂度等）
        try {
            // 引入密码验证器
            $validator = new PasswordValidator();
            $validator::validate($password);
        } catch (LoginException $e) {
            // 密码验证失败，直接抛出异常
            throw new LoginException(
                $e->getMessage(),
                $e->getCode()
            );
        }
        
        // 获取配置
        $table = $this->config['table'] ?? 'sys_admin';
        $usernameField = $this->config['username_field'] ?? 'username';
        $passwordField = $this->config['password_field'] ?? 'password';
        $saltField = $this->config['salt_field'] ?? 'salt';
        $statusField = $this->config['status_field'] ?? 'enabled';
        $activeStatus = $this->config['active_status'] ?? 1;
        
        // 检查用户名是否已存在
        $existUser = Db::table($table)
            ->where($usernameField, '=', $username)
            ->find();
            
        if ($existUser) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::USERNAME_EXIST),
                ApiCode::USERNAME_EXIST
            );
        }
        
        // 生成盐值
        $salt = $this->generateSalt($password);
        
        // 加密密码
        $encryptedPassword = $this->encryptPassword($password, $salt);
        
        // 准备用户数据
        $userData = [
            $usernameField => $username,
            $passwordField => $encryptedPassword,
            $saltField => $salt,
            $statusField => $activeStatus,
            'user_no' => IdGenerator::generateIdFromDb(),
            'createtime' => date('Y-h-d H:i:s', time()),
            'updatetime' => date('Y-h-d H:i:s', time()),
        ];
        
        // 合并额外的用户数据
        if (isset($params['user_data']) && is_array($params['user_data'])) {
            $userData = array_merge($userData, $params['user_data']);
        }
        
        // 插入用户数据
        try {
            $userId = Db::table($table)->insertGetId($userData);
            
            if (!$userId) {
                throw new LoginException(
                    ApiCode::getMessage(ApiCode::AUTO_REGISTER_FAILED),
                    ApiCode::AUTO_REGISTER_FAILED
                );
            }
            
            // 查询完整的用户信息
            $user = Db::table($table)->where('id', '=', $userId)->find();
            
            // 返回注册结果
            return [
                'code' => 0,
                'msg' => '注册成功',
                'user_id' => $userId,
                'user' => $user
            ];
        } catch (\Exception $e) {
            throw new LoginException(
                '注册失败：' . $e->getMessage(),
                ApiCode::AUTO_REGISTER_FAILED
            );
        }
    }
}