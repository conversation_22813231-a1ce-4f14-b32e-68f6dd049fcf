<?php

namespace app\common\login;

trait AbstractSend
{
    /**
     * 发送
     * @param string $receiver 身份标识（用户名、手机号、邮箱、第三方ID等）
     * @param array $params 额外参数
     * @return array 发送结果
     */
    abstract public function sendCode($receiver, array $params = []);

    /**
     * 校验
     * @param string $identity 身份标识（用户名、手机号、邮箱、第三方ID等）
     * @param string $code 凭证（密码、验证码、token等）
     * @param array $params 额外参数
     * @return array 登录结果
     */
    abstract public function verify($receiver, $code, array $params = []);
}