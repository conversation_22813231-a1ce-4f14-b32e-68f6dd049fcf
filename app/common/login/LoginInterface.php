<?php

namespace app\common\login;

/**
 * 登录接口
 */
interface LoginInterface
{
    /**
     * 登录验证
     * @param string $identity 身份标识（用户名、手机号、邮箱、第三方ID等）
     * @param string $credential 凭证（密码、验证码、token等）
     * @param array $params 额外参数
     * @return array 登录结果
     */
    public function login($identity, $credential, array $params = []);
    
    /**
     * 获取登录服务名称
     * @return string
     */
    public function getServiceName();
    
    /**
     * 获取登录服务提供商
     * @return string
     */
    public function getServiceProvider();
}