<?php

namespace app\common\login\qq;

use app\common\login\AbstractLogin;
use think\facade\Db;

/**
 * 默认QQ登录实现
 */
class DefaultQqLogin extends AbstractLogin
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'default';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'qq';
    
    /**
     * 登录验证
     * @param string $openid QQ用户唯一标识
     * @param string $credential 未使用，保持接口一致性
     * @param array $params 额外参数，包含QQ返回的用户信息
     * @return array 登录结果
     */
    public function login($openid, $credential, array $params = [])
    {
        // 获取配置
        $table = $this->config['table'] ?? 'sys_admin';
        $openidField = $this->config['openid_field'] ?? 'qq_openid';
        $statusField = $this->config['status_field'] ?? 'status';
        $activeStatus = $this->config['active_status'] ?? 1;
        
        // 查询用户信息
        $user = Db::table($table)
            ->where($openidField, '=', $openid)
            ->find();
        
        // 用户不存在，根据配置决定是否自动注册
        if (!$user) {
            $autoRegister = $this->config['auto_register'] ?? true;
            
            if ($autoRegister) {
                // 自动注册用户
                $userId = $this->registerUser($openid, $params);
                
                if ($userId) {
                    $user = Db::table($table)->where('id', '=', $userId)->find();
                } else {
                    return ['code' => 1, 'msg' => '自动注册失败'];
                }
            } else {
                return ['code' => 2, 'msg' => '用户未绑定QQ账号'];
            }
        }
        
        // 检查用户状态
        if (isset($user[$statusField]) && $user[$statusField] != $activeStatus) {
            return ['code' => 3, 'msg' => '账号已禁用'];
        }
        
        // 登录成功，返回用户信息
        return [
            'code' => 0,
            'msg' => '登录成功',
            'user_id' => $user['id'],
            'user' => $user
        ];
    }
    
    /**
     * 注册新用户
     * @param string $openid QQ用户唯一标识
     * @param array $params QQ返回的用户信息
     * @return int|bool 成功返回用户ID，失败返回false
     */
    protected function registerUser($openid, array $params = [])
    {
        // 获取配置
        $table = $this->config['table'] ?? 'sys_admin';
        $openidField = $this->config['openid_field'] ?? 'qq_openid';
        $nicknameField = $this->config['nickname_field'] ?? 'nickname';
        $avatarField = $this->config['avatar_field'] ?? 'avatar';
        $statusField = $this->config['status_field'] ?? 'enabled';
        $activeStatus = $this->config['active_status'] ?? 1;
        
        // 准备用户数据
        $userData = [
            $openidField => $openid,
            $statusField => $activeStatus,
            'createtime' => date('Y-h-d H:i:s', time())
        ];
        
        // 如果有昵称信息，添加到用户数据
        if (isset($params['nickname']) && $nicknameField) {
            $userData[$nicknameField] = $params['nickname'];
        }
        
        // 如果有头像信息，添加到用户数据
        if (isset($params['figureurl_qq_2']) && $avatarField) {
            $userData[$avatarField] = $params['figureurl_qq_2'];
        }
        
        // 添加用户
        try {
            $userId = Db::table($table)->insertGetId($userData);
            return $userId;
        } catch (\Exception $e) {
            // 记录错误日志
            // 这里可以添加日志记录代码
            return false;
        }
    }
    
    /**
     * 绑定QQ账号
     * @param int $userId 用户ID
     * @param string $openid QQ用户唯一标识
     * @param array $params 额外参数
     * @return bool 绑定结果
     */
    public function bind($userId, $openid, array $params = [])
    {
        // 获取配置
        $table = $this->config['table'] ?? 'sys_admin';
        $openidField = $this->config['openid_field'] ?? 'qq_openid';
        
        // 更新用户信息，绑定QQ账号
        try {
            Db::name($table)
                ->where('id', '=', $userId)
                ->update([$openidField => $openid]);
            return true;
        } catch (\Exception $e) {
            // 记录错误日志
            return false;
        }
    }
    
    /**
     * 解绑QQ账号
     * @param int $userId 用户ID
     * @return bool 解绑结果
     */
    public function unbind($userId)
    {
        // 获取配置
        $table = $this->config['table'] ?? 'sys_admin';
        $openidField = $this->config['openid_field'] ?? 'qq_openid';
        
        // 更新用户信息，解绑QQ账号
        try {
            Db::name($table)
                ->where('id', '=', $userId)
                ->update([$openidField => '']);
            return true;
        } catch (\Exception $e) {
            // 记录错误日志
            return false;
        }
    }
}