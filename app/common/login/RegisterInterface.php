<?php

namespace app\common\login;

/**
 * 注册接口
 */
interface RegisterInterface
{
    /**
     * 注册验证
     * @param string $identity 身份标识（用户名、手机号、邮箱等）
     * @param string $credential 凭证（密码、验证码等）
     * @param array $params 额外参数
     * @return array 注册结果
     */
    public function register($identity, $credential, array $params = []);
    
    /**
     * 获取注册服务名称
     * @return string
     */
    public function getServiceName();
    
    /**
     * 获取注册服务提供商
     * @return string
     */
    public function getServiceProvider();
}