<?php

namespace app\common\login;

use app\common\Auth;

/**
 * 注册服务管理类
 */
class RegisterService
{
    /**
     * 错误信息
     * @var string
     */
    protected static $error = '';

    /**
     * 错误码
     * @var int
     */
    protected static $code = 0;
    
    /**
     * 执行注册
     * @param string $identity 身份标识（用户名、手机号、邮箱等）
     * @param string $credential 凭证（密码、验证码等）
     * @param array $params 额外参数
     *        - entity string 可选，实体类名称，用于将用户信息填充到指定的实体类中
     *        - auto_login bool 可选，是否自动登录
     *        - with_refresh_token bool 可选，是否生成刷新令牌
     *        - expire_time int 可选，令牌过期时间（秒）
     *        - token_data array 可选，令牌自定义数据
     * @param string $type 注册类型，如 'username', 'sms', 'email'
     * @param string $provider 服务提供商，如 'default', 'aliyun', 'tencent'
     * @return array|bool 注册结果，成功返回用户信息数组（包含entity对象，如果指定了实体类），失败返回false
     */
    public static function register($identity, $credential, array $params = [], $type = null, $provider = null)
    {
        try {
            // 获取注册服务实例
            $service = RegisterFactory::getInstance($type, $provider);

            try {
                // 执行注册
                $result = $service->register($identity, $credential, $params);

                // 生成令牌（如果需要）
                if (isset($params['auto_login']) && $params['auto_login']) {
                    $withRefreshToken = $params['with_refresh_token'] ?? true;
                    $expireTime = $params['expire_time'] ?? 7200;
                    $expireRefreshTime = $params['expire_refresh_time'] ?? 86400;
                    $customData = $params['token_data'] ?? [];

                    // 添加用户ID到自定义数据
                    $customData['user_id'] = $result['user_id'];

                    // 创建令牌
                    $token = Auth::createToken($result['user_id'], $customData, $expireTime, $withRefreshToken, $expireRefreshTime);

                    // 将令牌添加到结果中
                    $result['token'] = $token;
                }

                // 如果提供了实体类，则将用户信息填充到实体类中
                if (isset($params['entity']) && !empty($params['entity']) && class_exists($params['entity'])) {
                    $entityClass = $params['entity'];
                    $entity = new $entityClass();

                    // 将用户数据填充到实体对象中
                    foreach ($result['user'] as $key => $value) {
                        if (property_exists($entity, $key)) {
                            $entity->$key = $value;
                        }
                    }
                    
                    // 将实体对象添加到结果中
                    $result['entity'] = $entity;
                }

                return $result;
            } catch (LoginException $e) {
                // 注册失败
                self::$code = $e->getCode();
                self::$error = $e->getMessage();
                return false;
            }

        } catch (\Exception $e) {
            self::$code = $e->getCode();
            self::$error = $e->getMessage();
            return false;
        }
    }
    
    /**
     * 使用用户名密码注册
     * @param string $username 用户名
     * @param string $password 密码
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return array|bool 注册结果
     */
    public static function registerByUsername($username, $password, array $params = [], $provider = null)
    {
        return self::register($username, $password, $params, 'username', $provider);
    }
    
    /**
     * 使用短信验证码注册
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return array|bool 注册结果
     */
    public static function registerBySms($mobile, $code, array $params = [], $provider = null)
    {
        return self::register($mobile, $code, $params, 'sms', $provider);
    }
    
    /**
     * 使用邮箱验证码注册
     * @param string $email 邮箱
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return array|bool 注册结果
     */
    public static function registerByEmail($email, $code, array $params = [], $provider = null)
    {
        return self::register($email, $code, $params, 'email', $provider);
    }

    public static function sendCode($recevier, $type, $provider = null, array $params = [])
    {
        // 获取注册服务实例
        $service = RegisterFactory::getInstance($type, $provider);
        return $service->sendCode($recevier, $params);
    }
    
    /**
     * 获取错误信息
     * @return string
     */
    public static function getError()
    {
        return self::$error;
    }

    /**
     * 获取错误码
     * @return int
     */
    public static function getCode()
    {
        return self::$code;
    }
}