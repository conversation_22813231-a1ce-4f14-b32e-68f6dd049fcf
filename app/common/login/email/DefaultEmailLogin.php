<?php

namespace app\common\login\email;

use app\common\ApiCode;
use app\common\login\AbstractLogin;
use app\common\login\AbstractSend;
use app\common\login\LoginException;
use app\common\login\SendException;
use app\common\login\validator\EmailValidator;
use app\common\login\validator\PasswordValidator;
use app\common\login\verification\VerificationCodeService;
use app\common\message\email\VerificationEmailTemplate;
use think\facade\Db;

/**
 * 默认邮箱登录实现
 */
class DefaultEmailLogin extends AbstractLogin
{
    use AbstractSend;
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'default';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'email';
    
    /**
     * 登录验证
     * @param string $email 邮箱
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return array 登录结果
     */
    public function login($email, $code, array $params = [])
    {
        if(is_array($params) && !is_null($params)) {
            $this->config = array_merge($params, $this->config);
        }

        // 验证参数
        if (empty($email)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::EMAIL_EMPTY),
                ApiCode::EMAIL_EMPTY
            );
        }

        if (empty($code)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::CODE_EMPTY),
                ApiCode::CODE_EMPTY
            );
        }

        // 验证邮箱是否符合规范（长度、复杂度等）
        try {
            // 引入邮箱验证器
            $validator = new EmailValidator();
            $validator::validate($email);
        } catch (LoginException $e) {
            // 密码验证失败，直接抛出异常
            throw new LoginException(
                $e->getMessage(),
                $e->getCode()
            );
        }

        // 验证用户名是否符合规范（禁用词检测等）
        try {
            // 引入用户名验证器
            $validator = new PasswordValidator();
            $validator::validate($params['password'] ?? '');
        } catch (LoginException $e) {
            // 用户名验证失败，直接抛出异常
            throw new LoginException(
                $e->getMessage(),
                $e->getCode()
            );
        }

        // 获取配置
        $table = $params['table'] ?? ($this->config['table'] ?? 'qi_users');
        $emailField = $params['email_field'] ?? ($this->config['email_field'] ?? 'mail');
        $statusField = $params['status_field'] ?? ($this->config['status_field'] ?? 'enabled');
        $activeStatus = $params['status_field'] ?? ($this->config['active_status'] ?? 1);
        $codeExpire = $params['code_expire'] ?? ($this->config['code_expire'] ?? 300); // 验证码有效期，默认5分钟
        $autoRegister = $params['auto_register'] ?? ($this->config['auto_register'] ?? false);

        // 验证码校验
        $verify = $this->verify($email, $code, $params);
        
        if (!$verify) {
            //验证码错误或过期
            throw new LoginException(
                ApiCode::getMessage(ApiCode::CODE_ERROR),
                ApiCode::CODE_ERROR
            );
        }

        // 查询用户信息
        $user = Db::table($table)
            ->where($emailField, '=', $email)
            ->find();
        
        // 用户不存在，根据配置决定是否自动注册
        if (!$user) {

            if ($autoRegister) {
                // 自动注册用户
                $userId = $this->registerUser($email, $params);
                
                if ($userId) {
                    $user = Db::name($table)->where('id', '=', $userId)->find();
                } else {
                    //自动注册失败'
                    throw new LoginException(
                        ApiCode::getMessage(ApiCode::AUTO_REGISTER_FAILED),
                        ApiCode::AUTO_REGISTER_FAILED
                    );
                }
            } else {
                //用户不存在'
                throw new LoginException(
                    ApiCode::getMessage(ApiCode::USER_NOT_EXIST),
                    ApiCode::USER_NOT_EXIST
                );
            }
        }
        
        // 检查用户状态
        if (isset($user[$statusField]) && $user[$statusField] != $activeStatus) {
            //账号已禁用'
            throw new LoginException(
                ApiCode::getMessage(ApiCode::ACCOUNT_DISABLED),
                ApiCode::ACCOUNT_DISABLED
            );
        }
        
        // 登录成功，返回用户信息
        return [
            'success' => true,
            'msg' => '登录成功',
            'user_id' => $user['id'],
            'user' => $user
        ];
    }
    
    /**
     * 注册新用户
     * @param string $email 邮箱
     * @param array $params 额外参数
     * @return int|bool 成功返回用户ID，失败返回false
     */
    protected function registerUser($email, array $params = [])
    {
        // 获取配置
        $table = $param['table'] ?? ($this->config['table'] ?? 'sys_admin');
        $emailField = $param['mail'] ?? ($this->config['email_field'] ?? 'mail');
        $statusField = $param['status_field'] ?? ($this->config['status_field'] ?? 'enabled');
        $activeStatus = $param['active_status'] ?? ($this->config['active_status'] ?? 1);

        // 准备用户数据
        $userData = [
            $emailField => $email,
            $statusField => $activeStatus,
            'createtime' => date('Y-h-d H:i:s', time())
        ];
        
        // 如果有昵称信息，添加到用户数据
        if (isset($params['nickname']) && isset($this->config['nickname_field'])) {
            $userData[$this->config['nickname_field']] = $params['nickname'];
        }
        
        // 添加用户
        try {
            $userId = Db::table($table)->insertGetId($userData);
            return $userId;
        } catch (\Exception $e) {
            // 记录错误日志
            return false;
        }
    }
    
    /**
     * 发送验证码
     * @param string $email 邮箱
     * @return array 发送结果
     */
    public function sendCode($email, $params = [])
    {

        if(is_array($params) && !is_null($params)) {
            $this->config = array_merge($params, $this->config);
        }

        // 验证参数
        if (empty($email)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::BAD_REQUEST),
                ApiCode::BAD_REQUEST
            );
        }

        // 生成验证码
        $code = mt_rand(100000, 999999);
        $codeExpire = $params['code_expire'] ?? ($this->config['code_expire'] ?? 600); // 验证码有效期，默认10分钟

        // 发送邮件
        try {

            // 生成邮件内容
            $gennerateParams = [
                'email' => $email,
                'code' => $code,
                'hostname' => '',
                'expire_minutes' => $codeExpire,
                'support_url' => '',
                'type' => 'email',
                'email_provider' => 'smtp',
                'verify_provider' => 'default',
                'table' => 'sys_email_code',
                'connection' => 'mysql',
                'storage_type' => 'mysql',
                'subject' => '您的验证码',
                'is_html' => true
            ];

            $params = array_merge($gennerateParams, $params);

            // 获取邮件HTML内容
            $content = VerificationEmailTemplate::getTemplate($params);
            $emailParams = [
                'scene' => 'login',
                'storage_type' => $params['storage_type'],
                // 可以覆盖MySQL存储的默认配置
                'storage_config' => [
                    'table' => $params['table'],
                    'connection' => $params['connection'], // 使用指定的数据库连接
                ],
                'is_html' => $params['is_html'],
                'subject' => $params['subject'],
                'content' => $content,
                'code_expire' => $codeExpire,
            ];
            // 通过参数指定存储类型
            $result = VerificationCodeService::send($email, array_merge($params, $emailParams), $params['type'], $params['verify_provider']);

            return $result;

        } catch (\Exception $e) {
            throw new SendException(
                $e->getMessage(),
                $e->getCode()
            );
        }
    }

    public function verify($receiver, $code, array $params = [])
    {

        // 生成邮件内容
        $gennerateParams = [
            'code' => $code,
            'type' => 'email',
            'email_provider' => 'smtp',
            'verify_provider' => 'default',
            'table' => 'sys_email_code',
            'connection' => 'mysql',
            'storage_type' => 'mysql'
        ];

        $params = array_merge($gennerateParams, $params);

        $emailParams = [
            'scene' => 'login',
            'storage_type' => $params['storage_type'],
            // 可以覆盖MySQL存储的默认配置
            'storage_config' => [
                'table' => $params['table'],
                'connection' => $params['connection'], // 使用指定的数据库连接
            ],
        ];
        try {
            // 通过参数指定存储类型
            $result = VerificationCodeService::verify($receiver, $code, array_merge($params, $emailParams), $params['type'], $params['verify_provider']);

            return $result;
        } catch (\Exception $e) {
            throw new SendException(
                $e->getMessage(),
                $e->getCode()
            );
        }
    }
}