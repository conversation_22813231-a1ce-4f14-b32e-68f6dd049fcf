<?php

namespace app\common\login;

class RegisterException extends \Exception
{

    /**
     * Constructor
     *
     * @param string $message Exception message
     * @param int $code Exception code
     * @param \Throwable|null $previous Previous exception
     */
    public function __construct($message = "Register failed", $code = 6000, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

}
