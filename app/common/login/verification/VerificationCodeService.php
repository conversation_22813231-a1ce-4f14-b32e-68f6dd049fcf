<?php

namespace app\common\login\verification;

use app\common\login\SendException;

/**
 * 验证码服务管理类
 */
class VerificationCodeService
{
    /**
     * 错误信息
     * @var string
     */
    protected static $error = '';

    /**
     * 错误码
     * @var int
     */
    protected static $code = 0;
    
    /**
     * 发送验证码
     * @param string $receiver 接收者（手机号或邮箱）
     * @param array $params 额外参数
     * @param string $type 验证码类型，如 'sms', 'email'
     * @param string $provider 服务提供商，如 'default', 'aliyun', 'tencent'
     * @return array|bool 发送结果，成功返回结果数组，失败返回false
     */
    public static function send($receiver, array $params = [], $type = null, $provider = null)
    {
        try {
            // 获取验证码服务实例
            $service = VerificationCodeFactory::getInstance($type, $provider, $params);

            try {
                // 执行发送
                $result = $service->send($receiver, $params);

                return $result;
            } catch (SendException $e) {
                self::$error = $e->getMessage();
                self::$code = $e->getCode();
                throw new SendException(
                    $e->getMessage(),
                    $e->getCode()
                );
            }
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            self::$code = $e->getCode();
            throw new SendException(
                $e->getMessage(),
                $e->getCode()
            );
        }
    }
    
    /**
     * 验证验证码
     * @param string $receiver 接收者（手机号或邮箱）
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param string $type 验证码类型，如 'sms', 'email'
     * @param string $provider 服务提供商，如 'default', 'aliyun', 'tencent'
     * @return bool 验证结果
     */
    public static function verify($receiver, $code, array $params = [], $type = null, $provider = null)
    {
        try {
            // 获取验证码服务实例
            $service = VerificationCodeFactory::getInstance($type, $provider, $params);

            try {
                // 执行验证
                return $service->verify($receiver, $code, $params);
            } catch (\Exception $e) {
                self::$error = $e->getMessage();
                self::$code = $e->getCode();
                return false;
            }
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            self::$code = $e->getCode();
            return false;
        }
    }
    
    /**
     * 获取错误信息
     * @return string
     */
    public static function getError()
    {
        return self::$error;
    }
    
    /**
     * 获取错误码
     * @return int
     */
    public static function getCode()
    {
        return self::$code;
    }
}