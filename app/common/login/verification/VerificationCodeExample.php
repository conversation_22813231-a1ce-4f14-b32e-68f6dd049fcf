<?php

namespace app\common\login\verification;

/**
 * 验证码服务示例类
 */
class VerificationCodeExample
{
    /**
     * 短信验证码发送示例
     * @return array
     */
    public static function sendSmsExample()
    {
        // 接收者手机号
        $mobile = '13800138000';
        
        // 基本用法
        $result = VerificationCodeService::send($mobile, [], 'sms');
        
        // 带场景的用法（如注册、登录、找回密码等）
        $result = VerificationCodeService::send($mobile, [
            'scene' => 'register',
            'template_param' => [
                'app' => '示例应用',
            ]
        ], 'sms');
        
        // 自定义验证码生成规则
        $result = VerificationCodeService::send($mobile, [
            'code_length' => 4,
            'code_type' => 'numeric', // numeric, alpha, alphanumeric
            'code_expire' => 600, // 10分钟
        ], 'sms');
        
        // 使用特定的短信提供商
        $result = VerificationCodeService::send($mobile, [], 'sms', 'aliyun');
        
        return $result;
    }
    
    /**
     * 邮箱验证码发送示例
     * @return array
     */
    public static function sendEmailExample()
    {
        // 接收者邮箱
        $email = '<EMAIL>';
        
        // 基本用法
        $result = VerificationCodeService::send($email, [], 'email');
        
        // 自定义邮件主题和内容
        $result = VerificationCodeService::send($email, [
            'subject' => '您的验证码',
            'content' => '您好，您的验证码是：{code}，有效期10分钟。',
            'code_expire' => 600, // 10分钟
        ], 'email');
        
        // 使用自定义模板
        $result = VerificationCodeService::send($email, [
            'template' => '<div style="text-align:center"><h2>验证码</h2><p>您的验证码是：<strong>{code}</strong>，有效期10分钟。</p></div>',
            'subject' => '您的验证码',
        ], 'email');
        
        // 使用特定的邮件提供商
        $result = VerificationCodeService::send($email, [], 'email', 'smtp');
        
        return $result;
    }
    
    /**
     * 验证码验证示例
     * @return bool
     */
    public static function verifyExample()
    {
        // 短信验证码验证
        $mobile = '13800138000';
        $code = '123456';
        $result = VerificationCodeService::verify($mobile, $code, ['scene' => 'register'], 'sms');
        
        // 邮箱验证码验证
        $email = '<EMAIL>';
        $code = '123456';
        $result = VerificationCodeService::verify($email, $code, [], 'email');
        
        return $result;
    }
    
    /**
     * 在登录场景中使用验证码示例
     * @return array
     */
    public static function loginExample()
    {
        // 手机号和验证码
        $mobile = '13800138000';
        $code = '123456';
        
        // 验证验证码
        $verifyResult = VerificationCodeService::verify($mobile, $code, ['scene' => 'login'], 'sms');
        
        if (!$verifyResult) {
            return ['code' => 1, 'msg' => '验证码错误或已过期'];
        }
        
        // 验证码正确，继续登录流程...
        
        return ['code' => 0, 'msg' => '登录成功'];
    }
    
    /**
     * 在注册场景中使用验证码示例
     * @return array
     */
    public static function registerExample()
    {
        // 邮箱和验证码
        $email = '<EMAIL>';
        $code = '123456';
        
        // 验证验证码
        $verifyResult = VerificationCodeService::verify($email, $code, ['scene' => 'register'], 'email');
        
        if (!$verifyResult) {
            return ['code' => 1, 'msg' => '验证码错误或已过期'];
        }
        
        // 验证码正确，继续注册流程...
        
        return ['code' => 0, 'msg' => '注册成功'];
    }
    
    /**
     * 使用钩子的示例
     */
    public static function hooksExample()
    {
        // 要使用钩子，需要继承验证码服务类并重写钩子方法
        
        // 例如，创建一个自定义的短信验证码类：
        /*
        class CustomSmsVerificationCode extends \app\common\login\verification\sms\DefaultSmsVerificationCode
        {
            protected function beforeSend($mobile, $code, array $params = [])
            {
                // 在发送前检查发送频率
                $lastSendTime = Cache::get('last_send_time_' . $mobile);
                if ($lastSendTime && time() - $lastSendTime < 60) {
                    // 一分钟内不允许重复发送
                    return false;
                }
                
                // 记录本次发送时间
                Cache::set('last_send_time_' . $mobile, time(), 3600);
                
                return true;
            }
            
            protected function afterSend($mobile, $code, array $params = [], $result = true)
            {
                // 记录发送日志
                Log::info('验证码发送', [
                    'mobile' => $mobile,
                    'code' => $code,
                    'result' => $result,
                    'time' => date('Y-m-d H:i:s'),
                ]);
            }
            
            protected function afterVerify($mobile, $code, array $params = [], $result = false)
            {
                // 记录验证日志
                Log::info('验证码验证', [
                    'mobile' => $mobile,
                    'code' => $code,
                    'result' => $result,
                    'time' => date('Y-m-d H:i:s'),
                ]);
                
                // 如果验证失败，记录失败次数
                if (!$result) {
                    $failCount = Cache::get('verify_fail_count_' . $mobile, 0);
                    Cache::set('verify_fail_count_' . $mobile, $failCount + 1, 3600);
                    
                    // 如果失败次数过多，可以采取一些措施，如锁定账号
                    if ($failCount + 1 >= 5) {
                        // 锁定账号逻辑...
                    }
                }
            }
        }
        */
    }
}