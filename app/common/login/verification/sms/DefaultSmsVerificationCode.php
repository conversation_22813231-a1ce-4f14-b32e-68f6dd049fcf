<?php

namespace app\common\login\verification\sms;

use app\common\ApiCode;
use app\common\login\SendException;
use app\common\login\verification\AbstractVerificationCode;
use app\common\message\MessageService;

/**
 * 默认短信验证码实现类
 */
class DefaultSmsVerificationCode extends AbstractVerificationCode
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'default';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'sms';
    
    /**
     * 发送验证码
     * @param string $mobile 手机号
     * @param array $params 额外参数
     * @return array 发送结果
     */
    public function send($mobile, array $params = [])
    {
        // 合并配置
        if (!empty($params)) {
            $this->config = array_merge($this->config, $params);
        }

        // 生成验证码
        $code = $this->config['code'] ?? $this->generateCode($params);

        // 发送前钩子
        if (!$this->beforeSend($mobile, $code, $params)) {
            //验证码发送被拦截
            throw new SendException(
                ApiCode::getMessage(ApiCode::CODE_ABANDONED),
                ApiCode::CODE_ABANDONED
            );
        }
        
        // 存储验证码到缓存
        $storeResult = $this->storeCode($mobile, $code, $params);

        if ($storeResult === false) {
            //验证码发送失败
            throw new SendException(
                ApiCode::getMessage(ApiCode::CODE_SEND_FAILED),
                ApiCode::CODE_SEND_FAILED
            );
        }
        
        // 准备短信内容
        $templateParam = ['code' => $code];
        
        // 如果有自定义模板参数，则合并
        if (isset($params['template_param']) && is_array($params['template_param'])) {
            $templateParam = array_merge($templateParam, $params['template_param']);
        }
        
        // 发送短信
        try {
            // 获取短信提供商
            $smsProvider = $params['sms_provider'] ?? null;
            
            // 发送短信
            $sendResult = MessageService::sendSms($mobile, $templateParam, $params, $smsProvider);

            // 处理发送结果
            $result = [];
            if ($sendResult && isset($sendResult['success']) && $sendResult['success'] === true) {
            } else {
                $errorMsg = isset($sendResult['message']) ? $sendResult['message'] : '验证码发送失败';
                throw new SendException(
                    $errorMsg,
                    ApiCode::CODE_SEND_FAILED
                );
            }
            
            // 发送后钩子
            $this->afterSend($mobile, $code, $params, $sendResult['success'] === true);
            
            return $sendResult;
        } catch (\Exception $e) {

            // 发送后钩子
            $this->afterSend($mobile, $code, $params, false);

            throw new SendException(
                $e->getMessage(),
                ApiCode::CODE_SEND_FAILED
            );
        }
    }
    
    /**
     * 发送前钩子
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 是否继续发送
     */
    protected function beforeSend($mobile, $code, array $params = [])
    {
        // 检查手机号格式
        if (empty($mobile)) {
            return false;
        }
        
        // 可以在这里添加更多的验证逻辑，例如手机号格式验证、发送频率限制等
        
        return true;
    }
    
    /**
     * 发送后钩子
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param bool $result 发送结果
     * @return void
     */
    protected function afterSend($mobile, $code, array $params = [], $result = true)
    {
        // 可以在这里添加发送后的处理逻辑，例如记录发送日志等
    }
    
    /**
     * 验证前钩子
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 是否继续验证
     */
    protected function beforeVerify($mobile, $code, array $params = [])
    {
        // 检查手机号和验证码
        if (empty($mobile) || empty($code)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证后钩子
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param bool $result 验证结果
     * @return void
     */
    protected function afterVerify($mobile, $code, array $params = [], $result = false)
    {
        // 可以在这里添加验证后的处理逻辑，例如记录验证日志等
    }
}