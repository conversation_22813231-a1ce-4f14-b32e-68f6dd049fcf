<?php

namespace app\common\login\verification;

use app\common\login\verification\storage\VerificationCodeStorageFactory;

/**
 * 验证码抽象基类
 */
abstract class AbstractVerificationCode implements VerificationCodeInterface
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = '';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = '';
    
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];
    
    /**
     * 存储实例
     * @var \app\common\login\verification\storage\VerificationCodeStorageInterface
     */
    protected $storage = null;
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
        
        // 初始化存储实例
        $storageType = $config['storage_type'] ?? config('verification_code.default_storage', 'default');
        $storageConfig = $config['storage_config'] ?? config('verification_code.storage.' . $storageType, []);
        $this->storage = VerificationCodeStorageFactory::getInstance($storageType, $storageConfig);
    }
    
    /**
     * 获取验证码服务名称
     * @return string
     */
    public function getServiceName()
    {
        return $this->serviceName;
    }
    
    /**
     * 获取验证码服务提供商
     * @return string
     */
    public function getServiceProvider()
    {
        return $this->provider;
    }
    
    /**
     * 生成验证码
     * @param array $params 额外参数
     * @return string 生成的验证码
     */
    protected function generateCode(array $params = [])
    {
        $length = $params['code_length'] ?? 6;
        $type = $params['code_type'] ?? 'numeric';
        
        switch ($type) {
            case 'numeric':
                return str_pad(mt_rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
            case 'alpha':
                $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                $code = '';
                for ($i = 0; $i < $length; $i++) {
                    $code .= $characters[mt_rand(0, strlen($characters) - 1)];
                }
                return $code;
            case 'alphanumeric':
                $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                $code = '';
                for ($i = 0; $i < $length; $i++) {
                    $code .= $characters[mt_rand(0, strlen($characters) - 1)];
                }
                return $code;
            default:
                return str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
        }
    }
    
    /**
     * 存储验证码
     * @param string $receiver 接收者
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 存储结果
     */
    protected function storeCode($receiver, $code, array $params = [])
    {
        $params['receiver'] = $receiver;
        $cacheKey = $this->getCacheKey($receiver, $params);
        $codeExpire = $params['code_expire'] ?? $this->config['code_expire'] ?? config('verification_code.code_expire', 300); // 验证码有效期，默认5分钟
        
        // 使用存储实例存储验证码
        return $this->storage->store($cacheKey, $code, $codeExpire, $params);
    }
    
    /**
     * 获取缓存中的验证码
     * @param string $receiver 接收者
     * @param array $params 额外参数
     * @return string|null 验证码
     */
    protected function getStoredCode($receiver, array $params = [])
    {
        $params['receiver'] = $receiver;
        $cacheKey = $this->getCacheKey($receiver, $params);
        return $this->storage->get($cacheKey, $params);
    }
    
    /**
     * 删除缓存中的验证码
     * @param string $receiver 接收者
     * @param array $params 额外参数
     * @return bool 删除结果
     */
    protected function deleteStoredCode($receiver, array $params = [])
    {
        $params['receiver'] = $receiver;
        $cacheKey = $this->getCacheKey($receiver, $params);
        return $this->storage->delete($cacheKey, $params);
    }
    
    /**
     * 获取缓存键名
     * @param string $receiver 接收者
     * @param array $params 额外参数
     * @return string 缓存键名
     */
    protected function getCacheKey($receiver, array $params = [])
    {
        $scene = $params['scene'] ?? 'default';
        return $this->serviceName . '_' . $scene . '_code_' . $receiver;
    }
    
    /**
     * 发送前钩子
     * @param string $receiver 接收者
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 是否继续发送
     */
    protected function beforeSend($receiver, $code, array $params = [])
    {
        return true;
    }
    
    /**
     * 发送后钩子
     * @param string $receiver 接收者
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param bool $result 发送结果
     * @return void
     */
    protected function afterSend($receiver, $code, array $params = [], $result = true)
    {
        // 子类可以重写此方法
    }
    
    /**
     * 验证前钩子
     * @param string $receiver 接收者
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 是否继续验证
     */
    protected function beforeVerify($receiver, $code, array $params = [])
    {
        return true;
    }
    
    /**
     * 验证后钩子
     * @param string $receiver 接收者
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param bool $result 验证结果
     * @return void
     */
    protected function afterVerify($receiver, $code, array $params = [], $result = false)
    {
        // 子类可以重写此方法
    }
    
    /**
     * 验证验证码
     * @param string $receiver 接收者
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 验证结果
     */
    public function verify($receiver, $code, array $params = [])
    {
        // 验证前钩子
        if (!$this->beforeVerify($receiver, $code, $params)) {
            return false;
        }
        
        $params['receiver'] = $receiver;
        $cacheKey = $this->getCacheKey($receiver, $params);
        
        // 检查验证尝试次数是否超过限制
        $maxAttempts = $params['max_attempts'] ?? $this->config['max_attempts'] ?? config('verification_code.max_attempts', 5);
        $attempts = $this->storage->getAttempts($cacheKey, $params);
        
        if ($attempts >= $maxAttempts) {
            // 超过最大尝试次数，删除验证码
            $this->deleteStoredCode($receiver, $params);
            // 验证后钩子
            $this->afterVerify($receiver, $code, $params, false);
            return false;
        }
        
        // 获取存储的验证码
        $storedCode = $this->getStoredCode($receiver, $params);

        // 验证码为空或不匹配
        $result = !empty($storedCode) && $storedCode === $code;
        
        if ($result) {
            // 验证成功，删除验证码和尝试次数记录
            $this->deleteStoredCode($receiver, $params);
            $this->storage->resetAttempts($cacheKey, $params);
        } else {
            // 验证失败，增加尝试次数
            $this->storage->increaseAttempts($cacheKey, $params);
        }
        
        // 验证后钩子
        $this->afterVerify($receiver, $code, $params, $result);
        
        return $result;
    }
}