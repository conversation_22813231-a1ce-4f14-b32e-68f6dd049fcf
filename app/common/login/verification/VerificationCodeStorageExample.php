<?php

namespace app\common\login\verification;

/**
 * 验证码存储示例类
 */
class VerificationCodeStorageExample
{
    /**
     * 使用默认存储方式发送验证码示例
     * @return array
     */
    public static function sendWithDefaultStorage()
    {
        // 接收者手机号
        $mobile = '13800138000';
        
        // 使用系统默认存储方式（由配置文件verification_code.php中的default_storage决定）
        $result = VerificationCodeService::send($mobile, [], 'sms');
        
        return $result;
    }
    
    /**
     * 使用Redis存储方式发送验证码示例
     * @return array
     */
    public static function sendWithRedisStorage()
    {
        // 接收者手机号
        $mobile = '13800138000';
        
        // 方法1：通过参数指定存储类型
        $result = VerificationCodeService::send($mobile, [
            'storage_type' => 'redis',
            // 可以覆盖Redis存储的默认配置
            'storage_config' => [
                'store' => 'redis',
                'prefix' => 'custom_prefix:',
            ]
        ], 'sms');
        
        return $result;
    }
    
    /**
     * 使用MySQL存储方式发送验证码示例
     * @return array
     */
    public static function sendWithMySQLStorage()
    {
        // 接收者邮箱
        $email = '<EMAIL>';
        
        // 通过参数指定存储类型
        $result = VerificationCodeService::send($email, [
            'storage_type' => 'mysql',
            // 可以覆盖MySQL存储的默认配置
            'storage_config' => [
                'table' => 'custom_verification_code',
                'connection' => 'mysql', // 使用指定的数据库连接
            ],
            'subject' => '您的验证码',
            'content' => '您好，您的验证码是：{code}，有效期10分钟。',
        ], 'email');
        
        return $result;
    }
    
    /**
     * 全局修改默认存储方式示例
     */
    public static function changeDefaultStorageExample()
    {
        // 方法1：修改配置文件 config/verification_code.php 中的 'default_storage' 值
        // 'default_storage' => 'redis',
        
        // 方法2：通过环境变量修改（需要在.env文件中设置）
        // VERIFICATION_CODE_STORAGE=redis
        
        // 方法3：动态修改配置（仅对当前请求有效）
        config(['verification_code.default_storage' => 'redis']);
        
        // 之后的所有验证码操作都将使用Redis存储
        $mobile = '13800138000';
        $result = VerificationCodeService::send($mobile, [], 'sms');
        
        return $result;
    }
    
    /**
     * 验证使用不同存储方式的验证码
     * @return bool
     */
    public static function verifyWithDifferentStorage()
    {
        // 验证使用Redis存储的验证码
        $mobile = '13800138000';
        $code = '123456';
        $result = VerificationCodeService::verify($mobile, $code, [
            'storage_type' => 'redis'
        ], 'sms');
        
        // 验证使用MySQL存储的验证码
        $email = '<EMAIL>';
        $code = '654321';
        $result = VerificationCodeService::verify($email, $code, [
            'storage_type' => 'mysql'
        ], 'email');
        
        return $result;
    }
    
    /**
     * 自定义存储配置示例
     */
    public static function customStorageConfigExample()
    {
        // 自定义Redis配置
        $redisConfig = [
            'storage_type' => 'redis',
            'storage_config' => [
                'store' => 'redis',
                'prefix' => 'app_verification:',
            ]
        ];
        
        // 自定义MySQL配置
        $mysqlConfig = [
            'storage_type' => 'mysql',
            'storage_config' => [
                'table' => 'app_verification_code',
                'connection' => 'mysql',
            ]
        ];
        
        // 根据不同场景使用不同的存储配置
        $mobile = '13800138000';
        $scene = 'login';
        
        // 登录场景使用Redis（速度快）
        if ($scene === 'login') {
            $params = array_merge(['scene' => $scene], $redisConfig);
        } 
        // 注册场景使用MySQL（持久化）
        else if ($scene === 'register') {
            $params = array_merge(['scene' => $scene], $mysqlConfig);
        }
        // 其他场景使用默认存储
        else {
            $params = ['scene' => $scene];
        }
        
        $result = VerificationCodeService::send($mobile, $params, 'sms');
        
        return $result;
    }
}