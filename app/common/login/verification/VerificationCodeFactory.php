<?php

namespace app\common\login\verification;

/**
 * 验证码服务工厂类
 */
class VerificationCodeFactory
{
    /**
     * 获取验证码服务实例
     * @param string $type 验证码类型，如 'sms', 'email'
     * @param string $provider 服务提供商，如 'default', 'aliyun', 'tencent'
     * @param array $config 配置信息
     * @return VerificationCodeInterface 验证码服务实例
     */
    public static function getInstance($type = null, $provider = null, array $config = [])
    {
        // 默认类型
        if (empty($type)) {
            $type = config('message.default_type', 'sms');
        }
        
        // 默认提供商
        if (empty($provider)) {
            $provider = config('message.' . $type . '.default_provider', 'default');
        }
        
        // 类名格式：Default{Type}VerificationCode 或 {Provider}{Type}VerificationCode
        $className = '';
        if ($provider === 'default') {
            $className = '\\app\\common\\login\\verification\\' . strtolower($type) . '\\Default' . ucfirst($type) . 'VerificationCode';
        } else {
            $className = '\\app\\common\\login\\verification\\' . strtolower($type) . '\\' . ucfirst($provider) . ucfirst($type) . 'VerificationCode';
        }
        
        // 如果类不存在，则使用默认类
        if (!class_exists($className)) {
            $className = '\\app\\common\\login\\verification\\' . strtolower($type) . '\\Default' . ucfirst($type) . 'VerificationCode';
        }
        
        // 如果默认类也不存在，则抛出异常
        if (!class_exists($className)) {
            throw new \Exception("Verification code service not found: {$type} - {$provider}");
        }
        
        // 合并配置
        $defaultConfig = config('message.' . $type . '.providers.' . $provider, []);
        $mergedConfig = array_merge($defaultConfig, $config);
        
        // 创建实例
        return new $className($mergedConfig);
    }
}