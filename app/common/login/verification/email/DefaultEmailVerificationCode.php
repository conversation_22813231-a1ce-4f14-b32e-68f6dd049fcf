<?php

namespace app\common\login\verification\email;

use app\common\ApiCode;
use app\common\login\SendException;
use app\common\login\verification\AbstractVerificationCode;
use app\common\message\MessageService;

/**
 * 默认邮箱验证码实现类
 */
class DefaultEmailVerificationCode extends AbstractVerificationCode
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'default';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'email';
    
    /**
     * 发送验证码
     * @param string $email 邮箱地址
     * @param array $params 额外参数
     * @return array 发送结果
     */
    public function send($email, array $params = [])
    {
        // 合并配置
        if (!empty($params)) {
            $this->config = array_merge($this->config, $params);
        }

        // 生成验证码
        $code = $this->config['code'] ?? $this->generateCode($params);
        
        // 发送前钩子
        if (!$this->beforeSend($email, $code, $params)) {
            //验证码发送被拦截
            throw new SendException(
                ApiCode::getMessage(ApiCode::CODE_ABANDONED),
                ApiCode::CODE_ABANDONED
            );
        }
        
        // 准备邮件内容
        $scene = $params['scene'] ?? 'default';
        $subject = $params['subject'] ?? '验证码';
        $content = $params['content'] ?? "您的验证码是：{$code}，有效期" . ($this->config['code_expire'] ?? 300) / 60 . "分钟。";
        $params['scene'] = $scene;
        $params['subject'] = $subject;
        $is_html = $params['is_html'] ?? true;
        $params['is_html'] = $is_html;

        // 存储验证码到缓存
        $storeResult = $this->storeCode($email, $code, $params);

        if ($storeResult === false) {
            //验证码发送失败
            throw new SendException(
                ApiCode::getMessage(ApiCode::CODE_SEND_FAILED),
                ApiCode::CODE_SEND_FAILED
            );
        }

        // 如果有自定义模板，则使用自定义模板
        if (isset($params['template']) && !empty($params['template'])) {
            $template = $params['template'];
            $content = str_replace('{code}', $code, $template);
        }
        
        // 发送邮件
        try {
            // 获取邮件提供商
            $emailProvider = $params['email_provider'] ?? null;

            // 发送邮件
            $sendResult = MessageService::sendEmail($email, $content, $params, $emailProvider);

            // 处理发送结果
            if ($sendResult && isset($sendResult['success']) && $sendResult['success'] === true) {
            } else {
                $errorMsg = isset($sendResult['message']) ? $sendResult['message'] : '验证码发送失败';
                throw new SendException(
                    $errorMsg,
                    ApiCode::CODE_SEND_FAILED
                );
            }
            
            // 发送后钩子
            $this->afterSend($email, $code, $params, $sendResult['success'] === true);

            return $sendResult;
        } catch (\Exception $e) {

            // 发送后钩子
            $this->afterSend($email, $code, $params, false);

            throw new SendException(
                $e->getMessage(),
                ApiCode::CODE_SEND_FAILED
            );
            
        }
    }
    
    /**
     * 发送前钩子
     * @param string $email 邮箱地址
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 是否继续发送
     */
    protected function beforeSend($email, $code, array $params = [])
    {
        // 检查邮箱格式
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }
        
        // 可以在这里添加更多的验证逻辑，例如发送频率限制等
        
        return true;
    }
    
    /**
     * 发送后钩子
     * @param string $email 邮箱地址
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param bool $result 发送结果
     * @return void
     */
    protected function afterSend($email, $code, array $params = [], $result = true)
    {
        // 可以在这里添加发送后的处理逻辑，例如记录发送日志等
    }
    
    /**
     * 验证前钩子
     * @param string $email 邮箱地址
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 是否继续验证
     */
    protected function beforeVerify($email, $code, array $params = [])
    {
        // 检查邮箱和验证码
        if (empty($email) || empty($code)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证后钩子
     * @param string $email 邮箱地址
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param bool $result 验证结果
     * @return void
     */
    protected function afterVerify($email, $code, array $params = [], $result = false)
    {
        // 可以在这里添加验证后的处理逻辑，例如记录验证日志等
    }
}