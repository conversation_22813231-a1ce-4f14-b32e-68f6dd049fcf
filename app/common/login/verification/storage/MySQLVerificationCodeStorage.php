<?php

namespace app\common\login\verification\storage;

use think\facade\Db;

/**
 * MySQL验证码存储实现类
 */
class MySQLVerificationCodeStorage implements VerificationCodeStorageInterface
{
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];
    
    /**
     * 表名
     * @var string
     */
    protected $table = 'verification_code';
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
        
        // 设置表名
        if (isset($this->config['table']) && !empty($this->config['table'])) {
            $this->table = $this->config['table'];
        }
        
    }

    /**
     * 存储验证码
     * @param string $key 缓存键名
     * @param string $code 验证码
     * @param int $expire 过期时间（秒）
     * @return bool 存储结果
     */
    public function store($key, $code, $expire = 300, $params = [])
    {
        $now = time();
        $expireTime = $now + $expire;

        try {
            // 先尝试删除已存在的记录
            Db::table($this->table)->where('key', $params['receiver'])->where('event', $params['scene'])->delete();

            // 插入新记录，并重置尝试次数为0
            $result = Db::table($this->table)->insert([
                'key' => $params['receiver'],
                'event' => $params['scene'],
                'code' => $code,
                'expiretime' => $expireTime,
                'createtime' => $now,
                'attempts' => 0
            ]);

            return $result !== false;
        } catch (\Exception $e) {
            // 存储失败，记录日志
            if (function_exists('log_error')) {
                log_error('存储验证码失败：' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * 获取验证码
     * @param string $key 缓存键名
     * @return string|null 验证码
     */
    public function get($key, $params = [])
    {
        try {
            $now = time();
            $record = Db::table($this->table)
                ->where('key', $params['receiver'])
                ->where('event', $params['scene'])
                ->where('expiretime', '>', $now)
                ->find();

            return $record ? $record['code'] : null;
        } catch (\Exception $e) {
            // 获取失败，记录日志
            if (function_exists('log_error')) {
                log_error('获取验证码失败：' . $e->getMessage());
            }
            return null;
        }
    }
    
    /**
     * 删除验证码
     * @param string $key 缓存键名
     * @return bool 删除结果
     */
    public function delete($key, $params = [])
    {
        try {
            $result = Db::table($this->table)
                ->where('key', $params['receiver'])
                ->where('event', $params['scene'])
                ->delete();
            return $result !== false;
        } catch (\Exception $e) {
            // 删除失败，记录日志
            if (function_exists('log_error')) {
                log_error('删除验证码失败：' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * 增加验证尝试次数
     * @param string $key 缓存键名
     * @param array $params 额外参数
     * @return int 当前尝试次数
     */
    public function increaseAttempts($key, $params = [])
    {
        try {
            // 获取当前记录
            $record = Db::table($this->table)
                ->where('key', $params['receiver'])
                ->where('event', $params['scene'])
                ->find();
            
            if (!$record) {
                return 0; // 记录不存在
            }
            
            // 增加尝试次数
            $attempts = $record['attempts'] + 1;
            
            // 更新尝试次数
            Db::table($this->table)
                ->where('key', $params['receiver'])
                ->where('event', $params['scene'])
                ->update(['attempts' => $attempts]);
            
            return $attempts;
        } catch (\Exception $e) {
            // 增加尝试次数失败，记录日志
            if (function_exists('log_error')) {
                log_error('增加验证尝试次数失败：' . $e->getMessage());
            }
            return 0;
        }
    }
    
    /**
     * 获取验证尝试次数
     * @param string $key 缓存键名
     * @param array $params 额外参数
     * @return int 当前尝试次数
     */
    public function getAttempts($key, $params = [])
    {
        try {
            $record = Db::table($this->table)
                ->where('key', $params['receiver'])
                ->where('event', $params['scene'])
                ->find();
            
            return $record ? (int)$record['attempts'] : 0;
        } catch (\Exception $e) {
            // 获取尝试次数失败，记录日志
            if (function_exists('log_error')) {
                log_error('获取验证尝试次数失败：' . $e->getMessage());
            }
            return 0;
        }
    }
    
    /**
     * 重置验证尝试次数
     * @param string $key 缓存键名
     * @param array $params 额外参数
     * @return bool 重置结果
     */
    public function resetAttempts($key, $params = [])
    {
        try {
            $result = Db::table($this->table)
                ->where('key', $params['receiver'])
                ->where('event', $params['scene'])
                ->update(['attempts' => 0]);
            
            return $result !== false;
        } catch (\Exception $e) {
            // 重置尝试次数失败，记录日志
            if (function_exists('log_error')) {
                log_error('重置验证尝试次数失败：' . $e->getMessage());
            }
            return false;
        }
    }
}