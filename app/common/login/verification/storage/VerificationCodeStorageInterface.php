<?php

namespace app\common\login\verification\storage;

/**
 * 验证码存储接口
 */
interface VerificationCodeStorageInterface
{
    /**
     * 存储验证码
     * @param string $key 缓存键名
     * @param string $code 验证码
     * @param int $expire 过期时间（秒）
     * @return bool 存储结果
     */
    public function store($key, $code, $expire = 300, $params = []);
    
    /**
     * 获取验证码
     * @param string $key 缓存键名
     * @return string|null 验证码
     */
    public function get($key, $params = []);
    
    /**
     * 删除验证码
     * @param string $key 缓存键名
     * @return bool 删除结果
     */
    public function delete($key, $params = []);
    
    /**
     * 增加验证尝试次数
     * @param string $key 缓存键名
     * @param array $params 额外参数
     * @return int 当前尝试次数
     */
    public function increaseAttempts($key, $params = []);
    
    /**
     * 获取验证尝试次数
     * @param string $key 缓存键名
     * @param array $params 额外参数
     * @return int 当前尝试次数
     */
    public function getAttempts($key, $params = []);
    
    /**
     * 重置验证尝试次数
     * @param string $key 缓存键名
     * @param array $params 额外参数
     * @return bool 重置结果
     */
    public function resetAttempts($key, $params = []);
}