<?php

namespace app\common\login\verification\storage;

/**
 * 验证码存储工厂类
 */
class VerificationCodeStorageFactory
{
    /**
     * 获取验证码存储实例
     * @param string $type 存储类型，如 'default', 'redis', 'mysql'
     * @param array $config 配置信息
     * @return VerificationCodeStorageInterface 验证码存储实例
     */
    public static function getInstance($type = null, array $config = [])
    {
        // 默认存储类型
        if (empty($type)) {
            $type = config('verification_code.default_storage', 'default');
        }
        
        // 根据类型创建存储实例
        switch ($type) {
            case 'redis':
                return new RedisVerificationCodeStorage($config);
            case 'mysql':
                return new MySQLVerificationCodeStorage($config);
            case 'default':
            default:
                // 默认使用系统缓存
                return new DefaultVerificationCodeStorage($config);
        }
    }
}