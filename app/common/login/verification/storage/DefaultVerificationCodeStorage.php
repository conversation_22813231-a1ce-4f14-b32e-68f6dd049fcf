<?php

namespace app\common\login\verification\storage;

use think\facade\Cache;

/**
 * 默认验证码存储实现类（使用系统默认缓存）
 */
class DefaultVerificationCodeStorage implements VerificationCodeStorageInterface
{
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }
    
    /**
     * 存储验证码
     * @param string $key 缓存键名
     * @param string $code 验证码
     * @param int $expire 过期时间（秒）
     * @return bool 存储结果
     */
    public function store($key, $code, $expire = 300, $params = [])
    {
        // 存储验证码时重置尝试次数
        $this->resetAttempts($key, $params);
        return Cache::set($key, $code, $expire);
    }
    
    /**
     * 获取验证码
     * @param string $key 缓存键名
     * @return string|null 验证码
     */
    public function get($key, $params = [])
    {
        return Cache::get($key);
    }
    
    /**
     * 删除验证码
     * @param string $key 缓存键名
     * @return bool 删除结果
     */
    public function delete($key, $params = [])
    {
        // 删除验证码时也删除尝试次数记录
        $attemptsKey = $this->getAttemptsKey($key, $params);
        Cache::delete($attemptsKey);
        return Cache::delete($key);
    }
    
    /**
     * 增加验证尝试次数
     * @param string $key 缓存键名
     * @param array $params 额外参数
     * @return int 当前尝试次数
     */
    public function increaseAttempts($key, $params = [])
    {
        $attemptsKey = $this->getAttemptsKey($key, $params);
        $attempts = $this->getAttempts($key, $params);
        $attempts++;
        
        // 获取验证码的过期时间
        $codeExpire = $params['code_expire'] ?? $this->config['code_expire'] ?? 300;
        
        // 设置尝试次数的过期时间与验证码相同
        Cache::set($attemptsKey, $attempts, $codeExpire);
        
        return $attempts;
    }
    
    /**
     * 获取验证尝试次数
     * @param string $key 缓存键名
     * @param array $params 额外参数
     * @return int 当前尝试次数
     */
    public function getAttempts($key, $params = [])
    {
        $attemptsKey = $this->getAttemptsKey($key, $params);
        return (int)Cache::get($attemptsKey, 0);
    }
    
    /**
     * 重置验证尝试次数
     * @param string $key 缓存键名
     * @param array $params 额外参数
     * @return bool 重置结果
     */
    public function resetAttempts($key, $params = [])
    {
        $attemptsKey = $this->getAttemptsKey($key, $params);
        return Cache::delete($attemptsKey);
    }
    
    /**
     * 获取尝试次数的缓存键名
     * @param string $key 验证码缓存键名
     * @param array $params 额外参数
     * @return string 尝试次数缓存键名
     */
    protected function getAttemptsKey($key, $params = [])
    {
        return $key . '_attempts';
    }
}