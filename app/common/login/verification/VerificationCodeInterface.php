<?php

namespace app\common\login\verification;

/**
 * 验证码接口
 */
interface VerificationCodeInterface
{
    /**
     * 发送验证码
     * @param string $receiver 接收者（手机号或邮箱）
     * @param array $params 额外参数
     * @return array 发送结果
     */
    public function send($receiver, array $params = []);
    
    /**
     * 验证验证码
     * @param string $receiver 接收者（手机号或邮箱）
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return bool 验证结果
     */
    public function verify($receiver, $code, array $params = []);
    
    /**
     * 获取验证码服务名称
     * @return string
     */
    public function getServiceName();
    
    /**
     * 获取验证码服务提供商
     * @return string
     */
    public function getServiceProvider();
}