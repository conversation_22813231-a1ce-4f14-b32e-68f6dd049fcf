<?php

namespace app\common\login;

/**
 * 登录示例类
 * 展示如何使用不同的登录方式
 */
class LoginExample
{
    /**
     * 短信验证码登录示例
     * 
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return array 登录结果
     */
    public static function smsLogin($mobile, $code, array $params = [])
    {
        // 方式一：使用LoginService类（推荐）
        $result = LoginService::login($mobile, $code, $params, 'sms');
        
        if ($result === false) {
            // 登录失败，获取错误信息
            $error = LoginService::getError();
            return ['code' => 1, 'msg' => $error];
        }
        
        // 登录成功，返回结果（包含用户信息和token）
        return $result;
        
        // 方式二：直接使用LoginFactory获取登录实例
        /*
        $smsLogin = LoginFactory::getInstance('sms');
        $result = $smsLogin->login($mobile, $code, $params);
        return $result;
        */
    }
    
    /**
     * 邮箱验证码登录示例
     * 
     * @param string $email 邮箱地址
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return array 登录结果
     */
    public static function emailLogin($email, $code, array $params = [])
    {
        // 使用LoginService类
        $result = LoginService::login($email, $code, $params, 'email');
        
        if ($result === false) {
            return ['code' => 1, 'msg' => LoginService::getError()];
        }
        
        return $result;
    }
    
    /**
     * QQ第三方登录示例
     * 
     * @param string $openid QQ用户唯一标识
     * @param array $params QQ返回的用户信息等额外参数
     * @return array 登录结果
     */
    public static function qqLogin($openid, array $params = [])
    {
        // QQ登录时，credential参数通常不使用，保持接口一致性
        $credential = '';
        
        // 使用LoginService类
        $result = LoginService::login($openid, $credential, $params, 'qq');
        
        if ($result === false) {
            return ['code' => 1, 'msg' => LoginService::getError()];
        }
        
        return $result;
    }
    
    /**
     * 账号密码登录示例
     * 
     * @param string $username 用户名
     * @param string $password 密码
     * @param array $params 额外参数
     * @return array 登录结果
     */
    public static function passwordLogin($username, $password, array $params = [])
    {
        // 方式一：使用LoginService类（推荐）
        $result = LoginService::login($username, $password, $params, 'password');
        
        if ($result === false) {
            // 登录失败，获取错误信息
            $error = LoginService::getError();
            return ['code' => 1, 'msg' => $error];
        }
        
        // 登录成功，返回结果（包含用户信息和token）
        return $result;
        
        // 方式二：使用专用方法
        /*
        $result = LoginService::loginByPassword($username, $password, $params);
        
        if ($result === false) {
            return ['code' => 1, 'msg' => LoginService::getError()];
        }
        
        return $result;
        */
        
        // 方式三：直接使用LoginFactory获取登录实例
        /*
        $passwordLogin = LoginFactory::getInstance('password');
        $result = $passwordLogin->login($username, $password, $params);
        return $result;
        */
    }
    
    /**
     * 自定义登录服务提供商示例
     * 
     * @param string $identity 身份标识
     * @param string $credential 凭证
     * @param array $params 额外参数
     * @param string $type 登录类型
     * @param string $provider 服务提供商
     * @return array 登录结果
     */
    public static function customProviderLogin($identity, $credential, array $params = [], $type = 'sms', $provider = 'aliyun')
    {
        // 指定使用特定的服务提供商
        $result = LoginService::login($identity, $credential, $params, $type, $provider);
        
        if ($result === false) {
            return ['code' => 1, 'msg' => LoginService::getError()];
        }
        
        return $result;
    }
    
    /**
     * 登录并设置自定义令牌数据示例
     * 
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @return array 登录结果
     */
    public static function loginWithCustomTokenData($mobile, $code)
    {
        // 设置额外参数
        $params = [
            // 设置令牌过期时间（秒）
            'expire_time' => 86400, // 24小时
            
            // 是否生成刷新令牌
            'with_refresh_token' => true,
            
            // 自定义令牌数据
            'token_data' => [
                'role' => 'user',
                'permissions' => ['read', 'write'],
                'login_type' => 'sms'
            ]
        ];
        
        // 执行登录
        return self::smsLogin($mobile, $code, $params);
    }
    
    /**
     * 登录方式切换示例
     * 
     * @param string $loginType 登录类型
     * @param string $identity 身份标识
     * @param string $credential 凭证
     * @param array $params 额外参数
     * @return array 登录结果
     */
    public static function switchLoginType($loginType, $identity, $credential, array $params = [])
    {
        // 根据登录类型执行不同的登录方法
        switch ($loginType) {
            case 'sms':
                return self::smsLogin($identity, $credential, $params);
                
            case 'email':
                return self::emailLogin($identity, $credential, $params);
                
            case 'password':
                return self::passwordLogin($identity, $credential, $params);
                
            case 'qq':
                return self::qqLogin($identity, $params);
                
            default:
                return ['code' => 1, 'msg' => '不支持的登录类型'];
        }
    }
    
    /**
     * 使用示例
     */
    public static function example()
    {
        // 短信登录示例
        $mobile = '13800138000';
        $smsCode = '123456';
        $smsResult = self::smsLogin($mobile, $smsCode);
        
        // 邮箱登录示例
        $email = '<EMAIL>';
        $emailCode = '123456';
        $emailResult = self::emailLogin($email, $emailCode);
        
        // 密码登录示例
        $username = 'admin';
        $password = 'password123';
        $passwordResult = self::passwordLogin($username, $password);
        
        // QQ登录示例
        $qqOpenid = 'QQ_OPENID_EXAMPLE';
        $qqParams = [
            'nickname' => 'QQ用户',
            'avatar' => 'http://example.com/avatar.jpg',
            'gender' => '男'
        ];
        $qqResult = self::qqLogin($qqOpenid, $qqParams);
        
        // 自定义令牌数据示例
        $customTokenResult = self::loginWithCustomTokenData($mobile, $smsCode);
        
        // 登录方式切换示例
        $switchResult = self::switchLoginType('email', $email, $emailCode);
        
        return [
            'sms_login' => $smsResult,
            'email_login' => $emailResult,
            'password_login' => $passwordResult,
            'qq_login' => $qqResult,
            'custom_token' => $customTokenResult,
            'switch_login' => $switchResult
        ];
    }
}