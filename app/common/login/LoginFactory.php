<?php

namespace app\common\login;

use think\facade\Config;

/**
 * 登录服务工厂类
 */
class LoginFactory
{
    /**
     * 登录服务实例缓存
     * @var array
     */
    protected static $instances = [];
    
    /**
     * 获取登录服务实例
     * @param string $type 登录类型，如 'sms', 'email', 'password', 'qq', 'wechat'
     * @param string $provider 服务提供商，如 'default', 'aliyun', 'tencent'
     * @return LoginInterface
     * @throws \Exception 当找不到对应的登录服务实现时抛出异常
     */
    public static function getInstance($type = null, $provider = null)
    {
        // 如果未指定类型，则使用配置中的默认类型
        if (is_null($type)) {
            $type = Config::get('login.default_type', 'password');
        }
        
        // 如果未指定提供商，则使用配置中对应类型的默认提供商
        if (is_null($provider)) {
            $provider = Config::get("login.{$type}.default_provider", 'default');
        }
        
        // 生成缓存键
        $key = "{$type}:{$provider}";
        
        // 如果实例已存在，则直接返回
        if (isset(self::$instances[$key])) {
            return self::$instances[$key];
        }
        
        // 获取登录服务类
        $class = self::getLoginClass($type, $provider);
        
        // 获取配置
        $config = Config::get("login.{$type}.providers.{$provider}", []);

        // 创建实例
        $instance = new $class($config);
        
        // 缓存实例
        self::$instances[$key] = $instance;
        
        return $instance;
    }
    
    /**
     * 获取登录服务类名
     * @param string $type 登录类型
     * @param string $provider 服务提供商
     * @return string 完整类名
     * @throws \Exception 当找不到对应的登录服务类时抛出异常
     */
    protected static function getLoginClass($type, $provider)
    {
        // 尝试加载自定义类
        $customClass = Config::get("login.{$type}.providers.{$provider}.class", '');
        if ($customClass && class_exists($customClass)) {
            return $customClass;
        }
        
        // 构建默认类名
        $providerFormatted = ucfirst($provider);
        $typeFormatted = ucfirst($type);
        
        // 尝试加载内置类
        $class = "\\app\\common\\login\\{$type}\\{$providerFormatted}{$typeFormatted}Login";
        
        if (class_exists($class)) {
            return $class;
        }
        
        // 找不到对应的类，抛出异常
        throw new \Exception("Login service not found: {$type}/{$provider}");
    }
}