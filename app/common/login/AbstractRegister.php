<?php

namespace app\common\login;

use app\common\IdGenerator;

/**
 * 注册抽象基类
 */
abstract class AbstractRegister implements RegisterInterface
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = '';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = '';
    
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }
    
    /**
     * 获取注册服务名称
     * @return string
     */
    public function getServiceName()
    {
        return $this->serviceName;
    }
    
    /**
     * 获取注册服务提供商
     * @return string
     */
    public function getServiceProvider()
    {
        return $this->provider;
    }
    
    /**
     * 注册验证
     * @param string $identity 身份标识（用户名、手机号、邮箱等）
     * @param string $credential 凭证（密码、验证码等）
     * @param array $params 额外参数
     * @return array 注册结果
     */
    abstract public function register($identity, $credential, array $params = []);
    
    /**
     * 生成随机密码
     * @param int $length 密码长度
     * @return string
     */
    protected function generateRandomPassword($length = 8)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $password;
    }
    
    /**
     * 生成盐值
     * @param int $length 盐值长度
     * @return string
     */
    protected function generateSalt($password)
    {
        $salt = IdGenerator::generate(6);
        return $salt;
    }
    
    /**
     * 加密密码
     * @param string $password 原始密码
     * @param string $salt 盐值
     * @return string 加密后的密码
     */
    protected function encryptPassword($password, $salt = '')
    {
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        // 将版本标识符从 $2y$ 改为 $2a$（Java 兼容）
        $hashedPasswordForJava = str_replace('$2y$', '$2a$', $hashedPassword);
        return $hashedPasswordForJava;
    }
}