<?php

namespace app\common\login\util;

use app\common\IdGenerator;

class Password
{

    /**
     * 生成随机密码
     * @param int $length 密码长度
     * @return string
     */
    public static function generateRandomPassword($length = 8)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $password;
    }

    /**
     * 生成盐值
     * @param int $length 盐值长度
     * @return string
     */
    public static function generateSalt()
    {
        $salt = IdGenerator::generate(6);
        return $salt;
    }

    /**
     * 加密密码
     * @param string $password 原始密码
     * @param string $salt 盐值
     * @return string 加密后的密码
     */
    public static function encryptPassword($password, $salt = '')
    {
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        // 将版本标识符从 $2y$ 改为 $2a$（Java 兼容）
        $hashedPasswordForJava = str_replace('$2y$', '$2a$', $hashedPassword);
        return $hashedPasswordForJava;
    }

}
