<?php

namespace app\common\login\validator;

use app\common\ApiCode;
use app\common\login\LoginException;

/**
 * 密码验证类
 * 
 * 用于验证密码是否符合规范，包括长度限制、复杂度要求等
 */
class PasswordValidator
{
    /**
     * 密码最小长度
     * @var int
     */
    protected static $minLength = 8;
    
    /**
     * 密码最大长度
     * @var int
     */
    protected static $maxLength = 32;
    
    /**
     * 是否要求包含数字
     * @var bool
     */
    protected static $requireDigit = false;
    
    /**
     * 是否要求包含小写字母
     * @var bool
     */
    protected static $requireLowercase = false;
    
    /**
     * 是否要求包含大写字母
     * @var bool
     */
    protected static $requireUppercase = false;
    
    /**
     * 是否要求包含特殊字符
     * @var bool
     */
    protected static $requireSpecialChar = false;
    
    /**
     * 允许的特殊字符
     * @var string
     */
    protected static $specialChars = '!@#$%^&*()-_=+[]{}|;:,.<>?/~';
    
    /**
     * 验证密码
     * 
     * @param string $password 密码
     * @return bool 验证通过返回true
     * @throws LoginException 验证失败时抛出异常
     */
    public static function validate($password)
    {
        // 验证密码长度
        if (strlen($password) < self::$minLength) {
            throw new LoginException(
                '密码长度不能小于' . self::$minLength . '个字符',
                ApiCode::BAD_REQUEST
            );
        }
        
        if (strlen($password) > self::$maxLength) {
            throw new LoginException(
                '密码长度不能超过' . self::$maxLength . '个字符',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 验证是否包含数字
        if (self::$requireDigit && !preg_match('/\d/', $password)) {
            throw new LoginException(
                '密码必须包含至少一个数字',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 验证是否包含小写字母
        if (self::$requireLowercase && !preg_match('/[a-z]/', $password)) {
            throw new LoginException(
                '密码必须包含至少一个小写字母',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 验证是否包含大写字母
        if (self::$requireUppercase && !preg_match('/[A-Z]/', $password)) {
            throw new LoginException(
                '密码必须包含至少一个大写字母',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 验证是否包含特殊字符
        if (self::$requireSpecialChar && !preg_match('/[' . preg_quote(self::$specialChars, '/') . ']/', $password)) {
            throw new LoginException(
                '密码必须包含至少一个特殊字符',
                ApiCode::BAD_REQUEST
            );
        }
        
        return true;
    }
    
    /**
     * 设置密码最小长度
     * 
     * @param int $length 长度
     */
    public static function setMinLength($length)
    {
        self::$minLength = $length;
    }
    
    /**
     * 设置密码最大长度
     * 
     * @param int $length 长度
     */
    public static function setMaxLength($length)
    {
        self::$maxLength = $length;
    }
    
    /**
     * 设置是否要求包含数字
     * 
     * @param bool $require 是否要求
     */
    public static function setRequireDigit($require)
    {
        self::$requireDigit = $require;
    }
    
    /**
     * 设置是否要求包含小写字母
     * 
     * @param bool $require 是否要求
     */
    public static function setRequireLowercase($require)
    {
        self::$requireLowercase = $require;
    }
    
    /**
     * 设置是否要求包含大写字母
     * 
     * @param bool $require 是否要求
     */
    public static function setRequireUppercase($require)
    {
        self::$requireUppercase = $require;
    }
    
    /**
     * 设置是否要求包含特殊字符
     * 
     * @param bool $require 是否要求
     */
    public static function setRequireSpecialChar($require)
    {
        self::$requireSpecialChar = $require;
    }
    
    /**
     * 设置允许的特殊字符
     * 
     * @param string $chars 特殊字符
     */
    public static function setSpecialChars($chars)
    {
        self::$specialChars = $chars;
    }
}