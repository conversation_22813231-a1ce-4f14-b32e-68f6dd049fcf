<?php

namespace app\common\login\validator;

use app\common\ApiCode;
use app\common\login\LoginException;
use think\facade\Db;
use think\facade\Log;

/**
 * 用户名验证类
 * 
 * 用于验证用户名是否符合规范，包括长度限制、特殊字符过滤、敏感词检测等
 */
class UsernameValidator
{
    /**
     * 用户名最小长度
     * @var int
     */
    protected static $minLength = 3;
    
    /**
     * 用户名最大长度
     * @var int
     */
    protected static $maxLength = 20;
    
    /**
     * 禁用词列表
     * @var array
     */
    protected static $forbiddenWords = [
        'admin', 'administrator', 'root', 'system', 'superadmin',
        '管理员', '超级管理员', '系统', '官方', 'official',
        '测试', 'test', 'demo', '演示',
        '傻逼', '操你妈', '垃圾', '废物', 'fuck', 'shit',
        '习近平', '毛泽东', '江泽民', '胡锦涛', '温家宝',
        'hitler', 'nazi', 'isis', 'terrorist'
    ];
    
    /**
     * 验证用户名
     * 
     * @param string $username 用户名
     * @return bool|string 验证通过返回true，否则返回错误信息
     * @throws LoginException 验证失败时抛出异常
     */
    public static function validate($username)
    {
        // 验证用户名长度
        if (mb_strlen($username) < self::$minLength) {
            throw new LoginException(
                '用户名长度不能小于' . self::$minLength . '个字符',
                ApiCode::BAD_REQUEST
            );
        }
        
        if (mb_strlen($username) > self::$maxLength) {
            throw new LoginException(
                '用户名长度不能超过' . self::$maxLength . '个字符',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 验证用户名格式（只允许字母、数字、下划线和中文）
        if (!preg_match('/^[\w\x{4e00}-\x{9fa5}]+$/u', $username)) {
            throw new LoginException(
                '用户名只能包含字母、数字、下划线和中文',
                ApiCode::BAD_REQUEST
            );
        }

        // 加载数据库中的禁用词
        self::loadForbiddenWordsFromDatabase();
        
        // 验证用户名是否包含禁用词
        foreach (self::$forbiddenWords as $word) {
            if (stripos($username, $word) !== false) {
                throw new LoginException(
                    '用户名包含禁用词',
                    ApiCode::BAD_REQUEST
                );
            }
        }
        
        return true;
    }
    
    /**
     * 设置用户名最小长度
     * 
     * @param int $length 长度
     */
    public static function setMinLength($length)
    {
        self::$minLength = $length;
    }
    
    /**
     * 设置用户名最大长度
     * 
     * @param int $length 长度
     */
    public static function setMaxLength($length)
    {
        self::$maxLength = $length;
    }
    
    /**
     * 添加禁用词
     * 
     * @param string|array $words 禁用词或禁用词数组
     */
    public static function addForbiddenWords($words)
    {
        if (is_string($words)) {
            self::$forbiddenWords[] = $words;
        } elseif (is_array($words)) {
            self::$forbiddenWords = array_merge(self::$forbiddenWords, $words);
        }
    }
    
    /**
     * 设置禁用词列表
     * 
     * @param array $words 禁用词数组
     */
    public static function setForbiddenWords(array $words)
    {
        self::$forbiddenWords = $words;
    }
    
    /**
     * 获取禁用词列表
     * 
     * @return array 禁用词数组
     */
    public static function getForbiddenWords()
    {
        return self::$forbiddenWords;
    }
    
    /**
     * 从数据库加载禁用词
     * 
     * @param string $table 表名，默认为sensitive_words
     * @param string $field 字段名，默认为word
     * @param string $type 类型字段，默认为type
     * @param string $typeValue 类型值，默认为username
     * @return bool 是否成功加载
     */
    public static function loadForbiddenWordsFromDatabase($table = 'sys_sensitive_words', $field = 'word', $type = 'type', $typeValue = 'username')
    {
        try {
            // 从数据库加载禁用词
            $words = Db::table($table)
                ->where($type, $typeValue)
                ->column($field);
            
            if (!empty($words)) {
                // 合并到现有禁用词列表
                self::addForbiddenWords($words);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('加载用户名禁用词失败: ' . $e->getMessage());
            return false;
        }
    }
}