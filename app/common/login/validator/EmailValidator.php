<?php

namespace app\common\login\validator;

use app\common\ApiCode;
use app\common\login\LoginException;

/**
 * 邮箱验证类
 * 
 * 用于验证邮箱是否符合规范，包括格式验证、域名验证等
 */
class EmailValidator
{
    /**
     * 邮箱最大长度
     * @var int
     */
    protected static $maxLength = 100;
    
    /**
     * 是否验证邮箱域名
     * @var bool
     */
    protected static $validateDomain = true;
    
    /**
     * 禁用的邮箱域名列表
     * @var array
     */
    protected static $forbiddenDomains = [];
    
    /**
     * 验证邮箱
     * 
     * @param string $email 邮箱
     * @return bool 验证通过返回true
     * @throws LoginException 验证失败时抛出异常
     */
    public static function validate($email)
    {
        // 验证邮箱长度
        if (strlen($email) > self::$maxLength) {
            throw new LoginException(
                '邮箱长度不能超过' . self::$maxLength . '个字符',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 验证邮箱格式
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new LoginException(
                '邮箱格式不正确',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 提取域名部分
        $domain = substr(strrchr($email, "@"), 1);
        
        // 验证域名是否在禁用列表中
        if (in_array(strtolower($domain), array_map('strtolower', self::$forbiddenDomains))) {
            throw new LoginException(
                '该邮箱域名不允许使用',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 验证域名是否有效
        if (self::$validateDomain && !self::checkDomainMX($domain)) {
            throw new LoginException(
                '邮箱域名无效',
                ApiCode::BAD_REQUEST
            );
        }
        
        return true;
    }
    
    /**
     * 检查域名是否有MX记录
     * 
     * @param string $domain 域名
     * @return bool 有MX记录返回true
     */
    protected static function checkDomainMX($domain)
    {
        // 如果不验证域名，直接返回true
        if (!self::$validateDomain) {
            return true;
        }
        
        // 检查域名是否有MX记录
        return checkdnsrr($domain, 'MX');
    }
    
    /**
     * 设置邮箱最大长度
     * 
     * @param int $length 长度
     */
    public static function setMaxLength($length)
    {
        self::$maxLength = $length;
    }
    
    /**
     * 设置是否验证邮箱域名
     * 
     * @param bool $validate 是否验证
     */
    public static function setValidateDomain($validate)
    {
        self::$validateDomain = $validate;
    }
    
    /**
     * 设置禁用的邮箱域名列表
     * 
     * @param array $domains 域名列表
     */
    public static function setForbiddenDomains(array $domains)
    {
        self::$forbiddenDomains = $domains;
    }
    
    /**
     * 添加禁用的邮箱域名
     * 
     * @param string $domain 域名
     */
    public static function addForbiddenDomain($domain)
    {
        if (!in_array($domain, self::$forbiddenDomains)) {
            self::$forbiddenDomains[] = $domain;
        }
    }
}