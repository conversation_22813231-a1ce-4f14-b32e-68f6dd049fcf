<?php

namespace app\common\login\validator;

use app\common\ApiCode;
use app\common\login\LoginException;

/**
 * 手机号验证类
 * 
 * 用于验证手机号是否符合规范，包括长度验证、前缀验证等
 */
class PhoneValidator
{
    /**
     * 手机号长度
     * @var int
     */
    protected static $length = 11;
    
    /**
     * 是否仅验证中国手机号
     * @var bool
     */
    protected static $onlyChina = true;
    
    /**
     * 中国手机号前缀列表
     * @var array
     */
    protected static $chinaPrefixes = [
        '130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
        '145', '146', '147', '148', '149',
        '150', '151', '152', '153', '155', '156', '157', '158', '159',
        '166', '167', '170', '171', '172', '173', '175', '176', '177', '178',
        '180', '181', '182', '183', '184', '185', '186', '187', '188', '189',
        '190', '191', '192', '193', '195', '196', '197', '198', '199'
    ];
    
    /**
     * 验证手机号
     * 
     * @param string $phone 手机号
     * @return bool 验证通过返回true
     * @throws LoginException 验证失败时抛出异常
     */
    public static function validate($phone)
    {
        // 验证手机号是否为纯数字
        if (!ctype_digit($phone)) {
            throw new LoginException(
                '手机号只能包含数字',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 验证手机号长度
        if (strlen($phone) != self::$length) {
            throw new LoginException(
                '手机号长度必须为' . self::$length . '位',
                ApiCode::BAD_REQUEST
            );
        }
        
        // 如果仅验证中国手机号，则验证前缀
        if (self::$onlyChina) {
            $prefix = substr($phone, 0, 3);
            if (!in_array($prefix, self::$chinaPrefixes)) {
                throw new LoginException(
                    '手机号前缀无效',
                    ApiCode::BAD_REQUEST
                );
            }
        }
        
        return true;
    }
    
    /**
     * 设置手机号长度
     * 
     * @param int $length 长度
     */
    public static function setLength($length)
    {
        self::$length = $length;
    }
    
    /**
     * 设置是否仅验证中国手机号
     * 
     * @param bool $only 是否仅验证中国手机号
     */
    public static function setOnlyChina($only)
    {
        self::$onlyChina = $only;
    }
    
    /**
     * 设置中国手机号前缀列表
     * 
     * @param array $prefixes 前缀列表
     */
    public static function setChinaPrefixes(array $prefixes)
    {
        self::$chinaPrefixes = $prefixes;
    }
    
    /**
     * 添加中国手机号前缀
     * 
     * @param string $prefix 前缀
     */
    public static function addChinaPrefix($prefix)
    {
        if (!in_array($prefix, self::$chinaPrefixes)) {
            self::$chinaPrefixes[] = $prefix;
        }
    }
}