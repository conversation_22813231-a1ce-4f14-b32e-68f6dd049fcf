<?php

namespace app\common\login;

/**
 * 登录抽象基类
 */
abstract class AbstractLogin implements LoginInterface
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = '';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = '';
    
    /**
     * 配置信息
     * @var array
     */
    protected $config = [];
    
    /**
     * 构造函数
     * @param array $config 配置信息
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
    }
    
    /**
     * 获取登录服务名称
     * @return string
     */
    public function getServiceName()
    {
        return $this->serviceName;
    }
    
    /**
     * 获取登录服务提供商
     * @return string
     */
    public function getServiceProvider()
    {
        return $this->provider;
    }
    
    /**
     * 登录验证
     * @param string $identity 身份标识（用户名、手机号、邮箱、第三方ID等）
     * @param string $credential 凭证（密码、验证码、token等）
     * @param array $params 额外参数
     * @return array 登录结果
     */
    abstract public function login($identity, $credential, array $params = []);
}