<?php

namespace app\common\login\sms;

use app\common\ApiCode;
use app\common\IdGenerator;
use app\common\login\AbstractRegister;
use app\common\login\AbstractSend;
use app\common\login\LoginException;
use app\common\login\SendException;
use app\common\login\validator\EmailValidator;
use app\common\login\validator\PasswordValidator;
use app\common\login\validator\PhoneValidator;
use app\common\login\validator\UsernameValidator;
use app\common\login\verification\VerificationCodeService;
use think\facade\Db;

/**
 * 默认短信注册实现类
 */
class DefaultSmsRegister extends AbstractRegister
{
    use AbstractSend;
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'default';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'sms';
    
    /**
     * 执行短信注册
     * 
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return array 注册结果
     */
    public function register($mobile, $code, array $params = [])
    {
        if(is_array($params) && !is_null($params)) {
            $this->config = array_merge($params, $this->config);
        }
        
        // 验证参数
        if (empty($mobile)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::BAD_REQUEST),
                ApiCode::BAD_REQUEST
            );
        }
        
        if (empty($code)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::CODE_EMPTY),
                ApiCode::CODE_EMPTY
            );
        }

        // 验证邮箱是否符合规范（长度、复杂度等）
        try {
            // 引入手机验证器
            $validator = new PhoneValidator();
            $validator::validate($mobile);
        } catch (LoginException $e) {
            // 密码验证失败，直接抛出异常
            throw new LoginException(
                $e->getMessage(),
                $e->getCode()
            );
        }

        // 验证用户名是否符合规范（禁用词检测等）
        try {
            // 引入用户名验证器
            $validator = new PasswordValidator();
            $validator::validate($params['password'] ?? '');
        } catch (LoginException $e) {
            // 用户名验证失败，直接抛出异常
            throw new LoginException(
                $e->getMessage(),
                $e->getCode()
            );
        }

        if (!empty($params['username'])) {
            // 验证用户名是否符合规范（禁用词检测等）
            try {
                // 引入用户名验证器
                $validator = new UsernameValidator();
                $validator::validate($params['username'] ?? '');
            } catch (LoginException $e) {
                // 用户名验证失败，直接抛出异常
                throw new LoginException(
                    $e->getMessage(),
                    $e->getCode()
                );
            }
        }

        // 获取配置
        $table = $params['table'] ?? ($this->config['table'] ?? 'qi_users');
        $mobileField = $params['mobile_field'] ?? ($this->config['mobile_field'] ?? 'mobile');
        $passwordField = $params['password_field'] ?? ($this->config['password_field'] ?? 'password');
        $saltField = $params['salt_field'] ?? ($this->config['salt_field'] ?? 'salt');
        $statusField = $params['status_field'] ?? ($this->config['status_field'] ?? 'enabled');
        $activeStatus = $params['active_status'] ?? ($this->config['active_status'] ?? 1);
        $codeExpire = $params['code_expire'] ?? ($this->config['code_expire'] ?? 300); // 验证码有效期，默认5分钟

        // 验证码校验
//        $verify = $this->verify($mobile, $code, $params);
//
//        if (!$verify) {
//            //验证码错误或过期
//            throw new LoginException(
//                ApiCode::getMessage(ApiCode::CODE_ERROR),
//                ApiCode::CODE_ERROR
//            );
//        }

        // 检查手机号是否已存在
        $existUser = Db::table($table)
            ->where($mobileField, '=', $mobile)
            ->find();
            
        if ($existUser) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::MOBILE_EXIST),
                ApiCode::MOBILE_EXIST
            );
        }
        
        // 生成随机密码（如果没有提供）
        $password = $params['password'] ?? $this->generateRandomPassword();
        
        // 生成盐值
        $salt = $this->generateSalt($password);
        
        // 加密密码
        $encryptedPassword = $this->encryptPassword($password, $salt);
        
        // 准备用户数据
        $userData = [
            $mobileField => $mobile,
            $passwordField => $encryptedPassword,
            $saltField => $salt,
            $statusField => $activeStatus,
            'user_no' => IdGenerator::generateIdFromDb(),
            'createtime' => date('Y-h-d H:i:s', time()),
            'updatetime' => date('Y-h-d H:i:s', time()),
            'logintime' => date('Y-h-d H:i:s', time()),
        ];
        
        // 合并额外的用户数据
        if (isset($params['user_data']) && is_array($params['user_data'])) {
            $userData = array_merge($userData, $params['user_data']);
        }
        
        // 插入用户数据
        try {
            $userId = Db::table($table)->insertGetId($userData);
            
            if (!$userId) {
                throw new LoginException(
                    ApiCode::getMessage(ApiCode::AUTO_REGISTER_FAILED),
                    ApiCode::AUTO_REGISTER_FAILED
                );
            }
            
            // 查询完整的用户信息
            $user = Db::table($table)->where('id', '=', $userId)->find();
            
            // 返回注册结果
            return [
                'success' => true,
                'msg' => '注册成功',
                'user_id' => $userId,
                'user' => $user
            ];
        } catch (\Exception $e) {
            throw new LoginException(
                '注册失败：' . $e->getMessage(),
                ApiCode::AUTO_REGISTER_FAILED
            );
        }
    }
    
    /**
     * 发送验证码
     * @param string $mobile 手机号
     * @return array 发送结果
     */
    public function sendCode($mobile, $params = [])
    {
        // 生成验证码
        $code = mt_rand(100000, 999999);
        $codeExpire = $params['code_expire'] ?? ($this->config['code_expire'] ?? 600); // 验证码有效期，默认10分钟

        // 生成邮件内容
        $gennerateParams = [
            'mobile' => $mobile,
            'code' => $code,
            'expire_minutes' => $codeExpire,
            'type' => 'sms',
            'sms_provider' => 'aliyun',
            'verify_provider' => 'default',
            'table' => 'sys_sms_code',
            'connection' => 'mysql',
            'storage_type' => 'mysql',
        ];

        $params = array_merge($gennerateParams, $params);

        $mobileParams = [
            'scene' => 'register',
            'storage_type' => $params['storage_type'],
            // 可以覆盖Redis存储的默认配置
            'storage_config' => [
                'table' => $params['table'],
                'connection' => $params['connection'],
            ]
        ];
        
        // 发送短信
        try {
            // 这里可以调用短信发送服务
            // 例如：app\common\message\MessageService::sendSms($mobile, ['code' => $code]);

            // 方法1：通过参数指定存储类型
            $result = VerificationCodeService::send($mobile, array_merge($params, $mobileParams), $params['type'], $params['verify_provider']);
            
            return $result;
        } catch (\Exception $e) {
            throw new SendException(
                $e->getMessage(),
                $e->getCode()
            );
        }
    }

    public function verify($receiver, $code, array $params = [])
    {
        // 生成短信内容
        $gennerateParams = [
            'code' => $code,
            'type' => 'sms',
            'sms_provider' => 'aliyun',
            'verify_provider' => 'default',
            'table' => 'sys_sms_code',
            'connection' => 'mysql',
            'storage_type' => 'mysql',
        ];

        $params = array_merge($gennerateParams, $params);

        $mobileParams = [
            'scene' => 'register',
            'storage_type' => $params['storage_type'],
            // 可以覆盖Redis存储的默认配置
            'storage_config' => [
                'table' => $params['table'],
                'connection' => $params['connection'],
            ]
        ];

        try {
            // 通过参数指定存储类型
            $result = VerificationCodeService::verify($receiver, $code, array_merge($params, $mobileParams), $params['type'], $params['verify_provider']);

            return $result;
        } catch (\Exception $e) {
            throw new SendException(
                $e->getMessage(),
                $e->getCode()
            );
        }
    }
}