<?php

namespace app\common\login\sms;

use app\common\ApiCode;
use app\common\login\AbstractLogin;
use app\common\login\AbstractSend;
use app\common\login\LoginException;
use app\common\login\SendException;
use app\common\login\verification\VerificationCodeService;
use think\facade\Db;

/**
 * 默认短信登录实现
 */
class DefaultSmsLogin extends AbstractLogin
{
    use AbstractSend;
    /**
     * 服务提供商名称
     * @var string
     */
    protected $provider = 'default';
    
    /**
     * 服务名称
     * @var string
     */
    protected $serviceName = 'sms';
    
    /**
     * 登录验证
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param array $params 额外参数
     * @return array 登录结果
     */
    public function login($mobile, $code, array $params = [])
    {
        if(is_array($params) && !is_null($params)) {
            $this->config = array_merge($params, $this->config);
        }

        // 验证参数
        if (empty($mobile)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::BAD_REQUEST),
                ApiCode::BAD_REQUEST
            );
        }

        if (empty($code)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::CODE_EMPTY),
                ApiCode::CODE_EMPTY
            );
        }
        // 获取配置
        $table = $params['table'] ?? ($this->config['table'] ?? 'qi_users');
        $mobileField = $params['mobile_field'] ?? ($this->config['mobile_field'] ?? 'mobile');
        $statusField = $params['status_field'] ?? ($this->config['status_field'] ?? 'enabled');
        $activeStatus = $params['active_status'] ?? ($this->config['active_status'] ?? 1);
        $codeExpire = $params['code_expire'] ?? ($this->config['code_expire'] ?? 300); // 验证码有效期，默认5分钟
        $autoRegister = $params['auto_register'] ?? ($this->config['auto_register'] ?? false);

        // 验证码校验
        $verify = $this->verify($mobile, $code, $params);

        if (!$verify) {
            //验证码错误或过期
            throw new LoginException(
                ApiCode::getMessage(ApiCode::CODE_ERROR),
                ApiCode::CODE_ERROR
            );
        }
        
        // 查询用户信息
        $user = Db::table($table)
            ->where($mobileField, '=', $mobile)
            ->find();
        
        // 用户不存在，根据配置决定是否自动注册
        if (!$user) {

            if ($autoRegister) {
                // 自动注册用户
                $userId = $this->registerUser($mobile, $params);
                
                if ($userId) {
                    $user = Db::table($table)->where('id', '=', $userId)->find();
                } else {
                    //自动注册失败'
                    throw new LoginException(
                        ApiCode::getMessage(ApiCode::AUTO_REGISTER_FAILED),
                        ApiCode::AUTO_REGISTER_FAILED
                    );
                }
            } else {
                //用户不存在'
                throw new LoginException(
                    ApiCode::getMessage(ApiCode::USER_NOT_EXIST),
                    ApiCode::USER_NOT_EXIST
                );
            }
        }
        
        // 检查用户状态
        if (isset($user[$statusField]) && $user[$statusField] != $activeStatus) {
            //账号已禁用'
            throw new LoginException(
                ApiCode::getMessage(ApiCode::ACCOUNT_DISABLED),
                ApiCode::ACCOUNT_DISABLED
            );
        }
        
        // 登录成功，返回用户信息
        return [
            'code' => 0,
            'msg' => '登录成功',
            'user_id' => $user['id'],
            'user' => $user
        ];
    }
    
    /**
     * 注册新用户
     * @param string $mobile 手机号
     * @param array $params 额外参数
     * @return int|bool 成功返回用户ID，失败返回false
     */
    protected function registerUser($mobile, array $params = [])
    {
        // 获取配置
        $table = $params['table'] ?? ($this->config['table'] ?? 'qi_users');
        $mobileField = $params['mobile_field'] ?? ($this->config['mobile_field'] ?? 'mobile');
        $statusField = $params['status_field'] ?? ($this->config['status_field'] ?? 'enabled');
        $activeStatus = $params['active_status'] ?? ($this->config['active_status'] ?? 1);

        // 准备用户数据
        $userData = [
            $mobileField => $mobile,
            $statusField => $activeStatus,
            'createtime' => date('Y-h-d H:i:s', time())
        ];
        
        // 如果有昵称信息，添加到用户数据
        if (isset($params['nickname']) && isset($this->config['nickname_field'])) {
            $userData[$this->config['nickname_field']] = $params['nickname'];
        }
        
        // 添加用户
        try {
            $userId = Db::table($table)->insertGetId($userData);
            return $userId;
        } catch (\Exception $e) {
            // 记录错误日志
            return false;
        }
    }
    
    /**
     * 发送验证码
     * @param string $mobile 手机号
     * @return array 发送结果
     */
    public function sendCode($mobile, $params = [])
    {
        if(is_array($params) && !is_null($params)) {
            $this->config = array_merge($params, $this->config);
        }

        // 验证参数
        if (empty($mobile)) {
            throw new LoginException(
                ApiCode::getMessage(ApiCode::BAD_REQUEST),
                ApiCode::BAD_REQUEST
            );
        }

        // 生成验证码
        $code = mt_rand(100000, 999999);
        $codeExpire = $params['code_expire'] ?? ($this->config['code_expire'] ?? 600); // 验证码有效期，默认10分钟

        // 生成短信内容
        $gennerateParams = [
            'mobile' => $mobile,
            'code' => $code,
            'expire_minutes' => $codeExpire,
            'type' => 'sms',
            'sms_provider' => 'aliyun',
            'verify_provider' => 'default',
            'table' => 'sys_sms_code',
            'connection' => 'mysql',
            'storage_type' => 'mysql',
        ];

        $params = array_merge($gennerateParams, $params);

        $mobileParams = [
            'scene' => 'login',
            'storage_type' => $params['storage_type'],
            // 可以覆盖Redis存储的默认配置
            'storage_config' => [
                'table' => $params['table'],
                'connection' => $params['connection'],
            ]
        ];

        // 发送短信
        try {
            // 这里可以调用短信发送服务
            // 例如：app\common\message\MessageService::sendSms($mobile, ['code' => $code]);

            // 方法1：通过参数指定存储类型
            $result = VerificationCodeService::send($mobile, array_merge($params, $mobileParams), $params['type'], $params['verify_provider']);

            return $result;
        } catch (\Exception $e) {
            throw new SendException(
                $e->getMessage(),
                $e->getCode()
            );
        }
    }

    public function verify($receiver, $code, array $params = [])
    {
        // 生成短信内容
        $gennerateParams = [
            'code' => $code,
            'type' => 'sms',
            'sms_provider' => 'aliyun',
            'verify_provider' => 'default',
            'table' => 'sys_sms_code',
            'connection' => 'mysql',
            'storage_type' => 'mysql',
        ];

        $params = array_merge($gennerateParams, $params);

        $mobileParams = [
            'scene' => 'login',
            'storage_type' => $params['storage_type'],
            // 可以覆盖Redis存储的默认配置
            'storage_config' => [
                'table' => $params['table'],
                'connection' => $params['connection'],
            ]
        ];

        try {
            // 通过参数指定存储类型
            $result = VerificationCodeService::verify($receiver, $code, array_merge($params, $mobileParams), $params['type'], $params['verify_provider']);

            return $result;
        } catch (\Exception $e) {
            throw new SendException(
                $e->getMessage(),
                $e->getCode()
            );
        }
    }
}