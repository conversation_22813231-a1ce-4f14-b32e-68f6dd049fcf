<?php

namespace app\common\login;

use app\common\ApiCode;
use app\common\Auth;

/**
 * 登录服务管理类
 */
class LoginService
{
    /**
     * 错误信息
     * @var string
     */
    protected static $error = '';

    protected static $code = 0;
    
    /**
     * 执行登录
     * @param string $identity 身份标识（用户名、手机号、邮箱、第三方ID等）
     * @param string $credential 凭证（密码、验证码、token等）
     * @param array $params 额外参数
     *        - entity string 可选，实体类名称，用于将用户信息填充到指定的实体类中
     *        - with_refresh_token bool 可选，是否生成刷新令牌
     *        - expire_time int 可选，令牌过期时间（秒）
     *        - token_data array 可选，令牌自定义数据
     * @param string $type 登录类型，如 'sms', 'email', 'password', 'qq', 'wechat'
     * @param string $provider 服务提供商，如 'default', 'aliyun', 'tencent'
     * @return array|bool 登录结果，成功返回用户信息数组（包含entity对象，如果指定了实体类），失败返回false
     */
    public static function login($identity, $credential, array $params = [], $type = null, $provider = null)
    {
        try {
            // 获取登录服务实例
            $service = LoginFactory::getInstance($type, $provider);

            try {
                // 执行登录
                $result = $service->login($identity, $credential, $params);

                // 生成令牌
                $withRefreshToken = $params['with_refresh_token'] ?? true;
                $expireTime = $params['expire_time'] ?? 7200;
                $expireRefreshTime = $params['expire_refresh_time'] ?? 86400;
                $customData = $params['token_data'] ?? [];

                // 添加用户ID到自定义数据
                $customData['user_id'] = $result['user_id'];

                // 创建令牌
                $token = Auth::createToken($result['user_id'], $customData, $expireTime, $withRefreshToken, $expireRefreshTime);

                // 将令牌添加到结果中
                $result['token'] = $token;

                // 如果提供了实体类，则将用户信息填充到实体类中
                if (isset($params['entity']) && !empty($params['entity']) && class_exists($params['entity'])) {
                    $entityClass = $params['entity'];
                    $entity = new $entityClass();

                    // 将用户数据填充到实体对象中
                    foreach ($result['user'] as $key => $value) {
                        if (property_exists($entity, $key)) {
                            $entity->$key = $value;
                        }
                    }

                    // 将实体对象添加到结果中
                    $result['entity'] = $entity;
                }

                return $result;
            } catch (LoginException $e) {
                // 登录失败
                self::$code = $e->getCode();
                self::$error = $result['msg'] ?? $e->getMessage();
                return false;
            }

        } catch (\Exception $e) {
            self::$code = $e->getCode();
            self::$error = $e->getMessage();
            return false;
        }
    }
    
    /**
     * 使用短信验证码登录
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return array|bool 登录结果
     */
    public static function loginBySms($mobile, $code, array $params = [], $provider = null)
    {
        return self::login($mobile, $code, $params, 'sms', $provider);
    }
    
    /**
     * 使用邮箱验证码登录
     * @param string $email 邮箱
     * @param string $code 验证码
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return array|bool 登录结果
     */
    public static function loginByEmail($email, $code, array $params = [], $provider = null)
    {
        return self::login($email, $code, $params, 'email', $provider);
    }
    
    /**
     * 使用账号密码登录
     * @param string $username 用户名
     * @param string $password 密码
     * @param array $params 额外参数
     * @param string $provider 服务提供商
     * @return array|bool 登录结果
     */
    public static function loginByPassword($username, $password, array $params = [], $provider = null)
    {
        return self::login($username, $password, $params, 'password', $provider);
    }
    
    /**
     * 使用QQ登录
     * @param string $openid QQ用户唯一标识
     * @param array $params QQ返回的用户信息
     * @param string $provider 服务提供商
     * @return array|bool 登录结果
     */
    public static function loginByQQ($openid, array $params = [], $provider = null)
    {
        return self::login($openid, '', $params, 'qq', $provider);
    }
    
    /**
     * 使用微信登录
     * @param string $openid 微信用户唯一标识
     * @param array $params 微信返回的用户信息
     * @param string $provider 服务提供商
     * @return array|bool 登录结果
     */
    public static function loginByWechat($openid, array $params = [], $provider = null)
    {
        return self::login($openid, '', $params, 'wechat', $provider);
    }

    public static function sendCode($recevier, $type, $provider = null, array $params = [])
    {
        // 获取注册服务实例
        $service = LoginFactory::getInstance($type, $provider);
        return $service->sendCode($recevier, $params);
    }
    
    /**
     * 获取错误信息
     * @return string
     */
    public static function getError()
    {
        return self::$error;
    }

    public static function getCode()
    {
        return self::$code;
    }

    /**
     * 用户退出登录
     * @param string $token 用户令牌
     * @return bool 退出结果，成功返回true，失败返回false
     */
    public static function logout($token)
    {
        try {
            // 验证令牌是否有效
            if (!Auth::checkToken($token)) {
                self::$code = ApiCode::ACCESS_TOKEN_INVALID;
                self::$error = ApiCode::getMessage(ApiCode::ACCESS_TOKEN_INVALID);
                return false;
            }

            // 清除令牌
            $result = Auth::removeToken($token);
            if (!$result) {
                self::$code = ApiCode::SERVER_ERROR;
                self::$error = ApiCode::getMessage(ApiCode::SERVER_ERROR);
                return false;
            }

            return true;
        } catch (\Exception $e) {
            self::$code = $e->getCode();
            self::$error = $e->getMessage();
            return false;
        }
    }
}