<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 优惠券模型
 */
class Coupon extends Model
{
    // 设置表名
    protected $name = 'ad_coupon';
    
    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'name' => 'string',
        'type' => 'int',
        'value' => 'float',
        'min_order_amount' => 'float',
        'stock' => 'int',
        'per_limit' => 'int',
        'description' => 'string',
        'start_time' => 'int',
        'end_time' => 'int',
        'code' => 'string',
        'createtime' => 'int',
        'updatetime' => 'int'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 检查优惠券是否可用
     */
    public function checkAvailable()
    {
        if ($this->status != 1) {
            return false;
        }
        
        $now = time();
        if ($this->start_time > $now || $this->end_time < $now) {
            return false;
        }
        
        if ($this->stock <= 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 减少库存
     */
    public function decreaseStock($num = 1)
    {
        if ($num <= 0) {
            return false;
        }
        
        return $this->where('id', $this->id)
            ->where('stock', '>=', $num)
            ->dec('stock', $num)
            ->update();
    }
    
    /**
     * 增加库存
     */
    public function increaseStock($num = 1)
    {
        if ($num <= 0) {
            return false;
        }
        
        return $this->where('id', $this->id)
            ->inc('stock', $num)
            ->update();
    }
}