<?php

namespace app\common;

use think\facade\Cache;
use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * Token安全增强类
 * 提供速率限制、安全检查等功能
 */
class TokenSecurity
{
    // 速率限制缓存前缀
    protected static $rateLimitPrefix = 'token_rate_limit:';
    
    // 可疑活动缓存前缀
    protected static $suspiciousPrefix = 'token_suspicious:';
    
    // 日志前缀
    protected static $logPrefix = '[TOKEN_SECURITY]';
    
    /**
     * 检查Token验证速率限制
     * 
     * @param string $identifier 标识符（IP地址或用户ID）
     * @param int $maxAttempts 最大尝试次数
     * @param int $timeWindow 时间窗口（秒）
     * @return bool 是否允许继续
     */
    public static function checkRateLimit($identifier, $maxAttempts = 60, $timeWindow = 3600)
    {
        $key = self::$rateLimitPrefix . $identifier;
        $attempts = Cache::get($key, 0);
        
        if ($attempts >= $maxAttempts) {
            self::logSecurity('RATE_LIMIT_EXCEEDED', [
                'identifier' => $identifier,
                'attempts' => $attempts,
                'max_attempts' => $maxAttempts
            ]);
            return false;
        }
        
        // 增加尝试次数
        Cache::set($key, $attempts + 1, $timeWindow);
        return true;
    }
    
    /**
     * 记录失败的Token验证尝试
     * 
     * @param string $token Token
     * @param string $reason 失败原因
     * @param string $ip IP地址
     */
    public static function recordFailedAttempt($token, $reason, $ip = null)
    {
        $ip = $ip ?: Request::ip();
        
        // 记录IP级别的失败次数
        $ipKey = self::$suspiciousPrefix . 'ip:' . $ip;
        $ipFailures = Cache::get($ipKey, 0);
        Cache::set($ipKey, $ipFailures + 1, 3600); // 1小时
        
        // 记录Token级别的失败次数
        if ($token) {
            $tokenKey = self::$suspiciousPrefix . 'token:' . substr(md5($token), 0, 8);
            $tokenFailures = Cache::get($tokenKey, 0);
            Cache::set($tokenKey, $tokenFailures + 1, 3600); // 1小时
        }
        
        self::logSecurity('FAILED_ATTEMPT', [
            'token' => $token ? substr($token, 0, 8) . '...' : null,
            'reason' => $reason,
            'ip' => $ip,
            'ip_failures' => $ipFailures + 1,
            'user_agent' => Request::header('user-agent')
        ]);
        
        // 检查是否需要触发安全警报
        if ($ipFailures >= 10) {
            self::triggerSecurityAlert('HIGH_FAILURE_RATE', [
                'ip' => $ip,
                'failures' => $ipFailures + 1
            ]);
        }
    }
    
    /**
     * 检查IP是否可疑
     * 
     * @param string $ip IP地址
     * @return bool
     */
    public static function isSuspiciousIP($ip)
    {
        $key = self::$suspiciousPrefix . 'ip:' . $ip;
        $failures = Cache::get($key, 0);
        
        return $failures >= 20; // 1小时内失败20次认为可疑
    }
    
    /**
     * 验证Token格式
     * 
     * @param string $token Token
     * @return bool
     */
    public static function validateTokenFormat($token)
    {
        // UUID格式验证：8-4-4-4-12
        $pattern = '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i';
        return preg_match($pattern, $token) === 1;
    }
    
    /**
     * 检查Token是否来自可信来源
     * 
     * @param array $headers 请求头
     * @return bool
     */
    public static function checkTrustedSource($headers = null)
    {
        $headers = $headers ?: Request::header();
        
        // 检查User-Agent是否存在
        if (empty($headers['user-agent'])) {
            return false;
        }
        
        // 检查是否有可疑的User-Agent模式
        $suspiciousPatterns = [
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/bot/i'
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $headers['user-agent'])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 生成安全的随机字符串
     * 
     * @param int $length 长度
     * @return string
     */
    public static function generateSecureRandom($length = 32)
    {
        if (function_exists('random_bytes')) {
            return bin2hex(random_bytes($length / 2));
        }
        
        if (function_exists('openssl_random_pseudo_bytes')) {
            return bin2hex(openssl_random_pseudo_bytes($length / 2));
        }
        
        // 降级方案
        return substr(str_shuffle(str_repeat('0123456789abcdef', ceil($length / 16))), 0, $length);
    }
    
    /**
     * 触发安全警报
     * 
     * @param string $type 警报类型
     * @param array $data 警报数据
     */
    protected static function triggerSecurityAlert($type, $data)
    {
        self::logSecurity('SECURITY_ALERT', [
            'alert_type' => $type,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        // 这里可以添加更多的警报处理逻辑
        // 比如发送邮件、短信通知等
    }
    
    /**
     * 记录安全日志
     */
    private static function logSecurity($level, $message, $context = [])
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'ip' => request()->ip(),
            'user_agent' => request()->header('User-Agent')
        ];
        
        Log::write(json_encode($logData), self::$logPrefix);
    }
    
    /**
     * 清理安全相关缓存数据
     */
    public static function cleanup()
    {
        try {
            $prefix = Config::get('token.security.rate_limit.prefix', 'rate_limit:');
            $blacklistPrefix = Config::get('token.security.blacklist.prefix', 'token_blacklist:');
            
            // 清理速率限制缓存（这里只是示例，实际实现可能需要根据缓存驱动调整）
            // 注意：Redis和其他缓存驱动的清理方式可能不同
            
            // 记录清理操作
            self::logSecurity('info', 'Security cache cleanup completed', [
                'rate_limit_prefix' => $prefix,
                'blacklist_prefix' => $blacklistPrefix
            ]);
            
            return true;
        } catch (\Exception $e) {
            self::logSecurity('error', 'Security cache cleanup failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}