<?php
declare (strict_types = 1);

namespace app\common\controller;

use app\common\ApiCode;
use think\App;
use think\facade\Db;
use app\api\entity\UserEntity;

/**
 * API控制器基础类
 * 提供RESTful风格的CRUD操作方法
 */
abstract class BaseApiController extends BaseController
{
    /**
     * 模型实例
     * @var \think\Model
     */
    protected $model;
    
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [];
    
    /**
     * 允许修改的字段
     * @var array
     */
    protected $allowFields = [];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        
        // 初始化模型（子类可以在initialize方法中重写）
        $this->initialize();

    }

    /**
     * 获取列表
     * @return \think\Response
     */
    public function index()
    {
        // 构建查询参数
        list($page, $pageSize, $sort, $where) = $this->buildParames();
        
        // 查询前的钩子方法
        $this->beforeIndex($where, $sort);
        
        // 执行查询
        $list = $this->model
            ->where($where)
            ->order($sort)
            ->paginate([
                'list_rows' => $pageSize,
                'page' => $page,
            ]);
        
        // 查询后的钩子方法
        $data = $this->afterIndex($list);

        // 如果afterIndex方法没有返回数据，则使用默认格式
        if ($data === null) {
            $data = ['items' => $list->items(), 'total' => $list->total()];
        }

        $this->ok('success', $data);
    }
    
    /**
     * 获取详情
     * @param int|string $id 记录ID
     * @return \think\Response
     */
    public function read($id)
    {
        // 查询前的钩子方法
        $this->beforeRead($id);
        
        // 查询记录
        $info = $this->model->find($id);
        if (!$info) {
            $this->fail(ApiCode::getMessage(ApiCode::NOT_FOUND), [], ApiCode::NOT_FOUND);
        }
        
        // 查询后的钩子方法
        $data = $this->afterRead($info);
        
        // 如果afterRead方法没有返回数据，则使用原始数据
        if ($data === null) {
            $data = $info;
        }
        
        $this->ok('success', $data);
    }
    
    /**
     * 创建记录
     * @return \think\Response
     */
    public function save()
    {
        // 获取请求数据
        $post = $this->request->post();

        // 安全过滤，防止修改敏感字段
        $data = [];
        if (!empty($this->allowFields)) {
            foreach ($this->allowFields as $field) {
                if (isset($post[$field])) {
                    $data[$field] = $post[$field];
                }
            }
        } else {
            $data = $post;
        }

        if (empty($data)) {
            $this->fail(ApiCode::getMessage(ApiCode::BAD_REQUEST), [], ApiCode::BAD_REQUEST);
        }
        
        // 验证前的钩子方法
        $this->beforeValidate($data, 'save');

        // 验证数据
        if (!empty($this->validateRule)) {
            $this->validate($data, $this->getValidateRule('save'));
        }

        // 开始事务
        Db::startTrans();

        // 保存前的钩子方法
        $data = $this->beforeSave($data);

        // 如果beforeSave方法没有返回数据，则使用原始数据
        if ($data === null) {
            $data = $post;
        }

        try {
            // 保存数据
            $result = $this->model->save($data);

            if ($result === false) {
                Db::rollback();
                $this->fail(ApiCode::getMessage(ApiCode::SERVER_ERROR), [], ApiCode::SERVER_ERROR);
            }
            
            // 保存后的钩子方法
            $this->afterSave($this->model->id, $data);
            
            // 提交事务
            Db::commit();
            
        } catch (\Exception $e) {
            Db::rollback();
            $this->fail($e->getMessage(), [], ApiCode::SERVER_ERROR);
        }

        $this->ok('success', ['id' => $this->model->id]);
    }
    
    /**
     * 更新记录
     * @param int|string $id 记录ID
     * @return \think\Response
     */
    public function update($id)
    {
        // 查询记录是否存在
        $info = $this->model->find($id);
        if (!$info) {
            $this->fail(ApiCode::getMessage(ApiCode::NOT_FOUND), [], ApiCode::NOT_FOUND);
        }
        
        // 获取请求数据
        $put = $this->request->put();

        // 安全过滤，防止修改敏感字段
        $data = [];
        if (!empty($this->allowFields)) {
            foreach ($this->allowFields as $field) {
                if (isset($put[$field])) {
                    $data[$field] = $put[$field];
                }
            }
        } else {
            $data = $put;
        }

        if (empty($data)) {
            $this->fail(ApiCode::getMessage(ApiCode::BAD_REQUEST), [], ApiCode::BAD_REQUEST);
        }
        
        // 验证前的钩子方法
        $this->beforeValidate($data, 'update', $id);

        // 验证数据
        if (!empty($this->validateRule)) {
            $this->validate($data, $this->getValidateRule('update', $id));
        }
        
        // 开始事务
        Db::startTrans();

        // 更新前的钩子方法
        $data = $this->beforeUpdate($data, $id);

        // 如果beforeUpdate方法没有返回数据，则使用原始数据
        if ($data === null) {
            $data = $put;
        }

        try {
            // 更新数据
            $result = $this->model->where('id', $id)->update($data);

            if ($result === false) {
                Db::rollback();
                $this->fail(ApiCode::getMessage(ApiCode::SERVER_ERROR), [], ApiCode::SERVER_ERROR);
            }
            
            // 更新后的钩子方法
            $this->afterUpdate($id, $data);
            
            // 提交事务
            Db::commit();
            
        } catch (\Exception $e) {
            Db::rollback();
            $this->fail($e->getMessage(), [], ApiCode::SERVER_ERROR);
        }

        $this->ok('success');
    }
    
    /**
     * 删除记录
     * @param int|string $id 记录ID
     * @return \think\Response
     */
    public function delete($id)
    {
        // 删除前的钩子方法
        $canDelete = $this->beforeDelete($id);
        
        // 如果beforeDelete方法返回false，则阻止删除
        if ($canDelete === false) {
            $this->fail(ApiCode::getMessage(ApiCode::BAD_REQUEST), [], ApiCode::BAD_REQUEST);
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 执行删除
            $result = $this->model->where('id', $id)->delete();
            
            if ($result === false) {
                Db::rollback();
                $this->fail(ApiCode::getMessage(ApiCode::SERVER_ERROR), [], ApiCode::SERVER_ERROR);
            }
            
            // 删除后的钩子方法
            $this->afterDelete($id);
            
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->fail($e->getMessage(), [], ApiCode::SERVER_ERROR);
        }

        $this->ok('success');

    }
    
    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|string|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        return $this->validateRule;
    }
    
    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
    }
    
    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        return null;
    }
    
    /**
     * 详情查询前的钩子方法
     * @param int|string $id 记录ID
     */
    protected function beforeRead($id)
    {
    }
    
    /**
     * 详情查询后的钩子方法
     * @param \think\Model $info 记录对象
     * @return array|null
     */
    protected function afterRead($info)
    {
        return null;
    }
    
    /**
     * 验证前的钩子方法
     * @param array &$data 表单数据
     * @param string $scene 场景名称
     * @param int|string|null $id 记录ID（编辑时使用）
     */
    protected function beforeValidate(&$data, $scene = '', $id = null)
    {
    }
    
    /**
     * 保存前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeSave($data)
    {
        return null;
    }
    
    /**
     * 保存后的钩子方法
     * @param int|string $id 新增记录的ID
     * @param array $data 表单数据
     */
    protected function afterSave($id, $data)
    {
    }
    
    /**
     * 更新前的钩子方法
     * @param array $data 表单数据
     * @param int|string $id 记录ID
     * @return array|null
     */
    protected function beforeUpdate($data, $id)
    {
        return null;
    }
    
    /**
     * 更新后的钩子方法
     * @param int|string $id 记录ID
     * @param array $data 表单数据
     */
    protected function afterUpdate($id, $data)
    {
    }
    
    /**
     * 删除前的钩子方法
     * @param int|string $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        return null;
    }
    
    /**
     * 删除后的钩子方法
     * @param int|string $id 记录ID
     */
    protected function afterDelete($id)
    {
    }

    /**
     * 将字段名从驼峰命名转为下划线命名（用于数据库写入）
     * @param array $data
     * @return array
     */
    protected function formatToDatabase(array $data): array
    {
        $formatted = [];
        foreach ($data as $key => $value) {
            // 转换字段名
            $snakeKey = strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $key));
            $formatted[$snakeKey] = $value;
        }

        // 强制设置 createtime 和 updatetime 为当前时间戳
        $now = time();
        if (!isset($formatted['createtime'])) {
            $formatted['createtime'] = $now;
        }
        $formatted['updatetime'] = $now;

        return $formatted;
    }

    /**
     * 将字段名从下划线命名转为驼峰命名（用于返回给前端）
     * @param array $data
     * @return array
     */
    protected function formatFromDatabase(array $data): array
    {
        $formatted = [];
        foreach ($data as $key => $value) {
            // 转换字段名
            $parts = explode('_', $key);
            $camelKey = $parts[0];
            for ($i = 1; $i < count($parts); $i++) {
                $camelKey .= ucfirst($parts[$i]);
            }
            $formatted[$camelKey] = $value;
        }

        return $formatted;
    }

    /**
     * 格式化关键词字段，支持数组或字符串输入
     * @param mixed $data
     * @return string
     */
    protected function formatArrData($data): string
    {
        if (is_array($data)) {
            return implode(',', array_filter(array_map('trim', $data)));
        }

        if (is_string($data)) {
            // 如果是字符串，去除多余空格并返回
            return trim($data);
        }

        return '';
    }
}