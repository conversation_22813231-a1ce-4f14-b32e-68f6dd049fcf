<?php
declare (strict_types = 1);

namespace app\common\controller;

use app\common\ApiCode;
use think\App;
use think\facade\Db;

/**
 * 管理后台控制器基础类
 * 提供通用的CRUD操作方法
 */
abstract class BaseAdminController extends BaseController
{
    /**
     * 模型实例
     * @var \think\Model
     */
    protected $model;
    
    /**
     * 数据表名
     * @var string
     */
    protected $table;
    
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [];
    
    /**
     * 允许修改的字段
     * @var array
     */
    protected $allowFields = [];
    
    /**
     * 是否开启自动时间戳
     * @var bool
     */
    protected $autoTimestamp = true;
    
    /**
     * 分页大小
     * @var int
     */
    protected $pageSize = 10;
    
    /**
     * 页码
     * @var int
     */
    protected $currentPage = 1;
    
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        
        // 初始化模型（子类可以在initialize方法中重写）
        $this->initialize();
    }
    
    /**
     * 列表数据
     * @return \think\Response
     */
    public function index()
    {
        // 构建查询参数
        list($this->currentPage, $this->pageSize, $sort, $where) = $this->buildParames();
        
        // 查询前的钩子方法
        $this->beforeIndex($where, $sort);
        
        // 执行查询
        $list = $this->model
            ->where($where)
            ->order($sort)
            ->paginate([
                'list_rows' => $this->pageSize,
                'page' => $this->currentPage,
            ]);
        
        // 查询后的钩子方法
        $data = $this->afterIndex($list);

        // 如果afterIndex方法没有返回数据，则使用默认格式
        if ($data === null) {
            $data = ['record' => $list->items(), 'count' => $list->total()];
        }

        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $data);
        
    }
    
    /**
     * 添加记录
     * @return \think\Response
     */
    public function add()
    {
        if ($this->request->isPost()) {
            // 获取请求数据
            $post = $this->request->post();

            // 安全过滤，防止修改敏感字段
            $data = [];
            if (!empty($this->allowFields)) {
                foreach ($this->allowFields as $field) {
                    if (isset($post[$field])) {
                        $data[$field] = $post[$field];
                    }
                }
            } else {
                $data = $post;
            }

            if (empty($data)) {
                $this->fail('没有要添加的数据');
            }
            
            // 验证前的钩子方法
            $this->beforeValidate($data, 'add');

            // 验证数据
            if (!empty($this->validateRule)) {
                $this->validate($data, $this->getValidateRule('add'));
            }

            // 开始事务
            Db::startTrans();

            // 保存前的钩子方法
            $data = $this->beforeAdd($data);

            // 如果beforeAdd方法没有返回数据，则使用原始数据
            if ($data === null) {
                $data = $post;
            }

            try {
                // 保存数据
                $result = $this->model->save($data);

                if ($result === false) {
                    Db::rollback();
                    $this->fail('添加失败');
                }
                // 保存后的钩子方法
                $this->afterAdd($this->model->id, $data);
            } catch (\Exception $e) {
                Db::rollback();
                $this->fail('添加失败：' . $e->getMessage());
            }
            
            // 提交事务并返回成功响应
            Db::commit();
            $this->ok('添加成功');
        }
        
       $this->fail('请求方法不正确');
    }
    
    /**
     * 编辑记录
     * @param int|null $id 记录ID
     * @return \think\Response
     */
    public function edit($id = null)
    {
        if ($id === null) {
            $this->fail('参数错误');
        }

        // 查询记录是否存在
        $info = $this->model->find($id);

        if (!$info) {
            $this->fail('记录不存在');
        }
        
        if ($this->request->isPut()) {
            // 获取请求数据
            $put = $this->request->put();

            // 安全过滤，防止修改敏感字段
            $data = [];
            if (!empty($this->allowFields)) {
                foreach ($this->allowFields as $field) {
                    if (isset($put[$field])) {
                        $data[$field] = $put[$field];
                    }
                }
            } else {
                $data = $put;
            }

            if (empty($data)) {
                $this->fail('没有要更新的数据');
            }
            
            // 验证前的钩子方法
            $this->beforeValidate($data, 'edit', $id);

            // 验证数据
            if (!empty($this->validateRule)) {
                $this->validate($data, $this->getValidateRule('edit', $id));
            }
            
            // 开始事务
            Db::startTrans();

            // 更新前的钩子方法
            $data = $this->beforeEdit($data, $id);

            // 如果beforeEdit方法没有返回数据，则使用原始数据
            if ($data === null) {
                $data = $put;
            }

            try {
                // 更新数据
                $result = $this->model->where('id', $id)->update($data);

                if ($result === false) {
                    Db::rollback();
                    $this->fail('更新失败');
                }
                
                // 更新后的钩子方法
                $this->afterEdit($id, $data);
            } catch (\Exception $e) {
                Db::rollback();
                $this->fail('更新失败：' . $e->getMessage());
            }
            
            // 提交事务并返回成功响应
            Db::commit();
            $this->ok('更新成功');
        }
        
        // 获取详情前的钩子方法
        $this->beforeInfo($id);
        
        // 获取详情后的钩子方法
        $data = $this->afterInfo($info);
        
        // 如果afterInfo方法没有返回数据，则使用原始数据
        if ($data === null) {
            $data = $info;
        }
        
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $data);
    }
    
    /**
     * 删除记录
     * @param int|null $id 记录ID
     * @return \think\Response
     */
    public function delete($id = null)
    {
        if ($id === null) {
            $this->fail('参数错误');
        }
        
        // 删除前的钩子方法
        $canDelete = $this->beforeDelete($id);
        
        // 如果beforeDelete方法返回false，则阻止删除
        if ($canDelete === false) {
            $this->fail('无法删除该记录');
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 执行删除
            $result = $this->model->where('id', $id)->delete();
            
            if ($result === false) {
                Db::rollback();
                $this->fail('删除失败');
            }
        } catch (\Exception $e) {
            Db::rollback();
            $this->fail('删除失败：' . $e->getMessage());
        }
        // 删除后的钩子方法
        $this->afterDelete($id);
        Db::commit();
        $this->ok('删除成功');
    }
    
    /**
     * 获取记录详情
     * @param int|null $id 记录ID
     * @return \think\Response
     */
    public function info($id = null)
    {
        if ($id === null) {
            $this->fail('参数错误');
        }
        
        // 获取详情前的钩子方法
        $this->beforeInfo($id);
        
        // 查询记录
        $info = $this->model->find($id);
        if (!$info) {
            $this->fail('记录不存在');
        }
        
        // 获取详情后的钩子方法
        $data = $this->afterInfo($info);
        
        // 如果afterInfo方法没有返回数据，则使用原始数据
        if ($data === null) {
            $data = $info;
        }
        
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $data);
    }
    
    /**
     * 修改记录状态
     * @return \think\Response
     */
    public function status()
    {
        $id = $this->request->post('id');
        $status = $this->request->post('status');
        
        if (empty($id)) {
            $this->fail('参数错误');
        }
        
        // 状态修改前的钩子方法
        $canChange = $this->beforeStatus($id, $status);
        
        // 如果beforeStatus方法返回false，则阻止状态修改
        if ($canChange === false) {
            $this->fail('无法修改该记录状态');
        }
        
        // 获取状态字段名
        $statusField = $this->getStatusField();
        
        // 执行状态修改
        $result = $this->model->where('id', $id)->update([$statusField => $status]);
        
        if ($result) {
            // 状态修改后的钩子方法
            $this->afterStatus($id, $status);
            $this->ok('修改成功');
        } else {
            $this->fail('修改失败');
        }
    }
    
    /**
     * 获取状态字段名
     * @return string
     */
    protected function getStatusField()
    {
        return 'enabled';
    }
    
    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        return $this->validateRule;
    }
    
    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
    }
    
    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        return null;
    }
    
    /**
     * 验证前的钩子方法
     * @param array &$data 表单数据
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     */
    protected function beforeValidate(&$data, $scene = '', $id = null)
    {
    }
    
    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        return null;
    }
    
    /**
     * 添加后的钩子方法
     * @param int $id 新增记录的ID
     * @param array $data 表单数据
     */
    protected function afterAdd($id, $data)
    {
    }
    
    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        return null;
    }
    
    /**
     * 编辑后的钩子方法
     * @param int $id 记录ID
     * @param array $data 表单数据
     */
    protected function afterEdit($id, $data)
    {
    }
    
    /**
     * 删除前的钩子方法
     * @param int $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        return null;
    }
    
    /**
     * 删除后的钩子方法
     * @param int $id 记录ID
     */
    protected function afterDelete($id)
    {
    }
    
    /**
     * 获取详情前的钩子方法
     * @param int $id 记录ID
     */
    protected function beforeInfo($id)
    {
    }
    
    /**
     * 获取详情后的钩子方法
     * @param \think\Model $info 记录对象
     * @return array|null
     */
    protected function afterInfo($info)
    {
        return null;
    }
    
    /**
     * 状态修改前的钩子方法
     * @param int $id 记录ID
     * @param int|string $status 状态值
     * @return bool|null 返回false将阻止状态修改
     */
    protected function beforeStatus($id, $status)
    {
        return null;
    }
    
    /**
     * 状态修改后的钩子方法
     * @param int $id 记录ID
     * @param int|string $status 状态值
     */
    protected function afterStatus($id, $status)
    {
    }
    
    /**
     * 批量删除记录
     * @return \think\Response
     */
    public function batchDelete()
    {
        $ids = $this->request->param('ids');

        if (empty($ids) || !is_array($ids)) {
            $this->fail('参数错误');
        }
        
        // 删除前的钩子方法
        $canDelete = $this->beforeBatchDelete($ids);
        
        // 如果beforeBatchDelete方法返回false，则阻止删除
        if ($canDelete === false) {
            $this->fail('无法删除选中记录');
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 执行批量删除
            $result = $this->model->where('id', 'in', $ids)->delete();

            if ($result === false) {
                Db::rollback();
                $this->fail('删除失败');
            }
            
            // 删除后的钩子方法
            $this->afterBatchDelete($ids);
            
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->fail('删除失败：' . $e->getMessage());
        }

        $this->ok('删除成功');
    }
    
    /**
     * 批量删除前的钩子方法
     * @param array $ids 记录ID数组
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeBatchDelete($ids)
    {
        return null;
    }
    
    /**
     * 批量删除后的钩子方法
     * @param array $ids 记录ID数组
     */
    protected function afterBatchDelete($ids)
    {
    }

    /**
     * 构建树形结构
     * @param array $list 权限规则列表
     * @param int $pid 父级ID
     * @return array
     */
    protected function buildTree($list, $pid = 0)
    {
        $tree = [];
        foreach ($list as $item) {
            if ($item['pid'] == $pid) {
                $children = $this->buildTree($list, $item['id']);
                if (!empty($children)) {
                    $item['children'] = $children;
                }
                $tree[] = $item;
            }
        }
        return $tree;
    }

    /**
     * 将字段名从驼峰命名转为下划线命名（用于数据库写入）
     * @param array $data
     * @return array
     */
    protected function formatToDatabase(array $data): array
    {
        $formatted = [];
        foreach ($data as $key => $value) {
            // 转换字段名
            $snakeKey = strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $key));
            $formatted[$snakeKey] = $value;
        }

        // 强制设置 createtime 和 updatetime 为当前时间戳
        $now = time();
        if (!isset($formatted['createtime'])) {
            $formatted['createtime'] = $now;
        }
        $formatted['updatetime'] = $now;

        return $formatted;
    }

    /**
     * 将字段名从下划线命名转为驼峰命名（用于返回给前端）
     * @param array $data
     * @return array
     */
    protected function formatFromDatabase(array $data): array
    {
        $formatted = [];
        foreach ($data as $key => $value) {
            // 转换字段名
            $parts = explode('_', $key);
            $camelKey = $parts[0];
            for ($i = 1; $i < count($parts); $i++) {
                $camelKey .= ucfirst($parts[$i]);
            }
            $formatted[$camelKey] = $value;
        }

        return $formatted;
    }

    /**
     * 格式化关键词字段，支持数组或字符串输入
     * @param mixed $data
     * @return string
     */
    protected function formatArrData($data): string
    {
        if (is_array($data)) {
            return implode(',', array_filter(array_map('trim', $data)));
        }

        if (is_string($data)) {
            // 如果是字符串，去除多余空格并返回
            return trim($data);
        }

        return '';
    }
}