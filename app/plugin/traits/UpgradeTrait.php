<?php

namespace app\plugin\traits;

use app\plugin\core\SqlHelper;
use think\facade\Log;
use think\facade\Db;

trait UpgradeTrait
{

    protected $upgrade;
    /**
     * 升级插件
     * @param string $newVersion 新版本号
     * @return bool
     */
    public function upgrade(string $newVersion): bool
    {
        // 获取当前版本
        $currentVersion = $this->version;

        // 检查版本号是否合法
        if (!version_compare($newVersion, $currentVersion, '>')) {
            throw new \RuntimeException("New version {$newVersion} is not greater than current version {$currentVersion}");
        }

        // 检查最低版本要求
        $manifest = $this->getManifest();
        $minVersion = $manifest['upgrade']['minimum_version'] ?? '0.0.0';
        if (version_compare($currentVersion, $minVersion, '<')) {
            throw new \RuntimeException("Current version {$currentVersion} is lower than minimum required version {$minVersion}");
        }
        try {
            
            // 备份当前版本
            if ($manifest['upgrade']['backup'] ?? true) {
                $this->backupPlugin();
            }
            
            // 执行预升级脚本
            $this->executeUpgradeScript('pre_upgrade');

            // 停用插件
            $this->disable();

            // 更新插件文件
            $this->updatePluginFiles($newVersion);

            // 更新数据库
            $this->updateDatabase($currentVersion, $newVersion);

            // 执行后升级脚本
            $this->executeUpgradeScript('post_upgrade');

            // 更新版本号
            $this->version = $newVersion;

            $this->savePluginInfo();
            // 重新启用插件
            $this->enable();
            
            return true;
        } catch (\Exception $e) {
            Log::error("App {$this->name} upgrade failed: " . $e->getMessage());
            // 升级失败时回滚
            $this->rollbackUpgrade();
            return false;
        }
    }
    
    /**
     * 备份插件
     */
    protected function backupPlugin(): void
    {
        try {
            $rollbackManager = new \app\plugin\core\UpgradeRollbackManager(
                $this->pluginDir,
                $this->name,
                $this->version
            );
            
            $backupDir = $rollbackManager->startBackup();
            Log::info("Plugin {$this->name} backup completed successfully at: {$backupDir}");
        } catch (\Exception $e) {
            Log::error("Plugin {$this->name} backup failed: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 执行升级脚本
     */
    protected function executeUpgradeScript(string $type): void
    {
        $manifest = $this->getManifest();
        $scriptPath = $manifest['upgrade']['scripts'][$type] ?? '';

        if (!empty($scriptPath)) {
            $fullPath = $this->pluginDir . $this->name . '/' . $scriptPath;
            if (file_exists($fullPath)) {
                include $fullPath;
            }
        }
    }
    
    /**
     * 更新插件文件
     */
    protected function updatePluginFiles(string $newVersion): void
    {
        $manifest = $this->getManifest();
        $upgradeSource = $manifest['upgrade']['source'] ?? 'local';
        $tempDir = $this->pluginDir . 'temp/' . $this->name . '_' . $newVersion;
        $extractDir = $tempDir . '/extract';
        
        try {
            // 创建临时目录
            if (!is_dir($extractDir)) {
                mkdir($extractDir, 0755, true);
            }
            
            if ($upgradeSource === 'remote') {
                // 从远程下载升级包
                $downloadUrl = sprintf(
                    $manifest['upgrade']['download_url'],
                    $this->name,
                    $newVersion
                );
                $zipFile = $tempDir . '/upgrade.zip';
                
                // 下载文件
                $content = file_get_contents($downloadUrl);
                if ($content === false) {
                    throw new \RuntimeException("Failed to download upgrade package from: {$downloadUrl}");
                }
                file_put_contents($zipFile, $content);
                
                // 解压文件
                $zip = new \ZipArchive();
                if ($zip->open($zipFile) === true) {
                    $zip->extractTo($extractDir);
                    $zip->close();
                } else {
                    throw new \RuntimeException("Failed to extract upgrade package");
                }
            } else {
                // 从本地升级包更新
                $localPackage = $this->pluginDir . $this->name . "/upgrade/v{$newVersion}.zip";
                if (!file_exists($localPackage)) {
                    throw new \RuntimeException("Upgrade package not found: {$localPackage}");
                }
                
                // 解压本地升级包
                $zip = new \ZipArchive();
                if ($zip->open($localPackage) === true) {
                    $zip->extractTo($extractDir);
                    $zip->close();
                } else {
                    throw new \RuntimeException("Failed to extract local upgrade package");
                }
            }
            
            // 验证升级包
            $this->validateUpgradePackage($extractDir, $newVersion);
            
            // 更新插件文件
            $pluginPath = $this->pluginDir . $this->name;
            $excludes = ['config', 'data', '.git', '.svn', 'upgrade'];
            $versionDir = $extractDir . '/v' . $newVersion;
            
            // 仅更新升级包中包含的文件
            if (is_dir($versionDir)) {
                $this->copyPluginFiles($versionDir, $pluginPath, $excludes);
            } else {
                throw new \RuntimeException("Version directory not found in upgrade package");
            }

        } finally {
            // 清理临时文件
            if (is_dir($tempDir)) {
                $this->removeDirectory($tempDir);
            }
        }
    }
    
    /**
     * 递归删除插件文件（排除特定目录）
     */
    protected function removePluginFiles(string $dir, array $excludes): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') {
                continue;
            }
            
            $path = $dir . '/' . $item;
            if (is_dir($path)) {
                if (!in_array($item, $excludes)) {
                    $this->removeDirectory($path);
                }
            } else {
                unlink($path);
            }
        }
    }
    
    /**
     * 递归复制插件文件（排除特定目录）
     */
    protected function copyPluginFiles(string $source, string $target, array $excludes): void
    {
        if (!is_dir($source)) {
            return;
        }
        
        if (!is_dir($target)) {
            mkdir($target, 0755, true);
        }
        
        $items = scandir($source);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') {
                continue;
            }
            
            $sourcePath = $source . '/' . $item;
            $targetPath = $target . '/' . $item;
            
            if (is_dir($sourcePath)) {
                if (!in_array($item, $excludes)) {
                    $this->copyDirectory($sourcePath, $targetPath);
                }
            } else {
                copy($sourcePath, $targetPath);
            }
        }
    }
    
    /**
     * 验证升级包
     */
    protected function validateUpgradePackage(string $packageDir, string $version): void
    {
        // 检查manifest.json
        $manifestFile = $packageDir . '/v' . $version . '/manifest.json';
        if (!file_exists($manifestFile)) {
            throw new \RuntimeException("manifest.json not found in upgrade package");
        }
        
        $manifest = json_decode(file_get_contents($manifestFile), true);
        if ($manifest === null) {
            throw new \RuntimeException("Invalid manifest.json in upgrade package");
        }
        
        // 验证版本号
        if ($manifest['version'] !== $version) {
            throw new \RuntimeException(
                "Version mismatch: expected {$version}, got {$manifest['version']}"
            );
        }
    }
    
    /**
     * 复制目录
     */
    protected function copyDirectory(string $source, string $target): void
    {
        if (!is_dir($target)) {
            mkdir($target, 0755, true);
        }
        
        $dir = opendir($source);
        while (($file = readdir($dir)) !== false) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            $sourcePath = $source . '/' . $file;
            $targetPath = $target . '/' . $file;
            
            is_dir($sourcePath) ? 
                $this->copyDirectory($sourcePath, $targetPath) : 
                copy($sourcePath, $targetPath);
        }
        closedir($dir);
    }
    
    /**
     * 删除目录
     */
    protected function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }
    
    /**
     * 更新数据库
     */
    protected function updateDatabase(string $fromVersion, string $toVersion): void
    {
        $sqlFile = $this->pluginDir . $this->name . "/upgrade/{$fromVersion}_to_{$toVersion}.sql";
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            if (!empty($sql)) {
                SqlHelper::parser($sql);
            }
        }
    }
    
    /**
     * 回滚升级
     */
    protected function rollbackUpgrade(): void
    {
        try {
            $rollbackManager = new \app\plugin\core\UpgradeRollbackManager(
                $this->pluginDir,
                $this->name,
                $this->version
            );
            
            if (!$rollbackManager->rollback()) {
                throw new \RuntimeException('Failed to rollback plugin upgrade');
            }
            
            // 重新加载插件配置
            $this->reloadConfig();
            Log::info("Plugin {$this->name} rollback completed successfully");
        } catch (\Exception $e) {
            Log::error("Plugin {$this->name} rollback failed: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取插件配置信息
     * @return array
     * @throws \RuntimeException
     */
    protected function getManifest(): array
    {
        $manifestFile = $this->pluginDir . $this->name . '/manifest.json';
        if (!file_exists($manifestFile)) {
            throw new \RuntimeException("App manifest file not found: {$manifestFile}");
        }
    
        $content = file_get_contents($manifestFile);
        if ($content === false) {
            throw new \RuntimeException("Failed to read manifest file: {$manifestFile}");
        }
    
        $manifest = json_decode($content, true);
        if ($manifest === null) {
            throw new \RuntimeException("Invalid JSON format in manifest file: {$manifestFile}");
        }
    
        // 验证必要的配置项
        $requiredFields = ['name', 'version', 'title'];
        foreach ($requiredFields as $field) {
            if (!isset($manifest[$field]) || empty($manifest[$field])) {
                throw new \RuntimeException("Missing required field '{$field}' in manifest file");
            }
        }
    
        return $manifest;
    }
    
    /**
     * 保存插件配置信息
     * @throws \RuntimeException
     */
    protected function savePluginInfo(): void
    {
        // 使用ConfigTrait中的setConfig方法更新版本号
        // 这样会同时更新数据库和manifest.json文件
        $this->setConfig(['version' => $this->version]);
    }
}