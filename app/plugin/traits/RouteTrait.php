<?php

namespace app\plugin\traits;

use app\plugin\core\PluginRoute;
use think\facade\Log;

/**
 * 插件路由管理特性
 */
trait RouteTrait
{

    /**
     * 插件路由实例
     * @var PluginRoute
     */
    protected $routeInstance;

    /**
     * 注册路由
     */
    protected function registerRoutes(): void
    {
        // 初始化插件路由实例
        if (!$this->routeInstance) {
            $this->routeInstance = new PluginRoute($this->name);
        }

        try {
            // 获取并验证路由配置
            $routeFile = $this->pluginDir . $this->name . '/route.php';
            if (file_exists($routeFile)) {
                $route = require $routeFile;
                if (!is_array($route)) {
                    Log::error("Invalid route configuration: {$routeFile}, expected array but got " . gettype($routeFile));
                    return;
                }
                // 注册路由
                $this->routeInstance->registerRoutes($route);
            }

        } catch (\Exception $e) {
            // 记录错误日志
            error_log("App route registration failed: " . $e->getMessage());
            // 重新抛出异常以中断执行
        }
    }
}