<?php

namespace app\plugin\traits;

use app\plugin\core\PluginManager;
use think\facade\Log;

/**
 * 插件依赖管理特性
 */
trait DependencyTrait
{
    /**
     * 插件依赖
     * @var array
     */
    protected $dependencies = [];

    /**
     * 获取插件依赖
     * @return array
     */
    public function getDependencies(): array
    {
        return $this->dependencies;
    }
    
    /**
     * 检查插件依赖是否满足
     * @return bool
     */
    public function checkDependencies(): bool
    {
        if (empty($this->dependencies)) {
            return true;
        }

        $pluginManager = app(PluginManager::class);
        foreach ($this->dependencies as $dependency => $version) {
            // 检查依赖插件是否存在
            if (!$pluginManager->hasPlugin($dependency)) {
                Log::error("App {$this->name} depends on {$dependency}, but it is not installed.");
                return false;
            }
            
            // 检查依赖插件是否启用
            if (!$pluginManager->isPluginEnabled($dependency)) {
                Log::error("App {$this->name} depends on {$dependency}, but it is not enabled.");
                return false;
            }
            
            // 检查依赖插件版本是否满足要求
            if ($version && !$this->checkVersion($pluginManager->getPluginVersion($dependency), $version)) {
                Log::error("App {$this->name} depends on {$dependency} {$version}, but the installed version does not match.");
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查版本是否满足要求
     * @param string $currentVersion 当前版本
     * @param string $requiredVersion 要求版本
     * @return bool
     */
    protected function checkVersion(string $currentVersion, string $requiredVersion): bool
    {
        return version_compare($currentVersion, $requiredVersion, '>=');
    }

    /**
     * 检查是否有其他插件依赖于此插件
     * @return bool
     */
    protected function hasPluginsDependingOnThis(): bool
    {
        $pluginManager = app(PluginManager::class);
        $plugins = $pluginManager->getPlugins();
        
        foreach ($plugins as $plugin) {
            if ($plugin->getName() === $this->name) {
                continue;
            }
            
            $dependencies = $plugin->getDependencies();
            if (isset($dependencies[$this->name])) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查是否有其他启用的插件依赖于此插件
     * @return bool
     */
    protected function hasEnabledPluginsDependingOnThis(): bool
    {
        $pluginManager = app(PluginManager::class);
        $plugins = $pluginManager->getEnabledPlugins();
        
        foreach ($plugins as $plugin) {
            if ($plugin->getName() === $this->name) {
                continue;
            }
            
            $dependencies = $plugin->getDependencies();
            if (isset($dependencies[$this->name])) {
                return true;
            }
        }
        
        return false;
    }
}