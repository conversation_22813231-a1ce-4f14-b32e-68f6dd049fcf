<?php

namespace app\plugin\traits;

use app\plugin\core\HookManager;

/**
 * 插件钩子特性
 */
trait HookTrait
{
    /**
     * 钩子列表
     * @var array
     */
    protected $hooks = [];

    protected $name;
    
    /**
     * 触发插件生命周期事件
     * @param string $event 事件名称
     * @param array $params 事件参数
     * @return mixed
     */
    protected function triggerPluginEvent(string $event, array $params = []): mixed
    {
        /** @var HookManager $hookManager */
        $hookManager = app(HookManager::class);
        
        if (!$hookManager->isPluginEvent($event)) {
            return null;
        }
        
        $params['name'] = $this->name;
        return $hookManager->execute($event, $params);
    }
    
    /**
     * 注册钩子
     * @param string $hook 钩子名称
     * @param callable $callback 回调函数
     * @param int $priority 优先级，数字越小优先级越高
     * @return void
     */
    protected function addHook(string $hook, callable $callback, int $priority = 10): void
    {
        $hookManager = app(HookManager::class);
        $hookManager->add($hook, $callback, $priority, $this->name);
        $this->hooks[$hook] = [
            'callback' => $callback,
            'priority' => $priority
        ];
    }

    /**
     * 移除钩子
     * @param string $hook 钩子名称
     * @return void
     */
    protected function removeHook(string $hook): void
    {
        if (isset($this->hooks[$hook])) {
            $hookManager = app(HookManager::class);
            $hookManager->remove($hook, $this->name);
            unset($this->hooks[$hook]);
        }
    }
    
    /**
     * 移除所有钩子
     * @return void
     */
    protected function removeAllHooks(): void
    {
        $hookManager = app(HookManager::class);
        foreach ($this->hooks as $hook => $info) {
            $hookManager->remove($hook, $this->name);
        }
        $this->hooks = [];
    }
    
    /**
     * 执行钩子
     * @param string $hook 钩子名称
     * @param mixed $params 参数
     * @param string|null $plugin 插件名称，如果指定则只执行该插件的钩子
     * @return mixed
     */
    protected function doHook(string $hook, mixed $params = [], ?string $plugin = null)
    {
        $hookManager = app(HookManager::class);
        if ($plugin === null) {
            $plugin = $this->name;
        }
        return $hookManager->execute($hook, $params, $plugin);
    }
    
    /**
     * 执行特定插件的钩子
     * @param string $hook 钩子名称
     * @param mixed $params 参数
     * @return mixed
     */
    protected function doPluginHook(string $hook, mixed $params = [])
    {
        $hookManager = app(HookManager::class);
        return $hookManager->executePluginHook($hook, $this->name, $params);
    }
}