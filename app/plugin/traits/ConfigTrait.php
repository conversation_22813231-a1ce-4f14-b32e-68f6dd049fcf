<?php

namespace app\plugin\traits;

use app\plugin\core\PluginConfig;
use think\facade\Db;

/**
 * 插件配置管理特性
 */
trait ConfigTrait
{
    /**
     * 插件配置实例
     * @var PluginConfig
     */
    protected $configInstance;

    protected $pluginDir;

    /**
     * 插件配置
     * @var array
     */
    protected $config = [];

    /**
     * 获取插件配置
     * @return array
     */
    public function getConfig(): array
    {
        return $this->configInstance->get();
    }

    /**
     * 设置插件配置
     * @param array $config
     * @return bool
     */
    public function setConfig(array $config): bool
    {
        // 更新数据库配置
        $this->config = array_merge($this->config, $config);
        $this->saveConfig();
        
        // 更新manifest.json配置
        return $this->configInstance->set($config);
    }

    /**
     * 创建插件配置表
     */
    protected function createConfigTable(): void
    {
        $tableName = "plugin_{$this->name}_config";
        $sql = "CREATE TABLE IF NOT EXISTS `{$tableName}` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(50) NOT NULL,
            `value` text,
            `created_at` datetime DEFAULT NULL,
            `updated_at` datetime DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        Db::execute($sql);
    }

    /**
     * 删除插件配置表
     */
    protected function dropConfigTable(): void
    {
        $tableName = "plugin_{$this->name}_config";
        $sql = "DROP TABLE IF EXISTS `{$tableName}`";
        Db::execute($sql);
    }

    /**
     * 保存默认配置
     */
    protected function saveDefaultConfig(): void
    {
        $tableName = "plugin_{$this->name}_config";
        foreach ($this->config as $name => $value) {
            Db::name($tableName)->insert([
                'name' => $name,
                'value' => is_array($value) ? json_encode($value) : $value,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        // 同步更新manifest.json配置
        $this->configInstance->set($this->config);
    }

    /**
     * 保存配置
     */
    protected function saveConfig(): void
    {
        $tableName = "plugin_{$this->name}_config";
        foreach ($this->config as $name => $value) {
            Db::name($tableName)
                ->where('name', $name)
                ->update([
                    'value' => is_array($value) ? json_encode($value) : $value,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        }
        
        // 同步更新manifest.json配置
        $this->configInstance->set($this->config);
    }

    /**
     * 更新插件状态
     * @param bool $enabled
     */
    protected function updatePluginStatus(bool $enabled): void
    {
        $tableName = "plugin_{$this->name}_config";
        Db::name($tableName)
            ->where('name', 'enabled')
            ->update([
                'value' => $enabled ? 'true' : 'false',
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
        // 同步更新manifest.json配置
        $this->configInstance->set(['enabled' => $enabled]);
    }
}