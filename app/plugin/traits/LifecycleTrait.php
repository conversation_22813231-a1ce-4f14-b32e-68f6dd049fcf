<?php

namespace app\plugin\traits;

use app\plugin\constant\AppsName;
use app\plugin\constant\AppsPath;
use app\plugin\core\SqlHelper;
use think\facade\Config;
use think\facade\Log;
use think\facade\Db;

/**
 * 插件生命周期管理特性
 */
trait LifecycleTrait
{
    /**
     * 插件是否已启动
     * @var bool
     */
    protected $booted = false;
    /**
     * 安装插件
     * @return bool
     */
    public function install(): bool
    {
        $rollbackManager = new \app\plugin\core\InstallRollbackManager($this->name);

        try {
            // 检查依赖关系
            if (!$this->checkDependencies()) {
                Log::error("App {$this->name} installation failed: dependencies not satisfied.");
                return false;
            }

            // 触发插件禁用前事件
            $this->doHook('plugin.before_install', ['name' => $this->name]);

            // 创建插件配置表
            $this->createConfigTable();
            $rollbackManager->recordStep('configTable');

            // 执行安装SQL
            $this->executeInstallSql();
            $rollbackManager->recordStep('installSql');

            // 创建必要的目录
            $this->createDirectories();
            $rollbackManager->recordStep('directories');

            // 安装composer依赖
            $this->installComposerDependencies();
            $rollbackManager->recordStep('composerDependencies');

            // 保存默认配置
            $this->saveDefaultConfig();
            $rollbackManager->recordStep('defaultConfig');
            
            // 触发插件安装后事件
            $this->doHook('plugin.install', ['name' => $this->name]);
            return true;
        } catch (\Exception $e) {
            Log::error("App {$this->name} installation failed: " . $e->getMessage());
            // 执行回滚操作
            $rollbackManager->rollback();
            return false;
        }
    }

    /**
     * 安装Composer依赖
     */
    protected function installComposerDependencies(): void
    {
        $composerFile = $this->pluginDir . $this->name . '/composer.json';
        $vendorAutoload = $this->pluginDir . $this->name . '/vendor/autoload.php';
        if (file_exists($composerFile)) {
            $pluginPath = $this->pluginDir . $this->name;
            // 使用exec替代shell_exec以获取返回状态
            // 使用完整的PHP和Composer路径
            $phpPath = '/opt/homebrew/opt/php@8.3/bin/php';
            $composerPath = '/usr/local/bin/composer';
            $command = "cd {$pluginPath} && {$phpPath} {$composerPath} install --no-dev 2>&1";
            exec($command, $output, $returnCode);

            // 检查执行结果
            if ($returnCode !== 0) {
                $errorMsg = implode("\n", $output);
                Log::error("Failed to install composer dependencies for plugin {$this->name}: {$errorMsg}");
                return;
            }

            // 验证安装是否成功
            if (!file_exists($vendorAutoload)) {
                Log::error("Composer install completed but vendor/autoload.php not found for plugin {$this->name}");
                return;
            }
        }
    }

    /**
     * 清理Composer依赖
     */
    protected function cleanComposerDependencies(): void
    {
        $pluginPath = $this->pluginDir . $this->name;
        $vendorDir = $pluginPath . '/vendor';
        $composerLock = $pluginPath . '/composer.lock';
        
        // 删除vendor目录
        if (is_dir($vendorDir)) {
            shell_exec("rm -rf {$vendorDir}");
        }
        
        // 删除composer.lock文件
        if (file_exists($composerLock)) {
            unlink($composerLock);
        }
    }

    /**
     * 卸载插件
     * @return bool
     */
    public function uninstall(): bool
    {
        try {
            // 检查是否有其他插件依赖于此插件
            if ($this->hasPluginsDependingOnThis()) {
                Log::error("App {$this->name} uninstall failed: other apps depend on it.");
                return false;
            }
            
            // 触发插件禁用前事件
            $this->doHook('plugin.before_uninstall', ['name' => $this->name]);
            
            // 注销插件路由
            if ($this->routeInstance) {
                $this->routeInstance->unregister();
            }
            
            // 移除所有钩子
            $this->removeAllHooks();
            
            // 执行卸载SQL
            $this->executeUninstallSql();
            // 更新插件状态
            $this->disable();
            // 删除插件配置表
            $this->dropConfigTable();
            // 清理资源
            $this->cleanupResources();
            // 安装composer依赖
            $this->cleanComposerDependencies();
            
            // 触发插件卸载后事件
            $this->doHook('plugin.uninstall', ['name' => $this->name]);
            return true;
        } catch (\Exception $e) {
            Log::error("App {$this->name} uninstall failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 启用插件
     * @return bool
     */
    public function enable(): bool
    {
        try {
            // 检查依赖关系
            if (!$this->checkDependencies()) {
                Log::error("App {$this->name} enable failed: dependencies not satisfied.");
                return false;
            }
            
            // 触发插件启用前事件
            $this->doHook('plugin.before_enable', ['name' => $this->name]);
            
            // 更新插件状态
            $this->updatePluginStatus(true);
            // 注册路由
            $this->routeInstance->register();
            
            // 触发插件启用后事件
            $this->doHook('plugin.enable', ['name' => $this->name]);
            return true;
        } catch (\Exception $e) {
            Log::error("App {$this->name} enable failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 禁用插件
     * @return bool
     */
    public function disable(): bool
    {
        try {
            // 检查是否有其他启用的插件依赖于此插件
            if ($this->hasEnabledPluginsDependingOnThis()) {
                Log::error("App {$this->name} disable failed: other enabled apps depend on it.");
                return false;
            }
            
            // 触发插件禁用前事件
            $this->doHook('plugin.before_disable', ['name' => $this->name]);
            
            // 更新插件状态
            $this->updatePluginStatus(false);
            
            // 触发插件禁用后事件
            $this->doHook('plugin.disable', ['name' => $this->name]);
            return true;
        } catch (\Exception $e) {
            Log::error("App {$this->name} disable failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 启动插件
     * @return bool
     */
    public function boot(): bool
    {
        // 防止重复启动
        if ($this->booted) {
            return true;
        }

        try {
            // 触发插件启动前事件
            $this->doHook('plugin.before_boot', ['name' => $this->name]);
            // 加载插件配置
            $config = $this->getConfig();
            Config::set($config, "plugin.{$this->name}");

            // 注册插件路由
            if (file_exists(app()->getRootPath() . AppsName::APPS_NAME . "/{$this->name}/route.php")) {
                $this->registerRoutes();
            }
            
            $this->booted = true;
            // 触发插件启动后事件
            $this->doHook('plugin.after_boot', ['name' => $this->name]);
            return true;
        } catch (\Exception $e) {

            Log::error("App {$this->name} boot failed: " . $e->getMessage());
        }
        return false;
    }

    /**
     * 创建必要的目录
     */
    protected function createDirectories(): void
    {
        $pluginPath = AppsPath::APPS_PATH . "{$this->name}";
        $directories = [
            $pluginPath . '/src',
            $pluginPath . '/src/data',
            $pluginPath . '/views',
            $pluginPath . '/src/hooks',
            $pluginPath . '/src/controller',
            $pluginPath . '/src/lang',
            $pluginPath . '/vendor',
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * 清理资源
     */
    protected function cleanupResources(): void
    {
        // 清理可能的缓存文件或临时文件
        // 子类可以重写此方法实现更多的资源清理
    }

    /**
     * 执行安装SQL
     */
    protected function executeInstallSql(): void
    {
        $sqlFile = AppsPath::APPS_PATH . "{$this->name}/install.sql";
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            SqlHelper::parser($sql);
        }
    }

    /**
     * 执行卸载SQL
     */
    protected function executeUninstallSql(): void
    {
        $sqlFile = AppsPath::APPS_PATH . "{$this->name}/uninstall.sql";
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            SqlHelper::parser($sql);
        }
    }



    /**
     * 获取插件钩子配置
     * @return array
     */
    protected function getHookConfig(): array
    {
        $configFile = AppsPath::APPS_PATH . "{$this->name}/hooks.php";
        if (!file_exists($configFile)) {
            return [];
        }
        return include $configFile;
    }
}