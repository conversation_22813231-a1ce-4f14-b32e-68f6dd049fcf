<?php

namespace app\plugin\traits;

use think\facade\Lang;

trait LanguageTrait
{
    /**
     * 语言包目录
     * @var string
     */
    protected $langDir;

    /**
     * 当前语言
     * @var string
     */
    protected $currentLang;

    /**
     * 语言配置
     * @var array
     */
    protected $langConfig = [];

    /**
     * 加载语言包
     * @param string $lang 语言标识
     * @param string $module 模块名称
     * @return void
     */
    protected function loadLanguage(string $lang = null, string $module = ''): void
    {
        $this->currentLang = $lang ?: config('lang.default_lang');
        $this->langDir = $this->pluginDir . $this->name . '/lang/';

        // 加载manifest.json中的语言配置
        $manifestFile = $this->pluginDir . $this->name . '/manifest.json';
        if (file_exists($manifestFile)) {
            $manifest = json_decode(file_get_contents($manifestFile), true);
            if (isset($manifest['languages'])) {
                $this->langConfig = $manifest['languages'];
            }
        }

        // 检查语言包目录是否存在
        if (!is_dir($this->langDir)) {
            return;
        }

        // 确定要加载的语言文件路径
        $langPath = $this->langDir . $this->currentLang;
        if ($module) {
            $langPath .= '/' . $module;
        }

        // 加载语言包文件
        if (is_dir($langPath)) {
            // 如果是目录，加载目录下所有语言文件
            $files = glob($langPath . '/*.php');
            foreach ($files as $file) {
                Lang::load($file, $this->name);
            }
        } else {
            // 尝试加载单个语言文件
            $langFile = $langPath . '.php';
            if (file_exists($langFile)) {
                Lang::load($langFile, $this->name);
            }
        }
    }

    /**
     * 获取翻译
     * @param string $key 翻译键名
     * @param array $vars 变量替换
     * @param string $lang 语言
     * @return string
     */
    protected function trans(string $key, array $vars = [], string $lang = null): string
    {
        return Lang::get($key, $vars, $lang ?: $this->currentLang, $this->name);
    }

    /**
     * 设置当前语言
     * @param string $lang
     * @return void
     */
    public function setLanguage(string $lang): void
    {
        $this->loadLanguage($lang);
    }

    /**
     * 获取当前语言
     * @return string
     */
    public function getCurrentLanguage(): string
    {
        return $this->currentLang;
    }
}