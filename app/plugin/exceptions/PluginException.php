<?php

namespace app\plugin\exceptions;

/**
 * 插件异常类
 */
class PluginException extends \Exception
{
    /**
     * 插件名称
     * @var string
     */
    protected $pluginName;
    
    /**
     * 构造函数
     * @param string $message 异常信息
     * @param string $pluginName 插件名称
     * @param int $code 异常代码
     * @param \Throwable|null $previous 上一个异常
     */
    public function __construct(string $message, string $pluginName = '', int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->pluginName = $pluginName;
    }
    
    /**
     * 获取插件名称
     * @return string
     */
    public function getPluginName(): string
    {
        return $this->pluginName;
    }
}