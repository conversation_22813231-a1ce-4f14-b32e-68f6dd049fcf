<?php

namespace app\plugin\exceptions;

use app\plugin\exceptions\PluginException;
use think\facade\Log;

/**
 * 插件错误处理器
 */
class PluginErrorHandler
{
    /**
     * 处理插件异常
     * @param \Throwable $e 异常对象
     * @return array 错误信息数组
     */
    public static function handle(\Throwable $e): array
    {
        // 获取基本错误信息
        $errorInfo = [
            'code' => $e->getCode() ?: 500,
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'type' => get_class($e),
            'plugin_name' => ''
        ];

        // 如果是插件异常，添加插件名称
        if ($e instanceof PluginException) {
            $errorInfo['plugin_name'] = $e->getPluginName();
        }

        // 记录错误日志
        Log::error('Plugin Error: ' . json_encode($errorInfo, JSON_UNESCAPED_UNICODE));

        return $errorInfo;
    }

    /**
     * 格式化错误信息
     * @param array $errorInfo 错误信息数组
     * @return string 格式化后的错误信息
     */
    public static function formatError(array $errorInfo): string
    {
        $error = "错误信息:\n";
        $error .= "----------------------------------------\n";
        $error .= "错误类型: {$errorInfo['type']}\n";
        if (!empty($errorInfo['plugin_name'])) {
            $error .= "插件名称: {$errorInfo['plugin_name']}\n";
        }
        $error .= "错误信息: {$errorInfo['message']}\n";
        $error .= "文件位置: {$errorInfo['file']}\n";
        $error .= "错误行号: {$errorInfo['line']}\n";
        $error .= "----------------------------------------\n";
        $error .= "堆栈跟踪:\n{$errorInfo['trace']}";
        
        return $error;
    }
}