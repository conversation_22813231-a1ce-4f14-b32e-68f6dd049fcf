<?php

namespace app\plugin\exceptions;

/**
 * 插件依赖异常类
 */
class PluginDependencyException extends PluginException
{
    /**
     * 依赖插件名称
     * @var string
     */
    protected $dependencyName;
    
    /**
     * 构造函数
     * @param string $message 异常信息
     * @param string $pluginName 插件名称
     * @param string $dependencyName 依赖插件名称
     * @param int $code 异常代码
     * @param \Throwable|null $previous 上一个异常
     */
    public function __construct(string $message, string $pluginName = '', string $dependencyName = '', int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $pluginName, $code, $previous);
        $this->dependencyName = $dependencyName;
    }
    
    /**
     * 获取依赖插件名称
     * @return string
     */
    public function getDependencyName(): string
    {
        return $this->dependencyName;
    }
}