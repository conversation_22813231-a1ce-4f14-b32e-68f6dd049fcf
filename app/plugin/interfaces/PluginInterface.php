<?php

namespace app\plugin\interfaces;

/**
 * 插件接口
 * 所有插件必须实现此接口
 */
interface PluginInterface
{
    /**
     * 获取插件信息
     * @return array
     */
    public function getInfo(): array;

    /**
     * 安装插件
     * @return bool
     */
    public function install(): bool;

    /**
     * 卸载插件
     * @return bool
     */
    public function uninstall(): bool;

    /**
     * 启用插件
     * @return bool
     */
    public function enable(): bool;

    /**
     * 禁用插件
     * @return bool
     */
    public function disable(): bool;

    /**
     * 获取插件配置
     * @return array
     */
    public function getConfig(): array;

    /**
     * 设置插件配置
     * @param array $config
     * @return bool
     */
    public function setConfig(array $config): bool;
} 