<?php

namespace app\plugin\core;

use app\plugin\constant\AppsName;
use app\plugin\constant\AppsPath;
use think\facade\Log;
use think\facade\Route;

/**
 * 插件路由管理器
 */
class PluginRoute
{
    /**
     * 插件名称
     * @var string
     */
    protected $name;

    /**
     * 路由配置路径
     * @var string
     */
    protected $routePath;

    /**
     * 构造函数
     * @param string $name 插件名称
     */
    public function __construct(string $name)
    {
        $this->name = $name;
        $this->routePath = AppsPath::APPS_PATH . "{$name}/route.php";
    }

    /**
     * 注册路由
     * @return void
     * @throws \Exception
     */
    public function register(): void
    {
        if (file_exists($this->routePath)) {
            try {
                $routes = include $this->routePath;
                if (!is_array($routes)) {
                    Log::warning("Invalid route configuration: {$this->routePath}, expected array but got " . gettype($routes));
                    return;
                }
                $this->registerRoutes($routes);
            } catch (\Exception $e) {
                // 记录错误日志
                error_log("App route registration failed: " . $e->getMessage());
                // 重新抛出异常以中断执行
            }
        }
    }

    /**
     * 注销路由
     * @return void
     */
    public function unregister(): void
    {
        $pluginPrefix = AppsName::APPS_NAME . '/' . $this->name;
        // 删除插件相关的所有路由
        Route::group($pluginPrefix, function () use ($pluginPrefix) {
            // 获取当前路由组中的所有路由规则
            $rules = Route::getRuleList();
            // 遍历并删除每个路由规则
            foreach ($rules as $rule => $ruleItem) {
                if (strpos($rule, $pluginPrefix) === 0) {
                    Route::delete($rule);
                }
            }
        });
    }

    /**
     * 注册路由规则
     * @param array $routes
     * @return void
     */
    public function registerRoutes(array $routes): void
    {
        // 使用统一的插件路由前缀
        $pluginPrefix = 'apps/' . $this->name;

        // 收集所有路由配置
        $collectedRoutes = [
            'groups' => [],    // 存储所有分组路由
            'aliases' => [],   // 存储所有别名路由
            'routes' => []     // 存储所有独立路由
        ];

        // 收集分组路由
        if (isset($routes['group'])) {
            if (is_array($routes['group'])) {
                $collectedRoutes['groups'][] = $routes['group'];
            }
        }
        // 支持多个分组路由
        foreach ($routes as $key => $value) {
            if (strpos($key, 'group_') === 0 && is_array($value)) {
                $collectedRoutes['groups'][] = $value;
            }
        }

        // 收集别名路由
        if (isset($routes['aliases']) && is_array($routes['aliases'])) {
            $collectedRoutes['aliases'] = $routes['aliases'];
        }

        // 收集独立路由
        if (isset($routes['routes']) && is_array($routes['routes'])) {
            $collectedRoutes['routes'] = $routes['routes'];
        }

        // 创建主路由组并注册所有路由
        Route::group($pluginPrefix, function () use ($collectedRoutes) {
            // 1. 首先注册分组路由
            foreach ($collectedRoutes['groups'] as $group) {
                $prefix = $group['prefix'] ?? '';
                $middleware = $group['middleware'] ?? [];
                $groupPrefix = ltrim($prefix, '/');

                if (!empty($groupPrefix)) {
                    Route::group($groupPrefix, function () use ($group) {
                        if (isset($group['routes']) && is_array($group['routes'])) {
                            foreach ($group['routes'] as $rule => $config) {
                                $this->registerRouteRule($rule, $config);
                            }
                        }
                    })->middleware($middleware);
                } else {
                    if (isset($group['routes']) && is_array($group['routes'])) {
                        foreach ($group['routes'] as $rule => $config) {
                            $this->registerRouteRule($rule, $config);
                        }
                    }
                }
            }

            // 2. 注册别名路由组
            foreach ($collectedRoutes['aliases'] as $alias => $groupConfig) {
                if (!is_array($groupConfig)) continue;

                $prefix = $groupConfig['prefix'] ?? '';
                $middleware = $groupConfig['middleware'] ?? [];
                $groupPrefix = ltrim($prefix, '/');

                if (!empty($groupPrefix)) {
                    Route::group($groupPrefix, function () use ($groupConfig) {
                        if (isset($groupConfig['routes']) && is_array($groupConfig['routes'])) {
                            foreach ($groupConfig['routes'] as $rule => $config) {
                                $this->registerRouteRule($rule, $config);
                            }
                        }
                    })->middleware($middleware);
                } else {
                    if (isset($groupConfig['routes']) && is_array($groupConfig['routes'])) {
                        foreach ($groupConfig['routes'] as $rule => $config) {
                            $this->registerRouteRule($rule, $config);
                        }
                    }
                }
            }

            // 3. 最后注册独立路由
            foreach ($collectedRoutes['routes'] as $rule => $config) {
                if (strpos($rule, '@') === 0) {
                    $alias = substr($rule, 1);
                    $aliasConfig = $collectedRoutes['aliases'][$alias] ?? null;

                    if ($aliasConfig && is_array($aliasConfig)) {
                        $prefix = $aliasConfig['prefix'] ?? '';
                        $middleware = $aliasConfig['middleware'] ?? [];
                        $groupPrefix = ltrim($prefix, '/');

                        if (!empty($groupPrefix)) {
                            Route::group($groupPrefix, function () use ($config) {
                                foreach ($config as $subRule => $subConfig) {
                                    $this->registerRouteRule($subRule, $subConfig);
                                }
                            })->middleware($middleware);
                        } else {
                            foreach ($config as $subRule => $subConfig) {
                                $this->registerRouteRule($subRule, $subConfig);
                            }
                        }
                    } else {
                        $this->registerRouteRule($rule, $config);
                    }
                } else {
                    $this->registerRouteRule($rule, $config);
                }
            }
        });
    }

    /**
     * 注册路由规则
     * @param mixed $rule 路由规则
     * @param mixed $config 路由配置
     * @return void
     */
    protected function registerRouteRule($rule, $config): void
    {
        // 处理数字索引的详细路由定义
        if (is_numeric($rule) && is_array($config)) {
            $method = $config['method'] ?? '*';
            $routeRule = $config['rule'] ?? '';
            $controller = $config['controller'] ?? '';
            $action = $config['action'] ?? '';
            $middleware = $config['middleware'] ?? [];
            $name = $config['name'] ?? '';
            
            // 处理点号分隔的控制器路径（如 open.Developer）
            if (strpos($controller, '.') !== false) {
                $controllerParts = explode('.', $controller);
                // 在控制器路径中插入controller层
                $lastPart = array_pop($controllerParts);
                $controllerParts[] = 'controller';
                $controllerParts[] = $lastPart;
                $controller = implode('\\', $controllerParts);
            }

            // 检查控制器类型
            if ($controller instanceof \Closure) {
                // 如果是闭包函数，直接使用
                $route = Route::rule($routeRule, $controller)->method($method);
            } else if (class_exists($controller)) {
                $route = Route::rule($routeRule, "{$controller}@{$action}");
            } else {
                // 处理字符串类型的控制器
                if (!empty($controller)) {
                    if (strpos($controller, '\\') === false) {
                        // 尝试构建插件控制器完整命名空间
                        $fullController = AppsName::APPS_NAME . "\\{$this->name}\\src\\controller\\{$controller}";
                        if (class_exists($fullController)) {
                            $controller = $fullController;
                        } else {
                            $controller = AppsName::APPS_NAME . "\\{$this->name}\\controller\\{$controller}";
                        }
                    }
                    $route = Route::rule($routeRule, "{$controller}@{$action}")->method($method);
                } else {
                    // 尝试构建插件控制器完整命名空间
                    $fullController = AppsName::APPS_NAME . "\\{$this->name}\\src\\{$controller}";
                    if (class_exists($fullController)) {
                        $controller = $fullController;
                    } else {
                        $controller = AppsName::APPS_NAME . "\\{$this->name}\\{$controller}";
                    }
                    $route = Route::rule($rule, "{$controller}@{$action}")->method($method);
                }
            }
            
            // 添加中间件
            if (!empty($middleware) && isset($route)) {
                $route->middleware($middleware);
            }

            // 添加路由名称
            if (!empty($name) && isset($route)) {
                $route->name($name);
            }
        } 
        // 处理简单路由定义
        else if (is_string($config)) {
            // 优先检查是否包含@分隔符
            if (strpos($config, '@') !== false) {
                list($controller, $action) = explode('@', $config);
            } 
            // 其次检查是否包含/分隔符
            elseif (strpos($config, '/') !== false) {
                list($controller, $action) = explode('/', $config);
            }
            // 无法解析的格式
            else {
                Log::warning("Invalid route format: {$config}");
                return;
            }

            // 处理点号分隔的控制器路径（如 admin.Platform）
            if (strpos($controller, '.') !== false) {
                $controllerParts = explode('.', $controller);
                // 在控制器路径中插入controller层
                $lastPart = array_pop($controllerParts);
                $controllerParts[] = 'controller';
                $controllerParts[] = $lastPart;
                $controller = implode('\\', $controllerParts);
            }

            // 检查控制器类型
            if (class_exists($controller)) {
                Route::rule($rule, "{$controller}@{$action}");
            } else if ($controller instanceof \Closure) {
                // 如果是闭包函数，直接使用
                Route::rule($rule, $controller);
            } else if (strpos($controller, '\\') === false) {
                // 尝试构建插件控制器完整命名空间
                $fullController = AppsName::APPS_NAME . "\\{$this->name}\\src\\controller\\{$controller}";
                if (class_exists($fullController)) {
                    $controller = $fullController;
                } else {
                    $controller = AppsName::APPS_NAME . "\\{$this->name}\\controller\\{$controller}";
                }
                Route::rule($rule, "{$controller}@{$action}");
            } else {
                // 尝试构建插件控制器完整命名空间，支持多级目录
                $fullController = AppsName::APPS_NAME . "\\{$this->name}\\src\\{$controller}";
                if (class_exists($fullController)) {
                    $controller = $fullController;
                } else {
                    $controller = AppsName::APPS_NAME . "\\{$this->name}\\{$controller}";
                }
                Route::rule($rule, "{$controller}@{$action}");
            }
        } else if ($config instanceof \Closure) {
            // 如果是闭包函数，直接使用
            Route::rule($rule, $config);
        }
    }
}