<?php

namespace app\plugin\core;

use think\facade\Log;

/**
 * 钩子管理器
 */
class HookManager
{
    /**
     * 钩子列表
     * @var array
     */
    protected $hooks = [];

    /**
     * 插件生命周期事件
     * @var array
     */
    protected const PLUGIN_EVENTS = [
        'plugin.before_install' => '插件安装前',
        'plugin.install' => '插件安装',
        'plugin.before_uninstall' => '插件卸载前',
        'plugin.uninstall' => '插件卸载',
        'plugin.before_enable' => '插件启用前',
        'plugin.enable' => '插件启用',
        'plugin.before_disable' => '插件禁用前',
        'plugin.disable' => '插件禁用',
        'plugin.init' => '插件初始化',
        'plugin.before_boot' => '插件启动前',
        'plugin.after_boot' => '插件启动后',
        'plugin.before_unregister_hooks' => '插件钩子注销前',
        'plugin.after_unregister_hooks' => '插件钩子注销后'
    ];

    public function init(): void {
    }
    
    /**
     * 添加钩子
     * @param string $hook 钩子名称
     * @param callback $callback 回调函数
     * @param int $priority 优先级，数字越小优先级越高
     * @param string $plugin 插件名称
     * @return void
     */
    public function add(string $hook, callable $callback, int $priority = 10, string $plugin = ''): void
    {
        // 验证钩子名称
        if (empty($hook)) {
            Log::error($hook . '钩子名称不能为空');
            return;
        }

        // 验证回调函数
        if (!is_callable($callback)) {
            Log::error( '无效的回调函数: hook:' . $hook . ', callback: ' . $callback);
            return;
        }

        try {

            // 初始化钩子数组
            if (!isset($this->hooks[$hook])) {
                $this->hooks[$hook] = [];
            }

            // 添加钩子
            $this->hooks[$hook][] = [
                'callback' => $callback,
                'priority' => $priority,
                'plugin' => $plugin
            ];

            // 按优先级排序
            $this->sortHooks($hook);

        } catch (\InvalidArgumentException $e) {
            // 记录参数验证错误
            $errorMessage = sprintf(
                "参数验证失败: %s\n钩子名称: %s\n插件名称: %s\n优先级: %d",
                $e->getMessage(),
                $hook,
                $plugin ?: '系统',
                $priority
            );
            Log::error($errorMessage);
        } catch (\Throwable $e) {
            // 记录其他类型错误
            $errorMessage = sprintf(
                "添加钩子时发生错误: %s\n错误类型: %s\n钩子名称: %s\n插件名称: %s\n优先级: %d\n堆栈跟踪:\n%s",
                $e->getMessage(),
                get_class($e),
                $hook,
                $plugin ?: '系统',
                $priority,
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
        }
    }
    
    /**
     * 移除钩子
     * @param string $hook 钩子名称
     * @param string $plugin 插件名称
     * @return void
     */
    public function remove(string $hook, string $plugin): void
    {
        if (!isset($this->hooks[$hook])) {
            return;
        }
        
        foreach ($this->hooks[$hook] as $key => $item) {
            if ($item['plugin'] === $plugin) {
                unset($this->hooks[$hook][$key]);
            }
        }
        
        // 重新索引数组
        $this->hooks[$hook] = array_values($this->hooks[$hook]);
    }
    
    /**
     * 执行钩子
     * @param string $hook 钩子名称
     * @param mixed $params 参数
     * @param string|null $plugin 插件名称，如果指定则只执行该插件的钩子
     * @return mixed
     */
    public function execute(string $hook, mixed $params = [], ?string $plugin = null)
    {
        if (!isset($this->hooks[$hook])) {
            return null;
        }

        $results = [];
        foreach ($this->hooks[$hook] as $item) {
            try {
                if (!isset($item['callback']) || !is_callable($item['callback'])) {
                    Log::error("钩子 {$hook} 的回调函数无效");
                    continue;
                }
                
                // 如果指定了插件名称，则只执行该插件的钩子
                if ($plugin !== null && $item['plugin'] !== $plugin) {
                    continue;
                }
                
                $results[] = call_user_func_array($item['callback'], [$params]);
            } catch (\Exception $e) {
                Log::error("执行钩子 {$hook} 失败: " . $e->getMessage());
            }
        }

        return $results;
    }
    
    /**
     * 执行特定插件的钩子
     * @param string $hook 钩子名称
     * @param string $plugin 插件名称
     * @param mixed $params 参数
     * @return mixed
     */
    public function executePluginHook(string $hook, string $plugin, mixed $params = [])
    {
        return $this->execute($hook, $params, $plugin);
    }
    
    /**
     * 检查是否为有效的插件事件
     * @param string $hook 钩子名称
     * @return bool
     */
    public function isPluginEvent(string $hook): bool
    {
        return array_key_exists($hook, self::PLUGIN_EVENTS);
    }

    /**
     * 检查钩子是否存在
     * @param string $hook 钩子名称
     * @return bool
     */
    public function has(string $hook): bool
    {
        return isset($this->hooks[$hook]) && !empty($this->hooks[$hook]);
    }
    
    /**
     * 获取钩子列表
     * @return array
     */
    public function getHooks(): array
    {
        return $this->hooks;
    }
    
    /**
     * 按优先级排序钩子
     * @param string $hook 钩子名称
     * @return void
     */
    protected function sortHooks(string $hook): void
    {
        if (!isset($this->hooks[$hook])) {
            return;
        }
        
        usort($this->hooks[$hook], function($a, $b) {
            return $a['priority'] - $b['priority'];
        });
    }
}