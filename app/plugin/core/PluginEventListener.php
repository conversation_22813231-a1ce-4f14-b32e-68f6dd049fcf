<?php

namespace app\plugin\core;

use think\facade\Log;
use app\plugin\traits\HookTrait;

/**
 * 插件事件监听器
 */
class PluginEventListener
{    
    use HookTrait;
    
    /**
     * 插件名称
     * @var string
     */
    protected $pluginName;
    
    /**
     * 构造函数
     * @param string $pluginName 插件名称
     */
    public function __construct(string $pluginName = '')
    {
        // 设置插件名称
        $this->pluginName = $pluginName;
        
        // 设置HookTrait中的name属性
        if (empty($this->name)) {
            $this->name = $pluginName ?: 'system';
        }
        
        // 注册标准事件监听器
        $this->registerEventListeners();
        
        // 加载插件特定的钩子配置
        $this->loadPluginHooks();
    }
    
    /**
     * 加载插件特定的钩子配置
     */
    protected function loadPluginHooks(): void
    {
        try {
            // 如果有指定插件名称，尝试加载插件特定的钩子配置
            if (!empty($this->pluginName)) {
                $hooksFile = __APPS_PATH__ . $this->pluginName . DIRECTORY_SEPARATOR . 'hooks.php';

                if (file_exists($hooksFile)) {
                    $hooks = include $hooksFile;
                    if (empty($hooks) || !is_array($hooks)) {
                        Log::warning("Invalid or empty hook configuration in plugin {$this->pluginName}");
                        return;
                    }
                    
                    // 遍历注册钩子
                    foreach ($hooks as $hook => $config) {
                        if (!is_string($hook)) {
                            Log::warning("Invalid hook name in plugin {$this->pluginName}");
                            continue;
                        }

                        // 处理不同类型的监听器配置
                        $priority = 0;
                        $listener = $config;

                        // 如果配置是数组且包含优先级设置
                        if (is_array($config) && isset($config['handler'])) {
                            $listener = $config['handler'];
                            $priority = isset($config['priority']) && is_int($config['priority']) ? $config['priority'] : 0;
                        }

                        // 处理不同类型的监听器
                        try {
                            if (is_string($listener)) {
                                // 字符串方法名，将当前插件实例作为上下文
                                if (!method_exists($this, $listener)) {
                                    Log::warning("Method {$listener} does not exist in plugin {$this->pluginName}");
                                    continue;
                                }
                                $callback = [$this, $listener];
                            } elseif (is_array($listener) && isset($listener[0]) && is_string($listener[0])) {
                                // 数组形式的回调，第一个元素为类名
                                $className = $listener[0];
                                if (!class_exists($className)) {
                                    Log::warning("Class {$className} does not exist for hook {$hook} in plugin {$this->pluginName}");
                                    continue;
                                }
                                $methodName = $listener[1] ?? '__invoke';
                                $listenerInstance = new $className();
                                if (!method_exists($listenerInstance, $methodName)) {
                                    Log::warning("Method {$methodName} does not exist in class {$className}");
                                    continue;
                                }
                                $callback = [$listenerInstance, $methodName];
                            } elseif (is_callable($listener)) {
                                // 可调用的回调函数
                                $callback = $listener;
                            } else {
                                Log::warning("Invalid hook listener configuration for {$hook} in plugin {$this->pluginName}");
                                continue;
                            }

                            // 注册钩子事件
                            $this->addHook($hook, $callback, $priority);
                        } catch (\Exception $e) {
                            Log::error("Failed to register hook {$hook} in plugin {$this->pluginName}: " . $e->getMessage());
                        }
                    }
                    
                    Log::info("已加载插件[{$this->pluginName}]的钩子配置");
                }
            }
        } catch (\Exception $e) {
            Log::error("加载插件钩子配置失败: {$e->getMessage()}");
        }
    }
    
    /**
     * 注册事件监听器
     */
    public function registerEventListeners(): void
    {
        try {
            if (empty($this->name)) {
                Log::error("App name is not initialized");
                return;
            }

                $this->addHook('plugin.before_install', [$this, 'onBeforeInstall']);
            $this->addHook('plugin.install', [$this, 'onInstall']);
            $this->addHook('plugin.before_uninstall', [$this, 'onBeforeUninstall']);
            $this->addHook('plugin.uninstall', [$this, 'onUninstall']);
            $this->addHook('plugin.before_enable', [$this, 'onBeforeEnable']);
            $this->addHook('plugin.enable', [$this, 'onEnable']);
            $this->addHook('plugin.before_disable', [$this, 'onBeforeDisable']);
            $this->addHook('plugin.disable', [$this, 'onDisable']);
            $this->addHook('plugin.config.update', [$this, 'onConfigUpdate']);
            $this->addHook('plugin.resource.publish', [$this, 'onResourcePublish']);
            $this->addHook('plugin.upgrade', [$this, 'onUpgrade']);
            $this->addHook('plugin.before_boot', [$this, 'onBeforeBoot']);
            $this->addHook('plugin.after_boot', [$this, 'onAfterBoot']);
            
            Log::info("插件[{$this->name}]事件监听器注册完成");
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "注册事件监听器失败: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $this->name ?? '未指定',
                $e->getTraceAsString()
            );
            
            Log::error($errorMessage);
        }
    }
    /**
     * 插件安装前事件
     * @param array $params 参数
     * @return bool
     */
    public function onBeforeInstall(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件安装前事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件安装前事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }

    /**
     * 插件卸载前事件
     * @param array $params 参数
     * @return bool
     */
    public function onBeforeUninstall(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件卸载前事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件卸载前事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }
    /**
     * 插件安装事件
     * @param array $params 参数
     * @return void
     */
    public function onInstall(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件安装事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件安装事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }
    
    /**
     * 插件卸载事件
     * @param array $params 参数
     * @return void
     */
    public function onUninstall(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件卸载事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件卸载事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }
    
    /**
     * 插件启用事件
     * @param array $params 参数
     * @return void
     */
    public function onEnable(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件启用事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件启用事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }
    
    /**
     * 插件禁用事件
     * @param array $params 参数
     * @return void
     */
    public function onDisable(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件禁用事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件禁用事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }

    /**
     * 插件启用前事件
     * @param array $params 参数
     * @return bool
     */
    public function onBeforeEnable(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件启用前事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件启用前事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }

    /**
     * 插件禁用前事件
     * @param array $params 参数
     * @return bool
     */
    public function onBeforeDisable(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件禁用前事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件禁用前事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }

    /**
     * 插件配置更新事件
     * @param array $params 参数
     * @return bool
     */
    public function onConfigUpdate(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件配置更新事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件配置更新事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }

    /**
     * 插件资源发布事件
     * @param array $params 参数
     * @return bool
     */
    public function onResourcePublish(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件资源发布事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件资源发布事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }

    /**
     * 插件升级事件
     * @param array $params 参数
     * @return bool
     */
    public function onUpgrade(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件升级事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件升级事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }

    /**
     * 插件启动前事件
     * @param array $params 参数
     * @return bool
     */
    public function onBeforeBoot(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件启动前事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件启动前事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }

    /**
     * 插件启动后事件
     * @param array $params 参数
     * @return bool
     */
    public function onAfterBoot(array $params): bool
    {
        try {
            $pluginName = $params['name'] ?? $this->pluginName ?? '';
            $time = date('Y-m-d H:i:s');
            Log::info("[{$time}] 插件启动后事件触发 - 插件名称: {$pluginName}, 参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            return true;
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "插件启动后事件异常: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $params['name'] ?? $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );
            Log::error($errorMessage);
            return false;
        }
    }
}