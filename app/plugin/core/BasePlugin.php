<?php

namespace app\plugin\core;

use app\plugin\constant\AppsName;
use app\plugin\constant\AppsPath;
use app\plugin\interfaces\PluginInterface;
use app\plugin\traits\HookTrait;
use app\plugin\traits\ConfigTrait;
use app\plugin\traits\RouteTrait;
use app\plugin\traits\DependencyTrait;
use app\plugin\traits\LifecycleTrait;
use app\plugin\traits\UpgradeTrait;
use app\plugin\traits\LanguageTrait;
use think\facade\Db;
use think\facade\Log;

/**
 * 插件基类
 */
abstract class BasePlugin implements PluginInterface
{
    use HookTrait, ConfigTrait, RouteTrait, DependencyTrait, LifecycleTrait, UpgradeTrait, LanguageTrait;
    
    /**
     * 插件名称
     * @var string
     */
    protected $name;

    /**
     * 插件版本
     * @var string
     */
    protected $version;

    /**
     * 插件作者
     * @var string
     */
    protected $author;

    /**
     * 插件标题
     * @var string
     */
    protected $title;

    /**
     * 插件描述
     * @var string
     */
    protected $description;

    /**
     * 构造函数
     */
    public function __construct()
    {
        try {
            $this->pluginDir = AppsPath::APPS_PATH;
            // 从类名中解析出插件名称
            $className = get_class($this);
            $parts = explode('\\', $className);
            // 插件类都在plugins目录下，格式为: apps\pluginName\App
            $this->name = $parts[1] ?? '';
            if (empty($this->name)) {
                Log::warning('Unable to determine plugin name from class: ' . $className);
                return;
            }
            $this->init();
            $this->configInstance = new PluginConfig($this->name);
            $this->routeInstance = new PluginRoute($this->name);
            $this->loadLanguage();
        } catch (\Exception $e) {
            throw new \app\plugin\exceptions\PluginException(
                $e->getMessage(),
                $this->name,
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * 初始化插件
     */
    protected function init(): void
    {
        $manifestPath = $this->pluginDir . $this->name . '/manifest.json';
        if (!file_exists($manifestPath)) {
            Log::warning('App manifest file not found');
            return;
        }

        $manifest = json_decode(file_get_contents($manifestPath), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::warning('Invalid plugin manifest file');
            return;
        }

        // 校验必需字段
        $requiredFields = ['name', 'version', 'author', 'title', 'description'];
        foreach ($requiredFields as $field) {
            if (!isset($manifest[$field]) || empty($manifest[$field])) {
                Log::warning("Required field '{$field}' is missing or empty in manifest file");
                return;
            }
            if (!is_string($manifest[$field])) {
                Log::warning("Field '{$field}' must be a string in manifest file");
                return;
            }
        }

        // 校验可选字段
        if (isset($manifest['dependencies']) && !is_array($manifest['dependencies'])) {
            Log::warning("Field 'dependencies' must be an array in manifest file");
            return;
        }
        if (isset($manifest['config']) && !is_array($manifest['config'])) {
            Log::warning("Field 'config' must be an array in manifest file");
            return;
        }
        if (isset($manifest['hooks']) && !is_array($manifest['hooks'])) {
            Log::warning("Field 'hooks' must be an array in manifest file");
            return;
        }

        $this->name = $manifest['name'];
        $this->version = $manifest['version'];
        $this->author = $manifest['author'];
        $this->title = $manifest['title'];
        $this->description = $manifest['description'];
        $this->dependencies = $manifest['dependencies'] ?? [];
        $this->config = $manifest['config'] ?? [];
        $this->upgrade = $manifest['upgrade'] ?? [];

        // 注册钩子
        if (isset($manifest['hooks']) && is_array($manifest['hooks'])) {
            foreach ($manifest['hooks'] as $hookName => $hookInfo) {
                if (!is_array($hookInfo) || !isset($hookInfo['description'])) {
                    Log::warning("Invalid hook configuration for '{$hookName}'");
                    return;
                }

                // 验证钩子参数配置
                if (isset($hookInfo['params']) && !is_array($hookInfo['params'])) {
                    Log::warning("Hook params must be an array for '{$hookName}'");
                    return;
                }
                
                // 获取钩子处理器类
                $handlerClass = isset($hookInfo['handler']) ? $hookInfo['handler'] : '\\' . AppsName::APPS_NAME . '\\' . $this->name . '\\src\\hooks\\HookHandlers';
                if (!class_exists($handlerClass)) {
                    Log::warning("Hook handler class '{$handlerClass}' not found");
                    return;
                }

                // 获取处理方法名
                $methodName = isset($hookInfo['method']) ? $hookInfo['method'] : 'on' . str_replace('.', '', ucwords($hookName, '.'));
                if (!method_exists($handlerClass, $methodName)) {
                    Log::warning("Hook handler method '{$methodName}' not found in class '{$handlerClass}'");
                    return;
                }

                // 注册钩子回调函数
                $this->addHook($hookName, function($params) use ($handlerClass, $methodName, $hookName, $hookInfo) {
                    // 验证必需参数
                    if (isset($hookInfo['params'])) {
                        foreach ($hookInfo['params'] as $paramName => $paramDesc) {
                            if (!isset($params[$paramName])) {
                                Log::warning("Missing required parameter '{$paramName}' for hook '{$hookName}'");
                                return;
                            }
                        }
                    }

                    // 创建处理器实例并调用方法
                    $handler = new $handlerClass();
                    return $handler->$methodName($params);
                });
            }
        }
        
        // 加载插件composer依赖
        $this->loadComposerDependencies();
    }

    /**
     * 加载插件Composer依赖
     */
    protected function loadComposerDependencies(): void
    {
        $composerFile = $this->pluginDir . $this->name . '/composer.json';

        if (file_exists($composerFile)) {
//            if (!file_exists($vendorAutoload)) {
//                $pluginPath = $this->pluginDir . $this->name;
//
//                // 使用完整的PHP和Composer路径
//                $phpPath = '/opt/homebrew/opt/php@8.3/bin/php';
//                $composerPath = '/usr/local/bin/composer';
//                $command = "cd {$pluginPath} && {$phpPath} {$composerPath} install --no-dev 2>&1";
//
//                exec($command, $output, $returnCode);
//
//                if ($returnCode !== 0) {
//                    $errorMsg = implode("\n", $output);
//                    throw new \RuntimeException(
//                        "Failed to install composer dependencies for plugin {$this->name}: {$errorMsg}"
//                    );
//                }
//
//                if (!file_exists($vendorAutoload)) {
//                    throw new \RuntimeException(
//                        "Composer install completed but vendor/autoload.php not found for plugin {$this->name}"
//                    );
//                }
//            }

        }
    }

    /**
     * 获取插件信息
     * @return array
     */
    public function getInfo(): array
    {
        return [
            'name' => $this->name,
            'version' => $this->version,
            'author' => $this->author,
            'title' => $this->title,
            'description' => $this->description,
            'dependencies' => $this->dependencies,
            'config' => $this->config,
            'installed' => $this->isInstalled(),
            'upgrade' => $this->upgrade,
        ];
    }

    /**
     * 检查插件是否已安装
     * @return bool
     */
    public function isInstalled(): bool
    {
        // 检查插件目录是否存在
        $pluginPath = $this->pluginDir . $this->name;
        if (!is_dir($pluginPath)) {
            return false;
        }

        // 检查manifest.json文件是否存在且有效
        $manifestPath = $pluginPath . '/manifest.json';
        if (!file_exists($manifestPath)) {
            return false;
        }

        try {
            $manifest = json_decode(file_get_contents($manifestPath), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }

        // 检查数据库中是否存在插件配置表
        try {
            $tableName = 'plugin_' . $this->name . '_config';
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($result);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取插件名称
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }
}