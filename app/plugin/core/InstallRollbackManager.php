<?php

namespace app\plugin\core;

use app\plugin\constant\AppsPath;
use think\facade\Db;
use think\facade\Log;

/**
 * 插件安装回滚管理器
 */
class InstallRollbackManager
{
    /**
     * 插件名称
     * @var string
     */
    protected $pluginName;

    /**
     * 已执行的安装步骤
     * @var array
     */
    protected $executedSteps = [];

    /**
     * 构造函数
     * @param string $pluginName 插件名称
     */
    public function __construct(string $pluginName)
    {
        $this->pluginName = $pluginName;
    }

    /**
     * 记录执行的安装步骤
     * @param string $step 步骤名称
     */
    public function recordStep(string $step): void
    {
        $this->executedSteps[] = $step;
    }

    /**
     * 执行回滚
     */
    public function rollback(): void
    {
        try {
            // 按照安装步骤的相反顺序执行回滚
            $steps = array_reverse($this->executedSteps);
            foreach ($steps as $step) {
                $method = 'rollback' . ucfirst($step);
                if (method_exists($this, $method)) {
                    $this->$method();
                }
            }
            Log::info("Plugin {$this->pluginName} installation rollback completed successfully");
        } catch (\Exception $e) {
            Log::error("Plugin {$this->pluginName} installation rollback failed: " . $e->getMessage());
        }
    }

    /**
     * 回滚配置表创建
     */
    protected function rollbackConfigTable(): void
    {
        try {
            $tableName = config('database.prefix') . 'plugin_' . strtolower($this->pluginName) . '_config';
            if (Db::query("SHOW TABLES LIKE '{$tableName}'")) {
                Db::execute("DROP TABLE IF EXISTS `{$tableName}`");
            }
        } catch (\Exception $e) {
            Log::error("Failed to rollback config table for plugin {$this->pluginName}: " . $e->getMessage());
        }
    }

    /**
     * 回滚SQL安装
     */
    protected function rollbackInstallSql(): void
    {
        try {
            // 执行卸载SQL（如果存在）
            $uninstallFile = AppsPath::APPS_PATH . "{$this->pluginName}/uninstall.sql";
            if (file_exists($uninstallFile)) {
                $sql = file_get_contents($uninstallFile);
                if (!empty($sql)) {
                    SqlHelper::parser($sql);
                }
            }
        } catch (\Exception $e) {
            Log::error("Failed to rollback SQL installation for plugin {$this->pluginName}: " . $e->getMessage());
        }
    }

    /**
     * 回滚目录创建
     */
    protected function rollbackDirectories(): void
    {
        try {
            $pluginPath = AppsPath::APPS_PATH . "{$this->pluginName}";
            $directories = [
                $pluginPath . '/src/data',
                $pluginPath . '/src/hooks',
                $pluginPath . '/src/controller',
                $pluginPath . '/src/lang',
                $pluginPath . '/vendor',
            ];

            foreach ($directories as $dir) {
                if (is_dir($dir) && count(scandir($dir)) <= 2) { // 只删除空目录
                    rmdir($dir);
                }
            }

            // 尝试删除父目录（如果为空）
            $parentDirs = [
                $pluginPath . '/src',
                $pluginPath . '/views'
            ];
            foreach ($parentDirs as $dir) {
                if (is_dir($dir) && count(scandir($dir)) <= 2) {
                    rmdir($dir);
                }
            }
        } catch (\Exception $e) {
            Log::error("Failed to rollback directories for plugin {$this->pluginName}: " . $e->getMessage());
        }
    }

    /**
     * 回滚Composer依赖安装
     */
    protected function rollbackComposerDependencies(): void
    {
        try {
            $vendorDir = AppsPath::APPS_PATH . "{$this->pluginName}/vendor";
            $composerLock = AppsPath::APPS_PATH . "{$this->pluginName}/composer.lock";

            // 删除vendor目录
            if (is_dir($vendorDir)) {
                $this->removeDirectory($vendorDir);
            }

            // 删除composer.lock文件
            if (file_exists($composerLock)) {
                unlink($composerLock);
            }
        } catch (\Exception $e) {
            Log::error("Failed to rollback composer dependencies for plugin {$this->pluginName}: " . $e->getMessage());
        }
    }

    /**
     * 回滚默认配置保存
     */
    protected function rollbackDefaultConfig(): void
    {
        try {
            $tableName = config('database.prefix') . 'plugin_' . strtolower($this->pluginName) . '_config';
            if (Db::query("SHOW TABLES LIKE '{$tableName}'")) {
                Db::table($tableName)->where('1=1')->delete();
            }
        } catch (\Exception $e) {
            Log::error("Failed to rollback default config for plugin {$this->pluginName}: " . $e->getMessage());
        }
    }

    /**
     * 递归删除目录
     * @param string $dir 目录路径
     */
    protected function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }
}