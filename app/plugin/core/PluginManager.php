<?php

namespace app\plugin\core;

use app\plugin\constant\AppsName;
use app\plugin\constant\AppsPath;
use app\plugin\interfaces\PluginInterface;
use think\facade\Log;

/**
 * 插件管理器
 */
class PluginManager
{
    /**
     * 需要过滤的系统目录
     * @var array
     */
    protected $filterDirs = ['backups', 'temp'];

    /**
     * 插件目录
     * @var string
     */
    protected $pluginDir;

    /**
     * 已加载的插件
     * @var array
     */
    protected $plugins = [];
    
    /**
     * 已启用的插件
     * @var array
     */
    protected $enabledPlugins = [];
    
    /**
     * 事件监听器
     * @var PluginEventListener
     */
    protected $eventListener;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->pluginDir = AppsPath::APPS_PATH;
        if (!is_dir($this->pluginDir)) {
            mkdir($this->pluginDir, 0755, true);
        }
    }

    /**
     * 加载所有插件
     * @return array
     */
    public function loadPlugins(bool $enabled = true, ?string $targetPlugin = null): array
    {
        $pluginDirs = glob($this->pluginDir . '*', GLOB_ONLYDIR);
        $pluginList = [];
        // 先扫描插件目录，获取插件列表和配置信息
        foreach ($pluginDirs as $dir) {
            $pluginName = basename($dir);
            
            // 过滤系统目录
            if (in_array($pluginName, $this->filterDirs)) {
                continue;
            }
            
            $manifestFile = $dir . DIRECTORY_SEPARATOR . 'manifest.json';
            
            if (!file_exists($manifestFile)) {
                Log::warning("插件 {$pluginName} 的manifest.json文件不存在");
                continue;
            }
            
            try {
                $manifest = json_decode(file_get_contents($manifestFile), true);
                if (!$manifest) {
                    Log::warning("插件 {$pluginName} 的manifest.json文件格式错误");
                    continue;
                }

                $pluginList[$pluginName] = $manifest;
            } catch (\Exception $e) {
                Log::warning("读取插件 {$pluginName} 的manifest.json文件失败: " . $e->getMessage());
                continue;
            }
        }

        // 根据配置决定是否加载插件
        foreach ($pluginList as $pluginName => $manifest) {
            try {

                // 判断是否需要加载插件
                $shouldLoad = !$enabled ||
                             $pluginName === $targetPlugin ||
                             (isset($manifest['config']['global_startup']['value']) && $manifest['config']['global_startup']['value']);

                if ($shouldLoad) {
                    //加载自动注册
                    $this->loadComposerAutoload($pluginName);
                }

                if ($shouldLoad && $this->loadPlugin($pluginName)) {
                    if (!$enabled || (isset($manifest['config']['enabled']) && $manifest['config']['enabled'])) {
                        $this->enabledPlugins[$pluginName] = $this->plugins[$pluginName];
                    }
                }
            } catch (\Exception $e) {
                Log::error("加载插件 {$pluginName} 失败: " . $e->getMessage());
                // 跳过加载失败的插件而不是抛出异常
                continue;
            }
        }

        // 按照依赖关系排序插件
        $this->sortPluginsByDependencies();

        return $this->enabledPlugins;
    }

    protected function loadComposerAutoload(string $pluginName): void
    {
        $composerFile = $this->pluginDir . $pluginName . '/composer.json';
        $vendorAutoload = $this->pluginDir . $pluginName . '/vendor/autoload.php';

        if (file_exists($composerFile)) {
            if (file_exists($vendorAutoload)) {
                require_once $vendorAutoload;
            }
        }
    }

    /**
     * 加载单个插件
     * @param string $pluginName
     * @return bool
     */
    public function loadPlugin(string $pluginName): bool
    {
        $pluginClass = "\\" . AppsName::APPS_NAME . "\\{$pluginName}\\App";
        if (!class_exists($pluginClass)) {
            Log::warning("插件主类 {$pluginClass} 不存在");
            return false;
        }

        $plugin = new $pluginClass();
        if (!$plugin instanceof PluginInterface) {
            Log::warning("插件主类 {$pluginClass} 没有继承: PluginInterface");
            return false;
        }

        $this->plugins[$pluginName] = $plugin;
        return true;

    }

    /**
     * 初始化事件监听器
     * @param string|null $pluginName 插件名称
     * @return PluginEventListener|null
     */
    public function initEventListener(?string $pluginName = null): ?PluginEventListener
    {
        try {
            // 如果有指定插件名称，尝试加载对应的监听器类
            if ($pluginName) {
                $listenerClass = "\\" . AppsName::APPS_NAME . "\\{$pluginName}\\EventListener";

                // 如果插件特定的监听器类不存在或不是PluginEventListener的子类，则使用默认监听器
                if (!class_exists($listenerClass) || !is_subclass_of($listenerClass, PluginEventListener::class)) {
                    $listenerClass = config('plugin.event_listener') ?: PluginEventListener::class;
                }
            } else {
                // 没有指定插件名称时使用默认配置
                $listenerClass = config('plugin.event_listener') ?: PluginEventListener::class;
            }

            // 确保最终使用的监听器类继承自PluginEventListener
            if (!is_subclass_of($listenerClass, PluginEventListener::class)) {
                $listenerClass = PluginEventListener::class;
            }

            // 绑定事件监听器类并传入插件名称
            app()->bind(PluginEventListener::class, function() use ($listenerClass, $pluginName) {
                try {
                    return new $listenerClass($pluginName ?? '');
                } catch (\Exception $e) {
                    Log::error(sprintf(
                        "事件监听器初始化失败: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                        $e->getMessage(),
                        $pluginName ?? '未指定',
                        $e->getTraceAsString()
                    ));
                    return null;
                }
            });
            
            $this->eventListener = app()->make(PluginEventListener::class);
            return $this->eventListener;
        } catch (\Exception $e) {
            Log::error(sprintf(
                "初始化事件监听器失败: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $pluginName ?? '未指定',
                $e->getTraceAsString()
            ));
            return null;
        }
    }
    
    /**
     * 安装插件
     * @param string $pluginName
     * @return bool
     */
    public function installPlugin(string $pluginName): bool
    {
        if (!isset($this->plugins[$pluginName])) {
            return false;
        }

        // 初始化事件监听器
        $this->initEventListener($pluginName);

        try {
            return $this->plugins[$pluginName]->install();
        } catch (\Exception $e) {
            Log::error("安装插件 {$pluginName} 失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 卸载插件
     * @param string $pluginName
     * @return bool
     */
    public function uninstallPlugin(string $pluginName): bool
    {
        if (!isset($this->plugins[$pluginName])) {
            return false;
        }

        try {
            $result = $this->plugins[$pluginName]->uninstall();
            if ($result) {
                // 清理已加载的插件实例
                unset($this->plugins[$pluginName]);
                // 清理已启用的插件实例
                unset($this->enabledPlugins[$pluginName]);
            }
            return $result;
        } catch (\Exception $e) {
            Log::error("卸载插件 {$pluginName} 失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 启用插件
     * @param string $pluginName
     * @return bool
     */
    public function enablePlugin(string $pluginName): bool
    {
        if (!isset($this->plugins[$pluginName])) {
            return false;
        }

        try {
            return $this->plugins[$pluginName]->enable();
        } catch (\Exception $e) {
            Log::error("启用插件 {$pluginName} 失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 禁用插件
     * @param string $pluginName
     * @return bool
     */
    public function disablePlugin(string $pluginName): bool
    {
        if (!isset($this->plugins[$pluginName])) {
            return false;
        }

        try {
            return $this->plugins[$pluginName]->disable();
        } catch (\Exception $e) {
            Log::error("禁用插件 {$pluginName} 失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取插件信息
     * @param string $pluginName
     * @return array|null
     */
    public function getPluginInfo(string $pluginName): ?array
    {
        if (!isset($this->plugins[$pluginName])) {
            return null;
        }

        return $this->plugins[$pluginName]->getInfo();
    }

    /**
     * 获取插件配置
     * @param string $pluginName
     * @return array|null
     */
    public function getPluginConfig(string $pluginName): ?array
    {
        if (!isset($this->plugins[$pluginName])) {
            return null;
        }

        return $this->plugins[$pluginName]->getConfig();
    }

    /**
     * 设置插件配置
     * @param string $pluginName
     * @param array $config
     * @return bool
     */
    public function setPluginConfig(string $pluginName, array $config): bool
    {
        if (!isset($this->plugins[$pluginName])) {
            return false;
        }

        return $this->plugins[$pluginName]->setConfig($config);
    }

    /**
     * 获取所有插件
     * @return array
     */
    public function getPlugins(): array
    {
        return $this->plugins;
    }
    
    /**
     * 获取已启用的插件
     * @return array
     */
    public function getEnabledPlugins(): array
    {
        return $this->enabledPlugins;
    }
    
    /**
     * 检查插件是否存在
     * @param string $pluginName
     * @return bool
     */
    public function hasPlugin(string $pluginName): bool
    {
        return isset($this->plugins[$pluginName]);
    }
    
    /**
     * 检查插件是否启用
     * @param string $pluginName
     * @return bool
     */
    public function isPluginEnabled(string $pluginName): bool
    {
        return isset($this->enabledPlugins[$pluginName]);
    }
    
    /**
     * 获取插件版本
     * @param string $pluginName
     * @return string|null
     */
    public function getPluginVersion(string $pluginName): ?string
    {
        if (!isset($this->plugins[$pluginName])) {
            return null;
        }
        
        $info = $this->plugins[$pluginName]->getInfo();
        return $info['version'] ?? null;
    }
    
    /**
     * 按照依赖关系排序插件
     * @return void
     */
    protected function sortPluginsByDependencies(): void
    {
        $sorted = [];
        $visited = [];
        
        // 深度优先搜索遍历依赖图
        $visit = function($pluginName) use (&$visit, &$sorted, &$visited) {
            // 已经访问过，跳过
            if (isset($visited[$pluginName])) {
                return;
            }
            
            // 标记为已访问
            $visited[$pluginName] = true;
            
            // 获取依赖
            $dependencies = [];
            if (isset($this->plugins[$pluginName])) {
                $dependencies = $this->plugins[$pluginName]->getDependencies();
            }
            
            // 先访问所有依赖
            foreach ($dependencies as $dependency => $version) {
                if (isset($this->plugins[$dependency])) {
                    $visit($dependency);
                }
            }
            
            // 将当前插件加入排序结果
            $sorted[$pluginName] = $this->plugins[$pluginName] ?? null;
        };
        
        // 遍历所有插件
        foreach (array_keys($this->plugins) as $pluginName) {
            $visit($pluginName);
        }
        
        // 更新插件列表
        $this->plugins = array_filter($sorted);
        
        // 更新已启用插件列表
        $enabledPlugins = [];
        
        foreach ($this->enabledPlugins as $pluginName => $plugin) {
            if (isset($this->plugins[$pluginName])) {
                $enabledPlugins[$pluginName] = $this->plugins[$pluginName];
            }
        }
        $this->enabledPlugins = $enabledPlugins;
    }
    
    /**
     * 启动所有已启用的插件
     * @return void
     */
    /**
     * 启动插件
     * @param string|null $pluginName 指定插件名称，null表示启动所有已启用插件
     * @param string|null $targetPluginName 目标插件名称，用于初始化事件监听器
     * @return bool
     */
    public function bootPlugins(?string $pluginName = null, ?string $targetPluginName = null): bool
    {
        // 确保插件已加载
        if (empty($this->plugins)) {
            try {
                $this->loadPlugins(true, $targetPluginName);
            } catch (\Exception $e) {
                Log::error("加载插件失败: " . $e->getMessage());
                return false;
            }
        }
        
        // 初始化事件监听器（如果尚未初始化）
        if ($this->eventListener === null) {
            $this->initEventListener($targetPluginName ?? $pluginName);
        }

        // 如果指定了插件名称，只启动该插件
        if ($pluginName !== null) {
            // 如果插件未加载，尝试加载它
            if (!isset($this->enabledPlugins[$pluginName]) && isset($this->plugins[$pluginName])) {
                $this->enabledPlugins[$pluginName] = $this->plugins[$pluginName];
            }
            
            if (isset($this->enabledPlugins[$pluginName])) {
                try {
                    $this->enabledPlugins[$pluginName]->boot();
                    return true;
                } catch (\Exception $e) {
                    Log::error("启动插件 {$pluginName} 异常: " . $e->getMessage());
                    return false;
                }
            }
            return false;
        }

        // 否则启动所有已启用的插件
        $success = true;
        foreach ($this->enabledPlugins as $name => $plugin) {
            try {
                $plugin->boot();
            } catch (\Exception $e) {
                Log::error("启动插件 {$name} 异常: " . $e->getMessage());
                $success = false;
            }
        }

        return $success;
    }

    public function upgradePlugin(string $pluginName, string $newVersion): bool
    {
        if (!isset($this->plugins[$pluginName])) {
            return false;
        }

        try {
            return $this->plugins[$pluginName]->upgrade($newVersion);
        } catch (\Exception $e) {
            Log::error("升级插件 {$pluginName} 失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取事件监听器
     * @return PluginEventListener|null
     */
    public function getEventListener(): ?PluginEventListener
    {
        return $this->eventListener;
    }
}