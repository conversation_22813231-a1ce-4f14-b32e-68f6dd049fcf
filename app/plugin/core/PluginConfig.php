<?php

namespace app\plugin\core;

use app\plugin\constant\AppsPath;

/**
 * 插件配置类
 */
class PluginConfig
{
    /**
     * 插件名称
     * @var string
     */
    protected $name;

    /**
     * 配置文件路径
     * @var string
     */
    protected $manifestPath;

    protected $config = [];

    /**
     * 构造函数
     * @param string $name 插件名称
     */
    public function __construct(string $name)
    {
        $this->name = $name;
        $this->manifestPath = AppsPath::APPS_PATH . "{$name}/manifest.json";
        $this->config = $this->loadConfig();
    }

    /**
     * 获取配置
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    public function get(string $key = '', $default = null)
    {
        $config = $this->config;
        if (empty($key)) {
            return $config;
        }
        return $config[$key] ?? $default;
    }

    /**
     * 设置配置
     * @param string|array $key 配置键或配置数组
     * @param mixed $value 配置值
     * @return bool
     */
    public function set($key, $value = null): bool
    {
        $config = $this->config;
        if (is_array($key)) {
            $config = array_merge($config, $key);
        } else {
            $config[$key] = $value;
        }
        return $this->saveConfig($config);
    }

    /**
     * 加载配置
     * @return array
     */
    protected function loadConfig(): array
    {
        if (file_exists($this->manifestPath)) {
            $manifest = json_decode(file_get_contents($this->manifestPath), true);
            return $manifest['config'] ?? [];}
        return [];
    }

    /**
     * 保存配置
     * @param array $config
     * @return bool
     */
    protected function saveConfig(array $config): bool
    {
        if (file_exists($this->manifestPath)) {
            $manifest = json_decode(file_get_contents($this->manifestPath), true);
            $manifest['config'] = $config;
            return file_put_contents($this->manifestPath, json_encode($manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) !== false;
        }
        return false;
    }
}