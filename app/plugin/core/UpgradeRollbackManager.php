<?php

namespace app\plugin\core;

use think\facade\Db;
use think\facade\Log;

/**
 * 插件升级回滚管理器
 */
class UpgradeRollbackManager
{
    /**
     * 备份信息
     * @var array
     */
    protected $backupInfo = [];

    /**
     * 插件目录
     * @var string
     */
    protected $pluginDir;

    /**
     * 插件名称
     * @var string
     */
    protected $pluginName;

    /**
     * 当前版本
     * @var string
     */
    protected $currentVersion;

    /**
     * 构造函数
     * @param string $pluginDir 插件目录
     * @param string $pluginName 插件名称
     * @param string $currentVersion 当前版本
     */
    public function __construct(string $pluginDir, string $pluginName, string $currentVersion)
    {
        $this->pluginDir = $pluginDir;
        $this->pluginName = $pluginName;
        $this->currentVersion = $currentVersion;
    }

    /**
     * 开始备份
     * @return string 备份目录路径
     */
    public function startBackup(): string
    {
        // 创建备份目录
        $backupDir = $this->pluginDir . 'backups/' . $this->pluginName . '_' . $this->currentVersion . '_' . date('YmdHis');
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }

        // 备份插件文件
        $this->backupFiles($backupDir);

        // 备份数据库
        $this->backupDatabase($backupDir);

        // 记录备份信息
        $this->backupInfo = [
            'dir' => $backupDir,
            'version' => $this->currentVersion,
            'time' => time(),
            'tables' => $this->getPluginTables()
        ];
        $this->saveBackupInfo($backupDir);

        return $backupDir;
    }

    /**
     * 执行回滚
     * @param string $backupDir 备份目录
     * @return bool
     */
    public function rollback(string $backupDir = ''): bool
    {
        try {
            // 如果未指定备份目录，则使用最新的备份
            if (empty($backupDir)) {
                $backupDir = $this->getLatestBackup();
                if (empty($backupDir)) {
                    throw new \RuntimeException('No backup found');
                }
            }

            // 加载备份信息
            $this->loadBackupInfo($backupDir);

            // 开启事务
            Db::startTrans();

            try {
                // 还原数据库
                $this->restoreDatabase($backupDir);

                // 还原文件
                $this->restoreFiles($backupDir);

                // 提交事务
                Db::commit();
                return true;
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error("Plugin {$this->pluginName} rollback failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 备份文件
     * @param string $backupDir 备份目录
     */
    protected function backupFiles(string $backupDir): void
    {
        $pluginPath = $this->pluginDir . $this->pluginName;
        $excludes = ['.git', '.svn', 'runtime', 'backups'];

        // 复制插件文件到备份目录
        $this->copyDirectory($pluginPath, $backupDir, $excludes);
    }

    /**
     * 备份数据库
     * @param string $backupDir 备份目录
     */
    protected function backupDatabase(string $backupDir): void
    {
        $tables = $this->getPluginTables();
        $sql = '';

        foreach ($tables as $table) {
            // 获取建表语句
            $createTable = Db::query("SHOW CREATE TABLE `{$table}`")[0]['Create Table'];
            $sql .= "\n\n-- 表结构 `{$table}`\n";
            $sql .= "{$createTable};\n\n";

            // 获取数据
            $data = Db::table($table)->select()->toArray();
            if (!empty($data)) {
                $sql .= "-- 表数据 `{$table}`\n";
                foreach ($data as $row) {
                    $values = array_map(function ($value) {
                        return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }, $row);
                    $sql .= "INSERT INTO `{$table}` VALUES (" . implode(',', $values) . ");\n";
                }
            }
        }

        // 保存SQL文件
        file_put_contents($backupDir . '/database_backup.sql', $sql);
    }

    /**
     * 还原数据库
     * @param string $backupDir 备份目录
     */
    protected function restoreDatabase(string $backupDir): void
    {
        $sqlFile = $backupDir . '/database_backup.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            if (!empty($sql)) {
                // 删除现有表数据
                foreach ($this->backupInfo['tables'] as $table) {
                    Db::execute("TRUNCATE TABLE `{$table}`");
                }

                // 执行备份SQL
                SqlHelper::parser($sql);
            }
        }
    }

    /**
     * 还原文件
     * @param string $backupDir 备份目录
     */
    protected function restoreFiles(string $backupDir): void
    {
        $pluginPath = $this->pluginDir . $this->pluginName;
        $excludes = ['.git', '.svn', 'runtime', 'backups'];

        // 删除现有文件
        $this->removeDirectory($pluginPath, $excludes);

        // 还原备份文件
        $this->copyDirectory($backupDir, $pluginPath, $excludes);
    }

    /**
     * 获取插件相关的数据表
     * @return array
     */
    protected function getPluginTables(): array
    {
        // 获取所有表
        $tables = Db::query('SHOW TABLES');
        $prefix = config('database.connections.mysql.prefix');
        $pluginPrefix = strtolower($this->pluginName);

        // 筛选插件相关的表
        $pluginTables = [];
        foreach ($tables as $table) {
            $tableName = current($table);
            // 匹配插件前缀的表
            if (strpos($tableName, $prefix . 'plugin_' . $pluginPrefix) === 0) {
                $pluginTables[] = $tableName;
            }
        }

        return $pluginTables;
    }

    /**
     * 保存备份信息
     * @param string $backupDir 备份目录
     */
    protected function saveBackupInfo(string $backupDir): void
    {
        $infoFile = $backupDir . '/backup_info.json';
        file_put_contents($infoFile, json_encode($this->backupInfo, JSON_PRETTY_PRINT));
    }

    /**
     * 加载备份信息
     * @param string $backupDir 备份目录
     */
    protected function loadBackupInfo(string $backupDir): void
    {
        $infoFile = $backupDir . '/backup_info.json';
        if (file_exists($infoFile)) {
            $this->backupInfo = json_decode(file_get_contents($infoFile), true);
        } else {
            throw new \RuntimeException('Backup info file not found');
        }
    }

    /**
     * 获取最新的备份目录
     * @return string
     */
    protected function getLatestBackup(): string
    {
        $backupDir = $this->pluginDir . 'backups';
        if (is_dir($backupDir)) {
            $backups = glob($backupDir . '/' . $this->pluginName . '_*');
            if (!empty($backups)) {
                return end($backups);
            }
        }
        return '';
    }

    /**
     * 复制目录
     * @param string $source 源目录
     * @param string $target 目标目录
     * @param array $excludes 排除的目录
     */
    protected function copyDirectory(string $source, string $target, array $excludes = []): void
    {
        if (!is_dir($target)) {
            mkdir($target, 0755, true);
        }

        $dir = opendir($source);
        while (($file = readdir($dir)) !== false) {
            if ($file === '.' || $file === '..' || in_array($file, $excludes)) {
                continue;
            }

            $sourcePath = $source . '/' . $file;
            $targetPath = $target . '/' . $file;

            is_dir($sourcePath) ?
                $this->copyDirectory($sourcePath, $targetPath, $excludes) :
                copy($sourcePath, $targetPath);
        }
        closedir($dir);
    }

    /**
     * 删除目录
     * @param string $dir 目录路径
     * @param array $excludes 排除的目录
     */
    protected function removeDirectory(string $dir, array $excludes = []): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            if (in_array($file, $excludes)) {
                continue;
            }

            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path, $excludes) : unlink($path);
        }

        // 只有当目录为空时才删除
        if (count(array_diff(scandir($dir), ['.', '..'])) === 0) {
            rmdir($dir);
        }
    }
}