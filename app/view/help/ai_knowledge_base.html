<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能知识库 - 帮助中心</title>
    <link rel="stylesheet" href="/static/css/ai-knowledge-base.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e1e5e9;
            padding: 20px 0;
            margin-bottom: 20px;
        }
        
        .header-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 0;
            color: #6c757d;
            font-size: 16px;
        }
        
        .footer {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
            font-size: 14px;
        }
        
        .stats-bar {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #007bff;
            display: block;
        }
        
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .stats-bar {
                flex-direction: column;
                gap: 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🤖 AI智能知识库</h1>
            <p>基于人工智能的智能问答系统，快速找到您需要的答案</p>
        </div>
    </div>
    
    <div class="ai-kb-wrapper">
        <!-- 统计信息 -->
        <div class="stats-bar" id="stats-bar">
            <div class="stat-item">
                <span class="stat-number" id="total-questions">-</span>
                <div class="stat-label">知识条目</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="total-categories">-</span>
                <div class="stat-label">分类数量</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">24/7</span>
                <div class="stat-label">在线服务</div>
            </div>
        </div>
        
        <!-- AI知识库容器 -->
        <div id="ai-kb-container"></div>
        
        <!-- 功能介绍 -->
        <div style="background: white; border: 1px solid #e1e5e9; border-radius: 12px; padding: 20px; margin-top: 20px;">
            <h3 style="margin: 0 0 15px 0; color: #495057;">✨ 功能特色</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <h4 style="margin: 0 0 8px 0; color: #007bff;">🧠 智能理解</h4>
                    <p style="margin: 0; font-size: 14px; color: #6c757d;">基于先进的AI技术，理解您的自然语言问题</p>
                </div>
                <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <h4 style="margin: 0 0 8px 0; color: #28a745;">🔍 精准搜索</h4>
                    <p style="margin: 0; font-size: 14px; color: #6c757d;">结合关键词和语义搜索，找到最相关的答案</p>
                </div>
                <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <h4 style="margin: 0 0 8px 0; color: #ffc107;">💬 上下文记忆</h4>
                    <p style="margin: 0; font-size: 14px; color: #6c757d;">记住对话历史，提供连贯的多轮对话体验</p>
                </div>
                <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <h4 style="margin: 0 0 8px 0; color: #dc3545;">⚡ 即时响应</h4>
                    <p style="margin: 0; font-size: 14px; color: #6c757d;">快速响应，通常在2秒内给出准确答案</p>
                </div>
            </div>
        </div>
        
        <!-- 使用提示 -->
        <div style="background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 12px; padding: 20px; margin-top: 20px;">
            <h3 style="margin: 0 0 15px 0; color: #0056b3;">💡 使用提示</h3>
            <ul style="margin: 0; padding-left: 20px; color: #495057;">
                <li style="margin-bottom: 8px;">直接输入您的问题，无需特殊格式</li>
                <li style="margin-bottom: 8px;">可以使用自然语言描述问题，如"如何重置密码？"</li>
                <li style="margin-bottom: 8px;">点击热门问题或相关建议快速获取答案</li>
                <li style="margin-bottom: 8px;">AI会记住对话历史，可以进行连续提问</li>
                <li style="margin-bottom: 0;">如果AI无法回答，会推荐相关文档或建议联系人工客服</li>
            </ul>
        </div>
    </div>
    
    <div class="footer">
        <p>© 2024 AI智能知识库 | 基于先进的人工智能技术 | 持续学习，不断改进</p>
    </div>
    
    <script src="/static/js/ai-knowledge-base.js"></script>
    <script>
        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/ai/kb/stats');
                const result = await response.json();
                
                if (result.code === 200) {
                    const stats = result.data;
                    document.getElementById('total-questions').textContent = stats.total_questions || 0;
                    document.getElementById('total-categories').textContent = stats.total_categories || 0;
                }
            } catch (error) {
                console.error('Load stats failed:', error);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            
            // 添加一些示例问题到热门问题中
            setTimeout(() => {
                const popularDiv = document.getElementById('ai-kb-popular-questions');
                if (popularDiv && popularDiv.style.display === 'none') {
                    popularDiv.style.display = 'block';
                }
            }, 1000);
        });
        
        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K 聚焦到搜索框
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const input = document.getElementById('ai-kb-input');
                if (input) {
                    input.focus();
                }
            }
            
            // ESC 清除搜索建议
            if (e.key === 'Escape') {
                const suggestions = document.getElementById('ai-kb-suggestions');
                if (suggestions) {
                    suggestions.style.display = 'none';
                }
            }
        });
        
        // 添加页面可见性检测，页面重新可见时刷新统计
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                loadStats();
            }
        });
    </script>
</body>
</html>
