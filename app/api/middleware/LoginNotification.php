<?php

namespace app\api\middleware;

use app\common\ApiCode;
use app\common\message\email\NoticeEmailTemplate;
use app\common\message\MessageService;
use think\facade\Config;
use think\facade\Log;

/**
 * 登录通知中间件
 * 用于在用户登录成功后发送通知（邮件、短信、微信）
 */
class LoginNotification
{
    /**
     * 处理请求
     * @param \think\Request $request 请求对象
     * @param \Closure $next 闭包
     * @return mixed
     */
    public function handle($request, \Closure $next)
    {
        // 先执行后续操作
        $response = $next($request);

        // 获取响应数据
        $responseData = json_decode($response->getContent(), true);

        // 检查是否登录成功
        if (isset($responseData['code']) && $responseData['code'] === 200) {
            // 获取用户信息
            $userData = $responseData['data'] ?? [];

            // 异步发送通知，避免影响响应速度
            try {
                $this->sendNotification($userData);
            } catch (\Exception $e) {
                // 记录错误日志，但不影响正常响应
                Log::error('登录通知发送失败：' . $e->getMessage());
            }
        }
        
        return $response;
    }
    
    /**
     * 发送登录通知
     * @param array $userData 用户数据
     */
    protected function sendNotification(array $userData)
    {
        // 获取通知配置
        $config = Config::get('notification', []);

        // 如果没有启用通知或登录通知，则直接返回
        if (empty($config['enabled']) || (isset($config['login']['enabled']) && !$config['login']['enabled'])) {
            return;
        }
        
        // 获取用户信息
        $userId = $userData['user']['user_id'] ?? 0;
        $username = $userData['user']['username'] ?? '';
        $email = $userData['user']['mail'] ?? '';
        $mobile = $userData['user']['mobile'] ?? '';
        
        // 获取当前时间
        $loginTime = date('Y-m-d H:i:s');
        
        // 获取客户端IP
        $ip = request()->ip();

        // 获取登录通知配置
        $loginConfig = $config['login'] ?? [];
        
        // 发送邮件通知 - 优先使用登录特定配置，如果不存在则使用兼容配置
        $emailEnabled = isset($loginConfig['email']['enabled']) ? $loginConfig['email']['enabled'] : $config['email_enabled'];
        if ($emailEnabled && !empty($email)) {
            $this->noticeByEmail($userData);
        }

        // 发送短信通知 - 优先使用登录特定配置，如果不存在则使用兼容配置
        $smsEnabled = isset($loginConfig['sms']['enabled']) ? $loginConfig['sms']['enabled'] : $config['sms_enabled'];
        $smsTemplateCode = isset($loginConfig['sms']['template_code']) ? $loginConfig['sms']['template_code'] : $config['sms_template_code'];
        if ($smsEnabled && !empty($mobile)) {
            $smsContent = ["username" => $username, "time" => $loginTime, "ip" => $ip];
            MessageService::sendSms($mobile, $smsContent);
        }
        
        // 发送微信通知 - 优先使用登录特定配置，如果不存在则使用兼容配置
        $wechatEnabled = isset($loginConfig['wechat']['enabled']) ? $loginConfig['wechat']['enabled'] : $config['wechat_enabled'];
        $wechatTemplateId = isset($loginConfig['wechat']['template_id']) ? $loginConfig['wechat']['template_id'] : $config['wechat_template_id'];
        if ($wechatEnabled && !empty($wechatTemplateId)) {
            // 获取用户绑定的微信openid
            $openid = $this->getUserOpenid($userId);
            
            if (!empty($openid)) {
                // 微信模板消息参数
                $wechatParams = [
                    'template_id' => $config['wechat_template_id'],
                    'data' => [
                        'first' => ['value' => '账号登录通知', 'color' => '#173177'],
                        'keyword1' => ['value' => $username, 'color' => '#173177'],
                        'keyword2' => ['value' => $loginTime, 'color' => '#173177'],
                        'keyword3' => ['value' => $ip, 'color' => '#173177'],
                        'remark' => ['value' => '如非本人操作，请立即修改密码。', 'color' => '#FF0000']
                    ]
                ];
                
                // 发送微信模板消息
                MessageService::send($openid, '', $wechatParams, 'wechat');
            }
        }
    }
    
    /**
     * 获取用户绑定的微信openid
     * @param int $userId 用户ID
     * @return string|null 微信openid
     */
    protected function getUserOpenid($userId)
    {
        // 这里需要根据实际情况从数据库或缓存中获取用户绑定的微信openid
        // 示例代码，实际使用时需要替换为真实的实现
        if (empty($userId)) {
            return null;
        }
        
        try {
            // 从数据库查询用户绑定的微信openid
            // 假设有一个user_wechat表存储用户与微信的绑定关系
            $openid = \think\facade\Db::table('user_wechat')
                ->where('user_id', $userId)
                ->value('openid');
                
            return $openid;
        } catch (\Exception $e) {
            Log::error('获取用户微信openid失败：' . $e->getMessage());
            return null;
        }
    }

    protected function noticeByEmail($data)
    {

        $email = $data['user']['mail'] ?? '';
        $subject = 'ApiDisk账号登入成功通知';

        // 生成邮件内容
        $params = [
            'name' => 'ApiDisk',
            'email' => $email,
            'title' => 'ApiDisk账号登入成功通知',
            'content' => 'ApiDisk账号登入成功通知',
            'changes' => [
                '登入时间:' . $data['user']['logintime'],
                '登入ip:' . $data['user']['login_ip'],
                '如非本人操作，请立即修改密码。'
            ],
            'hostname' => 'ApiDisk',
            'steps' => [
                '登录到您的ApiDisk账户。',
                '进入用户中心。',
                '点击"安全"，然后选择"修改密码"。',
            ],
            'help_url' => 'https://help.apidisk.com',
            'support_email' => '<EMAIL>',
            'is_html' => true,
            // 获取邮件提供商
            'email_provider' => 'smtp',
            'host' => 'gz-smtp.qcloudmail.com',
            'port' => 465,
            'username' => '<EMAIL>',
            'password' => 'HUANxiang2025',
            'secure' => 'ssl', // tls 或 ssl
            'from' => '<EMAIL>',
            'from_name' => 'ApiDisk',
            'subject' => $subject
        ];

        // 获取邮件HTML内容
        $content = NoticeEmailTemplate::getTemplate($params);

        $emailProvider = $params['email_provider'] ?? null;

        // 发送邮件
        $sendResult = MessageService::sendEmail($email, $content, $params, $emailProvider);

        // 处理发送结果
        if ($sendResult && isset($sendResult['success']) && $sendResult['success'] === true) {
        } else {
            $errorMsg = isset($sendResult['message']) ? $sendResult['message'] : '验证码发送失败';
            return json($errorMsg, ApiCode::CODE_SEND_FAILED);
        }

        return json(ApiCode::getMessage(ApiCode::SUCCESS), ApiCode::SUCCESS);

    }
}