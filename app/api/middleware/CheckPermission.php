<?php

namespace app\api\middleware;

use app\common\ApiCode;
use app\common\Auth;
use think\facade\Db;
use app\api\entity\UserEntity;
use app\api\entity\RoleEntity;

class CheckPermission
{

    public function handle($request, \Closure $next)
    {
        $token = $request->header("token");
        if (empty($token)) {
            $data['code'] = ApiCode::UN_LOGIN;
            $data['message'] = ApiCode::getMessage(ApiCode::UN_LOGIN);
            return json($data,ApiCode::UN_LOGIN);
        }
        try {
            $user = Auth::checkToken($token);
        }catch (\Exception $e){
            $data['code'] = $e->getCode();
            $data['message'] = $e->getMessage();
            return json($data,$e->getCode());
        }
        // 这里写权限判断逻辑，例如检查用户角色或权限
        $url = $request->url();
        $permission = parse_url($url, PHP_URL_PATH);
        if (!$this->hasPermission($user, $permission)) {
            $data['code'] = ApiCode::FORBIDDEN;
            $data['message'] = ApiCode::getMessage(ApiCode::FORBIDDEN);
            return json($data,ApiCode::FORBIDDEN);
        }

        $userEntity = new UserEntity();

        $userList = Db::table('qi_users')
            ->where('id', $user['user_id'])
            ->find();

        // 将用户数据填充到实体对象中
        foreach ($userList as $key => $value) {
            if (property_exists($userEntity, $key)) {
                $userEntity->$key = $value;
            }
        }

        // 全局绑定用户实体，使其在整个请求生命周期内可用
        bind(UserEntity::class, $userEntity);

        // 如果有权限，继续请求处理
        return $next($request);
    }

    protected function hasPermission($user, $permission)
    {
        $roleEntity = new RoleEntity();

        $roles = Db::table('qi_users')
            ->alias('u')
            ->join('qi_user_group_rule gr', 'gr.user_id = u.id')
            ->join('qi_user_group ug', 'ug.id = gr.group_id')
            ->where('u.id', $user['user_id'])
            ->where('u.enabled', 1) // 过滤有效权限
            ->distinct(true)
            ->column('ug.roles'); // 获取权限 URL 列
        if ($roles) {
            $permissions = Db::table('qi_user_rule')->where('id', 'in', $roles[0])->column('role');

            // 将用户数据填充到实体对象中
            if (property_exists($roleEntity, 'role')) {
                $roleEntity->role = $permissions;
            }

            // 全局绑定用户实体，使其在整个请求生命周期内可用
            bind(RoleEntity::class, $roleEntity);

        }
        if (in_array('*',$permissions ?? [])){
            return true;
        }
        return in_array($permission, $permissions ?? []);
    }


}