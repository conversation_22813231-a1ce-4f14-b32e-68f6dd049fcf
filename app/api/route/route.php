<?php

use app\api\middleware\CheckPermission;
use app\api\middleware\CommonConfigInit;
use think\facade\Route;

header('Access-Control-Allow-Origin:*');
// 响应类型
header('Access-Control-Allow-Methods:GET,POST,OPTIONS,PUT,DELETE');
header('Access-Control-Allow-Headers:sign-time,app-id,app-key,cms-token,plat-v,token,pid,Authorization,Content-Type,Depth,User-Agent,X-File-Size,X-Requested-With,X-Requested-By,If-Modified-Since,X-File-Name,X-File-Type,Cache-Control,Origin');
header('Access-Control-Request-Headers:*');

Route::group(function () {
    Route::post('/v1/refresh/token', 'RefreshToken/token')->option(['real_name' => '刷新token']);

    Route::post('/v1/register', 'Register/register')->option(['real_name' => '用户注册']);
    Route::post('/v1/register/email', 'Register/registerByEmail')->option(['real_name' => '用户邮箱注册']);
    Route::post('/v1/register/sms', 'Register/registerBySms')->option(['real_name' => '用户短信注册']);
    Route::post('/v1/register/email/sender', 'Sender/sendByEmail')->option(['real_name' => '用户邮箱注册验证码']);
    Route::post('/v1/register/sms/sender', 'Sender/sendBySms')->option(['real_name' => '用户短信注册验证码']);

    Route::post('/v1/login/password', 'Login/login')->option(['real_name' => '用户登录']);
    Route::post('/v1/login/email', 'Login/loginByEmail')->option(['real_name' => '用户邮箱登陆']);
    Route::post('/v1/login/sms', 'Login/loginBySms')->option(['real_name' => '用户短信登陆']);
    Route::post('/v1/login/email/sender', 'LoginSender/sendByEmail')->option(['real_name' => '用户邮箱验证码']);
    Route::post('/v1/login/sms/sender', 'LoginSender/sendBySms')->option(['real_name' => '用户短信验证码']);

    Route::post('/v1/notice/email/sender', 'Sender/noticeByEmail')->option(['real_name' => '用户邮件通知']);
})->middleware(CommonConfigInit::class);

Route::group(function () {

    Route::get('/v1/user', 'user.UserInfo/info')->option(['real_name' => '用户信息']);

})->middleware([
    CommonConfigInit::class,
    CheckPermission::class
]);

// 商品相关路由
// 商品基础接口
Route::get('/v1/goods', 'Goods/index')->option(['real_name' => '商品列表']);
Route::get('/v1/goods/:id', 'Goods/detail')->option(['real_name' => '商品详情']);
Route::post('/v1/goods', 'Goods/add')->option(['real_name' => '添加商品']);
Route::put('/v1/goods/:id', 'Goods/update')->option(['real_name' => '更新商品']);
Route::delete('/v1/goods/:id', 'Goods/delete')->option(['real_name' => '删除商品']);
Route::get('/v1/goods/search', 'Goods/search')->option(['real_name' => '搜索商品']);

// 商品分类接口
Route::get('/v1/goods/category', 'Category/index')->option(['real_name' => '分类列表']);
Route::get('/v1/goods/category/tree', 'Category/tree')->option(['real_name' => '分类树']);
Route::get('/v1/goods/category/:id', 'Category/detail')->option(['real_name' => '分类详情']);
Route::post('/v1/goods/category', 'Category/add')->option(['real_name' => '添加分类']);
Route::put('/v1/goods/category/:id', 'Category/update')->option(['real_name' => '更新分类']);
Route::delete('/v1/goods/category/:id', 'Category/delete')->option(['real_name' => '删除分类']);
Route::get('/v1/goods/category/:id/path', 'Category/path')->option(['real_name' => '分类路径']);
Route::post('/v1/goods/category/sort', 'Category/updateSort')->option(['real_name' => '更新分类排序']);

// 商品库存接口
Route::get('/v1/goods/stock/:goods_id', 'GoodsStock/index')->option(['real_name' => '库存信息']);
Route::post('/v1/goods/stock/batch', 'GoodsStock/batchGet')->option(['real_name' => '批量获取库存']);
Route::post('/v1/goods/stock/increase', 'GoodsStock/increase')->option(['real_name' => '增加库存']);
Route::post('/v1/goods/stock/decrease', 'GoodsStock/decrease')->option(['real_name' => '减少库存']);
Route::post('/v1/goods/stock/set', 'GoodsStock/set')->option(['real_name' => '设置库存']);
Route::get('/v1/goods/stock/:goods_id/logs', 'GoodsStock/logs')->option(['real_name' => '库存日志']);

// 商品价格接口
Route::get('/v1/goods/price/:goods_id', 'GoodsPrice/index')->option(['real_name' => '价格信息']);
Route::post('/v1/goods/price/batch', 'GoodsPrice/batchGet')->option(['real_name' => '批量获取价格']);
Route::post('/v1/goods/price/update', 'GoodsPrice/update')->option(['real_name' => '更新价格']);
Route::post('/v1/goods/price/batch/update', 'GoodsPrice/batchUpdate')->option(['real_name' => '批量更新价格']);
Route::post('/v1/goods/price/adjust', 'GoodsPrice/adjustByPercentage')->option(['real_name' => '按比例调整价格']);
Route::get('/v1/goods/price/:goods_id/logs', 'GoodsPrice/logs')->option(['real_name' => '价格日志']);


