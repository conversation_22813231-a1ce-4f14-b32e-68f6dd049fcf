<?php

namespace app\api\entity;

class UserEntity
{

    public $user_no;

    public $mail;

    public $mobile;

    public $id;

    public $login_ip;

    public $access_token;

    public $refresh_token;

    public $user_type;

    public $username;

    public $enabled;

    public function getEnabled()
    {
        return $this->enabled;
    }

    public function getUsername()
    {
        return $this->username;
    }

    public function getUserNo()
    {
        return $this->user_no;
    }

    public function getMail()
    {
        return $this->mail;
    }

    public function getMobile()
    {
        return $this->mobile;
    }

    public function getLoginIp()
    {
        return $this->login_ip;
    }

    public function getAccessToken()
    {
        return $this->access_token;
    }

    public function getRefreshToken()
    {
        return $this->refresh_token;
    }

    public function getUserType()
    {
        return $this->user_type;
    }

}
