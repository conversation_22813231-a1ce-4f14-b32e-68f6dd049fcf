<?php

namespace app\api\controller;

use app\api\middleware\RegisterNotification;
use app\common\ApiCode;
use app\common\controller\BaseController;
use app\common\IdGenerator;
use app\common\login\RegisterService;
use app\api\entity\UserEntity;

class Register extends BaseController
{

    // 定义中间件
    protected $middleware = [
        RegisterNotification::class => [
            'only' => ['register', 'registerByEmail', 'registerBySms'],
            // 添加后置中间件标识
            'end' => true
        ]
    ];
    public function register()
    {

        $username = $this->request->post(name: 'username');
        $password = $this->request->post('password');

        $param = [
            'auto_login' => true
        ];

        $result = RegisterService::register($username, $password, $param, 'password');

        if ($result === false) {
            // 登录失败，获取错误信息
            $error = RegisterService::getError();
            $code = RegisterService::getCode();
            $this->ok($error, [], $code);
        }

        // 登录成功，返回结果（包含用户信息和token）

        $this->ok('登入成功', $result, ApiCode::SUCCESS);

    }

    public function registerByEmail()
    {
        $email = $this->request->post('email');
        $username = $this->request->post(name: 'username');
        $password = $this->request->post('password');
        $code = $this->request->post('code');

        $param = [
            'auto_login' => false,
            'username' => $username,
            'password' => $password,
            'entity' => 'app\api\entity\UserEntity',
            'user_data' => [
                'login_ip' => $this->request->ip(),
                'user_no' => IdGenerator::generateIdFromDb(),
                'username' => $username
            ]
        ];

        $result = RegisterService::register($email, $code, $param, 'email');

        if ($result === false) {
            // 登录失败，获取错误信息
            $error = RegisterService::getError();
            $code = RegisterService::getCode();
            $this->ok($error, [], $code);
        }

        // 登录成功，返回结果（包含用户信息和token）

        $this->ok('登入成功', $result, ApiCode::SUCCESS);
    }

    public function registerBySms()
    {
        $mobile = $this->request->post('mobile');
        $password = $this->request->post('password');
        $code = $this->request->post('code');

        $param = [
            'auto_login' => false,
            'password' => $password,
            'entity' => 'app\api\entity\UserEntity',
            'user_data' => [
                'login_ip' => $this->request->ip(),
                'user_no' => IdGenerator::generateIdFromDb(),
                'username' => IdGenerator::generate(10, 'USER_')
            ]
        ];

        $result = RegisterService::register($mobile, $code, $param, 'sms');

        if ($result === false) {
            // 登录失败，获取错误信息
            $error = RegisterService::getError();
            $code = RegisterService::getCode();
            $this->ok($error, [], $code);
        }

        // 登录成功，返回结果（包含用户信息和token）

        $this->ok('登入成功', $result, ApiCode::SUCCESS);
    }

}
