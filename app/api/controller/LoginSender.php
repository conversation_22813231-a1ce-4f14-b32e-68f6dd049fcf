<?php

namespace app\api\controller;

use app\common\controller\BaseController;
use app\common\login\LoginService;
use app\common\login\RegisterService;

class LoginSender extends BaseController
{

    public function sendByEmail()
    {

        $email = $this->request->post('email');

        // 生成验证码
        $code = mt_rand(100000, 999999);
        $codeExpire = $this->config['code_expire'] ?? 300; // 验证码有效期，默认5分钟


        $params = [
            'hostname' => 'ApiDisk',
            'support_url' => 'https://support.apidisk.com'
        ];

        // 发送邮件
        try {
            $result = LoginService::sendCode($email, 'email', null, $params);

        } catch (\Exception $e) {
            $this->ok($e->getMessage(), '', $e->getCode());
        }

        $this->ok('验证码发送成功');

    }

    public function sendBySms()
    {

        $mobile = $this->request->post('mobile');

        // 生成验证码
        $code = mt_rand(100000, 999999);
        $codeExpire = $this->config['code_expire'] ?? 300; // 验证码有效期，默认5分钟


        $params = [
            'hostname' => 'ApiDisk',
            'support_url' => 'https://support.apidisk.com'
        ];

        // 发送邮件
        try {
            $result = LoginService::sendCode($mobile, 'sms', null, $params);
        } catch (\Exception $e) {
            $this->ok($e->getMessage(), '', $e->getCode());
        }

        $this->ok('验证码发送成功');

    }

}