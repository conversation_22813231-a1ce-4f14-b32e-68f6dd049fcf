<?php

namespace app\api\controller\order;

use app\common\controller\BaseApiController;
use app\model\Order as OrderModel;
use app\model\OrderGoods;
use app\model\UserAddress;
use app\model\Goods;
use think\facade\Db;

/**
 * 订单接口控制器
 * 用于订单的创建、查询和状态更新
 */
class Order extends BaseApiController
{
    /**
     * 订单状态常量
     */
    const STATUS_PENDING_PAYMENT = 1;    // 待支付
    const STATUS_PAID = 2;               // 已支付
    const STATUS_SHIPPED = 3;            // 已发货
    const STATUS_COMPLETED = 4;          // 已完成
    const STATUS_CANCELLED = 5;          // 已取消
    const STATUS_REFUNDING = 6;          // 退款中
    const STATUS_REFUNDED = 7;           // 已退款
    
    /**
     * 允许的字段
     * @var array
     */
    protected $allowFields = ['order_no', 'status', 'address_id', 'remark'];
    
    /**
     * 是否需要登录
     * @var bool
     */
    protected $needLogin = true;
    
    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 只查询当前用户的订单
        $where[] = ['user_id', '=', $this->userId];
        
        // 处理状态筛选
        $params = $this->request->get();
        if (isset($params['status'])) {
            $where[] = ['status', '=', intval($params['status'])];
        }
        
        // 默认按创建时间倒序
        $sort = ['create_time' => 'desc'];
    }
    
    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        $items = $list->items();
        foreach ($items as &$item) {
            $this->formatOrder($item);
        }
        
        return [
            'total' => $list->total(),
            'page' => $list->currentPage(),
            'limit' => $list->listRows(),
            'list' => $items
        ];
    }
    
    /**
     * 详情查询前的钩子方法
     * @param array &$where 查询条件
     */
    protected function beforeRead(&$where)
    {
        // 只允许查询当前用户的订单
        $where[] = ['user_id', '=', $this->userId];
    }
    
    /**
     * 详情查询后的钩子方法
     * @param \think\Model $info 记录对象
     * @return array|null
     */
    protected function afterRead($info)
    {
        if (!empty($info)) {
            $this->formatOrder($info);
        }
        return $info;
    }
    
    /**
     * 创建前的钩子方法
     * @param array &$data 创建数据
     * @return bool|string 返回false表示创建失败，返回错误信息
     */
    protected function beforeSave(&$data)
    {
        // 验证商品列表
        $goods = $this->request->post('goods');
        if (empty($goods) || !is_array($goods)) {
            return '商品列表不能为空';
        }
        
        // 验证收货地址
        if (empty($data['address_id'])) {
            return '请选择收货地址';
        }
        $address = UserAddress::where('id', $data['address_id'])
            ->where('user_id', $this->userId)
            ->find();
        if (!$address) {
            return '收货地址不存在';
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 生成订单号
            $data['order_no'] = $this->generateOrderNo();
            $data['user_id'] = $this->userId;
            $data['status'] = OrderModel::STATUS_PENDING;
            
            // 计算订单金额
            $totalAmount = 0;
            $orderGoods = [];
            foreach ($goods as $item) {
                $goodsInfo = Goods::where('id', $item['goods_id'])
                    ->where('status', 1)
                    ->find();
                if (!$goodsInfo) {
                    throw new \Exception('商品不存在或已下架');
                }
                if ($goodsInfo['stock'] < $item['quantity']) {
                    throw new \Exception('商品库存不足');
                }
                
                // 扣减库存
                $goodsInfo->stock -= $item['quantity'];
                $goodsInfo->save();
                
                // 计算商品总价
                $amount = $goodsInfo['price'] * $item['quantity'];
                $totalAmount += $amount;
                
                // 准备订单商品数据
                $orderGoods[] = [
                    'goods_id' => $item['goods_id'],
                    'quantity' => $item['quantity'],
                    'price' => $goodsInfo['price'],
                    'amount' => $amount
                ];
            }
            
            // 设置订单金额
            $data['amount'] = $totalAmount;
            
            // 保存收货地址信息
            $data['consignee'] = $address['consignee'];
            $data['mobile'] = $address['mobile'];
            $data['province'] = $address['province'];
            $data['city'] = $address['city'];
            $data['district'] = $address['district'];
            $data['address'] = $address['address'];
            
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }
    
    /**
     * 创建后的钩子方法
     * @param \think\Model $model 模型对象
     */
    protected function afterSave($model)
    {
        try {
            // 保存订单商品
            $goods = $this->request->post('goods');
            foreach ($goods as $item) {
                $goodsInfo = Goods::find($item['goods_id']);
                OrderGoods::create([
                    'order_id' => $model->id,
                    'goods_id' => $item['goods_id'],
                    'goods_name' => $goodsInfo['name'],
                    'goods_cover' => $goodsInfo['cover'],
                    'quantity' => $item['quantity'],
                    'price' => $goodsInfo['price'],
                    'amount' => $goodsInfo['price'] * $item['quantity']
                ]);
            }
            
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 更新订单状态
     * @param int $id 订单ID
     * @return void
     */
    public function updateStatus($id)
    {
        // 验证订单
        $order = OrderModel::where('id', $id)
            ->where('user_id', $this->userId)
            ->find();
        if (!$order) {
            $this->error('订单不存在');
        }
        
        // 验证状态
        $status = $this->request->post('status');
        if (!in_array($status, [OrderModel::STATUS_CANCELLED])) {
            $this->error('无效的订单状态');
        }
        
        // 验证状态变更
        if ($status == OrderModel::STATUS_CANCELLED) {
            if ($order->status != OrderModel::STATUS_PENDING) {
                $this->error('只有待支付订单可以取消');
            }
        }
        
        Db::startTrans();
        try {
            // 更新订单状态
            $order->status = $status;
            $order->save();
            
            // 如果是取消订单，恢复库存
            if ($status == OrderModel::STATUS_CANCELLED) {
                $orderGoods = OrderGoods::where('order_id', $id)->select();
                foreach ($orderGoods as $item) {
                    Goods::where('id', $item['goods_id'])
                        ->inc('stock', $item['quantity'])
                        ->update();
                }
            }
            
            Db::commit();
            $this->success('操作成功');
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 格式化订单数据
     * @param array &$order 订单数据
     */
    protected function formatOrder(&$order)
    {
        // 处理订单状态
        $statusMap = [
            OrderModel::STATUS_PENDING => '待支付',
            OrderModel::STATUS_PAID => '已支付',
            OrderModel::STATUS_SHIPPED => '已发货',
            OrderModel::STATUS_COMPLETED => '已完成',
            OrderModel::STATUS_CANCELLED => '已取消'
        ];
        $order['status_text'] = $statusMap[$order['status']] ?? '未知状态';
        
        // 格式化时间
        if (!empty($order['create_time'])) {
            $order['create_time_text'] = date('Y-m-d H:i:s', $order['create_time']);
        }
        if (!empty($order['pay_time'])) {
            $order['pay_time_text'] = date('Y-m-d H:i:s', $order['pay_time']);
        }
        if (!empty($order['ship_time'])) {
            $order['ship_time_text'] = date('Y-m-d H:i:s', $order['ship_time']);
        }
        if (!empty($order['complete_time'])) {
            $order['complete_time_text'] = date('Y-m-d H:i:s', $order['complete_time']);
        }
        
        // 关联订单商品
        $order['goods'] = OrderGoods::where('order_id', $order['id'])->select()->toArray();
    }
    
    /**
     * 生成订单号
     * @return string
     */
    protected function generateOrderNo()
    {
        return date('YmdHis') . mt_rand(1000, 9999) . $this->userId;
    }
}