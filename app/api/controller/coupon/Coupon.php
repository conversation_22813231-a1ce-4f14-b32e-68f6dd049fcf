<?php

namespace app\api\controller\coupon;

use app\common\controller\BaseApiController;
use app\model\Coupon as CouponModel;
use app\model\UserCoupon;
use think\facade\Db;

/**
 * 优惠券接口控制器
 * 用于优惠券的领取和使用
 */
class Coupon extends BaseApiController
{
    /**
     * 模型实例
     * @var string
     */
    protected $model = UserCoupon::class;
    
    /**
     * 允许的字段
     * @var array
     */
    protected $allowFields = ['coupon_id', 'status'];
    
    /**
     * 是否需要登录
     * @var bool
     */
    protected $needLogin = true;
    
    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 只查询当前用户的优惠券
        $where[] = ['user_id', '=', $this->userId];
        
        // 处理状态筛选
        $params = $this->request->get();
        if (isset($params['status'])) {
            $where[] = ['status', '=', intval($params['status'])];
        }
        
        // 默认按创建时间倒序
        $sort = ['create_time' => 'desc'];
    }
    
    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        $items = $list->items();
        foreach ($items as &$item) {
            $this->formatUserCoupon($item);
        }
        
        return [
            'total' => $list->total(),
            'page' => $list->currentPage(),
            'limit' => $list->listRows(),
            'list' => $items
        ];
    }
    
    /**
     * 详情查询前的钩子方法
     * @param array &$where 查询条件
     */
    protected function beforeRead(&$where)
    {
        // 只允许查询当前用户的优惠券
        $where[] = ['user_id', '=', $this->userId];
    }
    
    /**
     * 详情查询后的钩子方法
     * @param \think\Model $info 记录对象
     * @return array|null
     */
    protected function afterRead($info)
    {
        if (!empty($info)) {
            $this->formatUserCoupon($info);
        }
        return $info;
    }
    
    /**
     * 领取优惠券
     * @return void
     */
    public function receive()
    {
        $params = $this->request->post();
        
        // 参数验证
        if (empty($params['coupon_id'])) {
            $this->error('优惠券ID不能为空');
        }
        
        // 查询优惠券信息
        $coupon = CouponModel::where('id', $params['coupon_id'])->find();
        if (!$coupon) {
            $this->error('优惠券不存在');
        }
        
        // 检查优惠券是否可领取
        $now = time();
        if ($coupon->start_time > $now) {
            $this->error('优惠券活动未开始');
        }
        
        if ($coupon->end_time < $now) {
            $this->error('优惠券活动已结束');
        }
        
        if ($coupon->stock <= 0) {
            $this->error('优惠券已领完');
        }
        
        // 检查用户是否已领取过该优惠券
        $exists = UserCoupon::where([
            'user_id' => $this->userId,
            'coupon_id' => $params['coupon_id']
        ])->count();
        
        if ($exists >= $coupon->per_limit) {
            $this->error('您已达到领取上限');
        }
        
        Db::startTrans();
        try {
            // 减少优惠券库存
            $coupon->where('id', $params['coupon_id'])
                ->where('stock', '>', 0)
                ->dec('stock')
                ->update();
            
            // 用户领取优惠券
            $userCoupon = UserCoupon::create([
                'user_id' => $this->userId,
                'coupon_id' => $params['coupon_id'],
                'status' => UserCoupon::STATUS_UNUSED
            ]);
            
            Db::commit();
            $this->success('领取成功', ['id' => $userCoupon->id]);
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('领取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 检查订单可用优惠券
     * @return void
     */
    public function checkOrderCoupons()
    {
        $params = $this->request->post();
        
        // 参数验证
        if (empty($params['amount']) || floatval($params['amount']) <= 0) {
            $this->error('订单金额必须大于0');
        }
        
        $amount = floatval($params['amount']);
        $now = time();
        
        // 查询用户可用优惠券
        $userCoupons = UserCoupon::with(['coupon'])
            ->where([
                'user_id' => $this->userId,
                'status' => UserCoupon::STATUS_UNUSED
            ])
            ->select();
        
        $availableCoupons = [];
        foreach ($userCoupons as $userCoupon) {
            // 检查优惠券是否可用
            $checkResult = $userCoupon->checkAvailable($amount);
            if ($checkResult === true) {
                // 计算优惠金额
                $discountAmount = $userCoupon->calculateDiscount($amount);
                
                $couponInfo = $userCoupon->toArray();
                $couponInfo['discount_amount'] = $discountAmount;
                $availableCoupons[] = $couponInfo;
            }
        }
        
        // 按优惠金额排序
        usort($availableCoupons, function($a, $b) {
            return $b['discount_amount'] <=> $a['discount_amount'];
        });
        
        $this->success('获取成功', $availableCoupons);
    }
    
    /**
     * 使用优惠券
     * @param int $id 用户优惠券ID
     * @return void
     */
    public function use($id)
    {
        $params = $this->request->post();
        
        // 参数验证
        if (empty($params['order_id'])) {
            $this->error('订单ID不能为空');
        }
        
        // 查询优惠券信息
        $userCoupon = UserCoupon::where([
            'id' => $id,
            'user_id' => $this->userId,
            'status' => UserCoupon::STATUS_UNUSED
        ])->find();
        
        if (!$userCoupon) {
            $this->error('优惠券不存在或已使用');
        }
        
        // 更新优惠券状态
        $userCoupon->status = UserCoupon::STATUS_USED;
        $userCoupon->use_time = time();
        $userCoupon->order_id = $params['order_id'];
        
        if ($userCoupon->save()) {
            $this->success('使用成功');
        } else {
            $this->error('使用失败');
        }
    }
    
    /**
     * 格式化用户优惠券数据
     * @param array &$userCoupon 用户优惠券数据
     */
    protected function formatUserCoupon(&$userCoupon)
    {
        // 关联优惠券信息
        $userCoupon->append(['status_text', 'create_time_text', 'use_time_text'])
            ->with(['coupon' => function($query) {
                $query->append(['type_text', 'value_text', 'start_time_text', 'end_time_text']);
            }]);
    }
}