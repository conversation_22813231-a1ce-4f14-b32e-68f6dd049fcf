<?php

namespace app\api\controller;

use app\common\ApiCode;
use app\common\controller\BaseController;
use app\common\login\RegisterService;
use app\common\message\email\NoticeEmailTemplate;
use app\common\message\MessageService;

class Sender extends BaseController
{

    public function sendByEmail()
    {

        $email = $this->request->post('email');

        // 生成验证码
        $code = mt_rand(100000, 999999);
        $codeExpire = $this->config['code_expire'] ?? 300; // 验证码有效期，默认5分钟


        $params = [
            'hostname' => 'ApiDisk',
            'support_url' => 'https://support.apidisk.com'
        ];

        // 发送邮件
        try {
            $result = RegisterService::sendCode($email, 'email', null, $params);
        } catch (\Exception $e) {
            $this->ok($e->getMessage(), '', $e->getCode());
        }

        $this->ok('验证码发送成功');

    }

    public function sendBySms()
    {

        $mobile = $this->request->post('mobile');

        // 生成验证码
        $code = mt_rand(100000, 999999);
        $codeExpire = $this->config['code_expire'] ?? 300; // 验证码有效期，默认5分钟


        $params = [
            'hostname' => 'ApiDisk',
            'support_url' => 'https://support.apidisk.com'
        ];

        // 发送邮件
        try {
            $result = RegisterService::sendCode($mobile, 'sms', null, $params);
        } catch (\Exception $e) {
            $this->ok($e->getMessage(), '', $e->getCode());
        }

        $this->ok('验证码发送成功');

    }

    public function noticeByEmail()
    {

        $email = $this->request->post('email');
        $subject = 'ApiDisk账号注册成功通知';

        // 生成邮件内容
        $params = [
            'name' => 'ApiDisk',
            'email' => '<EMAIL>',
            'title' => 'Whois隐私设置更新通知',
            'content' => '我们将于2025年3月31日更新我们的Whois隐私设置。',
            'changes' => [
                '不再使用Whois隐私开关。将改为使用下拉菜单来设置隐私级别。',
                '您选择的隐私级别将应用于与注册商共享的信息，除非受到注册局限制。',
                '您现在可以为TLD选择隐私级别，这些TLD之前由于注册局限制而无法设置。请花点时间查看适用的TLD。'
            ],
            'hostname' => 'ApiDisk',
            'steps' => [
                '登录到您的ApiDisk账户。',
                '选择您希望更新的域名。',
                '点击"操作"按钮，然后选择"隐私设置"。',
                '使用下拉菜单为这些域名选择隐私级别。'
            ],
            'help_url' => 'https://help.apidisk.com',
            'support_email' => '<EMAIL>',
            'is_html' => true,
            // 获取邮件提供商
            'email_provider' => 'smtp',
            'host' => 'gz-smtp.qcloudmail.com',
            'port' => 465,
            'username' => '<EMAIL>',
            'password' => 'HUANxiang2025',
            'secure' => 'ssl', // tls 或 ssl
            'from' => '<EMAIL>',
            'from_name' => 'ApiDisk',
            'subject' => $subject
        ];

        // 获取邮件HTML内容
        $content = NoticeEmailTemplate::getTemplate($params);

        $emailProvider = $params['email_provider'] ?? null;

        // 发送邮件
        $sendResult = MessageService::sendEmail($email, $content, $params, $emailProvider);

        // 处理发送结果
        if ($sendResult && isset($sendResult['success']) && $sendResult['success'] === true) {
        } else {
            $errorMsg = isset($sendResult['message']) ? $sendResult['message'] : '验证码发送失败';
            $this->ok($errorMsg, '', ApiCode::CODE_SEND_FAILED);
        }

        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS));
    }

}