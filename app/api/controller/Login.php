<?php

namespace app\api\controller;

use app\common\ApiCode;
use app\common\controller\BaseController;
use app\common\login\LoginService;
use app\api\middleware\LoginNotification;

class Login extends BaseController
{
    // 定义中间件
    protected $middleware = [
        LoginNotification::class => [
            'only' => ['login', 'loginByEmail', 'loginBySms'],
            // 添加后置中间件标识
            'end' => true
        ]
    ];

    public function login()
    {

        $username = $this->request->post('username');
        $password = $this->request->post('password');

        $param = ['table' => 'qi_users', 'with_refresh_token' => true];

        $result = LoginService::login($username, $password, $param, 'password');

        if ($result === false) {
            // 登录失败，获取错误信息
            $error = LoginService::getError();
            $code = LoginService::getCode();
            $this->ok($error, [], $code);
        }

        // 登录成功，返回结果（包含用户信息和token）

        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $result, ApiCode::SUCCESS);
    }

    public function loginByEmail()
    {

        $params = [];
        $email = $this->request->post('email');
        $code = $this->request->post('code');

        // 使用LoginService类
        $result = LoginService::login($email, $code, $params, 'email');

        if ($result === false) {
            $this->ok(LoginService::getError(), '', LoginService::getCode());
        }

        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $result, ApiCode::SUCCESS);

    }

    public function loginBySms()
    {

        $params = [];
        $mobile = $this->request->post('mobile');
        $code = $this->request->post('code');

        // 使用LoginService类
        $result = LoginService::login($mobile, $code, $params, 'sms');

        if ($result === false) {
            $this->ok(LoginService::getError(), '', LoginService::getCode());
        }

        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $result, ApiCode::SUCCESS);

    }

}
