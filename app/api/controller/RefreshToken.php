<?php

namespace app\api\controller;

use app\common\Auth;
use app\common\controller\BaseController;
use app\common\TokenStorage;
use think\facade\Request;

class RefreshToken extends BaseController
{

    public function token()
    {

        $request = Request::instance();
        $token = $request->post("refresh_token");

        $result = Auth::refreshToken($token, 7200);

        $this->ok('刷新成功', $result);
    }

}