<?php

namespace app\api\controller\notification;

use app\common\ApiCode;
use app\common\controller\BaseController;
use app\api\entity\UserEntity;
use app\common\notification\NotificationService;
use app\common\notification\MessageConfig;
use app\common\message\MessageService;
use think\facade\Db;

/**
 * 通知发送控制器
 * 用于处理通知发送相关操作
 */
class NotificationSender extends BaseController
{
    /**
     * 根据配置发送额外的消息（邮件和短信）
     * 
     * @param int $userId 用户ID
     * @param int $type 通知类型
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @return void
     */
    protected function sendAdditionalMessages($userId, $type, $title, $content)
    {
        // 获取用户信息
        $user = Db::table('qi_users')->where('id', $userId)->find();
        if (empty($user)) {
            return;
        }
        
        // 检查是否需要发送邮件
        if (MessageConfig::shouldSendEmail($type) && !empty($user['mail'])) {
            // 发送邮件
            $emailContent = "【{$title}】{$content}";
            MessageService::sendEmail($user['mail'], $emailContent);
        }
        
        // 检查是否需要发送短信
        if (MessageConfig::shouldSendSms($type) && !empty($user['mobile'])) {
            // 发送短信
            $smsContent = ['title' => $title, 'content' => $content];
            MessageService::sendSms($user['mobile'], $smsContent);
        }
    }
    
    /**
     * 发送通知
     * 
     * @return \think\Response
     */
    public function send()
    {
        // 获取当前登录用户
        $userEntity = app(UserEntity::class);
        if (empty($userEntity->id)) {
            return json(['code' => ApiCode::UNAUTHORIZED, 'message' => '请先登录']);
        }
        
        // 获取请求参数
        $params = $this->request->post();
        
        // 参数验证
        if (empty($params['user_id'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '接收用户ID不能为空']);
        }
        
        if (empty($params['title'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '通知标题不能为空']);
        }
        
        if (empty($params['content'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '通知内容不能为空']);
        }
        
        // 获取通知类型，默认为系统通知
        $type = isset($params['type']) ? intval($params['type']) : NotificationService::TYPE_SYSTEM;
        
        // 创建通知服务实例
        $notificationService = new NotificationService();
        
        // 发送通知
        $result = $notificationService->create(
            $params['user_id'],
            $type,
            $params['title'],
            $params['content']
        );
        
        if ($result === false) {
            return json(['code' => ApiCode::ERROR, 'message' => '发送通知失败']);
        }
        
        // 根据配置决定是否发送邮件和短信
        $this->sendAdditionalMessages($params['user_id'], $type, $params['title'], $params['content']);
        
        return json([
            'code' => ApiCode::SUCCESS,
            'message' => '发送通知成功',
            'data' => ['notification_id' => $result]
        ]);
    }
    
    /**
     * 批量发送通知
     * 
     * @return \think\Response
     */
    public function batchSend()
    {
        // 获取当前登录用户
        $userEntity = app(UserEntity::class);
        if (empty($userEntity->id)) {
            return json(['code' => ApiCode::UNAUTHORIZED, 'message' => '请先登录']);
        }
        
        // 获取请求参数
        $params = $this->request->post();
        
        // 参数验证
        if (empty($params['user_ids']) || !is_array($params['user_ids'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '接收用户ID列表不能为空']);
        }
        
        if (empty($params['title'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '通知标题不能为空']);
        }
        
        if (empty($params['content'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '通知内容不能为空']);
        }
        
        // 获取通知类型，默认为系统通知
        $type = isset($params['type']) ? intval($params['type']) : NotificationService::TYPE_SYSTEM;
        
        // 创建通知服务实例
        $notificationService = new NotificationService();
        
        // 准备批量通知数据
        $notifications = [];
        foreach ($params['user_ids'] as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'type' => $type,
                'title' => $params['title'],
                'content' => $params['content']
            ];
        }
        
        // 批量发送通知
        $result = $notificationService->batchCreate($notifications);
        
        // 根据配置决定是否发送邮件和短信
        foreach ($params['user_ids'] as $userId) {
            $this->sendAdditionalMessages($userId, $type, $params['title'], $params['content']);
        }
        
        return json([
            'code' => ApiCode::SUCCESS,
            'message' => '批量发送通知成功',
            'data' => ['success_count' => $result]
        ]);
    }
    
    /**
     * 发送系统通知
     * 
     * @return \think\Response
     */
    public function sendSystem()
    {
        // 获取当前登录用户
        $userEntity = app(UserEntity::class);
        if (empty($userEntity->id)) {
            return json(['code' => ApiCode::UNAUTHORIZED, 'message' => '请先登录']);
        }
        
        // 获取请求参数
        $params = $this->request->post();
        
        // 参数验证
        if (empty($params['user_ids']) || !is_array($params['user_ids'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '接收用户ID列表不能为空']);
        }
        
        if (empty($params['title'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '通知标题不能为空']);
        }
        
        if (empty($params['content'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '通知内容不能为空']);
        }
        
        // 创建通知服务实例
        $notificationService = new NotificationService();
        
        // 发送系统通知
        $result = $notificationService->batchCreateSystemNotification(
            $params['user_ids'],
            $params['title'],
            $params['content']
        );
        
        // 根据配置决定是否发送邮件和短信
        foreach ($params['user_ids'] as $userId) {
            $this->sendAdditionalMessages($userId, NotificationService::TYPE_SYSTEM, $params['title'], $params['content']);
        }
        
        return json([
            'code' => ApiCode::SUCCESS,
            'message' => '发送系统通知成功',
            'data' => ['success_count' => $result]
        ]);
    }
}