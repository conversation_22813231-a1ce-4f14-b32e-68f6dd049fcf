<?php

namespace app\api\controller\notification;

use app\common\ApiCode;
use app\common\controller\BaseController;
use app\api\entity\UserEntity;
use think\facade\Db;

/**
 * 通知控制器
 * 用于处理通知相关操作
 */
class Notification extends BaseController
{
    /**
     * 获取通知列表
     * 
     * @return \think\Response
     */
    public function getList()
    {
        // 获取当前登录用户
        $userEntity = app(UserEntity::class);
        if (empty($userEntity->id)) {
            return json(['code' => ApiCode::UNAUTHORIZED, 'message' => '请先登录']);
        }
        
        // 获取请求参数
        $params = $this->request->get();
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['page_size']) ? intval($params['page_size']) : 10;
        $type = isset($params['type']) ? intval($params['type']) : 0;
        $isRead = isset($params['is_read']) ? intval($params['is_read']) : -1;
        
        // 构建查询条件
        $where = [
            'user_id' => $userEntity->id,
            'status' => 1
        ];
        
        // 按类型筛选
        if ($type > 0) {
            $where['type'] = $type;
        }
        
        // 按已读状态筛选
        if ($isRead !== -1) {
            $where['is_read'] = $isRead;
        }
        
        // 查询通知总数
        $total = Db::table('notification')
            ->where($where)
            ->count();
        
        // 查询通知列表
        $list = Db::table('notification')
            ->where($where)
            ->order('create_time', 'desc')
            ->limit(($page - 1) * $pageSize, $pageSize)
            ->select()
            ->toArray();
        
        // 处理返回数据
        $data = [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
            'total_pages' => ceil($total / $pageSize)
        ];
        
        return json([
            'code' => ApiCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ]);
    }
    
    /**
     * 获取通知详情
     * 
     * @return \think\Response
     */
    public function getDetail()
    {
        // 获取当前登录用户
        $userEntity = app(UserEntity::class);
        if (empty($userEntity->id)) {
            return json(['code' => ApiCode::UNAUTHORIZED, 'message' => '请先登录']);
        }
        
        // 获取请求参数
        $params = $this->request->get();
        
        // 参数验证
        if (empty($params['id'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '通知ID不能为空']);
        }
        
        // 查询通知详情
        $notification = Db::table('notification')
            ->where([
                'id' => $params['id'],
                'user_id' => $userEntity->id,
                'status' => 1
            ])
            ->find();
        
        if (empty($notification)) {
            return json(['code' => ApiCode::NOT_FOUND, 'message' => '通知不存在']);
        }
        
        // 如果通知未读，则标记为已读
        if ($notification['is_read'] == 0) {
            Db::table('notification')
                ->where('id', $notification['id'])
                ->update([
                    'is_read' => 1,
                    'update_time' => time()
                ]);
        }
        
        return json([
            'code' => ApiCode::SUCCESS,
            'message' => '获取成功',
            'data' => $notification
        ]);
    }
    
    /**
     * 标记通知为已读
     * 
     * @return \think\Response
     */
    public function markRead()
    {
        // 获取当前登录用户
        $userEntity = app(UserEntity::class);
        if (empty($userEntity->id)) {
            return json(['code' => ApiCode::UNAUTHORIZED, 'message' => '请先登录']);
        }
        
        // 获取请求参数
        $params = $this->request->post();
        
        // 参数验证
        if (empty($params['id'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '通知ID不能为空']);
        }
        
        // 查询通知是否存在
        $notification = Db::table('notification')
            ->where([
                'id' => $params['id'],
                'user_id' => $userEntity->id,
                'status' => 1
            ])
            ->find();
        
        if (empty($notification)) {
            return json(['code' => ApiCode::NOT_FOUND, 'message' => '通知不存在']);
        }
        
        // 标记为已读
        Db::table('notification')
            ->where('id', $notification['id'])
            ->update([
                'is_read' => 1,
                'update_time' => time()
            ]);
        
        return json([
            'code' => ApiCode::SUCCESS,
            'message' => '标记成功'
        ]);
    }
    
    /**
     * 标记所有通知为已读
     * 
     * @return \think\Response
     */
    public function markAllRead()
    {
        // 获取当前登录用户
        $userEntity = app(UserEntity::class);
        if (empty($userEntity->id)) {
            return json(['code' => ApiCode::UNAUTHORIZED, 'message' => '请先登录']);
        }
        
        // 标记所有未读通知为已读
        Db::table('notification')
            ->where([
                'user_id' => $userEntity->id,
                'is_read' => 0,
                'status' => 1
            ])
            ->update([
                'is_read' => 1,
                'update_time' => time()
            ]);
        
        return json([
            'code' => ApiCode::SUCCESS,
            'message' => '标记成功'
        ]);
    }
    
    /**
     * 删除通知
     * 
     * @return \think\Response
     */
    public function delete()
    {
        // 获取当前登录用户
        $userEntity = app(UserEntity::class);
        if (empty($userEntity->id)) {
            return json(['code' => ApiCode::UNAUTHORIZED, 'message' => '请先登录']);
        }
        
        // 获取请求参数
        $params = $this->request->post();
        
        // 参数验证
        if (empty($params['id'])) {
            return json(['code' => ApiCode::BAD_REQUEST, 'message' => '通知ID不能为空']);
        }
        
        // 查询通知是否存在
        $notification = Db::table('notification')
            ->where([
                'id' => $params['id'],
                'user_id' => $userEntity->id,
                'status' => 1
            ])
            ->find();
        
        if (empty($notification)) {
            return json(['code' => ApiCode::NOT_FOUND, 'message' => '通知不存在']);
        }
        
        // 软删除通知（将状态设为0）
        Db::table('notification')
            ->where('id', $notification['id'])
            ->update([
                'status' => 0,
                'update_time' => time()
            ]);
        
        return json([
            'code' => ApiCode::SUCCESS,
            'message' => '删除成功'
        ]);
    }
}