<?php

namespace app\api\controller\user;

use app\api\entity\UserEntity;
use app\common\ApiCode;
use app\common\controller\BaseController;
use app\common\IdGenerator;
use app\model\system\User;

/**
 * 用户信息接口控制器
 * 用于获取和更新用户信息
 */
class UserInfo extends BaseController
{
    
    /**
     * 获取用户基本信息
     * 
     * @return void
     */
    public function info(UserEntity $userEntity)
    {
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $userEntity, ApiCode::SUCCESS);
    }
    
    /**
     * 更新用户资料
     * 
     * @return void
     */
    public function update(UserEntity $userEntity)
    {
        $userId = $userEntity->id;
        
        // 获取更新数据
        $data = $this->request->post();
        
        // 安全过滤，防止修改敏感字段
        $allowFields = ['head_img', 'mobile', 'mail', 'username'];
        $updateData = [];
        foreach ($allowFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }
        
        if (empty($updateData)) {
            $this->ok('没有要更新的数据', [], ApiCode::BAD_REQUEST);
        }
        
        // 更新用户信息
        $result = User::where('id', $userId)->update($updateData);
        if ($result === false) {
            $this->ok('更新失败', [], ApiCode::SERVER_ERROR);
        }
        
        $this->ok('更新成功', [], ApiCode::SUCCESS);
    }
    
    /**
     * 修改密码
     * 
     * @return void
     */
    public function changePassword(UserEntity $userEntity)
    {
        $userId = $userEntity->id;
        $oldPassword = $this->request->post('old_password');
        $newPassword = $this->request->post('new_password');
        $confirmPassword = $this->request->post('confirm_password');
        
        // 验证参数
        if (empty($oldPassword)) {
            $this->ok('原密码不能为空', [], ApiCode::PASSWORD_EMPTY);
        }
        if (empty($newPassword)) {
            $this->ok('新密码不能为空', [], ApiCode::PASSWORD_EMPTY);
        }
        if ($newPassword !== $confirmPassword) {
            $this->ok('两次输入的密码不一致', [], ApiCode::BAD_REQUEST);
        }
        
        // 验证原密码
        $user = User::where('id', $userId)->find();
        if (empty($user)) {
            $this->ok(ApiCode::getMessage(ApiCode::USER_NOT_EXIST), [], ApiCode::USER_NOT_EXIST);
        }
        
        // 验证原密码是否正确
        $salt = $user->salt;
        if (password_verify($oldPassword . $salt, $user->password)) {
            $this->ok('原密码错误', [], ApiCode::PASSWORD_ERROR);
        }
        
        // 更新密码
        $encryptNewPassword = password_hash($newPassword . $salt, PASSWORD_DEFAULT);
        $result = User::where('id', $userId)->update(['password' => $encryptNewPassword]);
        if ($result === false) {
            $this->ok('密码修改失败', [], ApiCode::SERVER_ERROR);
        }
        
        $this->ok('密码修改成功', [], ApiCode::SUCCESS);
    }
    
    /**
     * 获取用户ID
     * 
     * @return void
     */
    public function getUserId(UserEntity $userEntity)
    {
        $userId = $userEntity->id;
        $this->ok('获取成功', ['user_id' => $userId], ApiCode::SUCCESS);
    }
    
    /**
     * 生成用户编号
     * 
     * @return void
     */
    public function generateUserNo()
    {
        $userNo = IdGenerator::generateIdFromDb('user_id', 9, 100000000);
        $this->ok('生成成功', ['user_no' => $userNo], ApiCode::SUCCESS);
    }
}