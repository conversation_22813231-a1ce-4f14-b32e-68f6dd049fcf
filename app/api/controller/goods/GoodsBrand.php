<?php

namespace app\api\controller\goods;

use app\common\controller\BaseApiController;
use app\model\GoodsBrand as GoodsBrandModel;

/**
 * 商品品牌接口控制器
 * 用于获取商品品牌信息
 */
class GoodsBrand extends BaseApiController
{
    /**
     * 模型实例
     * @var string
     */
    protected $model = GoodsBrandModel::class;
    
    /**
     * 允许的字段
     * @var array
     */
    protected $allowFields = ['name', 'logo', 'description', 'status'];
    
    /**
     * 可排序字段
     * @var array
     */
    protected $sortFields = ['sort', 'create_time'];
    
    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 处理关键词搜索
        $params = $this->request->get();
        if (!empty($params['keyword'])) {
            $where[] = ['name|description', 'like', '%' . $params['keyword'] . '%'];
        }
    }
    
    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        $items = $list->items();
        foreach ($items as &$item) {
            $this->formatBrand($item);
        }
        
        return [
            'total' => $list->total(),
            'page' => $list->currentPage(),
            'limit' => $list->listRows(),
            'list' => $items
        ];
    }
    
    /**
     * 详情查询后的钩子方法
     * @param \think\Model $info 记录对象
     * @return array|null
     */
    protected function afterRead($info)
    {
        if (!empty($info)) {
            $this->formatBrand($info);
        }
        return $info;
    }
    
    /**
     * 格式化品牌数据
     * @param array &$brand 品牌数据
     */
    protected function formatBrand(&$brand)
    {
        // 处理品牌logo
        $brand['logo'] = !empty($brand['logo']) ? $brand['logo'] : '';
        
        // 处理品牌图片
        if (!empty($brand['image'])) {
            $brand['image'] = $brand['image'];
        }
        
        // 处理品牌状态
        $brand['status_text'] = $brand['status'] == 1 ? '启用' : '禁用';
        
        // 格式化时间
        if (!empty($brand['create_time'])) {
            $brand['create_time_text'] = date('Y-m-d H:i:s', $brand['create_time']);
        }
        if (!empty($brand['update_time'])) {
            $brand['update_time_text'] = date('Y-m-d H:i:s', $brand['update_time']);
        }
    }
}