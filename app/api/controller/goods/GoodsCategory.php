<?php

namespace app\api\controller\goods;

use app\common\ApiCode;
use app\common\controller\BaseController;
use app\model\Category as GoodsCategoryModel;

/**
 * 商品分类接口控制器
 * 用于获取商品分类信息
 */
class GoodsCategory extends BaseController
{
    /**
     * 获取分类列表（树形结构）
     * 
     * @return void
     */
    public function index()
    {
        // 获取请求参数
        $params = $this->request->get();
        
        // 是否递归获取子分类
        $recursive = isset($params['recursive']) ? (bool)$params['recursive'] : true;
        
        // 父级ID，默认为0（顶级分类）
        $pid = isset($params['pid']) ? intval($params['pid']) : 0;
        
        // 获取分类树
        $categories = GoodsCategoryModel::getCategoryTree($pid, $recursive);
        
        // 返回数据
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $categories, ApiCode::SUCCESS);
    }
    
    /**
     * 获取分类详情
     * 
     * @param int $id 分类ID
     * @return void
     */
    public function detail($id)
    {
        if (empty($id)) {
            $this->error(ApiCode::getMessage(ApiCode::BAD_REQUEST), [], ApiCode::BAD_REQUEST);
        }
        
        // 获取分类详情
        $category = GoodsCategoryModel::find($id);
        
        if (!$category) {
            $this->error(ApiCode::getMessage(ApiCode::NOT_FOUND), [], ApiCode::NOT_FOUND);
        }
        
        // 获取分类路径（面包屑导航）
        $path = GoodsCategoryModel::getCategoryPath($id);
        
        $result = [
            'category' => $category,
            'path' => $path
        ];
        
        // 返回数据
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $result, ApiCode::SUCCESS);
    }
}