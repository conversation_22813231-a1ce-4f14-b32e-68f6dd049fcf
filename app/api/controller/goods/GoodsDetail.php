<?php

namespace app\api\controller\goods;

use app\common\ApiCode;
use app\common\controller\BaseController;
use app\model\Goods;
use app\model\GoodsBrand;
use app\model\Category;

/**
 * 商品详情接口控制器
 * 用于获取商品详细信息
 */
class GoodsDetail extends BaseController
{
    /**
     * 获取商品详情
     * 
     * @param int $id 商品ID
     * @return void
     */
    public function index($id)
    {
        if (empty($id)) {
            $this->error(ApiCode::getMessage(ApiCode::BAD_REQUEST), [], ApiCode::BAD_REQUEST);
        }
        
        // 获取商品详情
        $goods = Goods::alias('g')
            ->join('goods_category c', 'g.category_id = c.id', 'left')
            ->join('goods_brand b', 'g.brand_id = b.id', 'left')
            ->field('g.*, c.name as category_name, b.name as brand_name')
            ->where('g.id', $id)
            ->find();
        
        if (!$goods) {
            $this->error(ApiCode::getMessage(ApiCode::NOT_FOUND), [], ApiCode::NOT_FOUND);
        }
        
        // 获取分类路径（面包屑导航）
        $categoryPath = [];
        if ($goods['category_id']) {
            $categoryPath = Category::getCategoryPath($goods['category_id']);
        }
        
        // 获取品牌信息
        $brand = null;
        if ($goods['brand_id']) {
            $brand = GoodsBrand::getBrandDetail($goods['brand_id']);
        }
        
        // 组装返回数据
        $result = [
            'goods' => $goods,
            'category_path' => $categoryPath,
            'brand' => $brand
        ];
        
        // 返回数据
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $result, ApiCode::SUCCESS);
    }
}