<?php

namespace app\api\controller\goods;

use app\common\controller\BaseApiController;
use app\model\Goods;

/**
 * 商品列表接口控制器
 * 用于获取商品列表信息
 */
class GoodsList extends BaseApiController
{
    /**
     * 模型实例
     * @var string
     */
    protected $model = Goods::class;
    
    /**
     * 允许的字段
     * @var array
     */
    protected $allowFields = ['category_id', 'brand_id', 'keyword', 'min_price', 'max_price', 'status'];
    
    /**
     * 可排序字段
     * @var array
     */
    protected $sortFields = ['price', 'sales', 'create_time'];
    
    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 只显示上架商品
        $where[] = ['status', '=', 1];
        
        // 处理价格区间
        $params = $this->request->get();
        if (!empty($params['min_price'])) {
            $where[] = ['price', '>=', floatval($params['min_price'])];
        }
        if (!empty($params['max_price'])) {
            $where[] = ['price', '<=', floatval($params['max_price'])];
        }
        
        // 处理关键词搜索
        if (!empty($params['keyword'])) {
            $where[] = ['name|description', 'like', '%' . $params['keyword'] . '%'];
        }
    }
    
    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        $items = $list->items();
        foreach ($items as &$item) {
            // 处理商品图片
            $item['cover_image'] = !empty($item['cover_image']) ? $item['cover_image'] : '';
            
            // 处理商品价格
            $item['price'] = number_format($item['price'], 2);
            if (!empty($item['market_price'])) {
                $item['market_price'] = number_format($item['market_price'], 2);
            }
            
            // 处理商品状态
            $item['status_text'] = $item['status'] == 1 ? '上架' : '下架';
        }
        
        return [
            'total' => $list->total(),
            'page' => $list->currentPage(),
            'limit' => $list->listRows(),
            'list' => $items
        ];
    }
}