<?php

namespace app\api\controller\pay;

use app\common\controller\BaseController;
use app\common\pay\PayService;
use think\facade\Log;

/**
 * 支付回调控制器
 * 处理支付平台的异步通知
 */
class Notify extends BaseController
{
    /**
     * 支付宝支付回调
     * 
     * @return void
     */
    public function alipay()
    {
        try {
            // 创建支付宝支付服务实例
            $payService = PayService::alipay();
            
            // 验证支付宝回调通知
            $data = $payService->notify();
            
            // 记录回调数据
            Log::info('支付宝支付回调：' . json_encode($data, JSON_UNESCAPED_UNICODE));
            
            // 处理业务逻辑
            $this->handlePayNotify($data, 'alipay');
            
            // 返回成功响应
            return $payService->getDriver()->getPay()->alipay()->success();
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('支付宝支付回调处理失败：' . $e->getMessage());
            
            // 返回失败响应
            return 'fail';
        }
    }
    
    /**
     * 微信支付回调
     * 
     * @return void
     */
    public function wechat()
    {
        try {
            // 创建微信支付服务实例
            $payService = PayService::wechat();
            
            // 验证微信回调通知
            $data = $payService->notify();
            
            // 记录回调数据
            Log::info('微信支付回调：' . json_encode($data, JSON_UNESCAPED_UNICODE));
            
            // 处理业务逻辑
            $this->handlePayNotify($data, 'wechat');
            
            // 返回成功响应
            return $payService->getDriver()->getPay()->wechat()->success();
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('微信支付回调处理失败：' . $e->getMessage());
            
            // 返回失败响应
            return 'fail';
        }
    }
    
    /**
     * 处理支付回调通知
     * 
     * @param array $data 通知数据
     * @param string $driver 支付驱动
     * @return void
     */
    protected function handlePayNotify(array $data, string $driver)
    {
        // 获取商户订单号
        $outTradeNo = $data['out_trade_no'] ?? '';
        
        // 判断支付状态
        $isPaid = false;
        
        if ($driver == 'alipay') {
            // 支付宝支付状态判断
            $isPaid = isset($data['trade_status']) && in_array($data['trade_status'], ['TRADE_SUCCESS', 'TRADE_FINISHED']);
        } elseif ($driver == 'wechat') {
            // 微信支付状态判断
            $isPaid = isset($data['trade_state']) && $data['trade_state'] == 'SUCCESS';
        }
        
        if ($isPaid && !empty($outTradeNo)) {
            // TODO: 更新订单状态为已支付
            // 这里需要根据实际业务逻辑处理订单状态更新
            // 例如：调用订单服务更新订单状态
            // OrderService::updateOrderStatus($outTradeNo, 'paid');
            
            // 记录日志
            Log::info("订单 {$outTradeNo} 支付成功");
        }
    }
    
    /**
     * 支付宝退款回调
     * 
     * @return void
     */
    public function alipayRefund()
    {
        try {
            // 创建支付宝支付服务实例
            $payService = PayService::alipay();
            
            // 验证支付宝回调通知
            $data = $payService->notify();
            
            // 记录回调数据
            Log::info('支付宝退款回调：' . json_encode($data, JSON_UNESCAPED_UNICODE));
            
            // 处理退款业务逻辑
            $this->handleRefundNotify($data, 'alipay');
            
            // 返回成功响应
            return $payService->getDriver()->getPay()->alipay()->success();
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('支付宝退款回调处理失败：' . $e->getMessage());
            
            // 返回失败响应
            return 'fail';
        }
    }
    
    /**
     * 微信退款回调
     * 
     * @return void
     */
    public function wechatRefund()
    {
        try {
            // 创建微信支付服务实例
            $payService = PayService::wechat();
            
            // 验证微信回调通知
            $data = $payService->notify();
            
            // 记录回调数据
            Log::info('微信退款回调：' . json_encode($data, JSON_UNESCAPED_UNICODE));
            
            // 处理退款业务逻辑
            $this->handleRefundNotify($data, 'wechat');
            
            // 返回成功响应
            return $payService->getDriver()->getPay()->wechat()->success();
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('微信退款回调处理失败：' . $e->getMessage());
            
            // 返回失败响应
            return 'fail';
        }
    }
    
    /**
     * 处理退款回调通知
     * 
     * @param array $data 通知数据
     * @param string $driver 支付驱动
     * @return void
     */
    protected function handleRefundNotify(array $data, string $driver)
    {
        // 获取商户订单号
        $outTradeNo = $data['out_trade_no'] ?? '';
        $outRefundNo = $data['out_refund_no'] ?? '';
        
        // 判断退款状态
        $isRefunded = false;
        
        if ($driver == 'alipay') {
            // 支付宝退款状态判断
            $isRefunded = isset($data['refund_status']) && $data['refund_status'] == 'REFUND_SUCCESS';
        } elseif ($driver == 'wechat') {
            // 微信退款状态判断
            $isRefunded = isset($data['refund_status']) && $data['refund_status'] == 'SUCCESS';
        }
        
        if ($isRefunded && !empty($outTradeNo) && !empty($outRefundNo)) {
            // TODO: 更新订单退款状态
            // 这里需要根据实际业务逻辑处理订单退款状态更新
            // 例如：调用订单服务更新订单退款状态
            // OrderService::updateRefundStatus($outTradeNo, $outRefundNo, 'refunded');
            
            // 记录日志
            Log::info("订单 {$outTradeNo} 退款成功，退款单号：{$outRefundNo}");
        }
    }
}