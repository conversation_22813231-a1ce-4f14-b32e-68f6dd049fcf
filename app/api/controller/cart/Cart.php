<?php

namespace app\api\controller\cart;

use app\common\controller\BaseApiController;
use app\model\Cart as CartModel;
use app\model\Goods;
use think\facade\Db;

/**
 * 购物车接口控制器
 * 用于购物车的增删改查
 */
class Cart extends BaseApiController
{
    /**
     * 模型实例
     * @var string
     */
    protected $model = CartModel::class;
    
    /**
     * 允许的字段
     * @var array
     */
    protected $allowFields = ['goods_id', 'quantity', 'selected'];
    
    /**
     * 是否需要登录
     * @var bool
     */
    protected $needLogin = true;
    
    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 只查询当前用户的购物车
        $where[] = ['user_id', '=', $this->userId];
        
        // 默认按创建时间倒序
        $sort = ['create_time' => 'desc'];
    }
    
    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        $items = $list->items();
        foreach ($items as &$item) {
            $this->formatCartItem($item);
        }
        
        return [
            'total' => $list->total(),
            'page' => $list->currentPage(),
            'limit' => $list->listRows(),
            'list' => $items
        ];
    }
    
    /**
     * 保存前的钩子方法
     * @param array &$data 表单数据
     */
    protected function beforeSave(&$data)
    {
        // 检查商品是否存在
        $goods = Goods::where('id', $data['goods_id'])->find();
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        // 检查商品是否上架
        if ($goods->status != Goods::STATUS_ON_SALE) {
            $this->error('商品已下架');
        }
        
        // 检查库存
        if ($goods->stock < $data['quantity']) {
            $this->error('商品库存不足');
        }
        
        // 检查是否已在购物车中
        $exists = CartModel::where([
            'user_id' => $this->userId,
            'goods_id' => $data['goods_id']
        ])->find();
        
        if ($exists) {
            // 更新数量
            $newQuantity = $exists->quantity + $data['quantity'];
            if ($newQuantity > $goods->stock) {
                $this->error('商品库存不足');
            }
            
            $exists->quantity = $newQuantity;
            if ($exists->save()) {
                $this->success('添加成功', ['id' => $exists->id]);
            } else {
                $this->error('添加失败');
            }
        }
        
        // 设置用户ID
        $data['user_id'] = $this->userId;
    }
    
    /**
     * 更新购物车商品数量
     * @param int $id 购物车ID
     * @return void
     */
    public function updateQuantity($id)
    {
        $params = $this->request->post();
        
        // 参数验证
        if (!isset($params['quantity']) || intval($params['quantity']) <= 0) {
            $this->error('商品数量必须大于0');
        }
        
        // 查询购物车信息
        $cart = CartModel::where([
            'id' => $id,
            'user_id' => $this->userId
        ])->find();
        
        if (!$cart) {
            $this->error('购物车商品不存在');
        }
        
        // 检查商品库存
        $goods = Goods::where('id', $cart->goods_id)->find();
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        if ($goods->stock < $params['quantity']) {
            $this->error('商品库存不足');
        }
        
        // 更新数量
        $cart->quantity = $params['quantity'];
        if ($cart->save()) {
            $this->success('更新成功');
        } else {
            $this->error('更新失败');
        }
    }
    
    /**
     * 更新购物车商品选中状态
     * @param int $id 购物车ID
     * @return void
     */
    public function updateSelected($id)
    {
        $params = $this->request->post();
        
        // 参数验证
        if (!isset($params['selected'])) {
            $this->error('选中状态不能为空');
        }
        
        // 查询购物车信息
        $cart = CartModel::where([
            'id' => $id,
            'user_id' => $this->userId
        ])->find();
        
        if (!$cart) {
            $this->error('购物车商品不存在');
        }
        
        // 更新选中状态
        $cart->selected = boolval($params['selected']);
        if ($cart->save()) {
            $this->success('更新成功');
        } else {
            $this->error('更新失败');
        }
    }
    
    /**
     * 全选/取消全选
     * @return void
     */
    public function selectAll()
    {
        $params = $this->request->post();
        
        // 参数验证
        if (!isset($params['selected'])) {
            $this->error('选中状态不能为空');
        }
        
        // 更新所有购物车商品的选中状态
        $result = CartModel::where('user_id', $this->userId)
            ->update(['selected' => boolval($params['selected'])]);
        
        if ($result !== false) {
            $this->success('更新成功');
        } else {
            $this->error('更新失败');
        }
    }
    
    /**
     * 删除选中的购物车商品
     * @return void
     */
    public function deleteSelected()
    {
        $result = CartModel::where([
            'user_id' => $this->userId,
            'selected' => true
        ])->delete();
        
        if ($result !== false) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    /**
     * 格式化购物车商品数据
     * @param array &$cartItem 购物车商品数据
     */
    protected function formatCartItem(&$cartItem)
    {
        // 关联商品信息
        $cartItem->append(['create_time_text'])
            ->with(['goods' => function($query) {
                $query->append(['cover_url', 'price_text', 'status_text']);
            }]);
    }
}