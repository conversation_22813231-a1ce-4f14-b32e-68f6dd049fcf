<?php
declare (strict_types = 1);

namespace app\service;

use app\model\CustomerService as CustomerServiceModel;
use app\model\ChatSession;
use app\model\ChatMessage;
use think\facade\Db;

/**
 * 客服系统服务类
 */
class CustomerService
{
    /**
     * @var CustomerServiceModel
     */
    protected $model;

    /**
     * @var ChatSession
     */
    protected $sessionModel;

    /**
     * @var ChatMessage
     */
    protected $messageModel;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CustomerServiceModel();
        $this->sessionModel = new ChatSession();
        $this->messageModel = new ChatMessage();
    }

    /**
     * 创建新的客服会话
     * @param int $userId 用户ID
     * @param int|null $groupId 客服分组ID
     * @return array|null
     */
    public function createServiceSession($userId, $groupId = null)
    {
        // 检查用户是否已有活动会话
        $activeSession = $this->sessionModel->getUserActiveSession($userId);
        if ($activeSession) {
            return $activeSession;
        }

        // 分配客服
        $service = $this->model->assignService($groupId);
        if (!$service) {
            return null;
        }

        // 创建会话
        return $this->sessionModel->createSession($userId, $service['id']);
    }

    /**
     * 发送消息
     * @param array $messageData 消息数据
     * @return array|null
     */
    public function sendMessage($messageData)
    {
        // 开启事务
        Db::startTrans();
        try {
            // 创建消息
            $message = $this->messageModel->createMessage($messageData);
            if (!$message) {
                throw new \Exception('消息发送失败');
            }

            // 更新会话状态
            $this->sessionModel->updateLastMessageTime($messageData['session_id'], time());

            Db::commit();
            return $message;
        } catch (\Exception $e) {
            Db::rollback();
            return null;
        }
    }

    /**
     * 转接会话
     * @param int $sessionId 会话ID
     * @param int $fromServiceId 原客服ID
     * @param int $toServiceId 目标客服ID
     * @return bool
     */
    public function transferSession($sessionId, $fromServiceId, $toServiceId)
    {
        // 验证会话状态
        $session = $this->sessionModel->find($sessionId);
        if (!$session || $session['status'] != 1 || $session['service_id'] != $fromServiceId) {
            return false;
        }

        // 检查目标客服状态
        if (!$this->model->canAcceptNewSession($toServiceId)) {
            return false;
        }

        // 开启事务
        Db::startTrans();
        try {
            // 转移会话
            $this->sessionModel->transferSession($sessionId, $toServiceId);

            // 记录转接消息
            $this->messageModel->createMessage([
                'session_id' => $sessionId,
                'from_id' => $fromServiceId,
                'from_type' => 'system',
                'to_id' => $session['user_id'],
                'content' => '会话已转接至其他客服',
                'content_type' => 'text',
                'status' => 1,
                'read_status' => 0
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }

    /**
     * 结束会话
     * @param int $sessionId 会话ID
     * @param int $serviceId 客服ID
     * @return bool
     */
    public function endSession($sessionId, $serviceId)
    {
        $session = $this->sessionModel->find($sessionId);
        if (!$session || $session['status'] != 1 || $session['service_id'] != $serviceId) {
            return false;
        }

        // 开启事务
        Db::startTrans();
        try {
            // 结束会话
            $this->sessionModel->endSession($sessionId);

            // 记录结束消息
            $this->messageModel->createMessage([
                'session_id' => $sessionId,
                'from_id' => $serviceId,
                'from_type' => 'system',
                'to_id' => $session['user_id'],
                'content' => '会话已结束',
                'content_type' => 'text',
                'status' => 1,
                'read_status' => 0
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }

    /**
     * 获取客服工作统计
     * @param int $serviceId 客服ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return array
     */
    public function getServiceStatistics($serviceId, $startTime, $endTime)
    {
        // 获取基础统计数据
        $stats = $this->model->getPerformanceStats($serviceId, $startTime, $endTime);

        // 获取会话来源统计
        $sessionStats = $this->sessionModel->getSessionStats([
            ['service_id', '=', $serviceId],
            ['createtime', 'between', [strtotime($startTime), strtotime($endTime)]]
        ]);

        // 获取消息类型统计
        $messageStats = $this->messageModel->getMessageStats([
            ['from_id', '=', $serviceId],
            ['from_type', '=', 'service'],
            ['createtime', 'between', [strtotime($startTime), strtotime($endTime)]]
        ]);

        return array_merge($stats, [
            'session_source' => $sessionStats['source_stats'],
            'message_types' => $messageStats['type_stats']
        ]);
    }

    /**
     * 获取在线可用的客服列表
     * @param int|null $groupId 客服分组ID
     * @return array
     */
    public function getAvailableServices($groupId = null)
    {
        $services = $this->model->getOnlineServices($groupId);
        foreach ($services as &$service) {
            $service['current_sessions'] = $this->model->getCurrentSessionCount($service['id']);
            $service['available'] = $service['current_sessions'] < $service['max_sessions'];
        }
        return $services;
    }

    /**
     * 更新客服状态
     * @param int $serviceId 客服ID
     * @param array $data 更新数据
     * @return bool
     */
    public function updateServiceStatus($serviceId, $data)
    {
        // 验证数据
        if (isset($data['status']) && !in_array($data['status'], [0, 1])) {
            return false;
        }

        return $this->model->where('id', $serviceId)->update($data) !== false;
    }

    /**
     * 获取会话详情
     * @param int $sessionId 会话ID
     * @return array|null
     */
    public function getSessionDetail($sessionId)
    {
        $session = $this->sessionModel->with(['user', 'service'])->find($sessionId);
        if (!$session) {
            return null;
        }

        // 获取会话消息
        $messages = $this->messageModel->getSessionMessages($sessionId, 50);

        return [
            'session' => $session,
            'messages' => $messages
        ];
    }
}