<?php
declare (strict_types = 1);

namespace app\service;

use app\model\Task;
use think\facade\Log;
use think\facade\Cache;

/**
 * 定时任务服务类
 */
class TaskService
{
    /**
     * 执行所有启用的定时任务
     */
    public function runTasks()
    {
        try {
            // 获取所有启用的任务
            $tasks = Task::getEnabledTasks();
            
            foreach ($tasks as $task) {
                $this->executeTask($task);
            }
        } catch (\Exception $e) {
            Log::error('定时任务执行异常：' . $e->getMessage());
        }
    }
    
    /**
     * 执行单个任务
     * @param array $task 任务信息
     */
    public function executeTask($task)
    {
        try {
            // 检查是否允许并发执行
            if ($task['concurrent'] == 0) {
                $lockKey = 'task_lock_' . $task['id'];
                if (!$this->acquireLock($lockKey)) {
                    Log::info('任务正在执行中，跳过本次执行：' . $task['name']);
                    return;
                }
            }
            
            // 创建执行记录
            $startTime = time();
            $recordId = Task::recordTaskExecution($task['id'], 0, '', $startTime);
            
            // 执行命令
            $output = [];
            $code = 0;
            exec($task['command'] . ' 2>&1', $output, $code);
            $result = implode("\n", $output);
            
            // 更新执行记录
            $endTime = time();
            $status = $code === 0 ? 1 : 2; // 1:成功 2:失败
            	\think\facade\Db::table('ad_task_record')
                ->where('id', $recordId)
                ->update([
                    'end_time' => $endTime,
                    'status' => $status,
                    'result' => $result
                ]);
            
            // 执行失败且配置了重试
            if ($status == 2 && $task['retry_times'] > 0) {
                $this->retryTask($task, $result);
            }
            
            // 发送通知
            if ($status == 2 || !empty($task['notify_type'])) {
                $this->sendNotification($task, $status, $result);
            }
            
            // 释放并发锁
            if ($task['concurrent'] == 0) {
                $this->releaseLock('task_lock_' . $task['id']);
            }
            
            Log::info('任务执行完成：' . $task['name'] . '，耗时：' . ($endTime - $startTime) . '秒');
        } catch (\Exception $e) {
            Log::error('任务执行异常：' . $task['name'] . '，' . $e->getMessage());
            
            // 更新执行记录为失败状态
            if (isset($recordId)) {
                	\think\facade\Db::table('ad_task_record')
                    ->where('id', $recordId)
                    ->update([
                        'end_time' => time(),
                        'status' => 2,
                        'result' => $e->getMessage()
                    ]);
            }
            
            // 释放并发锁
            if ($task['concurrent'] == 0) {
                $this->releaseLock('task_lock_' . $task['id']);
            }
        }
    }
    
    /**
     * 重试任务
     * @param array $task 任务信息
     * @param string $lastError 上次执行错误信息
     */
    protected function retryTask($task, $lastError)
    {
        $retryCount = Cache::get('task_retry_' . $task['id'], 0);
        
        if ($retryCount < $task['retry_times']) {
            // 更新重试次数
            Cache::set('task_retry_' . $task['id'], $retryCount + 1, $task['retry_interval']);
            
            Log::info('任务将在' . $task['retry_interval'] . '秒后重试，当前重试次数：' . ($retryCount + 1) . '，任务：' . $task['name']);
            
            // 延迟重试
            sleep($task['retry_interval']);
            $this->executeTask($task);
        } else {
            // 清除重试计数
            Cache::delete('task_retry_' . $task['id']);
            Log::error('任务重试次数已达上限，任务：' . $task['name'] . '，最后错误：' . $lastError);
        }
    }
    
    /**
     * 发送通知
     * @param array $task 任务信息
     * @param int $status 执行状态
     * @param string $result 执行结果
     */
    protected function sendNotification($task, $status, $result)
    {
        try {
            if (empty($task['notify_type']) || empty($task['notify_target'])) {
                return;
            }
            
            $title = '定时任务【' . $task['name'] . '】执行' . ($status == 1 ? '成功' : '失败');
            $content = "任务ID：{$task['id']}\n"
                . "执行时间：" . date('Y-m-d H:i:s') . "\n"
                . "执行状态：" . ($status == 1 ? '成功' : '失败') . "\n"
                . "执行结果：\n{$result}";
            
            // 支持多种通知方式
            foreach ($task['notify_type'] as $type) {
                switch ($type) {
                    case 'email':
                        $this->sendEmail($task['notify_target'], $title, $content);
                        break;
                    case 'webhook':
                        $this->sendWebhook($task['notify_target'], $title, $content);
                        break;
                    case 'sms':
                        $this->sendSms($task['notify_target'], $title);
                        break;
                }
            }
        } catch (\Exception $e) {
            Log::error('发送任务通知失败：' . $e->getMessage());
        }
    }
    
    /**
     * 发送邮件通知
     * @param array $targets 通知目标
     * @param string $title 通知标题
     * @param string $content 通知内容
     */
    protected function sendEmail($targets, $title, $content)
    {
        // 调用邮件服务发送通知
        // TODO: 实现邮件发送逻辑
    }
    
    /**
     * 发送Webhook通知
     * @param array $targets Webhook地址
     * @param string $title 通知标题
     * @param string $content 通知内容
     */
    protected function sendWebhook($targets, $title, $content)
    {
        foreach ($targets as $webhook) {
            try {
                $client = new \GuzzleHttp\Client();
                $response = $client->post($webhook, [
                    'json' => [
                        'title' => $title,
                        'content' => $content
                    ]
                ]);
                
                if ($response->getStatusCode() != 200) {
                    throw new \Exception('Webhook响应状态码异常：' . $response->getStatusCode());
                }
            } catch (\Exception $e) {
                Log::error('Webhook通知发送失败：' . $e->getMessage());
            }
        }
    }
    
    /**
     * 发送短信通知
     * @param array $targets 手机号列表
     * @param string $title 通知标题
     */
    protected function sendSms($targets, $title)
    {
        // 调用短信服务发送通知
        // TODO: 实现短信发送逻辑
    }
    
    /**
     * 获取任务锁
     * @param string $key 锁键名
     * @param int $ttl 锁超时时间（秒）
     * @return bool
     */
    protected function acquireLock($key, $ttl = 3600)
    {
        return Cache::set($key, 1, $ttl);
    }
    
    /**
     * 释放任务锁
     * @param string $key 锁键名
     * @return bool
     */
    protected function releaseLock($key)
    {
        return Cache::delete($key);
    }
}