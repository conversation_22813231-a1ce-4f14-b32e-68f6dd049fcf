<?php
declare (strict_types = 1);

namespace app\service;

use think\facade\Cache;
use app\model\Region as RegionModel;

/**
 * 地区数据服务类
 */
class RegionService
{
    // 缓存前缀
    const CACHE_PREFIX = 'region:';
    // 缓存时间（7天）
    const CACHE_TTL = 604800;

    /**
     * 获取所有省份列表
     * @return array
     */
    public static function getProvinces()
    {
        return self::getCacheData('provinces', function() {
            return self::loadProvinces();
        });
    }

    /**
     * 获取指定省份的城市列表
     * @param string $province 省份名称
     * @return array
     */
    public static function getCities($province)
    {
        return self::getCacheData("cities:{$province}", function() use ($province) {
            return self::loadCities($province);
        });
    }

    /**
     * 获取指定城市的区县列表
     * @param string $province 省份名称
     * @param string $city 城市名称
     * @return array
     */
    public static function getDistricts($province, $city)
    {
        return self::getCacheData("districts:{$province}:{$city}", function() use ($province, $city) {
            return self::loadDistricts($province, $city);
        });
    }

    /**
     * 获取完整的地址数据
     * @return array
     */
    public static function getAllRegions()
    {
        return self::getCacheData('all', function() {
            $regions = [];
            $provinces = self::loadProvinces();
            
            foreach ($provinces as $province) {
                $cities = self::loadCities($province);
                $cityData = [];
                
                foreach ($cities as $city) {
                    $districts = self::loadDistricts($province, $city);
                    $cityData[] = [
                        'name' => $city,
                        'districts' => $districts
                    ];
                }
                
                $regions[] = [
                    'name' => $province,
                    'cities' => $cityData
                ];
            }
            
            return $regions;
        });
    }

    /**
     * 验证地址是否有效
     * @param string $province 省份
     * @param string $city 城市
     * @param string $district 区县
     * @return bool
     */
    public static function validateRegion($province, $city, $district)
    {
        $cities = self::getCities($province);
        if (!in_array($city, $cities)) {
            return false;
        }

        $districts = self::getDistricts($province, $city);
        return in_array($district, $districts);
    }

    /**
     * 从缓存获取数据，如果缓存不存在则通过回调函数获取
     * @param string $key 缓存键名
     * @param callable $callback 回调函数
     * @return mixed
     */
    protected static function getCacheData($key, $callback)
    {
        $cacheKey = self::CACHE_PREFIX . $key;
        $data = Cache::get($cacheKey);
        
        if ($data === null) {
            $data = call_user_func($callback);
            Cache::set($cacheKey, $data, self::CACHE_TTL);
        }
        
        return $data;
    }

    /**
     * 加载省份数据
     * @return array
     */
    protected static function loadProvinces()
    {
        // 这里可以从数据库或其他数据源加载省份数据
        return [
            '北京市', '天津市', '河北省', '山西省', '内蒙古自治区',
            '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省',
            '浙江省', '安徽省', '福建省', '江西省', '山东省',
            '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区',
            '海南省', '重庆市', '四川省', '贵州省', '云南省',
            '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区',
            '新疆维吾尔自治区', '台湾省', '香港特别行政区', '澳门特别行政区'
        ];
    }

    /**
     * 加载城市数据
     * @param string $province 省份名称
     * @return array
     */
    protected static function loadCities($province)
    {
        // 这里需要根据省份从数据库或其他数据源加载城市数据
        // 示例数据
        $cityData = [
            '北京市' => ['北京市'],
            '上海市' => ['上海市'],
            '广东省' => ['广州市', '深圳市', '珠海市', '汕头市', '韶关市', '佛山市', '江门市', '湛江市', '茂名市', '肇庆市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'],
            // 其他省份的城市数据...
        ];
        
        return $cityData[$province] ?? [];
    }

    /**
     * 加载区县数据
     * @param string $province 省份名称
     * @param string $city 城市名称
     * @return array
     */
    protected static function loadDistricts($province, $city)
    {
        // 这里需要根据省份和城市从数据库或其他数据源加载区县数据
        // 示例数据
        $districtData = [
            '广东省' => [
                '广州市' => ['荔湾区', '越秀区', '海珠区', '天河区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'],
                '深圳市' => ['福田区', '罗湖区', '南山区', '宝安区', '龙岗区', '盐田区', '龙华区', '坪山区', '光明区'],
                // 其他城市的区县数据...
            ],
            // 其他省份的区县数据...
        ];
        
        return $districtData[$province][$city] ?? [];
    }

    /**
     * 获取地区树形结构
     * @return array
     */
    public static function getTree()
    {
        return self::getCacheData('tree', function() {
            $jsonFile = app()->getRootPath() . 'app/sql/region_data.json';

            // 如果 JSON 文件存在，直接从文件读取
            if (file_exists($jsonFile)) {
                $jsonContent = file_get_contents($jsonFile);
                return json_decode($jsonContent, true);
            }

            // 从数据库读取数据
            $provinces = RegionModel::getProvinces();
            foreach ($provinces as &$province) {
                $cities = RegionModel::getCities($province['id']);
                foreach ($cities as &$city) {
                    $city['children'] = RegionModel::getDistricts($city['id']);
                }
                $province['children'] = $cities;
            }

            // 将数据保存到 JSON 文件
            file_put_contents($jsonFile, json_encode($provinces, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

            return $provinces;
        });
    }

    /**
     * 清除地区数据缓存
     */
    public static function clearCache()
    {
        Cache::tag(self::CACHE_PREFIX)->clear();
    }
}