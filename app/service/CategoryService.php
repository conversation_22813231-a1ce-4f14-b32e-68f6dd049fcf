<?php
declare (strict_types = 1);

namespace app\service;

use app\model\Category as GoodsCategoryModel;
use app\model\Goods;

/**
 * 商品分类服务类
 */
class CategoryService
{
    /**
     * 获取所有子分类ID
     * 
     * @param int $categoryId 分类ID
     * @return array 子分类ID数组
     */
    public static function getChildrenIds($categoryId)
    {
        $childIds = [];
        $children = GoodsCategoryModel::where('pid', $categoryId)->select()->toArray();
        
        foreach ($children as $child) {
            $childIds[] = $child['id'];
            $grandChildIds = self::getChildrenIds($child['id']);
            $childIds = array_merge($childIds, $grandChildIds);
        }
        
        return $childIds;
    }

    /**
     * 构建树状结构
     * 
     * @param array $categories 分类数组
     * @param int $pid 父级ID
     * @return array
     */
    public static function buildTree($categories, $pid)
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['pid'] == $pid) {
                $category['children'] = self::buildTree($categories, $category['id']);
                $tree[] = $category;
            }
        }
        return $tree;
    }

    /**
     * 获取分类树形结构
     * 
     * @param int $pid 父级ID
     * @param bool $recursive 是否递归获取子分类
     * @return array
     */
    public static function getCategoryTree($pid = 0, $recursive = true)
    {
        $query = GoodsCategoryModel::where('enabled', 1)
            ->order('sort', 'asc');

        if (!$recursive) {
            $query->where('pid', $pid);
        }

        $categories = $query->select()->toArray();

        if (!$recursive) {
            return $categories;
        }

        return self::buildTree($categories, $pid);
    }

    /**
     * 根据类型获取分类树
     * 
     * @param string $type 分类类型
     * @return array
     */
    public static function getCategoryTreeByType($type)
    {
        $categories = GoodsCategoryModel::where('type', $type)
            ->where('enabled', 1)
            ->order('sort', 'asc')
            ->select()
            ->toArray();

        return self::buildTree($categories, 0);
    }

    /**
     * 检查分类是否可以删除
     * 
     * @param int $categoryId 分类ID
     * @return bool
     */
    public static function canDelete($categoryId)
    {
        // 检查是否有子分类
        $childCount = GoodsCategoryModel::where('pid', $categoryId)->count();
        if ($childCount > 0) {
            return false;
        }
        
        // 检查是否有关联商品
        $goodsCount = Goods::where('category_id', $categoryId)->count();
        if ($goodsCount > 0) {
            return false;
        }
        
        return true;
    }

    /**
     * 批量更新分类排序
     * 
     * @param array $sortData 排序数据
     * @return int 成功更新的记录数
     */
    public static function updateSort($sortData)
    {
        $success = 0;
        foreach ($sortData as $item) {
            if (empty($item['id']) || !isset($item['sort'])) {
                continue;
            }
            
            $category = GoodsCategoryModel::find($item['id']);
            if (!$category) {
                continue;
            }
            
            $category->sort = $item['sort'];
            if ($category->save()) {
                $success++;
            }
        }
        
        return $success;
    }
}