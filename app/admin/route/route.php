<?php

use app\admin\middleware\CheckPermission;
use think\facade\Route;
use app\middleware\CrossOrigin;

Route::middleware([CrossOrigin::class]);

Route::get('test', function () {
    return 'Admin Test OK';
});

Route::post('/v1/login/password', 'Login/login')->option(['real_name' => '用户登录']);
Route::post('/v1/refresh/token', 'RefreshToken/token')->option(['real_name' => '刷新token']);

Route::group(function () {
    Route::get('/v1/plugins', 'Plugin/index')->option(['real_name' => '插件信息']);
    Route::get('/v1/plugins/config/:name', 'Plugin/getConfig')->option(['real_name' => '插件信息']);
    Route::post('/v1/plugins/config', 'Plugin/setConfig')->option(['real_name' => '插件信息']);
    Route::post('/v1/plugins/install/:name', 'Plugin/install')->option(['real_name' => '插件信息']);
    Route::put('/v1/plugins/upgrade/:name/:version', 'Plugin/upgrade')->option(['real_name' => '插件信息']);
    Route::delete('/v1/plugins/uninstall/:name', 'Plugin/uninstall')->option(['real_name' => '插件信息']);
    Route::put('/v1/plugins/enable/:name', 'Plugin/enable')->option(['real_name' => '插件信息']);
    Route::put('/v1/plugins/disable/:name', 'Plugin/disable')->option(['real_name' => '插件信息']);
    Route::post('/v1/plugins/uploadInstall', 'Plugin/uploadInstall')->option(['real_name' => '插件信息']);
});

/**
 * admin管理员接口路由
 */
Route::get('/v1/admin', 'system.Admin/index')->option(['real_name' => '管理员列表']);
Route::post('/v1/admin', 'system.Admin/add')->option(['real_name' => '管理员添加']);
Route::put('/v1/admin/:id$', 'system.Admin/edit')->option(['real_name' => '管理员修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/admin/:id$', 'system.Admin/delete')->option(['real_name' => '管理员删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/admin/batch', 'system.Admin/batchDelete')->option(['real_name' => '管理员批量删除']);
Route::get('/v1/admin/info/:id$', 'system.Admin/info')->option(['real_name' => '管理员信息'])->pattern(['id' => '\d+']);
Route::post('/v1/admin/status', 'system.Admin/status')->option(['real_name' => '管理员状态']);

Route::get('/v1/admin/group', 'system.AdminGroup/index')->option(['real_name' => '角色组列表']);
Route::get('/v1/admin/group/rule/:user_id$', 'system.AdminAuth/getGroupByUserId')->option(['real_name' => '管理员角色权限'])->pattern(['id' => '\d+']);
Route::get('/v1/admin/group/rule/auth/:groupId$', 'system.AdminAuth/getAuthByGroup')->option(['real_name' => '管理员角色权限'])->pattern(['id' => '\d+']);
Route::put('/v1/admin/group/rule', 'system.AdminAuth/assignGroupRules')->option(['real_name' => '管理员角色分配权限']);
Route::post('/v1/admin/group', 'system.AdminGroup/add')->option(['real_name' => '角色组添加']);
Route::put('/v1/admin/group/:id$', 'system.AdminGroup/edit')->option(['real_name' => '角色组修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/admin/group/:id$', 'system.AdminGroup/delete')->option(['real_name' => '角色组删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/admin/group/batch', 'system.AdminGroup/batchDelete')->option(['real_name' => '角色组批量删除']);
Route::get('/v1/admin/group/:id$', 'system.AdminGroup/info')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::post('/v1/admin/group/status', 'system.AdminGroup/status')->option(['real_name' => '用户信息']);

Route::get('/v1/admin/rule', 'system.AdminRule/index')->option(['real_name' => '用户信息']);
Route::post('/v1/admin/rule', 'system.AdminRule/add')->option(['real_name' => '用户信息']);
Route::put('/v1/admin/rule/:id$', 'system.AdminRule/edit')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::delete('/v1/admin/rule/:id$', 'system.AdminRule/delete')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::post('/v1/admin/rule/info', 'system.AdminRule/info')->option(['real_name' => '用户信息']);
Route::put('/v1/admin/rule/group', 'system.AdminAuth/assignRole')->option(['real_name' => '管理员角色分配权限']);
Route::get('/v1/admin/rule/tree', 'system.AdminRule/tree')->option(['real_name' => '用户信息']);
Route::post('/v1/admin/rule/status', 'system.AdminRule/status')->option(['real_name' => '用户信息']);

/**
 * user管理接口路由
 */
Route::get('/v1/user', 'user.User/index')->option(['real_name' => '用户信息']);
Route::get('/v1/user/info/:id$', 'user.User/info')->option(['real_name' => '用户列表'])->pattern(['id' => '\d+']);
Route::post('/v1/user', 'user.User/add')->option(['real_name' => '添加用户']);
Route::put('/v1/user/:id$', 'user.User/edit')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::delete('/v1/user/:id$', 'user.User/delete')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::delete('/v1/user/batch', 'user.User/batchDelete')->option(['real_name' => '用户信息']);
Route::post('/v1/user/status', 'user.User/status')->option(['real_name' => '用户信息']);
Route::get('/v1/user/role/group', 'user.User/getRoleGroup')->option(['real_name' => '获取用户组']);

Route::get('/v1/user/group', 'user.UserGroup/index')->option(['real_name' => '用户信息']);
Route::get('/v1/user/group/rule/:user_id$', 'user.UserAuth/getGroupByUserId')->option(['real_name' => '管理员角色权限'])->pattern(['id' => '\d+']);
Route::get('/v1/user/group/rule/auth/:groupId$', 'user.UserAuth/getAuthByGroup')->option(['real_name' => '管理员角色权限'])->pattern(['id' => '\d+']);
Route::put('/v1/user/group/rule', 'user.UserAuth/assignGroupRules')->option(['real_name' => '管理员角色分配权限']);
Route::post('/v1/user/group', 'user.UserGroup/add')->option(['real_name' => '用户信息']);
Route::put('/v1/user/group/:id$', 'user.UserGroup/edit')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::delete('/v1/user/group/:id$', 'user.UserGroup/delete')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::delete('/v1/user/group/batch', 'user.UserGroup/batchDelete')->option(['real_name' => '用户信息']);
Route::get('/v1/user/group/:id$', 'user.UserGroup/info')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::post('/v1/user/group/status', 'user.UserGroup/status')->option(['real_name' => '用户信息']);

Route::get('/v1/user/rule', 'user.UserRule/index')->option(['real_name' => '用户信息']);
Route::post('/v1/user/rule', 'user.UserRule/add')->option(['real_name' => '用户信息']);
Route::put('/v1/user/rule/:id$', 'user.UserRule/edit')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::delete('/v1/user/rule/:id$', 'user.UserRule/delete')->option(['real_name' => '用户信息'])->pattern(['id' => '\d+']);
Route::post('/v1/user/rule/info', 'user.UserRule/info')->option(['real_name' => '用户信息']);
Route::put('/v1/user/rule/group', 'user.UserAuth/assignRole')->option(['real_name' => '管理员角色分配权限']);
Route::get('/v1/user/rule/tree', 'user.UserRule/tree')->option(['real_name' => '用户信息']);
Route::post('/v1/user/rule/status', 'user.UserRule/status')->option(['real_name' => '用户信息']);

/**
 * 配置项管理接口路由
 */
Route::get('/v1/setting/order', 'config.Order/index')->option(['real_name' => '订单配置信息']);
Route::put('/v1/setting/order', 'config.Order/update')->option(['real_name' => '订单配置修改信息']);
Route::get('/v1/setting/site', 'config.Site/index')->option(['real_name' => '网站配置信息']);
Route::put('/v1/setting/site', 'config.Site/update')->option(['real_name' => '网站配置修改信息']);
Route::get('/v1/setting/message', 'config.Message/index')->option(['real_name' => '消息配置信息']);
Route::put('/v1/setting/message', 'config.Message/update')->option(['real_name' => '消息配置修改信息']);
Route::get('/v1/setting/token', 'config.Token/index')->option(['real_name' => '令牌配置信息']);
Route::put('/v1/setting/token', 'config.Token/update')->option(['real_name' => '令牌配置修改信息']);
Route::get('/v1/setting/order', 'config.Order/index')->option(['real_name' => '订单配置信息']);
Route::get('/v1/setting/upload', 'config.Upload/index')->option(['real_name' => '上传配置信息']);
Route::put('/v1/setting/upload', 'config.Upload/update')->option(['real_name' => '上传配置信息']);
Route::get('/v1/setting/waybill', 'config.Waybill/index')->option(['real_name' => '电子面单配置信息']);
Route::put('/v1/setting/waybill', 'config.Waybill/update')->option(['real_name' => '电子面单配置信息']);

/**
 * 菜单管理接口路由
 */
Route::get('/v1/category', 'Category/index')->option(['real_name' => '菜单列表']);
Route::get('/v1/category/type', 'Category/type')->option(['real_name' => '菜单列表']);
Route::post('/v1/category', 'Category/add')->option(['real_name' => '菜单添加']);
Route::put('/v1/category/:id$', 'Category/edit')->option(['real_name' => '菜单修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/category/:id$', 'Category/delete')->option(['real_name' => '菜单删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/category/batch', 'Category/batchDelete')->option(['real_name' => '菜单添加']);
Route::get('/v1/category/tree', 'Category/tree')->option(['real_name' => '菜单列表']);

/**
 * 帮助和公告管理接口路由
 */
Route::get('/v1/website/help', 'Help/index')->option(['real_name' => '帮助列表']);
Route::post('/v1/website/help', 'Help/add')->option(['real_name' => '帮助添加']);
Route::put('/v1/website/help/:id$', 'Help/edit')->option(['real_name' => '帮助修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/website/help/:id$', 'Help/delete')->option(['real_name' => '帮助删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/website/help/batch', 'Help/batchDelete')->option(['real_name' => '帮助批量删除'])->pattern(['id' => '\d+']);
Route::get('/v1/website/notice', 'Announcement/index')->option(['real_name' => '公告列表']);
Route::post('/v1/website/notice', 'Announcement/add')->option(['real_name' => '公告添加']);
Route::put('/v1/website/notice/:id$', 'Announcement/edit')->option(['real_name' => '公告修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/website/notice/:id$', 'Announcement/delete')->option(['real_name' => '公告删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/website/notice/batch', 'Announcement/batchDelete')->option(['real_name' => '公告批量删除'])->pattern(['id' => '\d+']);

/**
 * 定时任务管理接口路由
 */
Route::get('/v1/system/cron', 'system.Task/index')->option(['real_name' => '定时任务列表']);
Route::post('/v1/system/cron', 'system.Task/add')->option(['real_name' => '定时任务添加']);
Route::put('/v1/system/cron/:id$', 'system.Task/edit')->option(['real_name' => '定时任务修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/system/cron/:id$', 'system.Task/delete')->option(['real_name' => '定时任务删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/system/cron/batch', 'system.Task/batchDelete')->option(['real_name' => '定时任务批量删除'])->pattern(['id' => '\d+']);

/**
 * 附件上传接口路由
 */
Route::get('/v1/upload', 'Upload/index')->option(['real_name' => '附件列表']);
Route::put('/v1/upload', 'Upload/index')->option(['real_name' => '附件修改']);
Route::delete('/v1/upload/:path', 'Upload/delete')->option(['real_name' => '附件删除']);
Route::delete('/v1/upload/batch', 'Upload/batchDelete')->option(['real_name' => '附件批量删除']);

/**
 * 财务接口路由
 */
Route::get('/v1/finance', 'payment.PaymentRecord/index')->option(['real_name' => '财务列表']);
Route::put('/v1/finance', 'payment.PaymentRecord/index')->option(['real_name' => '财务修改']);
Route::delete('/v1/finance/:path', 'payment.PaymentRecord/delete')->option(['real_name' => '财务删除']);
Route::delete('/v1/finance/batch', 'payment.PaymentRecord/batchDelete')->option(['real_name' => '财务批量删除']);

/**
 * 系统地址接口
 */
Route::get('/v1/api/region', 'api.Region/tree')->option(['real_name' => '系统地址接口']);
Route::get('/v1/api/region/province', 'api.Region/provinces')->option(['real_name' => '系统地址接口']);
Route::get('/v1/api/region/city', 'api.Region/citiesByCode')->option(['real_name' => '系统地址接口']);
Route::get('/v1/api/region/district', 'api.Region/districtsByCode')->option(['real_name' => '系统地址接口']);

/**
 * 测试接口路由
 */
Route::get('/v1/test/email', 'Sender/sendByEmail')->option(['real_name' => '测试邮件发送']);
Route::get('/v1/test/sms', 'Sender/sendBySms')->option(['real_name' => '测试短信发送']);

Route::import(['admin/route/shop.php']);

Route::group(function () {
    Route::get('/v1/admin/group/rule', 'system.AdminAuth/getAuth')->option(['real_name' => '管理员角色权限']);

    Route::get('/v1/user/group/rule', 'user.UserAuth/getAuth')->option(['real_name' => '会员角色权限']);

    Route::put('/v1/password', 'UserInfo/changePassword')->option(['real_name' => '密码修改']);
    Route::post('/v1/logout', 'Logout/logout')->option(['real_name' => '退出']);
    Route::put('/v1/user_info', 'UserInfo/update')->option(['real_name' => '密码修改']);
    Route::get('/v1/user_info', 'UserInfo/info')->option(['real_name' => '管理员登入信息']);

    Route::post('/v1/upload', 'Upload/upload')->option(['real_name' => '单文件上传']);

})->middleware(CheckPermission::class);


