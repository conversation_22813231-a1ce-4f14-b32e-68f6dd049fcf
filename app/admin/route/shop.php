<?php

use think\facade\Route;

/**
 * 商品接口路由
 */
Route::get('/v1/shop/goods', 'goods.Goods/index')->option(['real_name' => '商品列表']);
Route::put('/v1/shop/goods/:id$', 'goods.Goods/edit')->option(['real_name' => '商品修改'])->pattern(['id' => '\d+']);;
Route::post('/v1/shop/goods', 'goods.Goods/add')->option(['real_name' => '商品添加']);
Route::delete('/v1/shop/goods/:id$', 'goods.Goods/delete')->option(['real_name' => '商品删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/goods/batch', 'goods.Goods/batchDelete')->option(['real_name' => '商品批量删除']);

/**
 * 商品品牌接口路由
 */
Route::get('/v1/shop/brand', 'goods.Brand/index')->option(['real_name' => '品牌列表']);
Route::put('/v1/shop/brand/:id$', 'goods.Brand/edit')->option(['real_name' => '品牌修改'])->pattern(['id' => '\d+']);;
Route::post('/v1/shop/brand', 'goods.Brand/add')->option(['real_name' => '品牌添加']);
Route::delete('/v1/shop/brand/:id$', 'goods.Brand/delete')->option(['real_name' => '品牌删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/brand/batch', 'goods.Brand/batchDelete')->option(['real_name' => '品牌批量删除']);

/**
 * 商品评价接口路由
 */
Route::get('/v1/shop/review', 'goods.GoodsComment/index')->option(['real_name' => '评价列表']);
Route::put('/v1/shop/review/:id$', 'goods.GoodsComment/index')->option(['real_name' => '评价修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/review/:id$', 'goods.GoodsComment/delete')->option(['real_name' => '评价删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/review/batch', 'goods.GoodsComment/batchDelete')->option(['real_name' => '评价批量删除']);

/**
 * 商品订单接口路由
 */
Route::get('/v1/shop/order', 'order.Order/index')->option(['real_name' => '订单列表']);
Route::put('/v1/shop/order:id$', 'order.Order/index')->option(['real_name' => '订单修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/order/:id$', 'order.Order/delete')->option(['real_name' => '订单删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/order/batch', 'order.Order/batchDelete')->option(['real_name' => '订单批量删除']);

/**
 * 运费接口路由
 */
Route::get('/v1/shop/freight', 'goods.ShippingFeeTemplate/index')->option(['real_name' => '运费列表']);
Route::get('/v1/api/shop/freight', 'api.ShippingFee/list')->option(['real_name' => '运费列表']);
Route::post('/v1/shop/freight', 'goods.ShippingFeeTemplate/add')->option(['real_name' => '运费添加']);
Route::put('/v1/shop/freight:id$', 'goods.ShippingFeeTemplate/index')->option(['real_name' => '运费修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/freight/:id$', 'goods.ShippingFeeTemplate/delete')->option(['real_name' => '运费删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/freight/batch', 'goods.ShippingFeeTemplate/batchDelete')->option(['real_name' => '订单运费删除']);

/**
 * 物流公司接口路由
 */
Route::get('/v1/shop/logistics/company', 'goods.LogisticsCompany/index')->option(['real_name' => '物流公司列表']);
Route::put('/v1/shop/logistics/company:id$', 'goods.LogisticsCompany/index')->option(['real_name' => '物流公司修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/logistics/company/:id$', 'goods.LogisticsCompany/delete')->option(['real_name' => '物流公司删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/logistics/company/batch', 'goods.LogisticsCompany/batchDelete')->option(['real_name' => '物流公司删除']);

/**
 * 优惠券接口路由
 */
Route::get('/v1/shop/coupon', 'coupon.Coupon/index')->option(['real_name' => '优惠券列表']);
Route::post('/v1/shop/coupon', 'coupon.Coupon/add')->option(['real_name' => '优惠券新增']);
Route::put('/v1/shop/coupon/:id$', 'coupon.Coupon/edit')->option(['real_name' => '优惠券修改'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/coupon/:id$', 'coupon.Coupon/delete')->option(['real_name' => '优惠券删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/coupon/batch', 'coupon.Coupon/batchDelete')->option(['real_name' => '优惠券删除']);

/**
 * 卡密接口路由
 */
Route::get('/v1/shop/card', 'goods.Card/index')->option(['real_name' => '卡密列表']);
Route::get('/v1/shop/card/export', 'goods.Card/export')->option(['real_name' => '卡密导出']);
Route::put('/v1/shop/card:id$', 'goods.Card/index')->option(['real_name' => '卡密修改'])->pattern(['id' => '\d+']);
Route::post('/v1/shop/card/generate', 'goods.Card/generate')->option(['real_name' => '卡密修改']);
Route::delete('/v1/shop/card/:id$', 'goods.Card/delete')->option(['real_name' => '卡密删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/card/batch', 'goods.Card/batchDelete')->option(['real_name' => '卡密删除']);

/**
 * 电子面单模版接口路由
 */
Route::get('/v1/shop/waybill/template', 'waybill.WaybillTemplate/index')->option(['real_name' => '电子面单模版列表']);
Route::put('/v1/shop/waybill/template/:id$', 'waybill.WaybillTemplate/edit')->option(['real_name' => '电子面单模版修改'])->pattern(['id' => '\d+']);
Route::post('/v1/shop/waybill/template', 'waybill.WaybillTemplate/add')->option(['real_name' => '电子面单模版修改']);
Route::delete('/v1/shop/waybill/template/:id$', 'waybill.WaybillTemplate/delete')->option(['real_name' => '电子面单模版删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/waybill/template/batch', 'waybill.WaybillTemplate/batchDelete')->option(['real_name' => '电子面单模版删除']);

Route::get('/v1/shop/waybill/sender', 'waybill.WaybillSender/index')->option(['real_name' => '电子面单模版列表']);
Route::put('/v1/shop/waybill/sender/:id$', 'waybill.WaybillSender/edit')->option(['real_name' => '电子面单模版修改'])->pattern(['id' => '\d+']);
Route::post('/v1/shop/waybill/sender', 'waybill.WaybillSender/add')->option(['real_name' => '电子面单模版修改']);
Route::delete('/v1/shop/waybill/sender/:id$', 'waybill.WaybillSender/delete')->option(['real_name' => '电子面单模版删除'])->pattern(['id' => '\d+']);
Route::delete('/v1/shop/waybill/sender/batch', 'waybill.WaybillSender/batchDelete')->option(['real_name' => '电子面单模版删除']);

Route::get('/v1/shop/waybill/print', 'waybill.WaybillPrint/index')->option(['real_name' => '电子面单模版列表']);
Route::post('/v1/shop/waybill/print/test', 'waybill.WaybillPrint/testPrint')->option(['real_name' => '电子面单模版列表']);
Route::put('/v1/shop/waybill/print/:id$', 'waybill.WaybillPrint/saveConfig')->option(['real_name' => '电子面单模版修改'])->pattern(['id' => '\d+']);
Route::post('/v1/shop/waybill/print', 'waybill.WaybillPrint/saveConfig')->option(['real_name' => '电子面单模版修改']);
