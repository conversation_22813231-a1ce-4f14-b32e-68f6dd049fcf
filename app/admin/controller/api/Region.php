<?php
declare (strict_types = 1);

namespace app\admin\controller\api;

use app\common\controller\BaseController;
use app\model\Region as RegionModel;
use app\service\RegionService;
use think\App;

/**
 * 地区管理API控制器
 */
class Region extends BaseController
{
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
    }
    
    /**
     * 获取省份列表
     */
    public function provinces()
    {
        $list = RegionModel::getProvinces();
        $this->ok('获取成功', $list);
    }
    
    /**
     * 获取城市列表
     */
    public function cities()
    {
        $provinceId = request()->param('province_id/d');
        if (!$provinceId) {
            $this->fail('请选择省份');
        }
        
        $list = RegionModel::getCities($provinceId);
        $this->ok('获取成功', $list);
    }
    
    /**
     * 获取区县列表
     */
    public function districts()
    {
        $cityId = request()->param('city_id/d');
        if (!$cityId) {
            $this->fail('请选择城市');
        }
        
        $list = RegionModel::getDistricts($cityId);
        $this->ok('获取成功', $list);
    }
    
    /**
     * 获取地区树形结构
     */
    public function tree()
    {
        $tree = $this->getTree();
        $this->ok('获取成功', $tree);
    }
    
    /**
     * 根据地区编码获取下级地区列表
     */
    public function getChildrenByCode()
    {
        $code = request()->param('code/s');
        if (!$code) {
            $this->error('请提供地区编码');
        }
        
        $list = RegionModel::getChildrenByCode($code);
        $this->ok('获取成功', $list);
    }
    
    /**
     * 根据地区编码获取城市列表
     */
    public function citiesByCode()
    {
        $provinceCode = request()->param('province_code/s');
        if (!$provinceCode) {
            $this->error('请提供省份编码');
        }
        
        $provinceId = RegionModel::getParentIdByCode($provinceCode);
        if (is_null($provinceId)) {
            $this->fail('无效的省份编码');
        }

        $list = RegionModel::getCities($provinceId);
        $this->ok('获取成功', $list);
    }
    
    /**
     * 根据地区编码获取区县列表
     */
    public function districtsByCode()
    {
        $cityCode = request()->param('city_code/s');
        if (!$cityCode) {
            $this->error('请提供城市编码');
        }
        
        $cityId = RegionModel::getParentIdByCode($cityCode);
        if (is_null($cityId)) {
            $this->fail('无效的城市编码');
        }
        
        $list = RegionModel::getDistricts($cityId);
        $this->ok('获取成功', $list);
    }

    /**
     * 获取地区树形结构
     * @return array
     */
    public static function getTree()
    {
        return RegionService::getTree();
    }
}