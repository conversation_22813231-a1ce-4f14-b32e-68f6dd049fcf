<?php
declare (strict_types = 1);

namespace app\admin\controller\api;

use app\common\controller\BaseController;
use app\model\GoodsBrand;
use think\App;

/**
 * 品牌管理API控制器
 */
class Brand extends BaseController
{
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
    }
    
    /**
     * 获取品牌列表
     */
    public function list()
    {
        $page = request()->param('page/d', 1);
        $limit = request()->param('limit/d', 10);
        $params = [
            'keyword' => request()->param('keyword'),
            'enabled' => request()->param('enabled'),
            'order_field' => request()->param('order_field'),
            'order_type' => request()->param('order_type')
        ];
        
        $result = GoodsBrand::getBrandList($params, $page, $limit);
        $this->ok('获取成功', $result);
    }
}