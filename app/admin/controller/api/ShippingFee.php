<?php
declare (strict_types = 1);

namespace app\admin\controller\api;

use app\common\controller\BaseController;
use app\model\ShippingFeeTemplate;
use think\App;

/**
 * 运费模板管理API控制器
 */
class ShippingFee extends BaseController
{
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
    }
    
    /**
     * 获取运费模板列表
     */
    public function list()
    {

        $query = ShippingFeeTemplate::order('id', 'desc');
        
        // 关键词搜索
        $keyword = request()->param('keyword');
        if ($keyword) {
            $query->where('name', 'like', '%' . $keyword . '%');
        }
        
        // 状态筛选
        $enabled = request()->param('enabled');
        if (isset($enabled)) {
            $query->where('status', $enabled);
        }
        
        $list = $query->select()->toArray();
        
        $this->ok('获取成功', $list);
    }
}