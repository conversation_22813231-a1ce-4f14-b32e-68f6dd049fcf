<?php

namespace app\admin\controller;

use app\common\controller\BaseController;
use think\facade\Cache;
use think\facade\Request;

class Index extends BaseController
{
    public function index()
    {
        Cache::tag('route')->clear();
        $request = Request::instance();
        $url = $request->url();
        $path = parse_url($url, PHP_URL_PATH);
        $cleanPath = ltrim($path, '/');
        var_dump($cleanPath);
    }

    public function test()
    {
        echo 2;die;
    }

}
