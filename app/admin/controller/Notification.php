<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\ApiCode;
use app\common\controller\BaseController;
use app\common\notification\NotificationService;
use think\facade\Request;

class Notification extends BaseController
{
    /**
     * 通知服务
     * @var NotificationService
     */
    protected $notificationService;

    public function __construct()
    {
        parent::__construct();
        $this->notificationService = new NotificationService();
    }

    /**
     * 发送系统通知
     * @return \think\Response
     */
    public function sendSystemNotification()
    {
        $userId = Request::param('user_id/d');
        $title = Request::param('title/s');
        $content = Request::param('content/s');

        if (empty($userId) || empty($title) || empty($content)) {
            $this->ok('参数错误', [], ApiCode::PARAM_ERROR);
        }

        $result = $this->notificationService->createSystemNotification($userId, $title, $content);
        if ($result) {
            $this->ok('发送成功');
        } else {
            $this->ok('发送失败', [], ApiCode::CODE_ERROR);
        }
    }

    /**
     * 发送订单通知
     * @return \think\Response
     */
    public function sendOrderNotification()
    {
        $userId = Request::param('user_id/d');
        $title = Request::param('title/s');
        $content = Request::param('content/s');

        if (empty($userId) || empty($title) || empty($content)) {
            $this->ok('参数错误', [], ApiCode::PARAM_ERROR);
        }

        $result = $this->notificationService->createOrderNotification($userId, $title, $content);
        if ($result) {
            $this->ok('发送成功');
        } else {
            $this->ok('发送失败', [], ApiCode::CODE_ERROR);
        }
    }

    /**
     * 发送活动通知
     * @return \think\Response
     */
    public function sendActivityNotification()
    {
        $userId = Request::param('user_id/d');
        $title = Request::param('title/s');
        $content = Request::param('content/s');

        if (empty($userId) || empty($title) || empty($content)) {
            $this->ok('参数错误', [], ApiCode::PARAM_ERROR);
        }

        $result = $this->notificationService->createActivityNotification($userId, $title, $content);
        if ($result) {
            $this->ok('发送成功');
        } else {
            $this->ok('发送失败', [], ApiCode::CODE_ERROR);
        }
    }

    /**
     * 发送优惠券通知
     * @return \think\Response
     */
    public function sendCouponNotification()
    {
        $userId = Request::param('user_id/d');
        $title = Request::param('title/s');
        $content = Request::param('content/s');

        if (empty($userId) || empty($title) || empty($content)) {
            $this->ok('参数错误', [], ApiCode::PARAM_ERROR);
        }

        $result = $this->notificationService->createCouponNotification($userId, $title, $content);
        if ($result) {
            $this->ok('发送成功');
        } else {
            $this->ok('发送失败', [], ApiCode::CODE_ERROR);
        }
    }

    /**
     * 批量发送系统通知
     * @return \think\Response
     */
    public function batchSendSystemNotification()
    {
        $userIds = Request::param('user_ids/a');
        $title = Request::param('title/s');
        $content = Request::param('content/s');

        if (empty($userIds) || empty($title) || empty($content)) {
            $this->ok('参数错误', [], ApiCode::PARAM_ERROR);
        }

        $result = $this->notificationService->batchCreateSystemNotification($userIds, $title, $content);
        if ($result > 0) {
            $this->ok('发送成功，成功发送' . $result . '条通知');
        } else {
            $this->ok('发送失败', [], ApiCode::CODE_ERROR);
        }
    }
}