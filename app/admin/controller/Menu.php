<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\ApiCode;
use app\admin\repository\MenuRepository;
use app\common\controller\BaseController;
use think\App;

class Menu extends BaseController
{
    /**
     * @var MenuRepository
     */
    protected $menuRepository;

    public function __construct(App $app, MenuRepository $menuRepository)
    {
        parent::__construct($app);
        $this->menuRepository = $menuRepository;
    }

    /**
     * 获取菜单列表
     * @return \think\Response
     */
    public function index()
    {
        try {
            $list = $this->menuRepository->getList();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $list);
    }

    /**
     * 获取树形菜单
     * @return \think\Response
     */
    public function tree()
    {
        try {
            $tree = $this->menuRepository->getTree();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $tree);
    }

    /**
     * 添加菜单
     * @return \think\Response
     */
    public function add()
    {
        $data = $this->request->post();
        try {
            $result = $this->menuRepository->add($data);
            if ($result) {
            } else {
                $this->error(ApiCode::getMessage(ApiCode::OPERATION_FAILED));
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS));
    }

    /**
     * 更新菜单
     * @return \think\Response
     */
    public function update()
    {
        $data = $this->request->put();
        try {
            $result = $this->menuRepository->update($data);
            if ($result) {
            } else {
                $this->error(ApiCode::getMessage(ApiCode::OPERATION_FAILED));
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS));
    }

    /**
     * 删除菜单
     * @param int $id
     * @return \think\Response
     */
    public function delete($id)
    {
        try {
            $result = $this->menuRepository->delete((int)$id);
            if ($result) {
            } else {
                $this->error(ApiCode::getMessage(ApiCode::OPERATION_FAILED));
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS));
    }
}