<?php

namespace app\admin\controller;

use app\common\Auth;
use app\common\controller\BaseController;
use app\common\TokenStorage;
use think\facade\Request;

class RefreshToken extends BaseController
{

    public function token()
    {

        $request = Request::instance();
        $token = $request->post("refresh_token");

//        TokenStorage::setTable('sys_admin_token');
//        TokenStorage::setRefreshTable('sys_admin_refresh_token');

        try {
            $result = Auth::refreshToken($token);

            if (!$result) {
                $this->fail('刷新失败', [], -2);
            }

            $result['refresh_token'] = $token;
        } catch (\Exception $e){
            $this->fail($e->getMessage(), [], -2);
        }

        $this->ok('刷新成功', $result);
    }

}