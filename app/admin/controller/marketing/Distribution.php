<?php

namespace app\admin\controller\marketing;

use app\model\marketing\Distribution as DistributionModel;
use think\App;
use app\common\controller\BaseAdminController;

class Distribution extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|规则名称' => 'require|unique:distribution_rule',
        'commission_rate|分销比例' => 'require|float|between:0,100',
        'min_order_amount|最低订单金额' => 'require|float|min:0',
        'start_time|开始时间' => 'require|integer',
        'end_time|结束时间' => 'require|integer|gt:start_time',
        'status|状态' => 'require|in:0,1'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new DistributionModel();
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit' && $id) {
            return [
                'name|规则名称' => 'require|unique:distribution_rule,name,' . $id,
                'commission_rate|分销比例' => 'require|float|between:0,100',
                'min_order_amount|最低订单金额' => 'require|float|min:0',
                'start_time|开始时间' => 'require|integer',
                'end_time|结束时间' => 'require|integer|gt:start_time',
                'status|状态' => 'require|in:0,1'
            ];
        }
        
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array
     */
    protected function beforeAdd($data)
    {
        // 处理商品ID
        if (isset($data['goods_ids']) && is_array($data['goods_ids'])) {
            $data['goods_ids'] = implode(',', $data['goods_ids']);
        }
        
        return $data;
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @return array
     */
    protected function beforeEdit($data)
    {
        // 处理商品ID
        if (isset($data['goods_ids']) && is_array($data['goods_ids'])) {
            $data['goods_ids'] = implode(',', $data['goods_ids']);
        }
        
        return $data;
    }
}