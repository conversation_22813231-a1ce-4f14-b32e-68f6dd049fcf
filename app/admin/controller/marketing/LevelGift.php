<?php

namespace app\admin\controller\marketing;

use app\model\LevelGift as LevelGiftModel;
use think\App;
use app\common\controller\BaseAdminController;

class LevelGift extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|活动名称' => 'require',
        'level_id|会员等级' => 'require|number',
        'stock|礼品库存' => 'require|number|egt:0',
        'receive_limit|领取限制' => 'require|number|egt:0',
        'start_time|开始时间' => 'require|number',
        'end_time|结束时间' => 'require|number|gt:start_time',
        'status|活动状态' => 'require|in:0,1,2'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new LevelGiftModel();
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 处理商品和优惠券ID
        if (!empty($data['goods_ids']) && is_array($data['goods_ids'])) {
            $data['goods_ids'] = implode(',', $data['goods_ids']);
        }
        if (!empty($data['coupon_ids']) && is_array($data['coupon_ids'])) {
            $data['coupon_ids'] = implode(',', $data['coupon_ids']);
        }
        
        return $data;
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 处理商品和优惠券ID
        if (!empty($data['goods_ids']) && is_array($data['goods_ids'])) {
            $data['goods_ids'] = implode(',', $data['goods_ids']);
        }
        if (!empty($data['coupon_ids']) && is_array($data['coupon_ids'])) {
            $data['coupon_ids'] = implode(',', $data['coupon_ids']);
        }
        
        return $data;
    }

    /**
     * 获取详情后的钩子方法
     * @param \think\Model $info 记录信息
     * @return array|null
     */
    protected function afterInfo($info)
    {
        // 处理商品和优惠券ID为数组
        if (!empty($info['goods_ids'])) {
            $info['goods_ids'] = explode(',', $info['goods_ids']);
        }
        if (!empty($info['coupon_ids'])) {
            $info['coupon_ids'] = explode(',', $info['coupon_ids']);
        }
        
        return $info;
    }

    /**
     * 删除前的钩子方法
     * @param int $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        // 检查是否有用户已领取该活动礼品
        $hasRecords = app\model\LevelGiftRecord::where('gift_id', $id)->count();
        if ($hasRecords > 0) {
            $this->error('该活动已有用户领取礼品，不能删除');
            return false;
        }
        return true;
    }
}