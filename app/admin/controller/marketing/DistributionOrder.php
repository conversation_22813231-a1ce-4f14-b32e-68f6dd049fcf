<?php

namespace app\admin\controller\marketing;

use app\model\marketing\DistributionOrder as DistributionOrderModel;
use think\App;
use app\common\controller\BaseAdminController;

class DistributionOrder extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'order_no|订单编号' => 'require|unique:distribution_order',
        'rule_id|规则ID' => 'require|integer',
        'user_id|会员ID' => 'require|integer',
        'distributor_id|分销员ID' => 'require|integer',
        'goods_id|商品ID' => 'require|integer',
        'order_amount|订单金额' => 'require|float|min:0',
        'commission_rate|分销比例' => 'require|float|between:0,100',
        'commission_amount|佣金金额' => 'require|float|min:0',
        'order_status|订单状态' => 'require|in:0,1,2,3,4',
        'settlement_status|结算状态' => 'require|in:0,1'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new DistributionOrderModel();
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit' && $id) {
            return [
                'order_status|订单状态' => 'require|in:0,1,2,3,4',
                'settlement_status|结算状态' => 'require|in:0,1'
            ];
        }
        
        return $this->validateRule;
    }

    /**
     * 更新结算状态
     * @param int $id 订单ID
     * @return \think\response\Json
     */
    public function settlement($id)
    {
        if (!$this->model->where('id', $id)->find()) {
            return $this->error('订单不存在');
        }

        $data = [
            'settlement_status' => 1,
            'settlement_time' => time(),
            'update_time' => time()
        ];

        if ($this->model->where('id', $id)->update($data)) {
            return $this->success('结算成功');
        }

        return $this->error('结算失败');
    }

    /**
     * 获取订单统计数据
     * @return \think\response\Json
     */
    public function statistics()
    {
        $data = [
            'total_amount' => $this->model->sum('order_amount'),
            'total_commission' => $this->model->sum('commission_amount'),
            'settled_count' => $this->model->where('settlement_status', 1)->count(),
            'unsettled_count' => $this->model->where('settlement_status', 0)->count()
        ];

        return $this->success('获取成功', $data);
    }
}