<?php

namespace app\admin\controller\marketing;

use app\model\LevelGiftRecord as LevelGiftRecordModel;
use think\App;
use app\common\controller\BaseAdminController;

class LevelGiftRecord extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'gift_id|活动ID' => 'require|number',
        'user_id|用户ID' => 'require|number',
        'user_name|会员姓名' => 'require',
        'user_phone|会员手机' => 'require|mobile',
        'level_id|会员等级' => 'require|number',
        'status|领取状态' => 'require|in:0,1'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new LevelGiftRecordModel();
    }

    /**
     * 获取列表前的钩子方法
     * @param array $params 查询参数
     * @return array|null
     */
    protected function beforeList($params)
    {
        // 添加查询条件
        if (!empty($params['gift_id'])) {
            $this->model = $this->model->where('gift_id', $params['gift_id']);
        }
        if (!empty($params['user_name'])) {
            $this->model = $this->model->where('user_name', 'like', '%' . $params['user_name'] . '%');
        }
        if (!empty($params['user_phone'])) {
            $this->model = $this->model->where('user_phone', 'like', '%' . $params['user_phone'] . '%');
        }
        if (isset($params['status']) && $params['status'] !== '') {
            $this->model = $this->model->where('status', $params['status']);
        }
        if (!empty($params['start_time'])) {
            $this->model = $this->model->where('receive_time', '>=', $params['start_time']);
        }
        if (!empty($params['end_time'])) {
            $this->model = $this->model->where('receive_time', '<=', $params['end_time']);
        }
        
        return $params;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 设置领取时间
        $data['receive_time'] = time();
        return $data;
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 不允许修改用户信息和活动信息
        unset($data['gift_id'], $data['user_id'], $data['user_name'], 
              $data['user_phone'], $data['level_id'], $data['receive_time']);
        return $data;
    }

    /**
     * 获取详情后的钩子方法
     * @param \think\Model $info 记录信息
     * @return array|null
     */
    protected function afterInfo($info)
    {
        // 获取关联的活动信息
        $info['gift'] = app\model\LevelGift::where('id', $info['gift_id'])
            ->field('id,name,level_id,stock,receive_limit')
            ->find();
        return $info;
    }
}