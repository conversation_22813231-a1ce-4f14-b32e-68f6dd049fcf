<?php

namespace app\admin\controller\marketing;

use app\model\GroupBuy as GroupBuyModel;
use think\App;
use app\common\controller\BaseAdminController;
use think\facade\Db;

class GroupBuy extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|拼团名称' => 'require',
        'type|拼团类型' => 'require|in:normal,newUser',
        'group_size|成团人数' => 'require|integer|min:2',
        'goods_ids|参与商品' => 'require|array',
        'start_time|开始时间' => 'require|integer',
        'end_time|结束时间' => 'require|integer|gt:start_time',
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new GroupBuyModel();
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 处理商品ID数组
        if (isset($data['goods_ids']) && is_array($data['goods_ids'])) {
            $data['goods_ids'] = implode(',', $data['goods_ids']);
            $data['goods_count'] = count($data['goods_ids']);
        }

        // 设置初始状态
        $data['status'] = 0;
        $data['success_count'] = 0;
        $data['ongoing_count'] = 0;

        return $data;
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 处理商品ID数组
        if (isset($data['goods_ids']) && is_array($data['goods_ids'])) {
            $data['goods_ids'] = implode(',', $data['goods_ids']);
            $data['goods_count'] = count($data['goods_ids']);
        }

        return $data;
    }

    /**
     * 获取列表前的钩子方法
     * @param array $params 查询参数
     * @return array|null
     */
    protected function beforeList($params)
    {
        // 添加时间范围查询
        if (!empty($params['time_range'])) {
            $timeRange = explode(' - ', $params['time_range']);
            $params['start_time'] = strtotime($timeRange[0]);
            $params['end_time'] = strtotime($timeRange[1]);
        }

        return $params;
    }

    /**
     * 获取列表后的钩子方法
     * @param array $data 列表数据
     * @return array|null
     */
    protected function afterList($data)
    {
        // 处理商品ID为数组
        foreach ($data as &$item) {
            if (!empty($item['goods_ids'])) {
                $item['goods_ids'] = explode(',', $item['goods_ids']);
            }
        }

        return $data;
    }

    /**
     * 更新活动状态
     * @param int $id 活动ID
     * @param int $status 状态值
     * @return bool
     */
    public function updateStatus($id, $status)
    {
        if (!in_array($status, [0, 1, 2])) {
            $this->error('状态值不正确');
        }

        $result = $this->model->where('id', $id)->update(['status' => $status]);
        return $result !== false;
    }
}