<?php

namespace app\admin\controller;

use app\admin\entity\UserEntity;
use app\common\controller\BaseAdminController;
use app\common\upload\UploadService;
use think\App;
use think\facade\Config;
use think\facade\Request;
use app\model\Attachment;

class Upload extends BaseAdminController
{

    private $config;

    /**
     * 构造方法
     * @access public
     * @param \think\App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->config = Config::get('upload');
        $this->model = new Attachment();
    }

    /**
     * 单文件上传
     */
    public function upload(UserEntity $userEntity)
    {
        $file = Request::file('file');
        if (!$file) {
            $this->error('请选择要上传的文件');
        }

        $config = $this->config[$this->config['default_type']];
        $providers = $config['providers'][$config['default_provider']];
        $domain = rtrim($providers['domain'], '/');

        $savePath = Request::param('save_path', 'uploads');
        $options = [];

        try {
            $result = UploadService::upload($file, $savePath, $options);
            if ($result === false) {
                $this->error(UploadService::getError());
            }
            
            // 保存附件信息
            $attachment = new Attachment();
            $attachment->save([
                'name' => $file->getOriginalName(),
                'path' => $result['path'],
                'url' => $result['url'],
                'size' => $result['size'],
                'mime_type' => $result['mime_type'],
                'extension' => $result['extension'],
                'storage' => 'local',
                'user_type' => 'admin',
                'user_id' => $userEntity->id,
                'enabled' => 1
            ]);
        } catch (\Exception $e) {
            $this->error(UploadService::getError());
        }

        $result['url'] = $domain . $result['url'];
        return $this->success('上传成功', $result);
    }

    /**
     * 多文件上传
     */
    public function multiUpload(UserEntity $userEntity)
    {
        $files = Request::file();
        if (empty($files)) {
            $this->error('请选择要上传的文件');
        }

        $savePath = Request::param('save_path', 'uploads');
        $options = [];
        $results = [];
        $errors = [];

        foreach ($files as $file) {
            try {
                $result = UploadService::upload($file, $savePath, $options);
                if ($result === false) {
                    $errors[] = ['name' => $file->getOriginalName(), 'error' => UploadService::getError()];
                    continue;
                }
                
                // 保存附件信息
                $attachment = new Attachment();
                $attachment->save([
                    'name' => $file->getOriginalName(),
                    'path' => $result['path'],
                    'url' => $result['url'],
                    'size' => $result['size'],
                    'mime_type' => $result['mime_type'],
                    'extension' => $result['extension'],
                    'storage' => 'local',
                    'user_type' => 'admin',
                    'user_id' => $userEntity->id,
                    'enabled' => 1
                ]);
                
                $results[] = $result;
            } catch (\Exception $e) {
                $errors[] = ['name' => $file->getOriginalName(), 'error' => $e->getMessage()];
            }
        }

        $this->ok('上传完成', [
            'success' => $results,
            'error' => $errors
        ]);
    }

    /**
     * 删除文件
     * @param $id
     */
    public function delete($id = null)
    {
        $filePath = Request::param('file_path');
        if (empty($filePath)) {
            $this->error('请指定要删除的文件');
        }

        try {
            // 删除文件
            $result = UploadService::delete($filePath);
            if ($result === false) {
                $this->error(UploadService::getError());
            }
            
            // 删除附件记录
            Attachment::where('path', $filePath)->delete();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        return true;
    }

    public function afterIndex($list)
    {
        $items = $list->items();

        if (empty($items)) {
            return ['record' => [], 'count' => 0];
        }

        $config = $this->config[$this->config['default_type']];
        $providers = $config['providers'][$config['default_provider']];
        $domain = rtrim($providers['domain'], '/');

        foreach ($items as &$item) {
            $item['url'] = $domain . $item['url'];
        }

        return ['record' => $items, 'count' => $list->total()];
    }
}