<?php
declare (strict_types = 1);

namespace app\admin\controller\waybill;

use app\common\controller\BaseAdminController;
use app\model\WaybillTemplate as WaybillTemplateModel;
use app\model\LogisticsCompany;
use app\model\WaybillSender;
use app\model\WaybillPrintConfig;
use think\App;
use app\model\Region;

/**
 * 电子面单模板管理API控制器
 */
class WaybillTemplate extends BaseAdminController
{
    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->model = new WaybillTemplateModel();
        
        // 定义验证规则
        $this->validateRule = [
            'name' => 'require|max:50',
            'logistics_id' => 'require|number',
            'sender_id' => 'require|number',
            'printer_id' => 'require|number',
            'status' => 'in:0,1',
        ];
        
        // 允许修改的字段
        $this->allowFields = [
            'name', 'logistics_id', 'sender_id',
            'printer_id', 'status'
        ];
    }

    /**
     * 获取列表
     */
    public function index()
    {
        // 构建查询参数
        list($this->currentPage, $this->pageSize, $sort, $where) = $this->buildParames();
        
        // 查询前的钩子方法
        $this->beforeIndex($where, $sort);
        
        // 执行查询
        $list = $this->model
            ->with([
                'logistics' => function($query) {
                    $query->field('id,name,kdn_code,kd100_free_code,kd100_pay_code,cainiao_bird_code');
                },
                'sender' => function($query) {
                    $query->field('id,name,phone,province,city,district,address,is_default,mobile')
                        ->withAttr('province', function($value) {
                            return Region::getByCode($value)?->name ?? '';
                        })
                        ->withAttr('city', function($value) {
                            return Region::getByCode($value)?->name ?? '';
                        })
                        ->withAttr('district', function($value) {
                            return Region::getByCode($value)?->name ?? '';
                        });
                },
                'printer' => function($query) {
                    $query->field('id,print_size,print_time,print_content,auto_print,print_preview,default_printer');
                }
            ])
            ->where($where)
            ->order($sort)
            ->paginate([
                'list_rows' => $this->pageSize,
                'page' => $this->currentPage,
            ]);
        
        // 查询后的钩子方法
        $data = $this->afterIndex($list);

        // 如果afterIndex方法没有返回数据，则使用默认格式
        if ($data === null) {
            $data = ['record' => $list->items(), 'count' => $list->total()];
        }

        $this->success('获取成功', $data);
    }

    /**
     * 获取详情
     */
    public function read($id)
    {
        $info = $this->model->with([
            'logistics' => function($query) {
                $query->field('id,name,kdn_code,kd100_free_code,kd100_pay_code,cainiao_bird_code');
            },
            'sender' => function($query) {
                $query->field('id,name,phone,province,city,district,address,is_default')
                    ->withAttr('province', function($value) {
                        return Region::getByCode($value)?->name ?? '';
                    })
                    ->withAttr('city', function($value) {
                        return Region::getByCode($value)?->name ?? '';
                    })
                    ->withAttr('district', function($value) {
                        return Region::getByCode($value)?->name ?? '';
                    });
            },
            'printer' => function($query) {
                $query->field('id,print_size,print_time,print_content,auto_print,print_preview');
            }
        ])->find($id);
        
        if (!$info) {
            $this->fail('模板不存在');
        }
        
        $this->success('获取成功', $info);
    }
    
    /**
     * 设置默认模板
     */
    public function setDefault()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            $this->fail('参数错误');
        }
        
        $template = $this->model->find($id);
        if (!$template) {
            $this->fail('模板不存在');
        }
        
        if ($template->status == 0) {
            $this->fail('禁用状态的模板不能设为默认');
        }
        
        if ($this->model->setDefaultTemplate($id)) {
            $this->success('设置成功');
        } else {
            $this->fail('设置失败');
        }
    }
}