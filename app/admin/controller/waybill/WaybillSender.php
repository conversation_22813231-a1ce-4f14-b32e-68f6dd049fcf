<?php
declare (strict_types = 1);

namespace app\admin\controller\waybill;

use app\common\controller\BaseAdminController;
use app\model\WaybillSender as WaybillSenderModel;
use app\model\Region;
use think\App;

/**
 * 电子面单发件人管理API控制器
 */
class WaybillSender extends BaseAdminController
{
    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->model = new WaybillSenderModel();
        
        // 定义验证规则
        $this->validateRule = [
            'name' => 'require|max:50',
            'phone' => 'max:20',
            'mobile' => 'require|max:20',
            'province' => 'require|max:6',
            'city' => 'require|max:6',
            'district' => 'require|max:6',
            'address' => 'require|max:255',
            'postcode' => 'require|max:6',
            'isDefault' => 'boolean',
        ];
        
        // 允许修改的字段
        $this->allowFields = [
            'name', 'phone', 'mobile', 'province', 'city',
            'district', 'address', 'postcode', 'is_default'
        ];
    }
    
    /**
     * 处理写入数据
     * @param array $data
     * @return array
     */
    protected function beforeAdd($data)
    {
        if (isset($data['isDefault'])) {
            $data['is_default'] = $data['isDefault'] ? 1 : 0;
            unset($data['isDefault']);
        }
        return $data;
    }
    
    /**
     * 设置默认发件人
     */
    public function setDefault()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            $this->fail('参数错误');
        }
        
        $sender = $this->model->find($id);
        if (!$sender) {
            $this->fail('发件人不存在');
        }
        
        if ($this->model->setDefaultSender($id)) {
            $this->success('设置成功');
        } else {
            $this->fail('设置失败');
        }
    }

    /**
     * 处理列表数据
     * @param array $list
     * @return array
     */
    protected function afterIndex($list)
    {
        if (empty($list)) {
            return ['record' => [], 'count' => 0];
        }

        // 收集所有地区代码
        $regionCodes = [];
        foreach ($list as $item) {
            $regionCodes[] = $item['province'];
            $regionCodes[] = $item['city'];
            $regionCodes[] = $item['district'];
        }
        $regionCodes = array_unique(array_filter($regionCodes));

        // 一次性查询所有地区数据
        $regions = Region::where('code', 'in', $regionCodes)
            ->where('status', 1)
            ->column('name', 'code');

        // 处理每条记录
        foreach ($list as &$item) {
            // 添加省市区名称
            $item['province_name'] = $regions[$item['province']] ?? '';
            $item['city_name'] = $regions[$item['city']] ?? '';
            $item['district_name'] = $regions[$item['district']] ?? '';
            
            // 组合完整地址
            $item['full_address'] = implode(' ', array_filter([
                $item['province_name'],
                $item['city_name'],
                $item['district_name'],
                $item['address']
            ]));
        }

        return ['record' => $list->items(), 'count' => $list->total()];
    }
}