<?php
declare (strict_types = 1);

namespace app\admin\controller\waybill;

use app\common\controller\BaseAdminController;
use app\model\WaybillPrintConfig;
use think\App;

/**
 * 电子面单打印管理API控制器
 */
class WaybillPrint extends BaseAdminController
{
    protected $model;

    protected // 验证数据
        $validateRule = [
        'default_printer' => 'require',
        'print_copies' => 'require|number|between:1,100',
        'auto_print' => 'require|in:0,1',
        'print_time' => 'require|in:1,2',
        'print_size' => 'require|in:1,2,3,4',
        'print_content' => 'require|array',
        'print_preview' => 'require|in:0,1'
    ];
    
    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->model = new WaybillPrintConfig();
    }

    /**
     * 获取打印配置
     */
    public function getConfig()
    {
        $config = $this->model->getCurrentConfig();

        // 获取可选项
        $options = [
            'print_sizes' => $this->model->getPrintSizes(),
            'print_times' => $this->model->getPrintTimes(),
            'print_content_items' => $this->model->getPrintContentItems()
        ];

        // 处理打印内容为数组
        if ($config && isset($config['print_content'])) {
            $config['print_content'] = explode(',', $config['print_content']);
        }

        $this->success('获取成功', [
            'config' => $config,
            'options' => $options
        ]);
    }

    public function afterIndex($list)
    {
        if (!$list->items()) {
            return ['record' => $list->items(), 'count' => 0];
        }

        foreach ($list->items() as &$item) {
            // 处理打印内容为数组
            if ($item && isset($item['print_content'])) {
                $item['print_content'] = explode(',', $item['print_content']);
            }
        }

        return ['record' => $list->items(), 'count' => $list->total()];

    }

    /**
     * 保存打印配置
     */
    public function saveConfig()
    {
        $data = $this->request->post();

        // 自定义尺寸验证
        if (isset($data['print_size']) && $data['print_size'] == WaybillPrintConfig::SIZE_CUSTOM) {
            $rule['custom_width'] = 'require|number|between:1,1000';
            $rule['custom_height'] = 'require|number|between:1,1000';
            $this->validate($data, $rule);
        }

        try {
            // 转换时间戳
            if (isset($data['createtime'])) {
                $data['createtime'] = strtotime($data['createtime']);
            }
            if (isset($data['updatetime'])) {
                $data['updatetime'] = strtotime($data['updatetime']);
            }

            if ($this->model->saveConfig($data)) {
            } else {
                $this->fail('保存失败');
            }
        } catch (\Exception $e) {
            $this->fail($e->getMessage());
        }
        $this->ok('保存成功');

    }

    /**
     * 获取打印尺寸列表
     */
    public function getPrintSizes()
    {
        $sizes = $this->model->getPrintSizes();
        $this->ok('获取成功', $sizes);
    }

    /**
     * 获取打印时机列表
     */
    public function getPrintTimes()
    {
        $times = $this->model->getPrintTimes();
        $this->ok('获取成功', $times);
    }

    /**
     * 获取打印内容项列表
     */
    public function getPrintContentItems()
    {
        $items = $this->model->getPrintContentItems();
        $this->ok('获取成功', $items);
    }

    /**
     * 测试打印
     */
    public function testPrint()
    {
        try {
            // 获取当前打印配置
            $config = $this->model->getCurrentConfig();
            if (!$config) {
                $this->fail('请先配置打印参数');
            }

            // 测试打印机连接
            if (\app\common\waybill\print\PrintManager::testConnection()) {
            } else {
                $this->fail('打印机连接测试失败');
            }
        } catch (\app\common\waybill\print\PrintException $e) {
            $this->fail($e->getMessage());
        } catch (\Exception $e) {
            $this->fail('系统错误：' . $e->getMessage());
        }
        $this->ok('打印机连接测试成功');

    }

    /**
     * 执行打印
     */
    public function doPrint()
    {
        try {
            $data = $this->request->post();

            // 验证必要参数
            $rule = [
                'waybill_no' => 'require',
                'express_code' => 'require',
                'sender_id' => 'number',
                'receiver.name' => 'require',
                'receiver.phone' => 'require',
                'receiver.address' => 'require',
                'goods' => 'array'
            ];
            
            $this->validate($data, $rule);

            // 执行打印
            if (\app\common\waybill\print\PrintManager::print($data)) {
            } else {
                $this->fail('打印失败');
            }
        } catch (\app\common\waybill\print\PrintException $e) {
            $this->fail($e->getMessage());
        } catch (\Exception $e) {
            $this->fail('系统错误：' . $e->getMessage());
        }
        $this->ok('打印成功');

    }

    /**
     * 获取打印预览数据
     */
    public function getPreview()
    {
        try {
            $data = $this->request->post();

            // 验证必要参数
            $rule = [
                'waybill_no' => 'require',
                'express_code' => 'require',
                'sender_id' => 'number',
                'receiver.name' => 'require',
                'receiver.phone' => 'require',
                'receiver.address' => 'require',
                'goods' => 'array'
            ];
            
            $this->validate($data, $rule);

            // 获取预览数据
            $previewData = \app\common\waybill\print\PrintManager::getPreviewData($data);
        } catch (\app\common\waybill\print\PrintException $e) {
            $this->fail($e->getMessage());
        } catch (\Exception $e) {
            $this->fail('系统错误：' . $e->getMessage());
        }
        $this->ok('获取成功', $previewData);

    }
}