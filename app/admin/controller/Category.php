<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\controller\BaseAdminController;
use app\model\Category as GoodsCategoryModel;
use app\model\Goods;
use think\App;

/**
 * 商品分类API控制器
 */
class Category extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|分类名称' => 'require|max:50',
        'pid|父级分类' => 'number|egt:0',
        'sort|排序' => 'number|egt:0',
        'enabled|状态' => 'in:0,1',
        'icon|图标' => 'max:255',
        'description|描述' => 'max:255'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new GoodsCategoryModel();
    }
    
    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit') {
            return [
                'name|分类名称' => 'max:50',
                'pid|父级分类' => 'number|egt:0',
                'sort|排序' => 'number|egt:0',
                'enabled|状态' => 'in:0,1',
                'icon|图标' => 'max:255',
                'description|描述' => 'max:255'
            ];
        }
        
        return $this->validateRule;
    }
    
    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 检查父级分类
        if (!empty($data['pid'])) {
            $parentCategory = GoodsCategoryModel::find($data['pid']);
            if (!$parentCategory) {
                $this->error('父级分类不存在');
            }
        }
        
        // 设置默认值
        if (!isset($data['enabled'])) {
            $data['enabled'] = 1;
        }
        if (!isset($data['sort'])) {
            $data['sort'] = 0;
        }
        if (!isset($data['pid'])) {
            $data['pid'] = 0;
        }
        
        return $data;
    }
    
    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 检查父级分类
        if (!empty($data['pid'])) {
            // 不能将自己设为自己的父级
            if ($data['pid'] == $id) {
                $this->error('不能将分类设为自己的父级');
            }
            
            $parentCategory = GoodsCategoryModel::find($data['pid']);
            if (!$parentCategory) {
                $this->error('父级分类不存在');
            }
            
            // 不能将自己设为自己子分类的父级
            $childIds = \app\service\CategoryService::getChildrenIds($id);
            if (in_array($data['pid'], $childIds)) {
                $this->error('不能将分类设为其子分类的父级');
            }
        }
        
        return $data;
    }
    
    /**
     * 删除前的钩子方法
     * @param int $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        if (!\app\service\CategoryService::canDelete($id)) {
            $this->error('该分类下有子分类或关联商品，不能删除');
        }
        
        return true;
    }
    
    /**
     * 获取分类树形结构
     */
    public function tree()
    {
        $pid = $this->request->param('pid', 0, 'intval');
        $recursive = $this->request->param('recursive', true, 'boolean');
        
        $categories = \app\service\CategoryService::getCategoryTree($pid, $recursive);
        
        $this->ok('获取成功', $categories);
    }

    public function type($type = null)
    {
        if (empty($type)) {
            $this->error('分类类型不能为空');
        }

        $tree = \app\service\CategoryService::getCategoryTreeByType($type);

        $this->ok('获取成功', $tree);
    }
    
    /**
     * 获取分类路径
     */
    public function path($id = null)
    {
        if (empty($id)) {
            $this->error('分类ID不能为空');
        }
        
        $path = GoodsCategoryModel::getCategoryPath($id);
        if (empty($path)) {
            $this->error('分类不存在');
        }
        
        $this->ok('获取成功', $path);
    }

    /**
     * 批量更新分类排序
     */
    public function updateSort()
    {
        $data = $this->request->post('data', []);
        if (empty($data)) {
            $this->error('参数错误');
        }
        
        $success = \app\service\CategoryService::updateSort($data);
        
        $this->ok('更新成功，共更新' . $success . '条记录');
    }
    

}