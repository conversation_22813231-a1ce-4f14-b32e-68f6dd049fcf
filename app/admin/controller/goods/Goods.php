<?php
declare (strict_types = 1);

namespace app\admin\controller\goods;

use app\common\controller\BaseAdminController;
use app\model\Goods as GoodsModel;
use app\model\Category;
use think\App;

/**
 * 商品API控制器
 */
class Goods extends BaseAdminController
{

    protected $filterParams = [
        'price_range_start',
        'price_range_end',
        'stock_range_start',
        'stock_range_end',
    ];

    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'title|商品标题' => 'require|max:255',
        'category_id|商品分类' => 'require|number',
        'brand_id|品牌id' => 'number',
        'description|商品描述' => 'max:65535',
        'freight_template_id|运费模版id' => 'number',
        'keywords|关键词' => 'max:255',
        'thumb|商品缩略图' => 'max:255',
        'images|商品图片' => 'array',
        'price|商品价格' => 'float|egt:0',
        'original_price|商品原价' => 'float|egt:0',
        'cost_price|成本价' => 'float|egt:0',
        'stock|商品库存' => 'number|egt:0',
        'weight|重量' => 'float|egt:0',
        'volume|体积' => 'float|egt:0',
        'specs|规格参数' => 'array',
        'params|商品参数' => 'array',
        'status|商品状态' => 'require|in:0,1',
        'sort|排序' => 'number|egt:0',
        'spec_type|规格类型' => 'require|in:0,1',
        'spec_combinations|商品SKU' => 'array'
    ];

    protected $allowFields = [
        'title', 'category_id', 'description', 'keywords', 'images',
        'thumb', 'price', 'original_price', 'cost_price', 'stock', 'weight',
        'volume', 'specs', 'params', 'status', 'sort', 'spec_type', 'spec_combinations', 'createtime', 'updatetime', 'views', 'sales', 'brand_id', 'freight_template_id'
    ];
    
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new GoodsModel();
    }
    
    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit') {
            return [
                'title|商品标题' => 'max:255',
                'category_id|商品分类' => 'number',
                'brand_id|品牌id' => 'number',
                'description|商品描述' => 'max:65535',
                'freight_template_id|运费模版id' => 'number',
                'keywords|关键词' => 'max:255',
                'thumb|商品缩略图' => 'max:255',
                'images|商品图片' => 'array',
                'price|商品价格' => 'float|egt:0',
                'original_price|商品原价' => 'float|egt:0',
                'cost_price|成本价' => 'float|egt:0',
                'stock|商品库存' => 'number|egt:0',
                'weight|重量' => 'float|egt:0',
                'volume|体积' => 'float|egt:0',
                'specs|规格参数' => 'array',
                'params|商品参数' => 'array',
                'status|商品状态' => 'in:0,1',
                'sort|排序' => 'number|egt:0'
            ];
        }
        
        return $this->validateRule;
    }
    
    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 检查分类是否存在
        $category = Category::find($data['category_id']);
        if (!$category) {
            $this->fail('商品分类不存在');
        }
        
        // 处理商品图片
        if (!empty($data['images']) && is_array($data['images'])) {
            $data['thumb'] = $data['images'][0] ?? '';
            // 图片数据将在afterAdd中保存到图片管理表
        }

        // 处理规格参数和商品参数
        if (!empty($data['specs'])) {
            // 验证规格参数格式
            foreach ($data['specs'] as $spec) {
                if (!isset($spec['name']) || empty($spec['name'])) {
                    $this->fail('规格参数名称不能为空');
                }
                if (!isset($spec['values']) || !is_array($spec['values']) || empty($spec['values'])) {
                    $this->fail('规格参数值不能为空且必须是数组');
                }
            }
            
            // 保存规格和规格值
            $specData = [];
            foreach ($data['specs'] as $spec) {
                $specData[] = [
                    'name' => $spec['name'],
                    'status' => 1,
                    'sort' => $spec['sort'] ?? 0,
                    'createtime' => time(),
                    'updatetime' => time()
                ];
            }
            // 批量插入规格
            $specModel = new \app\model\GoodsSpec();
            $specModel->insertAll($specData);
            // 获取插入后的规格ID
            $specIds = $specModel->where('name', 'in', array_column($specData, 'name'))->column('id');
            // 保存规格值
            foreach ($data['specs'] as $index => $spec) {
                \app\model\GoodsSpecValue::saveSpecValues($specIds[$index], $spec['values']);
            }
            // 保存规格数据用于afterAdd中创建规格关联
            $data['spec_ids'] = $specIds;
            $data['specs'] = json_encode($data['specs'], JSON_UNESCAPED_UNICODE);
        }

        if (!empty($data['params'])) {
            // 验证商品参数格式
            foreach ($data['params'] as $param) {
                if (!isset($param['name']) || empty($param['name'])) {
                    $this->fail('商品参数名称不能为空');
                }
                if (!isset($param['value'])) {
                    $this->fail('商品参数值不能为空');
                }
            }
            $data['params'] = json_encode($data['params'], JSON_UNESCAPED_UNICODE);
        }
        
        // 处理SKU数据
        if (isset($data['spec_type']) && $data['spec_type'] == 1) {
            // 多规格商品
            if (empty($data['spec_combinations'])) {
                $this->fail('请设置商品规格');
            }
            // 计算商品总库存和最低价格
            $data['stock'] = 0;
            $data['price'] = 0;
            $data['cost_price'] = 0;
            $data['original_price'] = 0;
            $data['weight'] = 0;
            $data['volume'] = 0;
            foreach ($data['spec_combinations'] as $sku) {
                $data['stock'] += $sku['stock'];
                if ($data['price'] == 0 || $sku['price'] < $data['price']) {
                    $data['price'] = $sku['price'];
                }
                if ($data['cost_price'] == 0 || $sku['cost_price'] < $data['cost_price']) {
                    $data['cost_price'] = $sku['cost_price'];
                }
                if ($data['original_price'] == 0 || $sku['original_price'] < $data['original_price']) {
                    $data['original_price'] = $sku['original_price'];
                }
                if ($data['weight'] == 0 || $sku['weight'] < $data['weight']) {
                    $data['weight'] = $sku['weight'];
                }
                if ($data['volume'] == 0 || $sku['volume'] < $data['volume']) {
                    $data['volume'] = $sku['volume'];
                }
            }
        } else {
            // 单规格商品
            if (empty($data['price'])) {
                $this->fail('请设置商品价格');
            }
            if (!isset($data['stock'])) {
                $this->fail('请设置商品库存');
            }
        }
        
        // 设置默认值
        $data['sales'] = 0;
        $data['views'] = 0;
        $data['createtime'] = time();
        $data['updatetime'] = time();
        return $data;
    }
    
    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        $goods = GoodsModel::find($id);
        if (!$goods) {
            $this->fail('商品不存在');
        }

        // 检查分类是否存在
        if (!empty($data['category_id'])) {
            $category = Category::find($data['category_id']);
            if (!$category) {
                $this->fail('商品分类不存在');
            }
        }
        
        // 处理商品图片
        if (!empty($data['images']) && is_array($data['images'])) {
            $data['thumb'] = $data['images'][0] ?? '';
            // 保存商品图片到图片管理表
            \app\model\GoodsImage::saveGoodsImages($id, $data['images']);
        }
        
        // 处理规格参数和商品参数
        if (!empty($data['specs'])) {
            // 验证规格参数格式
            foreach ($data['specs'] as $spec) {
                if (!isset($spec['name']) || empty($spec['name'])) {
                    $this->fail('规格参数名称不能为空');
                }
                if (!isset($spec['values']) || !is_array($spec['values']) || empty($spec['values'])) {
                    $this->fail('规格参数值不能为空且必须是数组');
                }
            }
            
            // 删除原有规格关联
            \app\model\GoodsSpecRelation::where('goods_id', $id)->delete();
            
            // 更新规格和规格值
            $specData = [];
            foreach ($data['specs'] as $spec) {
                $specData[] = [
                    'name' => $spec['name'],
                    'status' => 1,
                    'sort' => $spec['sort'] ?? 0,
                    'createtime' => time(),
                    'updatetime' => time()
                ];
            }
            
            // 批量插入或更新规格
            $specModel = new \app\model\GoodsSpec();
            $specModel->insertAll($specData);
            
            // 获取插入后的规格ID
            $specIds = $specModel->where('name', 'in', array_column($specData, 'name'))->column('id');
            
            // 保存规格值
            foreach ($data['specs'] as $index => $spec) {
                \app\model\GoodsSpecValue::saveSpecValues($specIds[$index], $spec['values']);
            }
            
            // 保存规格数据
            $data['spec_ids'] = $specIds;
            $data['specs'] = json_encode($data['specs'], JSON_UNESCAPED_UNICODE);
        }
        
        if (!empty($data['params'])) {
            // 验证商品参数格式
            foreach ($data['params'] as $param) {
                if (!isset($param['name']) || empty($param['name'])) {
                    $this->fail('商品参数名称不能为空');
                }
                if (!isset($param['value'])) {
                    $this->fail('商品参数值不能为空');
                }
            }
            $data['params'] = json_encode($data['params'], JSON_UNESCAPED_UNICODE);
        }
        
        // 处理SKU数据
        if (isset($data['spec_type'])) {
            if ($data['spec_type'] == 1) {
                // 多规格商品
                if (empty($data['spec_combinations'])) {
                    $this->fail('请设置商品规格');
                }
                
                // 计算商品总库存和最低价格
                $data['stock'] = 0;
                $data['price'] = 0;
                $data['cost_price'] = 0;
                $data['original_price'] = 0;
                $data['weight'] = 0;
                $data['volume'] = 0;
                
                foreach ($data['spec_combinations'] as $sku) {
                    $data['stock'] += $sku['stock'];
                    if ($data['price'] == 0 || $sku['price'] < $data['price']) {
                        $data['price'] = $sku['price'];
                    }
                    if ($data['cost_price'] == 0 || $sku['cost_price'] < $data['cost_price']) {
                        $data['cost_price'] = $sku['cost_price'];
                    }
                    if ($data['original_price'] == 0 || $sku['original_price'] < $data['original_price']) {
                        $data['original_price'] = $sku['original_price'];
                    }
                    if ($data['weight'] == 0 || $sku['weight'] < $data['weight']) {
                        $data['weight'] = $sku['weight'];
                    }
                    if ($data['volume'] == 0 || $sku['volume'] < $data['volume']) {
                        $data['volume'] = $sku['volume'];
                    }
                }
                
                // 删除原有SKU数据和规格值关联
                $oldSkuIds = \app\model\GoodsSku::where('goods_id', $id)->column('id');
                if (!empty($oldSkuIds)) {
                    \app\model\GoodsSkuSpecValue::whereIn('sku_id', $oldSkuIds)->delete();
                    \app\model\GoodsSku::whereIn('id', $oldSkuIds)->delete();
                }
                
                // 批量保存新的SKU数据
                $skuData = [];
                $skuSpecValueData = [];
                
                foreach ($data['spec_combinations'] as $sku) {
                    $specValueIds = [];
                    $specValueStr = [];
                    
                    if (!empty($sku['specs'])) {
                        foreach ($sku['specs'] as $spec) {
                            if (!empty($spec['value_id'])) {
                                $specValueIds[] = $spec['value_id'];
                                // 获取规格值信息
                                $specValue = \app\model\GoodsSpecValue::find($spec['value_id']);
                                if ($specValue) {
                                    // 获取规格名称
                                    $specName = \app\model\GoodsSpec::where('id', $specValue->spec_id)->value('name');
                                    $specValueStr[] = $specName . ':' . $specValue->value;
                                }
                            }
                        }
                    }
                    
                    $skuData[] = [
                        'goods_id' => $id,
                        'spec_value_ids' => implode(',', $specValueIds),
                        'spec_value_str' => implode(', ', $specValueStr),
                        'price' => $sku['price'] ?? 0,
                        'original_price' => $sku['original_price'] ?? 0,
                        'cost_price' => $sku['cost_price'] ?? 0,
                        'stock' => $sku['stock'] ?? 0,
                        'code' => $sku['code'] ?? '',
                        'weight' => $sku['weight'] ?? 0,
                        'volume' => $sku['volume'] ?? 0,
                        'status' => 1,
                        'createtime' => time(),
                        'updatetime' => time()
                    ];
                }
                
                // 批量创建SKU
                $skuModel = new \app\model\GoodsSku();
                $skuModel->insertAll($skuData);
            } else {
                // 单规格商品
                if (empty($data['price'])) {
                    $this->fail('请设置商品价格');
                }
                if (!isset($data['stock'])) {
                    $this->fail('请设置商品库存');
                }
                
                // 删除原有SKU数据和规格值关联
                $oldSkuIds = \app\model\GoodsSku::where('goods_id', $id)->column('id');
                if (!empty($oldSkuIds)) {
                    \app\model\GoodsSkuSpecValue::whereIn('sku_id', $oldSkuIds)->delete();
                    \app\model\GoodsSku::whereIn('id', $oldSkuIds)->delete();
                }
            }
        }
        
        // 记录价格变更
        if (isset($data['price']) && $data['price'] != $goods->price) {
            $this->recordPriceChange($goods, $data['price'], $data['original_price'] ?? $goods->original_price);
        }
        
        $data['updatetime'] = time();
        
        return $data;
    }
    
    /**
     * 查询前的钩子方法
     * @param array $where 查询条件
     * @param array $sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 按分类筛选（支持多级分类）
        $categoryId = $this->request->param('category_id', 0, 'intval');
        if ($categoryId > 0) {
            // 获取所有子分类ID
            $categoryIds = Category::getChildrenIds($categoryId);
            $categoryIds[] = $categoryId;
            $where[] = ['category_id', 'in', $categoryIds];
        }
        
        // 按关键词搜索
        $keyword = $this->request->param('keyword', '');
        if (!empty($keyword)) {
            $where[] = ['title|keywords|description', 'like', '%' . $keyword . '%'];
        }
        
        // 按价格区间筛选
        $minPrice = $this->request->param('price_range_start', 0, 'float');
        $maxPrice = $this->request->param('price_range_end', 0, 'float');
        if ($minPrice > 0) {
            $where[] = ['price', '>=', $minPrice];
        }
        if ($maxPrice > 0) {
            $where[] = ['price', '<=', $maxPrice];
        }
        
        // 按上下架状态筛选
        $status = $this->request->param('status', '', 'trim');
        if ($status !== '') {
            $where[] = ['status', '=', intval($status)];
        }

        // 按库存筛选
        $minStock = $this->request->param('stock_range_start', 0, 'intval');
        $maxStock = $this->request->param('stock_range_end', 0, 'intval');
        if ($minStock > 0) {
            $where[] = ['stock', '>=', $minStock];
        }
        if ($maxStock > 0) {
            $where[] = ['stock', '<=', $maxStock];
        }

        // 按规格类型筛选
        $specType = $this->request->param('spec_type', '', 'trim');
        if ($specType !== '') {
            $where[] = ['spec_type', '=', intval($specType)];
        }

        // 按创建时间筛选
        $startTime = $this->request->param('start_time', 0, 'intval');
        $endTime = $this->request->param('end_time', 0, 'intval');
        if ($startTime > 0) {
            $where[] = ['createtime', '>=', $startTime];
        }
        if ($endTime > 0) {
            $where[] = ['createtime', '<=', $endTime];
        }
        
        // 设置排序
        $sortField = $this->request->param('sort_field', '');
        $sortOrder = $this->request->param('sort_order', 'desc');
        if (!empty($sortField) && in_array($sortField, ['price', 'sales', 'views', 'stock', 'createtime'])) {
            $sort = [$sortField => $sortOrder];
        } else if (empty($sort)) {
            $sort = ['sort' => 'desc', 'id' => 'desc'];
        }
    }

    /**
     * 查询后的钩子方法
     * @param array $data 查询结果
     * @return array
     */
    protected function afterIndex($data)
    {
        $items = $data->items();

        if (empty($items)) {
            return ['record' => [], 'count' => 0];
        }

        // 获取所有商品ID
        $goodsIds = array_column($items, 'id');

        // 使用ThinkPHP 8的高效查询方式
        $goodsImages = \app\model\GoodsImage::where('goods_id', 'in', $goodsIds)
            ->field(['goods_id', 'url'])
            ->cache('goods_images_'.md5(implode(',', $goodsIds)), 3600)
            ->select()
            ->toArray();

        // 使用ThinkPHP 8的集合方法处理图片数据
        $groupedImages = [];
        foreach ($goodsImages as $image) {
            $groupedImages[$image['goods_id']][] = $image['url'];
        }
        $goodsImages = $groupedImages;

        // 批量预加载分类信息并添加缓存
        $categoryIds = array_unique(array_column($items, 'category_id'));
        $categories = Category::where('id', 'in', $categoryIds)
            ->cache('goods_categories_'.md5(implode(',', $categoryIds)), 86400)
            ->column('*', 'id');

        // 优化SKU数据查询，只查询必要字段
        $skus = \app\model\GoodsSku::with([
            'specValues' => function($query) {
                $query->field(['id', 'sku_id', 'spec_id', 'spec_value_id'])
                      ->with([
                          'spec' => function($q) { $q->field(['id', 'name']); },
                          'spec_value' => function($q) { $q->field(['id', 'value']); }
                      ]);
            }
        ])->where('goods_id', 'in', $goodsIds)
          ->field(['id', 'goods_id', 'price', 'original_price', 'cost_price', 'stock', 'weight', 'volume'])
          ->cache('goods_skus_with_specs_'.md5(implode(',', $goodsIds)), 3600)  // 缓存1小时
          ->select()
          ->toArray();

        // 使用更高效的数组分组方式
        $goodsSkus = [];
        foreach ($skus as $sku) {
            $goodsSkus[$sku['goods_id']][] = $sku;
        }

        // 组装数据
        foreach ($items as &$item) {
            // 添加商品图片
            $item['images'] = $goodsImages[$item['id']] ?? [];

            // 添加分类信息
            $item['category'] = $categories[$item['category_id']] ?? null;

            // 处理SKU数据，优化数据处理逻辑
            if (isset($item['spec_type']) && $item['spec_type'] == 1) {
                $currentSkus = $goodsSkus[$item['id']] ?? [];
                
                // 使用更高效的数据处理方式
                $specNameValues = [];
                $specCombinations = [];
                
                foreach ($currentSkus as $sku) {
                    $comb = [
                        'price' => $sku['price'],
                        'original_price' => $sku['original_price'],
                        'cost_price' => $sku['cost_price'],
                        'stock' => $sku['stock'],
                        'weight' => $sku['weight'],
                        'volume' => $sku['volume']
                    ];
                    
                    if (!empty($sku['specValues'])) {
                        foreach ($sku['specValues'] as $specValue) {
                            if (isset($specValue['spec']['name']) && isset($specValue['spec_value']['value'])) {
                                $specName = $specValue['spec']['name'];
                                $specVal = $specValue['spec_value']['value'];
                                
                                // 更新规格名值映射
                                if (!isset($specNameValues[$specName])) {
                                    $specNameValues[$specName] = [];
                                }
                                if (!in_array($specVal, $specNameValues[$specName])) {
                                    $specNameValues[$specName][] = $specVal;
                                }
                                
                                // 更新组合
                                $comb[$specName] = $specVal;
                            }
                        }
                    }
                    
                    $specCombinations[] = $comb;
                }
                
                // 转换规格数据格式
                $specs = [];
                foreach ($specNameValues as $specName => $values) {
                    $specs[] = [
                        'name' => $specName,
                        'values' => array_values($values)
                    ];
                }
                
                $item['specs'] = $specs;
                $item['specCombinations'] = $specCombinations;
            }
        }

        return ['record' => $items, 'count' => $data->total()];
    }

    /**
     * 添加后的钩子方法
     * @param int $id
     * @param $data
     * @return void
     */
    protected function afterAdd($id, $data)
    {
        // 保存商品图片到图片管理表
        if (!empty($data['images']) && is_array($data['images'])) {
            \app\model\GoodsImage::saveGoodsImages($this->model->id, $data['images']);
        }
        
        // 批量保存规格关联
        if (!empty($data['spec_ids'])) {
            $specRelationData = [];
            foreach ($data['spec_ids'] as $specId) {
                $specRelationData[] = [
                    'goods_id' => $this->model->id,
                    'spec_id' => $specId,
                    'sort' => 0
                ];
            }
            \app\model\GoodsSpecRelation::insertAll($specRelationData);
        }
        
        // 批量保存SKU数据
        if ($data['spec_type'] == 1 && !empty($data['spec_combinations'])) {
            // 获取所有规格和规格值信息
            $specList = \app\model\GoodsSpec::where('id', 'in', $data['spec_ids'])->column('*', 'name');
            $specValueList = \app\model\GoodsSpecValue::where('spec_id', 'in', $data['spec_ids'])
                ->field('id, spec_id, value')
                ->select()
                ->toArray();

            // 构建规格值映射
            $specValueMap = [];
            foreach ($specValueList as $value) {
                $specValueMap[$value['spec_id']][$value['value']] = $value['id'];
            }

            $skuDataList = [];
            $skuSpecValueList = [];
            
            foreach ($data['spec_combinations'] as $sku) {
                $specValueStr = [];
                $specValueIds = [];
                
                if (!empty($data['spec_ids']) && isset($specList[$sku['name']])) {
                    $specInfo = $specList[$sku['name']];
                    $specValue = $sku[$sku['name']];
                    
                    if (isset($specValueMap[$specInfo['id']][$specValue])) {
                        $specValueId = $specValueMap[$specInfo['id']][$specValue];
                        $specValueStr[] = $specInfo['name'] . ':' . $specValue;
                        $specValueIds[] = $specValueId;
                    }
                }

                // 准备SKU数据
                $skuData = [
                    'goods_id' => $this->model->id,
                    'spec_value_ids' => implode(',', $specValueIds),
                    'spec_value_str' => implode(', ', $specValueStr),
                    'price' => $sku['price'] ?? 0,
                    'original_price' => $sku['original_price'] ?? 0,
                    'cost_price' => $sku['cost_price'] ?? 0,
                    'stock' => $sku['stock'] ?? 0,
                    'code' => $sku['code'] ?? '',
                    'weight' => $sku['weight'] ?? 0,
                    'volume' => $sku['volume'] ?? 0,
                    'status' => 1,
                    'createtime' => time(),
                    'updatetime' => time()
                ];
                
                $skuModel = \app\model\GoodsSku::create($skuData);
                
                // 准备SKU规格值关联数据
                foreach ($specValueIds as $index => $specValueId) {
                    $skuSpecValueList[] = [
                        'sku_id' => $skuModel->id,
                        'spec_id' => $specInfo['id'],
                        'spec_value_id' => $specValueId
                    ];
                }
            }
            
            // 批量保存SKU规格值关联
            if (!empty($skuSpecValueList)) {
                \app\model\GoodsSkuSpecValue::insertAll($skuSpecValueList);
            }
        }
    }
    
    /**
     * 更新商品库存
     */
    public function updateStock()
    {
        $goodsId = $this->request->post('goods_id', 0, 'intval');
        $skuId = $this->request->post('sku_id', 0, 'intval');
        $num = $this->request->post('num', 0, 'intval');
        $type = $this->request->post('type', 'increase', 'trim');
        $remark = $this->request->post('remark', '', 'trim');
        
        if (empty($goodsId)) {
            $this->error('商品ID不能为空');
        }
        
        if ($num == 0) {
            $this->error('库存变更数量不能为0');
        }
        
        if (!in_array($type, ['increase', 'decrease', 'set_increase', 'set_decrease'])) {
            $this->error('库存变更类型错误');
        }
        
        $goods = GoodsModel::find($goodsId);
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        // 开启事务
        $goods->startTrans();
        try {
            $beforeStock = $goods->stock;
            
            if ($goods->spec_type == 1) {
                // 多规格商品，需要更新SKU库存
                if (empty($skuId)) {
                    throw new \Exception('多规格商品必须指定SKU');
                }
                
                $sku = \app\model\GoodsSku::where('goods_id', $goodsId)
                    ->where('id', $skuId)
                    ->find();
                    
                if (!$sku) {
                    throw new \Exception('商品SKU不存在');
                }
                
                $beforeSkuStock = $sku->stock;
                
                // 更新SKU库存
                if ($type == 'increase') {
                    $sku->setInc('stock', $num);
                    $goods->setInc('stock', $num);
                } elseif ($type == 'decrease') {
                    if ($sku->stock < $num) {
                        throw new \Exception('SKU库存不足');
                    }
                    $sku->setDec('stock', $num);
                    $goods->setDec('stock', $num);
                } elseif ($type == 'set_increase') {
                    $sku->stock = $num;
                    $sku->save();
                    // 重新计算商品总库存
                    $totalStock = \app\model\GoodsSku::where('goods_id', $goodsId)->sum('stock');
                    $goods->stock = $totalStock;
                    $goods->save();
                } elseif ($type == 'set_decrease') {
                    if ($num > $sku->stock) {
                        throw new \Exception('SKU库存不足');
                    }
                    $sku->stock = $num;
                    $sku->save();
                    // 重新计算商品总库存
                    $totalStock = \app\model\GoodsSku::where('goods_id', $goodsId)->sum('stock');
                    $goods->stock = $totalStock;
                    $goods->save();
                }
                
                // 记录SKU库存变更日志
                $afterSkuStock = $sku->stock;
                $this->recordSkuStockChange($sku, $num, $type, $beforeSkuStock, $afterSkuStock, $remark);
            } else {
                // 单规格商品，直接更新商品库存
                if ($type == 'decrease' && $goods->stock < $num) {
                    throw new \Exception('商品库存不足');
                }
                
                $result = GoodsModel::updateStock($goodsId, $num, $type);
                if (!$result) {
                    throw new \Exception('库存更新失败');
                }
            }
            
            // 记录商品库存变更日志
            $afterStock = GoodsModel::where('id', $goodsId)->value('stock');
            $this->recordStockChange($goods, $num, $type, $beforeStock, $afterStock, $remark);
            
            $goods->commit();
            $this->ok('库存更新成功');
        } catch (\Exception $e) {
            $goods->rollback();
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取商品分类列表
     */
    public function categoryList()
    {
        $pid = $this->request->param('pid', 0, 'intval');
        $recursive = $this->request->param('recursive', true, 'boolean');
        
        $categories = Category::getCategoryTree($pid, $recursive);
        
        $this->ok('获取成功', $categories);
    }
    
    /**
     * 添加商品分类
     */
    public function addCategory()
    {
        $data = $this->request->post();
        
        // 验证数据
        $validate = $this->validate($data, [
            'name|分类名称' => 'require|max:100',
            'pid|父级分类' => 'number|egt:0',
            'icon|分类图标' => 'max:255',
            'image|分类图片' => 'max:255',
            'sort|排序' => 'number|egt:0',
            'status|状态' => 'in:0,1',
            'is_show|是否显示' => 'in:0,1'
        ]);
        
        if ($validate !== true) {
            $this->error($validate);
        }
        
        // 检查父级分类
        if (!empty($data['pid'])) {
            $parentCategory = Category::find($data['pid']);
            if (!$parentCategory) {
                $this->error('父级分类不存在');
            }
            $data['level'] = $parentCategory->level + 1;
        } else {
            $data['level'] = 1;
        }
        
        // 设置默认值
        if (!isset($data['status'])) {
            $data['status'] = 1;
        }
        if (!isset($data['is_show'])) {
            $data['is_show'] = 1;
        }
        if (!isset($data['sort'])) {
            $data['sort'] = 0;
        }
        
        $data['create_time'] = time();
        $data['update_time'] = time();
        
        // 创建分类
        $category = Category::create($data);
        
        $this->ok('添加成功', $category);
    }
    
    /**
     * 更新商品分类
     */
    public function updateCategory($id = null)
    {
        if (empty($id)) {
            $this->error('分类ID不能为空');
        }
        
        $category = Category::find($id);
        if (!$category) {
            $this->error('分类不存在');
        }
        
        $data = $this->request->post();
        
        // 验证数据
        $validate = $this->validate($data, [
            'name|分类名称' => 'max:100',
            'pid|父级分类' => 'number|egt:0',
            'icon|分类图标' => 'max:255',
            'image|分类图片' => 'max:255',
            'sort|排序' => 'number|egt:0',
            'status|状态' => 'in:0,1',
            'is_show|是否显示' => 'in:0,1'
        ]);
        
        if ($validate !== true) {
            $this->error($validate);
        }
        
        // 检查父级分类
        if (!empty($data['pid'])) {
            // 不能将自己设为自己的父级
            if ($data['pid'] == $id) {
                $this->error('不能将分类设为自己的父级');
            }
            
            $parentCategory = Category::find($data['pid']);
            if (!$parentCategory) {
                $this->error('父级分类不存在');
            }
            $data['level'] = $parentCategory->level + 1;
        }
        
        $data['update_time'] = time();
        
        // 更新分类
        $category->save($data);
        
        $this->ok('更新成功', $category);
    }
    
    /**
     * 删除商品分类
     */
    public function deleteCategory($id = null)
    {
        if (empty($id)) {
            $this->error('分类ID不能为空');
        }
        
        $category = Category::find($id);
        if (!$category) {
            $this->error('分类不存在');
        }
        
        // 检查是否有子分类
        $childCount = Category::where('pid', $id)->count();
        if ($childCount > 0) {
            $this->error('该分类下有子分类，不能删除');
        }
        
        // 检查是否有关联商品
        $goodsCount = GoodsModel::where('category_id', $id)->count();
        if ($goodsCount > 0) {
            $this->error('该分类下有商品，不能删除');
        }
        
        // 删除分类
        $category->delete();
        
        $this->ok('删除成功');
    }
    
    /**
     * 删除商品
     */
    public function delete($id = null)
    {
        if (empty($id)) {
            $this->fail('商品ID不能为空');
        }
        
        $goods = GoodsModel::find($id);
        if (!$goods) {
            $this->fail('商品不存在');
        }
        
        // 开启事务
        $goods->startTrans();
        try {
            // 删除商品图片
            \app\model\GoodsImage::where('goods_id', $id)->delete();
            
            // 删除商品SKU及关联数据
            $skuIds = \app\model\GoodsSku::where('goods_id', $id)->column('id');
            if (!empty($skuIds)) {
                // 删除SKU规格值关联
                \app\model\GoodsSkuSpecValue::whereIn('sku_id', $skuIds)->delete();
                // 删除SKU
                \app\model\GoodsSku::whereIn('id', $skuIds)->delete();
            }
            
            // 删除商品规格关联和规格值
            if ($goods->spec_type == 1 && !empty($goods->specs)) {
                // 删除商品规格关联
                $specIds = \app\model\GoodsSpecRelation::where('goods_id', $id)->column('spec_id');
                \app\model\GoodsSpecRelation::where('goods_id', $id)->delete();

                if (!empty($specIds)) {
                    // 删除规格值
                    \app\model\GoodsSpecValue::whereIn('spec_id', $specIds)->delete();
                    // 删除规格
                    \app\model\GoodsSpec::destroy($specIds);
                }
            }
            // 删除商品
            $goods->delete();
            
            $goods->commit();
        } catch (\Exception $e) {
            $goods->rollback();
            $this->fail($e->getMessage());
        }

        $this->ok('删除成功');

    }
    
    /**
     * 搜索商品
     */
    public function search()
    {
        $params = $this->request->param();
        $page = $this->request->param('page', 1, 'intval');
        $limit = $this->request->param('limit', 10, 'intval');
        
        // 构建查询条件
        $where = [];
        $sort = [];
        $this->beforeIndex($where, $sort);
        
        // 获取商品列表
        $result = GoodsModel::getGoodsList($where, $page, $limit, $sort);
        
        // 处理商品数据
        if (!empty($result['data'])) {
            foreach ($result['data'] as &$item) {
                // 处理规格参数
                if (!empty($item['specs'])) {
                    $item['specs'] = json_decode($item['specs'], true);
                }
                // 处理商品参数
                if (!empty($item['params'])) {
                    $item['params'] = json_decode($item['params'], true);
                }
                // 获取分类信息
                $item['category'] = Category::field('id,name,pid,level')->find($item['category_id']);
                // 获取商品图片
                $item['images'] = \app\model\GoodsImage::where('goods_id', $item['id'])->column('image_url');
                // 获取SKU信息（如果是多规格商品）
                if ($item['spec_type'] == 1) {
                    $item['skus'] = \app\model\GoodsSku::where('goods_id', $item['id'])
                        ->field('id,specs,price,original_price,cost_price,stock,code,weight,volume')
                        ->select()
                        ->toArray();
                }
            }
        }
        
        $this->ok('搜索成功', $result);
    }

    /**
     * 获取商品详情
     */
    public function detail($id = null)
    {
        if (empty($id)) {
            $this->error('商品ID不能为空');
        }
        
        $goods = GoodsModel::find($id);
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        // 处理规格参数
        if (!empty($goods['specs'])) {
            $goods['specs'] = json_decode($goods['specs'], true);
        }
        
        // 处理商品参数
        if (!empty($goods['params'])) {
            $goods['params'] = json_decode($goods['params'], true);
        }
        
        // 获取分类信息
        $goods['category'] = Category::field('id,name,pid,level')->find($goods['category_id']);
        
        // 获取商品图片
        $goods['images'] = \app\model\GoodsImage::where('goods_id', $id)->column('image_url');
        
        // 获取SKU信息（如果是多规格商品）
        if ($goods['spec_type'] == 1) {
            $goods['skus'] = \app\model\GoodsSku::where('goods_id', $id)
                ->field('id,specs,price,original_price,cost_price,stock,code,weight,volume')
                ->select()
                ->toArray();
        }
        
        // 增加浏览次数
        $goods->setInc('views');
        
        $this->ok('获取成功', $goods);
    }
    
    /**
     * 批量删除
     */
    public function batchDelete()
    {
        $ids = $this->request->param('ids');
        if (empty($ids) || !is_array($ids)) {
            $this->fail('请选择要删除的商品');
        }
        
        // 开启事务
        $this->model->startTrans();
        try {
            // 删除商品图片
            \app\model\GoodsImage::whereIn('goods_id', $ids)->delete();
            
            // 获取所有SKU IDs
            $skuIds = \app\model\GoodsSku::whereIn('goods_id', $ids)->column('id');
            if (!empty($skuIds)) {
                // 删除SKU规格值关联
                \app\model\GoodsSkuSpecValue::whereIn('sku_id', $skuIds)->delete();
                // 删除SKU
                \app\model\GoodsSku::whereIn('id', $skuIds)->delete();
            }
            
            // 获取多规格商品的ID
            $multiSpecGoodsIds = $this->model->whereIn('id', $ids)
                ->where('spec_type', 1)
                ->whereNotNull('specs')
                ->column('id');
            
            if (!empty($multiSpecGoodsIds)) {
                // 获取所有规格IDs
                $specIds = \app\model\GoodsSpecRelation::whereIn('goods_id', $multiSpecGoodsIds)->column('spec_id');
                // 删除商品规格关联
                \app\model\GoodsSpecRelation::whereIn('goods_id', $multiSpecGoodsIds)->delete();
                
                if (!empty($specIds)) {
                    // 删除规格值
                    \app\model\GoodsSpecValue::whereIn('spec_id', $specIds)->delete();
                    // 删除规格
                    \app\model\GoodsSpec::destroy($specIds);
                }
            }
            
            // 删除商品
            $this->model->destroy($ids);
            
            $this->model->commit();
        } catch (\Exception $e) {
            $this->model->rollback();
            $this->fail('删除失败：' . $e->getMessage());
        }
        $this->ok('删除成功');

    }
}