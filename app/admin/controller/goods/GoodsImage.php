<?php
declare (strict_types = 1);

namespace app\admin\controller\goods;

use app\common\controller\BaseAdminController;
use app\model\GoodsImage as GoodsImageModel;
use app\model\Goods as GoodsModel;
use think\App;

/**
 * 商品图片API控制器
 */
class GoodsImage extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'goods_id|商品ID' => 'require|number',
        'url|图片URL' => 'require|max:255',
        'sort|排序' => 'number|egt:0'
    ];
    
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new GoodsImageModel();
    }
    
    /**
     * 获取商品图片列表
     * @param int $goods_id 商品ID
     * @return void
     */
    public function list($goods_id = 0)
    {
        if (!$goods_id) {
            $this->error('商品ID不能为空');
        }
        
        // 检查商品是否存在
        if (!GoodsModel::find($goods_id)) {
            $this->error('商品不存在');
        }
        
        $list = GoodsImageModel::where('goods_id', $goods_id)
            ->order('sort', 'asc')
            ->select();
        
        $this->success('获取成功', $list);
    }
    
    /**
     * 添加商品图片
     * @return void
     */
    public function add()
    {
        $data = $this->request->post();
        
        // 验证数据
        if (!$this->validate($data)) {
            $this->error($this->getError());
        }
        
        // 检查商品是否存在
        if (!GoodsModel::find($data['goods_id'])) {
            $this->error('商品不存在');
        }
        
        // 添加图片
        if ($this->model->save($data)) {
            // 如果是第一张图片，设置为缩略图
            $count = GoodsImageModel::where('goods_id', $data['goods_id'])->count();
            if ($count === 1) {
                $this->model->save(['is_thumb' => 1]);
                GoodsModel::where('id', $data['goods_id'])->update(['thumb' => $data['url']]);
            }
            
            $this->success('添加成功', $this->model);
        } else {
            $this->error('添加失败');
        }
    }
    
    /**
     * 删除商品图片
     * @param int $id 图片ID
     * @return void
     */
    public function delete($id = 0)
    {
        if (!$id) {
            $this->error('图片ID不能为空');
        }
        
        $image = GoodsImageModel::find($id);
        if (!$image) {
            $this->error('图片不存在');
        }
        
        // 删除图片
        if ($image->delete()) {
            // 如果删除的是缩略图，设置新的缩略图
            if ($image->is_thumb) {
                $newThumb = GoodsImageModel::where('goods_id', $image->goods_id)
                    ->order('sort', 'asc')
                    ->value('url');
                
                GoodsModel::where('id', $image->goods_id)
                    ->update(['thumb' => $newThumb ?: '']);
            }
            
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    /**
     * 更新图片排序
     * @return void
     */
    public function sort()
    {
        $sorts = $this->request->post('sorts/a');
        if (!$sorts) {
            $this->error('排序数据不能为空');
        }
        
        if (GoodsImageModel::updateSort($sorts)) {
            $this->success('更新成功');
        } else {
            $this->error('更新失败');
        }
    }
    
    /**
     * 设置商品缩略图
     * @param int $id 图片ID
     * @return void
     */
    public function setThumb($id = 0)
    {
        if (!$id) {
            $this->error('图片ID不能为空');
        }
        
        $image = GoodsImageModel::find($id);
        if (!$image) {
            $this->error('图片不存在');
        }
        
        // 更新缩略图状态
        GoodsImageModel::where('goods_id', $image->goods_id)->update(['is_thumb' => 0]);
        $image->save(['is_thumb' => 1]);
        
        // 更新商品缩略图
        GoodsModel::where('id', $image->goods_id)->update(['thumb' => $image->url]);
        
        $this->success('设置成功');
    }
}