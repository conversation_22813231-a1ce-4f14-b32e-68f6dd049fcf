<?php
declare (strict_types = 1);

namespace app\admin\controller\goods;

use app\common\controller\BaseAdminController;
use think\facade\Db;

/**
 * 商品评价管理
 */
class GoodsComment extends BaseAdminController
{

    protected $allowFields = ['goods_id', 'user_id', 'order_id', 'rating', 'content', 'images', 'is_anonymous', 'status'];

    /**
     * 初始化
     */
    protected function initialize()
    {
        $this->model = new \app\model\GoodsComment();
        $this->table = 'goods_comment';
        
        // 验证规则
        $this->validateRule = [
            'goods_id' => 'require|number',
            'user_id' => 'require|number',
            'order_id' => 'require|number',
            'rating' => 'require|between:1,5',
            'content' => 'require|length:1,1000',
            'images' => 'array',
            'is_anonymous' => 'in:0,1',
            'status' => 'in:0,1'
        ];
    }
    
    /**
     * 查询前处理
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 支持评分、时间范围筛选
        $rating = $this->request->param('rating/d');
        if ($rating) {
            $where[] = ['rating', '=', $rating];
        }
        
        $dateRange = $this->request->param('date_range/a');
        if (!empty($dateRange)) {
            $where[] = ['create_time', 'between', [strtotime($dateRange[0]), strtotime($dateRange[1])]];
        }
        
        // 默认按创建时间倒序
        if (empty($sort)) {
            $sort = ['create_time' => 'desc'];
        }
    }
    
    /**
     * 查询后处理
     */
    protected function afterIndex($list)
    {
        // 关联商品和用户信息
        $data = $list->items();
        foreach ($data as &$item) {
            // 商品信息
            $goods = Db::table('ad_goods')->field('name,thumb')->where('id', $item['goods_id'])->find();
            $item['goods_name'] = $goods ? $goods['name'] : '';
            $item['goods_thumb'] = $goods ? $goods['thumb'] : '';
            
            // 用户信息（匿名则隐藏）
            if (!$item['is_anonymous']) {
                $user = Db::table('qi_users')->field('nickname,avatar')->where('id', $item['user_id'])->find();
                $item['user_nickname'] = $user ? $user['nickname'] : '';
                $item['user_avatar'] = $user ? $user['avatar'] : '';
            }
            
            // 处理图片数组
            $item['images'] = $item['images'] ? explode(',', $item['images']) : [];
        }
        
        return ['record' => $data, 'count' => $list->total()];
    }
    
    /**
     * 添加前处理
     */
    protected function beforeAdd($data)
    {
        // 处理图片数组
        if (isset($data['images']) && is_array($data['images'])) {
            $data['images'] = implode(',', $data['images']);
        }

        return $data;
    }
    
    /**
     * 更新前处理
     * @param array &$data
     * @param $id
     */
    protected function beforeEdit($data, $id)
    {
        // 处理图片数组
        if (isset($data['images']) && is_array($data['images'])) {
            $data['images'] = implode(',', $data['images']);
        }

        return $data;
    }
}