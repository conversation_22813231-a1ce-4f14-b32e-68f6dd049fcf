<?php

namespace app\admin\controller\goods;

use app\common\controller\BaseAdminController;
use think\App;
use think\facade\Db;

class ShippingFeeTemplate extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|模板名称' => 'require|max:50',
        'type|计费方式' => 'require|in:1,2',
        'areas|配送区域' => 'require|array',
        'areas.*.area_ids|区域' => 'require|string',
        'areas.*.area_names|区域名称' => 'require|string',
        'areas.*.first_unit|首重/首件数量' => 'require|float|gt:0',
        'areas.*.first_fee|首重/首件费用' => 'require|float|gt:0',
        'areas.*.additional_unit|续重/续件数量' => 'require|float|gt:0',
        'areas.*.additional_fee|续重/续件费用' => 'require|float|gt:0',
    ];

    protected $allowFields = [
        'name',
        'type',
        'enabled',
        'areas'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new \app\model\ShippingFeeTemplate();
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array
     */
    protected function beforeAdd($data)
    {
        $data['createtime'] = time();
        $data['updatetime'] = time();
        return $data;
    }

    /**
     * 添加后的钩子方法
     * @param array $data 表单数据
     * @param int $id 新增记录ID
     */
    protected function afterAdd($data, $id)
    {
        return $this->model->saveTemplate(array_merge($data, ['id' => $id]));
    }

    /**
     * 更新后的钩子方法
     * @param array $data
     * @param $id
     */
    protected function afterEdit($data, $id)
    {
        return $this->model->saveTemplate(array_merge($data, ['id' => $id]));
    }

    /**
     * 删除后的钩子方法
     * @param $id
     */
    protected function afterDelete($id)
    {
        $areaModel = new \app\model\ShippingFeeArea();
        $areaModel->where('template_id', $id)->delete();
    }

    protected function afterIndex($list)
    {
        if (!$list->items()) {
            return ['record' => [], 'count' => 0];
        }

        $records = $list->items();
        $templateIds = array_column($records, 'id');
        
        // 获取区域设置
        $areaModel = new \app\model\ShippingFeeArea();
        $areaMap = $areaModel->getAreaSettings($templateIds);

        foreach ($records as &$value) {
            $value['type_name'] = $value['type'] == 1 ? '按重量' : '按件数';
            $value['areas'] = $areaMap[$value['id']] ?? [];
        }

        return ['record' => $records, 'count' => $list->total()];
    }

    /**
     * 获取详情
     * @param $id
     * @return \think\Response
     */
    public function detail($id)
    {
        $info = parent::info($id);
        if ($info) {
            // 获取区域设置
            $areaModel = new \app\model\ShippingFeeArea();
            $info['areas'] = $areaModel->getAreaSettings($id);
        }
        return $info;
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        return $this->validateRule;
    }
}