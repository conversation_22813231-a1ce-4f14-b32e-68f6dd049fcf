<?php
declare (strict_types = 1);

namespace app\admin\controller\goods;

use app\common\controller\BaseAdminController;
use app\model\GoodsBrand;
use think\App;

/**
 * 品牌管理API控制器
 */
class Brand extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|品牌名称' => 'require|max:100',
        'logo|品牌Logo' => 'require',
        'sort|排序' => 'number|egt:0',
        'enabled|状态' => 'require|in:0,1',
        'description|品牌描述' => 'max:1000'
    ];
    
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new GoodsBrand();
    }
    
    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit') {
            return [
                'name|品牌名称' => 'max:100',
                'logo|品牌Logo' => '',
                'sort|排序' => 'number|egt:0',
                'enabled|状态' => 'in:0,1',
                'description|品牌描述' => 'max:1000'
            ];
        }
        
        return $this->validateRule;
    }
    
    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 设置默认值
        if (!isset($data['sort'])) {
            $data['sort'] = 0;
        }
        if (!isset($data['enabled'])) {
            $data['enabled'] = 1;
        }
        
        return $data;
    }
    
    /**
     * 查询前的钩子方法
     * @param array $where 查询条件
     * @param array $sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 按关键词搜索
        $keyword = $this->request->param('keyword', '');
        if (!empty($keyword)) {
            $where[] = ['name|description', 'like', '%' . $keyword . '%'];
        }
        
        // 按状态筛选
        $status = $this->request->param('status', '', 'trim');
        if ($status !== '') {
            $where[] = ['enabled', '=', intval($status)];
        }
        
        // 设置默认排序
        if (empty($sort)) {
            $sort = ['sort' => 'asc', 'id' => 'desc'];
        }
    }
}