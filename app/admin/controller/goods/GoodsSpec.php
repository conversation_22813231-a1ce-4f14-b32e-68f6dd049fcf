<?php
declare (strict_types = 1);

namespace app\admin\controller\goods;

use app\common\controller\BaseAdminController;
use app\model\GoodsSpec as GoodsSpecModel;
use app\model\GoodsSpecValue as GoodsSpecValueModel;
use think\App;

/**
 * 商品规格API控制器
 */
class GoodsSpec extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|规格名称' => 'require|max:50',
        'sort|排序' => 'number|egt:0',
        'status|状态' => 'in:0,1'
    ];
    
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new GoodsSpecModel();
    }
    
    /**
     * 获取规格列表
     * @return void
     */
    public function list()
    {
        $withValues = $this->request->param('with_values', false, 'boolean');
        $list = GoodsSpecModel::getAllSpecs($withValues);
        $this->success('获取成功', $list);
    }
    
    /**
     * 添加规格
     * @return void
     */
    public function add()
    {
        $data = $this->request->post();
        
        // 验证数据
        if (!$this->validate($data)) {
            $this->error($this->getError());
        }
        
        // 添加规格
        if ($this->model->save($data)) {
            // 保存规格值
            $values = $this->request->post('values/a');
            if (!empty($values)) {
                GoodsSpecValueModel::saveSpecValues($this->model->id, $values);
            }
            
            $this->success('添加成功', $this->model);
        } else {
            $this->error('添加失败');
        }
    }
    
    /**
     * 编辑规格
     * @param int $id 规格ID
     * @return void
     */
    public function edit($id = 0)
    {
        if (!$id) {
            $this->error('规格ID不能为空');
        }
        
        $spec = GoodsSpecModel::find($id);
        if (!$spec) {
            $this->error('规格不存在');
        }
        
        $data = $this->request->post();
        
        // 验证数据
        if (!$this->validate($data)) {
            $this->error($this->getError());
        }
        
        // 更新规格
        if ($spec->save($data)) {
            // 更新规格值
            $values = $this->request->post('values/a');
            if (isset($values)) {
                GoodsSpecValueModel::saveSpecValues($id, $values);
            }
            
            $this->success('更新成功', $spec);
        } else {
            $this->error('更新失败');
        }
    }
    
    /**
     * 删除规格
     * @param int $id 规格ID
     * @return void
     */
    public function delete($id = 0)
    {
        if (!$id) {
            $this->error('规格ID不能为空');
        }
        
        $spec = GoodsSpecModel::find($id);
        if (!$spec) {
            $this->error('规格不存在');
        }
        
        // 删除规格及其规格值
        if ($spec->delete()) {
            GoodsSpecValueModel::where('spec_id', $id)->delete();
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }
    
    /**
     * 更新规格排序
     * @return void
     */
    public function sort()
    {
        $sorts = $this->request->post('sorts/a');
        if (!$sorts) {
            $this->error('排序数据不能为空');
        }
        
        $data = [];
        foreach ($sorts as $id => $sort) {
            $data[] = [
                'id' => $id,
                'sort' => $sort
            ];
        }
        
        if ($this->model->saveAll($data)) {
            $this->success('更新成功');
        } else {
            $this->error('更新失败');
        }
    }
    
    /**
     * 获取规格值列表
     * @param int $spec_id 规格ID
     * @return void
     */
    public function values($spec_id = 0)
    {
        if (!$spec_id) {
            $this->error('规格ID不能为空');
        }
        
        $values = GoodsSpecValueModel::getSpecValues($spec_id);
        $this->success('获取成功', $values);
    }
    
    /**
     * 更新规格值排序
     * @return void
     */
    public function valueSort()
    {
        $sorts = $this->request->post('sorts/a');
        if (!$sorts) {
            $this->error('排序数据不能为空');
        }
        
        if (GoodsSpecValueModel::updateSort($sorts)) {
            $this->success('更新成功');
        } else {
            $this->error('更新失败');
        }
    }
}