<?php
declare (strict_types = 1);

namespace app\admin\controller\goods;

use app\common\controller\BaseController;
use app\model\Goods;
use think\facade\Db;

/**
 * 商品价格API控制器
 */
class GoodsPrice extends BaseController
{
    /**
     * 获取商品价格信息
     */
    public function index($goods_id = null)
    {
        if (empty($goods_id)) {
            $this->error('商品ID不能为空');
        }
        
        $goods = Goods::find($goods_id);
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        $priceInfo = [
            'goods_id' => $goods->id,
            'goods_title' => $goods->title,
            'price' => $goods->price,
            'original_price' => $goods->original_price ?? 0,
            'update_time' => $goods->update_time
        ];
        
        $this->ok('获取成功', $priceInfo);
    }
    
    /**
     * 批量获取商品价格
     */
    public function batchGet()
    {
        $goodsIds = $this->request->param('goods_ids', []);
        if (empty($goodsIds)) {
            $this->error('商品ID不能为空');
        }
        
        $priceList = Goods::whereIn('id', $goodsIds)
            ->field('id as goods_id, title as goods_title, price, original_price, update_time')
            ->select()
            ->toArray();
        
        $this->ok('获取成功', $priceList);
    }
    
    /**
     * 更新商品价格
     */
    public function update()
    {
        $goodsId = $this->request->post('goods_id', 0, 'intval');
        $price = $this->request->post('price', 0, 'float');
        $originalPrice = $this->request->post('original_price', 0, 'float');
        $remark = $this->request->post('remark', '');
        
        if (empty($goodsId)) {
            $this->error('商品ID不能为空');
        }
        
        if ($price < 0) {
            $this->error('价格不能小于0');
        }
        
        $goods = Goods::find($goodsId);
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        // 记录原价格
        $oldPrice = $goods->price;
        $oldOriginalPrice = $goods->original_price ?? 0;
        
        // 开启事务
        Db::startTrans();
        try {
            // 更新价格
            $goods->price = $price;
            if ($originalPrice > 0) {
                $goods->original_price = $originalPrice;
            }
            $result = $goods->save();
            if (!$result) {
                throw new \Exception('价格更新失败');
            }
            
            // 记录价格变更日志
            $this->recordPriceLog($goodsId, $oldPrice, $price, $oldOriginalPrice, $originalPrice, $remark);
            
            Db::commit();
            $this->ok('价格更新成功');
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 批量更新商品价格
     */
    public function batchUpdate()
    {
        $data = $this->request->post('data', []);
        $remark = $this->request->post('remark', '');
        
        if (empty($data)) {
            $this->error('参数错误');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            $success = 0;
            foreach ($data as $item) {
                if (empty($item['goods_id']) || !isset($item['price']) || $item['price'] < 0) {
                    continue;
                }
                
                $goodsId = $item['goods_id'];
                $price = $item['price'];
                $originalPrice = $item['original_price'] ?? 0;
                
                $goods = Goods::find($goodsId);
                if (!$goods) {
                    continue;
                }
                
                // 记录原价格
                $oldPrice = $goods->price;
                $oldOriginalPrice = $goods->original_price ?? 0;
                
                // 更新价格
                $goods->price = $price;
                if ($originalPrice > 0) {
                    $goods->original_price = $originalPrice;
                }
                if ($goods->save()) {
                    // 记录价格变更日志
                    $this->recordPriceLog($goodsId, $oldPrice, $price, $oldOriginalPrice, $originalPrice, $remark);
                    $success++;
                }
            }
            
            Db::commit();
            $this->ok('批量更新成功，共更新' . $success . '条记录');
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 按比例调整价格
     */
    public function adjustByPercentage()
    {
        $goodsIds = $this->request->post('goods_ids', []);
        $percentage = $this->request->post('percentage', 0, 'float');
        $adjustType = $this->request->post('adjust_type', 'increase'); // increase or decrease
        $remark = $this->request->post('remark', '');
        
        if (empty($goodsIds)) {
            $this->error('商品ID不能为空');
        }
        
        if ($percentage <= 0) {
            $this->error('调整比例必须大于0');
        }
        
        if (!in_array($adjustType, ['increase', 'decrease'])) {
            $this->error('调整类型错误');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            $success = 0;
            $goods = Goods::whereIn('id', $goodsIds)->select();
            
            foreach ($goods as $item) {
                // 记录原价格
                $oldPrice = $item->price;
                
                // 计算新价格
                if ($adjustType == 'increase') {
                    $newPrice = $oldPrice * (1 + $percentage / 100);
                } else {
                    $newPrice = $oldPrice * (1 - $percentage / 100);
                }
                
                // 保留两位小数
                $newPrice = round($newPrice, 2);
                if ($newPrice < 0.01) {
                    $newPrice = 0.01; // 最低价格为0.01
                }
                
                // 更新价格
                $item->price = $newPrice;
                if ($item->save()) {
                    // 记录价格变更日志
                    $this->recordPriceLog($item->id, $oldPrice, $newPrice, 0, 0, 
                        $remark . '(' . ($adjustType == 'increase' ? '上调' : '下调') . $percentage . '%)');
                    $success++;
                }
            }
            
            Db::commit();
            $this->ok('价格调整成功，共调整' . $success . '条记录');
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取价格变更日志
     */
    public function logs()
    {
        $goodsId = $this->request->param('goods_id', 0, 'intval');
        $page = $this->request->param('page', 1, 'intval');
        $limit = $this->request->param('limit', 10, 'intval');
        
        if (empty($goodsId)) {
            $this->error('商品ID不能为空');
        }
        
        // 查询价格日志
        $query = Db::table('goods_price_log')
            ->where('goods_id', $goodsId)
            ->order('create_time', 'desc');
        
        $count = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();
        
        $result = [
            'total' => $count,
            'per_page' => $limit,
            'current_page' => $page,
            'last_page' => ceil($count / $limit),
            'data' => $list
        ];
        
        $this->ok('获取成功', $result);
    }
    
    /**
     * 记录价格变更日志
     * 
     * @param int $goodsId 商品ID
     * @param float $oldPrice 原价格
     * @param float $newPrice 新价格
     * @param float $oldOriginalPrice 原原价
     * @param float $newOriginalPrice 新原价
     * @param string $remark 备注
     * @return int 日志ID
     */
    protected function recordPriceLog($goodsId, $oldPrice, $newPrice, $oldOriginalPrice, $newOriginalPrice, $remark = '')
    {
        $goods = Goods::find($goodsId);
        if (!$goods) {
            return false;
        }
        
        $data = [
            'goods_id' => $goodsId,
            'goods_title' => $goods->title,
            'old_price' => $oldPrice,
            'new_price' => $newPrice,
            'old_original_price' => $oldOriginalPrice,
            'new_original_price' => $newOriginalPrice,
            'change_amount' => $newPrice - $oldPrice,
            'change_percentage' => $oldPrice > 0 ? round(($newPrice - $oldPrice) / $oldPrice * 100, 2) : 0,
            'remark' => $remark,
            'operator_id' => $this->userRow['id'] ?? 0,
            'operator_name' => $this->userRow['username'] ?? '',
            'create_time' => time()
        ];
        
        return Db::table('goods_price_log')->insertGetId($data);
    }
}