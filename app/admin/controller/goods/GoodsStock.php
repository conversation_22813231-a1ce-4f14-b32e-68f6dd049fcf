<?php
declare (strict_types = 1);

namespace app\admin\controller\goods;

use app\common\controller\BaseController;
use app\model\Goods;
use think\facade\Db;

/**
 * 商品库存API控制器
 */
class GoodsStock extends BaseController
{
    /**
     * 获取商品库存信息
     */
    public function index($goods_id = null)
    {
        if (empty($goods_id)) {
            $this->error('商品ID不能为空');
        }
        
        $goods = Goods::find($goods_id);
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        $stockInfo = [
            'goods_id' => $goods->id,
            'goods_title' => $goods->title,
            'stock' => $goods->stock,
            'update_time' => $goods->update_time
        ];
        
        $this->ok('获取成功', $stockInfo);
    }
    
    /**
     * 批量获取商品库存
     */
    public function batchGet()
    {
        $goodsIds = $this->request->param('goods_ids', []);
        if (empty($goodsIds)) {
            $this->error('商品ID不能为空');
        }
        
        $stockList = Goods::whereIn('id', $goodsIds)
            ->field('id as goods_id, title as goods_title, stock, update_time')
            ->select()
            ->toArray();
        
        $this->ok('获取成功', $stockList);
    }
    
    /**
     * 增加库存
     */
    public function increase()
    {
        $goodsId = $this->request->post('goods_id', 0, 'intval');
        $num = $this->request->post('num', 0, 'intval');
        $remark = $this->request->post('remark', '');
        
        if (empty($goodsId)) {
            $this->error('商品ID不能为空');
        }
        
        if ($num <= 0) {
            $this->error('增加库存数量必须大于0');
        }
        
        $goods = Goods::find($goodsId);
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 更新库存
            $result = Goods::updateStock($goodsId, $num);
            if (!$result) {
                throw new \Exception('库存更新失败');
            }
            
            // 记录库存日志
            $this->recordStockLog($goodsId, $num, 'increase', $remark);
            
            Db::commit();
            $this->ok('库存增加成功');
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 减少库存
     */
    public function decrease()
    {
        $goodsId = $this->request->post('goods_id', 0, 'intval');
        $num = $this->request->post('num', 0, 'intval');
        $remark = $this->request->post('remark', '');
        
        if (empty($goodsId)) {
            $this->error('商品ID不能为空');
        }
        
        if ($num <= 0) {
            $this->error('减少库存数量必须大于0');
        }
        
        $goods = Goods::find($goodsId);
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        // 检查库存是否足够
        if ($goods->stock < $num) {
            $this->error('商品库存不足');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 更新库存
            $result = Goods::updateStock($goodsId, -$num);
            if (!$result) {
                throw new \Exception('库存更新失败');
            }
            
            // 记录库存日志
            $this->recordStockLog($goodsId, $num, 'decrease', $remark);
            
            Db::commit();
            $this->ok('库存减少成功');
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 设置库存
     */
    public function set()
    {
        $goodsId = $this->request->post('goods_id', 0, 'intval');
        $stock = $this->request->post('stock', 0, 'intval');
        $remark = $this->request->post('remark', '');
        
        if (empty($goodsId)) {
            $this->error('商品ID不能为空');
        }
        
        if ($stock < 0) {
            $this->error('库存数量不能小于0');
        }
        
        $goods = Goods::find($goodsId);
        if (!$goods) {
            $this->error('商品不存在');
        }
        
        // 计算库存变化量
        $change = $stock - $goods->stock;
        $type = $change >= 0 ? 'set_increase' : 'set_decrease';
        $changeAbs = abs($change);
        
        // 开启事务
        Db::startTrans();
        try {
            // 直接设置库存
            $goods->stock = $stock;
            $result = $goods->save();
            if (!$result) {
                throw new \Exception('库存设置失败');
            }
            
            // 记录库存日志
            if ($change != 0) {
                $this->recordStockLog($goodsId, $changeAbs, $type, $remark);
            }
            
            Db::commit();
            $this->ok('库存设置成功');
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取库存变更日志
     */
    public function logs()
    {
        $goodsId = $this->request->param('goods_id', 0, 'intval');
        $page = $this->request->param('page', 1, 'intval');
        $limit = $this->request->param('limit', 10, 'intval');
        
        if (empty($goodsId)) {
            $this->error('商品ID不能为空');
        }
        
        // 查询库存日志
        $query = Db::table('goods_stock_log')
            ->where('goods_id', $goodsId)
            ->order('create_time', 'desc');
        
        $count = $query->count();
        $list = $query->page($page, $limit)->select()->toArray();
        
        $result = [
            'total' => $count,
            'per_page' => $limit,
            'current_page' => $page,
            'last_page' => ceil($count / $limit),
            'data' => $list
        ];
        
        $this->ok('获取成功', $result);
    }
    
    /**
     * 记录库存变更日志
     * 
     * @param int $goodsId 商品ID
     * @param int $num 变更数量
     * @param string $type 变更类型：increase(增加), decrease(减少), set_increase(设置增加), set_decrease(设置减少)
     * @param string $remark 备注
     * @return int 日志ID
     */
    protected function recordStockLog($goodsId, $num, $type, $remark = '')
    {
        $goods = Goods::find($goodsId);
        if (!$goods) {
            return false;
        }
        
        $data = [
            'goods_id' => $goodsId,
            'goods_title' => $goods->title,
            'change_num' => $num,
            'type' => $type,
            'before_stock' => $type == 'decrease' || $type == 'set_decrease' ? $goods->stock + $num : $goods->stock - $num,
            'after_stock' => $goods->stock,
            'remark' => $remark,
            'operator_id' => $this->userRow['id'] ?? 0,
            'operator_name' => $this->userRow['username'] ?? '',
            'create_time' => time()
        ];
        
        return Db::table('goods_stock_log')->insertGetId($data);
    }
}