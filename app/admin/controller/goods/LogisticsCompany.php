<?php

namespace app\admin\controller\goods;

use app\common\controller\BaseAdminController;
use think\App;
use think\facade\Db;

class LogisticsCompany extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|物流公司名称' => 'require|max:100',
        'logo|公司LOGO' => 'require|url',
        'url|官方网址' => 'require|url',
        'sort|排序' => 'require|number',
        'kdn_code|快递鸟编码' => 'require|max:50',
        'kd100_free_code|快递100免费版编码' => 'require|max:50',
        'kd100_pay_code|快递100付费版编码' => 'require|max:50',
        'cainiao_bird_code|菜鸟物流接口编码' => 'require|max:50',
        'express_query_code|快递查询接口编码' => 'require|max:50',
        'status|状态' => 'require|in:0,1'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new \app\model\LogisticsCompany();
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array
     */
    protected function beforeAdd($data)
    {
        $data['createtime'] = time();
        $data['updatetime'] = time();
        return $data;
    }

    /**
     * 更新前的钩子方法
     * @param array $data
     * @param $id
     * @return array
     */
    protected function beforeEdit($data, $id)
    {
        $data['updatetime'] = time();
        return $data;
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        return $this->validateRule;
    }
}