<?php

namespace app\admin\controller\goods;

use app\common\controller\BaseAdminController;
use app\model\Card as CardModel;
use think\App;
use think\facade\Db;

/**
 * 卡密管理控制器
 */
class Card extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|卡名称' => 'require',
        'card_key|卡密' => 'require|unique:card',
        'type|卡类型' => 'require|in:recharge,member,gift',
        'value|面值' => 'require|float|egt:0',
        'enabled|状态' => 'require|in:0,1,2',
        'valid_start_time|有效期开始' => 'require|integer',
        'valid_end_time|有效期结束' => 'require|integer|gt:valid_start_time',
        'remark|备注' => 'max:255',
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new CardModel();
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit' && $id) {
            return [
                'name|卡名称' => 'require',
                'card_key|卡密' => 'require|unique:card,card_key,' . $id,
                'type|卡类型' => 'require|in:recharge,member,gift',
                'value|面值' => 'require|float|egt:0',
                'enabled|状态' => 'require|in:0,1,2',
                'valid_start_time|有效期开始' => 'require|integer',
                'valid_end_time|有效期结束' => 'require|integer|gt:valid_start_time',
                'remark|备注' => 'max:255',
            ];
        }
        
        return $this->validateRule;
    }

    public function afterIndex($list)
    {

        $items = $list->items();

        if (empty($items)) {
            return ['record' => [], 'count' => 0];
        }

        foreach ($items as &$item) {
            $item['use_time'] = $item['use_time'] ? date('Y-m-d H:i:s', $item['use_time']) : '';
            $item['valid_start_time'] = date('Y-m-d H:i:s', $item['valid_start_time']);
            $item['valid_end_time'] = date('Y-m-d H:i:s', $item['valid_end_time']);
            $item['validTime'] = $item['valid_start_time'] . ' - ' . $item['valid_end_time'];
        }

        return ['record' => $items, 'count' => $list->total()];

    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 处理时间戳
        if (isset($data['valid_start_time']) && !is_numeric($data['valid_start_time'])) {
            $data['valid_start_time'] = strtotime($data['valid_start_time']);
        }
        
        if (isset($data['valid_end_time']) && !is_numeric($data['valid_end_time'])) {
            $data['valid_end_time'] = strtotime($data['valid_end_time']);
        }
        
        $data['createtime'] = time();
        
        return $data;
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 处理时间戳
        if (isset($data['valid_start_time']) && !is_numeric($data['valid_start_time'])) {
            $data['valid_start_time'] = strtotime($data['valid_start_time']);
        }
        
        if (isset($data['valid_end_time']) && !is_numeric($data['valid_end_time'])) {
            $data['valid_end_time'] = strtotime($data['valid_end_time']);
        }
        
        return $data;
    }

    /**
     * 批量生成卡密
     * @return \think\Response
     */
    public function generate()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();
            
            // 验证基础数据
            $rule = [
                'name|卡名称' => 'require',
                'type|卡类型' => 'require|in:recharge,member,gift',
                'value|面值' => 'require|float|egt:0',
                'quantity|生成数量' => 'require|integer|between:1,1000',
                'length|卡密长度' => 'require|integer|between:6,32',
                'valid_start_time|有效期开始' => 'require',
                'valid_end_time|有效期结束' => 'require',
                'prefix|前缀' => 'max:10',
                'suffix|后缀' => 'max:10',
                'remark|备注' => 'max:255',
            ];
            
            $this->validate($post, $rule);
            
            // 处理时间戳
            if (isset($post['valid_start_time']) && !is_numeric($post['valid_start_time'])) {
                $post['valid_start_time'] = strtotime($post['valid_start_time']);
            }
            
            if (isset($post['valid_end_time']) && !is_numeric($post['valid_end_time'])) {
                $post['valid_end_time'] = strtotime($post['valid_end_time']);
            }
            
            // 生成卡密
            $count = intval($post['quantity']);
            $length = intval($post['length']);
            $prefix = $post['prefix'] ?? '';
            $suffix = $post['suffix'] ?? '';
            
            $insertData = [];
            $now = time();
            
            for ($i = 0; $i < $count; $i++) {
                // 生成卡密
                $cardKey = $this->generateCardKey($length, $prefix, $suffix);
                
                // 检查卡密是否已存在
                while ($this->model->where('card_key', $cardKey)->find()) {
                    $cardKey = $this->generateCardKey($length, $prefix, $suffix);
                }
                
                $insertData[] = [
                    'name' => $post['name'],
                    'card_key' => $cardKey,
                    'type' => $post['type'],
                    'value' => $post['value'],
                    'quantity' => $post['quantity'],
                    'enabled' => 1, // 默认未使用
                    'valid_start_time' => $post['valid_start_time'],
                    'valid_end_time' => $post['valid_end_time'],
                    'createtime' => $now,
                    'remark' => $post['remark'] ?? '',
                ];
            }
            
            // 开始事务
            Db::startTrans();
            try {
                // 批量插入数据
                $this->model->insertAll($insertData);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->error('批量生成卡密失败：' . $e->getMessage());
            }
            $this->ok('批量生成卡密成功');
        }
        
        $this->error('请求方法不正确');
    }
    
    /**
     * 生成卡密
     * @param int $length 长度
     * @param string $prefix 前缀
     * @param string $suffix 后缀
     * @return string
     */
    protected function generateCardKey($length = 16, $prefix = '', $suffix = '')
    {
        // 字符集，排除易混淆的字符
        $chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        $cardKey = '';
        
        for ($i = 0; $i < $length; $i++) {
            $cardKey .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        
        // 每4个字符添加一个分隔符
        $cardKey = implode('-', str_split($cardKey, 4));
        
        return $prefix . $cardKey . $suffix;
    }
    
    /**
     * 导出卡密
     * @return \think\Response
     */
    public function export()
    {
        // 构建查询参数
        list($page, $pageSize, $sort, $where) = $this->buildParames();
        
        // 查询数据
        $list = $this->model
            ->where($where)
            ->order($sort)
            ->select()
            ->toArray();
        
        if (empty($list)) {
            $this->fail('没有数据可导出');
        }
        
        // 设置CSV头信息
        $header = ['卡密ID', '卡名称', '卡密', '卡类型', '面值', '状态', '有效期开始', '有效期结束', '创建时间', '使用时间', '备注'];
        
        // 准备数据
        $data = [];
        foreach ($list as $item) {
            $status = ['0' => '已禁用', '1' => '未使用', '2' => '已使用'];
            $types = ['recharge' => '充值卡', 'member' => '会员卡', 'gift' => '礼品卡'];
            $data[] = [
                $item['id'],
                $item['name'],
                $item['card_key'],
                $types[$item['type']] ?? $item['type'],
                $item['value'],
                $status[$item['enabled']] ?? $item['enabled'],
                date('Y-m-d H:i:s', $item['valid_start_time']),
                date('Y-m-d H:i:s', $item['valid_end_time']),
                $item['createtime'],
                $item['use_time'] ? date('Y-m-d H:i:s', $item['use_time']) : '',
                $item['remark'],
            ];
        }
        
        // 返回CSV数据
        $filename = '卡密数据_' . date('YmdHis') . '.csv';
        try {
            $this->exportCsv($filename, $header, $data);
        } catch (\Exception $e) {
            $this->fail('导出失败：' . $e->getMessage());
        }

        $this->ok('导出成功', ['filename' => $filename]);
        
    }
    
    /**
     * 导出CSV
     * @param string $filename 文件名
     * @param array $header 表头
     * @param array $data 数据
     */
    protected function exportCsv($filename, $header, $data)
    {
        try {
            // 清除之前的输出缓冲
            if (ob_get_level()) {
                ob_clean();
            }

            // 设置头信息
            header('Content-Type: text/csv; charset=GBK');
            header('Content-Disposition: attachment; filename="' . rawurlencode($filename) . '"');
            header('Pragma: no-cache');
            header('Expires: 0');
            
            // 打开输出流
            $fp = fopen('php://temp', 'w+');
            if ($fp === false) {
                throw new \Exception('无法创建输出流');
            }
            
            // 写入表头
            $header = array_map(function($value) {
                return iconv('UTF-8', 'GBK//IGNORE', $value);
            }, $header);
            if (fputcsv($fp, $header) === false) {
                throw new \Exception('写入表头失败');
            }
            
            // 写入数据
            foreach ($data as $row) {
                $row = array_map(function($value) {
                    // 处理空值
                    if ($value === null || $value === '') {
                        return '';
                    }
                    // 转换编码
                    return iconv('UTF-8', 'GBK//IGNORE', $value);
                }, $row);
                
                if (fputcsv($fp, $row) === false) {
                    throw new \Exception('写入数据失败');
                }
            }
            
            // 将指针移到开头
            rewind($fp);
            
            // 读取内容并输出
            fpassthru($fp);
            fclose($fp);
            exit;
            
        } catch (\Exception $e) {
            // 清除之前的输出缓冲
            if (ob_get_level()) {
                ob_clean();
            }
            
            // 记录错误日志
            error_log('导出CSV失败：' . $e->getMessage());
            
            // 返回错误信息
            $this->fail('导出CSV失败，请稍后重试');
        }
    }
    
    /**
     * 更新卡密状态
     * @return \think\Response
     */
    public function updateStatus()
    {
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $status = $this->request->post('status');
            
            if (empty($id) || !in_array($status, [0, 1, 2])) {
                $this->fail('参数错误');
            }
            
            $data = ['status' => $status];
            
            // 如果标记为已使用，记录使用时间
            if ($status == 2) {
                $data['use_time'] = time();
            }
            
            // 更新状态
            $result = $this->model->where('id', $id)->update($data);
            
            if ($result !== false) {
                $this->ok('更新状态成功');
            } else {
                $this->fail('更新状态失败');
            }
        }
        
        $this->fail('请求方法不正确');
    }
}