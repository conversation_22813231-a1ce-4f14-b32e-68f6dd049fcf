<?php

namespace app\admin\controller;

use app\admin\entity\UserEntity;
use app\common\ApiCode;
use app\common\controller\BaseController;
use app\common\login\util\Password;
use app\model\system\Admin;
use think\facade\Db;

/**
 * 用户信息接口控制器
 * 用于获取和更新用户信息
 */
class UserInfo extends BaseController
{
    
    /**
     * 获取用户基本信息
     * 
     * @return void
     */
    public function info(UserEntity $userEntity)
    {
        $userId = $userEntity->id;
        
        // 查询用户关联的角色组ID
        $groupIds = Db::table('sys_admin_group_rule')->where('user_id', $userId)->column('group_id');
        
        // 查询角色组信息
        $roles = [];
        if (!empty($groupIds)) {
            $roles = Db::table('sys_admin_group')->where('id', 'in', $groupIds)->column('name');
        }
        
        // 将角色信息添加到用户实体中
        $userEntity->roles = $roles;
        
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $userEntity, ApiCode::SUCCESS);
    }
    
    /**
     * 更新用户资料
     * 
     * @return void
     */
    public function update(UserEntity $userEntity)
    {
        $userId = $userEntity->id;
        
        // 获取更新数据
        $data = $this->request->post();

        // 安全过滤，防止修改敏感字段
        $allowFields = ['head_img', 'mobile', 'mail', 'username', 'nickname', 'profile'];
        $updateData = [];
        foreach ($allowFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        if (empty($updateData)) {
            $this->ok('没有要更新的数据', [], ApiCode::BAD_REQUEST);
        }
        
        // 验证数据
        $validate = $this->validate($updateData, [
            'head_img' => 'url',
            'mobile' => 'mobile',
            'mail' => 'email',
            'username' => 'length:2,30',
            'nickname' => 'length:2,30',
            'profile' => 'max:500'
        ], [
            'head_img.url' => '头像地址格式不正确',
            'mobile.mobile' => '手机号格式不正确',
            'mail.email' => '邮箱格式不正确',
            'username.length' => '用户名长度必须在2-30个字符之间',
            'nickname.length' => '昵称长度必须在2-30个字符之间',
            'profile.max' => '个人简介不能超过500个字符'
        ]);
        
        if (true !== $validate) {
            $this->ok($validate, [], ApiCode::BAD_REQUEST);
        }
        
        // 更新用户信息
        $result = Admin::where('id', $userId)->update($updateData);
        if ($result === false) {
            $this->ok('更新失败', [], ApiCode::SERVER_ERROR);
        }
        
        $this->ok('更新成功', [], ApiCode::SUCCESS);
    }
    
    /**
     * 修改密码
     * 
     * @return void
     */
    public function changePassword(UserEntity $userEntity)
    {
        $userId = $userEntity->id;
        $oldPassword = $this->request->put('old_password');
        $newPassword = $this->request->put('new_password');
        $confirmPassword = $this->request->put('confirm_password');
        
        // 验证参数
        if (empty($oldPassword)) {
            $this->ok('原密码不能为空', [], ApiCode::PASSWORD_EMPTY);
        }
        if (empty($newPassword)) {
            $this->ok('新密码不能为空', [], ApiCode::PASSWORD_EMPTY);
        }
        if ($newPassword !== $confirmPassword) {
            $this->ok('两次输入的密码不一致', [], ApiCode::BAD_REQUEST);
        }
        
        // 验证原密码
        $user = Admin::where('id', $userId)->find();
        if (empty($user)) {
            $this->ok(ApiCode::getMessage(ApiCode::USER_NOT_EXIST), [], ApiCode::USER_NOT_EXIST);
        }

        // 验证原密码是否正确
        $salt = $user->salt;
        if (!password_verify($oldPassword, $user->password)) {
            $this->ok('原密码错误', [], ApiCode::PASSWORD_ERROR);
        }
        
        // 更新密码
        $encryptNewPassword = Password::encryptPassword($newPassword, $salt);
        $result = Admin::where('id', $userId)->update(['password' => $encryptNewPassword]);
        if ($result === false) {
            $this->ok('密码修改失败', [], ApiCode::SERVER_ERROR);
        }
        
        $this->ok('密码修改成功', [], ApiCode::SUCCESS);
    }
    
    /**
     * 获取用户ID
     * 
     * @return void
     */
    public function getUserId(UserEntity $userEntity)
    {
        $userId = $userEntity->id;
        $this->ok('获取成功', ['user_id' => $userId], ApiCode::SUCCESS);
    }

}