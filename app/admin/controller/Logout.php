<?php

namespace app\admin\controller;

use app\common\ApiCode;
use app\common\controller\BaseController;
use app\common\login\LoginService;
use think\facade\Request;

class Logout extends BaseController
{
    public function logout()
    {
        // 获取当前用户的token
        $token = Request::header('token');
        
        if (empty($token)) {
            $this->ok(ApiCode::getMessage(ApiCode::ACCESS_TOKEN_INVALID), [], ApiCode::ACCESS_TOKEN_INVALID);
        }
        
        // 调用登出服务清除token
        $result = LoginService::logout($token);
        
        if ($result === false) {
            // 登出失败，获取错误信息
            $error = LoginService::getError();
            $code = LoginService::getCode();
            $this->ok($error, [], $code);
        }
        
        // 登出成功
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), [], ApiCode::SUCCESS);
    }
}