<?php
declare (strict_types = 1);

namespace app\admin\controller\system;

use app\common\controller\BaseAdminController;
use think\App;
use think\facade\Db;

/**
 * 定时任务管理API控制器
 */
class Task extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|任务名称' => 'require|max:50',
        'command|执行命令' => 'require|max:255',
        'rule|执行规则' => 'require|max:50',
        'status|状态' => 'require|in:0,1',
        'concurrent|是否允许并发' => 'in:0,1',
        'retry_times|重试次数' => 'number|egt:0',
        'retry_interval|重试间隔(秒)' => 'number|egt:0',
        'notify_type|通知方式' => 'array',
        'notify_target|通知对象' => 'array',
        'description|任务描述' => 'max:255'
    ];

    protected $allowFields = [
        'name',
        'command',
        'rule',
        'status',
        'concurrent',
        'retry_times',
        'retry_interval',
        'notify_type',
        'notify_target',
        'description',
        'createtime',
        'updatetime'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new \app\model\Task();
    }

    /**
     * 查询前处理
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 支持按任务名称、状态、创建时间范围筛选
        $name = $this->request->param('name');
        if ($name) {
            $where[] = ['name', 'like', '%' . $name . '%'];
        }

        $status = $this->request->param('status');
        if (isset($status)) {
            $where[] = ['status', '=', $status];
        }

        $dateRange = $this->request->param('date_range/a');
        if (!empty($dateRange)) {
            $where[] = ['createtime', 'between', [strtotime($dateRange[0]), strtotime($dateRange[1])]];
        }

        // 默认按创建时间倒序
        if (empty($sort)) {
            $sort = ['createtime' => 'desc'];
        }
    }

    /**
     * 查询后处理
     */
    protected function afterIndex($list)
    {
        $data = $list->items();
        foreach ($data as &$item) {
            // 处理通知方式和通知对象
            $item['notify_type'] = $item['notify_type'] ? json_decode($item['notify_type'], true) : [];
            $item['notify_target'] = $item['notify_target'] ? json_decode($item['notify_target'], true) : [];
            
            // 获取最近一次执行记录
            $lastRecord = Db::table('ad_task_record')
                ->where('task_id', $item['id'])
                ->order('id', 'desc')
                ->find();
            
            $item['last_execute_time'] = $lastRecord ? $lastRecord['start_time'] : null;
            $item['last_execute_status'] = $lastRecord ? $lastRecord['status'] : null;
            $item['last_execute_result'] = $lastRecord ? $lastRecord['result'] : null;
        }

        return ['record' => $data, 'count' => $list->total()];
    }

    /**
     * 添加前处理
     */
    protected function beforeAdd($data)
    {
        // 处理通知方式和通知对象
        if (isset($data['notify_type']) && is_array($data['notify_type'])) {
            $data['notify_type'] = json_encode($data['notify_type']);
        }
        if (isset($data['notify_target']) && is_array($data['notify_target'])) {
            $data['notify_target'] = json_encode($data['notify_target']);
        }

        $data['createtime'] = time();
        $data['updatetime'] = time();

        return $data;
    }

    /**
     * 更新前处理
     */
    protected function beforeEdit($data, $id)
    {
        // 处理通知方式和通知对象
        if (isset($data['notify_type']) && is_array($data['notify_type'])) {
            $data['notify_type'] = json_encode($data['notify_type']);
        }
        if (isset($data['notify_target']) && is_array($data['notify_target'])) {
            $data['notify_target'] = json_encode($data['notify_target']);
        }

        $data['updatetime'] = time();

        return $data;
    }

    /**
     * 获取任务执行记录
     */
    public function records($task_id = null)
    {
        if (!$task_id) {
            $this->fail('任务ID不能为空');
        }

        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 10);

        $query = Db::table('ad_task_record')->where('task_id', $task_id);

        // 支持按执行状态和时间范围筛选
        $status = $this->request->param('status');
        if (isset($status)) {
            $query->where('status', $status);
        }

        $dateRange = $this->request->param('date_range/a');
        if (!empty($dateRange)) {
            $query->whereBetween('start_time', [strtotime($dateRange[0]), strtotime($dateRange[1])]);
        }

        $list = $query->order('id', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        $this->ok('获取成功', [
            'record' => $list->items(),
            'count' => $list->total()
        ]);
    }

    /**
     * 手动执行任务
     */
    public function execute($id)
    {
        if (!$id) {
            $this->fail('任务ID不能为空');
        }

        $task = $this->model->find($id);
        if (!$task) {
            $this->fail('任务不存在');
        }

        if ($task->status == 0) {
            $this->fail('禁用状态的任务不能执行');
        }

        try {
            // 创建执行记录
            $record = [
                'task_id' => $id,
                'start_time' => time(),
                'status' => 0 // 执行中
            ];
            $recordId = Db::table('ad_task_record')->insertGetId($record);

            // 执行任务命令
            $command = $task->command;
            $output = [];
            $code = 0;
            exec($command . ' 2>&1', $output, $code);

            // 更新执行记录
            $update = [
                'end_time' => time(),
                'status' => $code === 0 ? 1 : 2, // 1:成功 2:失败
                'result' => implode("\n", $output)
            ];
            Db::table('ad_task_record')->where('id', $recordId)->update($update);

            if ($code === 0) {
            } else {
                $this->fail('执行失败：' . implode("\n", $output));
            }
        } catch (\Exception $e) {
            // 更新执行记录为失败状态
            if (isset($recordId)) {
                Db::table('ad_task_record')->where('id', $recordId)->update([
                    'end_time' => time(),
                    'status' => 2,
                    'result' => $e->getMessage()
                ]);
            }
            $this->fail('执行异常：' . $e->getMessage());
        }
        $this->success('执行成功');

    }

    /**
     * 获取任务统计信息
     */
    public function statistics()
    {
        try {
            // 统计总任务数
            $total = $this->model->count();

            // 统计各状态任务数
            $statusCount = $this->model
                ->group('status')
                ->column('count(*)', 'status');

            // 统计今日执行次数
            $todayCount = Db::table('ad_task_record')
                ->whereTime('start_time', 'today')
                ->count();

            // 统计今日执行失败次数
            $todayFailCount = Db::table('ad_task_record')
                ->whereTime('start_time', 'today')
                ->where('status', 2)
                ->count();

        } catch (\Exception $e) {
            $this->fail('获取统计信息失败：' . $e->getMessage());
        }
        $this->ok('获取成功', [
            'total' => $total,
            'enabled' => $statusCount[1] ?? 0,
            'disabled' => $statusCount[0] ?? 0,
            'today_execute' => $todayCount,
            'today_fail' => $todayFailCount
        ]);
    }
}