<?php

namespace app\admin\controller\system;

use app\common\IdGenerator;
use app\common\login\util\Password;
use app\model\system\Admin as AdminModel;
use app\model\system\AdminGroupRule;
use think\App;
use app\common\controller\BaseAdminController;
use think\facade\Db;

class Admin extends BaseAdminController
{

    private $groups;
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'username|用户名' => 'require|unique:sys_admin',
        'password|密码' => 'require',
        'nickname|昵称' => 'require',
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new AdminModel();
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit' && $id) {
            return [
                'mail|邮箱' => 'email',
                'nickname|昵称' => 'require',
            ];
        }
        
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        try {
            // 处理密码
            $salt = Password::generateSalt($data['password']);
            $data['salt'] = $salt;
            $data['password'] = Password::encryptPassword($data['password'], $salt);
            $data['user_no'] = IdGenerator::generateIdFromDb();
        } catch (\Exception $e){
        }

        
        // 保存管理员信息
        return $data;
    }

    private function generateSalt($password)
    {
        $hashed = password_hash($password, PASSWORD_DEFAULT);
        return $hashed;
    }

    /**
     * 加密密码
     * @param string $password 原始密码
     * @param string $salt 盐值
     * @return string 加密后的密码
     */
    private function encryptPassword($password, $salt = '')
    {
        return password_hash($password . $salt, PASSWORD_DEFAULT);
    }
    
    /**
     * 添加后的钩子方法
     * @param int $id 新增记录的ID
     * @param array $data 表单数据
     */
    protected function afterAdd($id, $data)
    {
        // 保存管理员与用户组的关联
        $groupRuleData = [];
        if (!$this->groups) {
            return;
        }
        foreach ($this->groups as $groupId) {
            $groupRuleData[] = [
                'user_id' => $id,
                'group_id' => $groupId
            ];
        }
        
        if (!empty($groupRuleData)) {
            try {
                (new AdminGroupRule())->saveAll($groupRuleData);
            } catch (\Exception $e) {
                throw $e;
            }
        }
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 如果有密码，则更新密码
        if (!empty($data['password'])) {
            $salt = $this->generateSalt($data['password']);
            $data['salt'] = $salt;
            $data['password'] = $this->encryptPassword($data['password'], $salt);
        } else {
            unset($data['password']);
        }
        
        return $data;
    }
    
    /**
     * 编辑后的钩子方法
     * @param int $id 记录ID
     * @param array $data 表单数据
     */
    protected function afterEdit($id, $data)
    {
        if (!$this->groups) {
            return;
        }
        try {
            // 删除旧的关联
            AdminGroupRule::where('user_id', $id)->delete();
            
            // 保存管理员与用户组的关联
            $groupRuleData = [];
            foreach ($this->groups as $groupId) {
                $groupRuleData[] = [
                    'user_id' => $id,
                    'group_id' => $groupId
                ];
            }
            
            if (!empty($groupRuleData)) {
                (new AdminGroupRule())->saveAll($groupRuleData);
            }
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 删除前的钩子方法
     * @param int $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        return true;
    }
    
    /**
     * 删除后的钩子方法
     * @param int $id 记录ID
     */
    protected function afterDelete($id)
    {
        try {
            // 删除关联
            AdminGroupRule::where('user_id', $id)->delete();
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取详情后的钩子方法
     * @param \think\Model $info 记录信息
     * @return array|null
     */
    protected function afterInfo($info)
    {
        // 获取管理员所属的用户组
        $groups = AdminGroupRule::where('user_id', $info->id)->column('group_id');
        $info['groups'] = $groups;
        
        return $info;
    }

    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        $items = $list->items();
        if (empty($items)) {
            return ['record' => [], 'count' => 0];
        }

        // 获取所有用户ID
        $userIds = array_column($items, 'id');

        // 查询用户与角色组的关联关系
        $groupRules = AdminGroupRule::where('user_id', 'in', $userIds)
            ->field('user_id, group_id')
            ->select()
            ->toArray();

        // 获取所有角色组ID
        $groupIds = array_unique(array_column($groupRules, 'group_id'));

        // 查询角色组信息
        $groups = [];
        if (!empty($groupIds)) {
            $groups = Db::table('sys_admin_group')
                ->where('id', 'in', $groupIds)
                ->column('name', 'id');
        }

        // 按用户ID分组整理角色组信息
        $userGroups = [];
        foreach ($groupRules as $rule) {
            $userId = $rule['user_id'];
            $groupId = $rule['group_id'];
            if (isset($groups[$groupId])) {
                $userGroups[$userId][] = [
                    'id' => $groupId,
                    'name' => $groups[$groupId]
                ];
            }
        }

        // 将角色组信息添加到用户数据中
        foreach ($items as &$item) {
            $item['groups'] = $userGroups[$item['id']] ?? [];
        }

        return ['record' => $items, 'count' => $list->total()];
    }
    
    /**
     * 获取所有管理员（用于下拉选择）
     */
    public function all()
    {
        $list = $this->model->where('enabled', 1)->field('id, username, nickname')->select();
        $this->ok('获取成功', $list);
    }

}