<?php

namespace app\admin\controller\system;

use app\common\controller\BaseAdminController;
use app\model\system\AdminGroup as AdminGroupModel;
use think\App;
use think\facade\Db;

class AdminGroup extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|角色组名称' => 'require|unique:sys_admin_group',
        'rules|权限规则' => 'array',
    ];

    /**
     * 保存的权限规则
     * @var array
     */
    private $rules = [];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new AdminGroupModel();
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit' && $id) {
            return [
                'name|角色组名称' => 'require|unique:sys_admin_group,name,' . $id,
                'rules|权限规则' => 'array',
            ];
        }
        
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 保存权限规则信息
        $this->rules = $data['rules'] ?? [];
        unset($data['rules']);
        
        return $data;
    }
    
    /**
     * 添加后的钩子方法
     * @param int $id 新增记录的ID
     * @param array $data 表单数据
     */
    protected function afterAdd($id, $data)
    {
        // 保存角色组与权限规则的关联
        if (!empty($this->rules)) {
            $rulesStr = implode(',', $this->rules);
            $this->model->where('id', $id)->update(['roles' => $rulesStr]);
        }
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 保存权限规则信息
        $this->rules = $data['roles'] ?? [];
        unset($data['roles']);
        // 处理权限规则
        if (!empty($this->rules) && is_array($this->rules)) {
            $rulesStr = implode(',', $this->rules);
            $data['roles'] = $rulesStr;
        } else {
            $data['roles'] = $this->rules;
        }

        return $data;
    }
    
    /**
     * 删除前的钩子方法
     * @param int $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        // 检查是否有管理员使用该角色组
        $count = Db::table('sys_admin_group_rule')->where('group_id', $id)->count();
        if ($count > 0) {
            $this->error('该角色组下有管理员，无法删除');
        }
        
        return true;
    }

    /**
     * 删除前的钩子方法
     * @param array $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeBatchDelete($id)
    {
        // 检查是否有管理员使用该角色组
        $count = Db::table('sys_admin_group_rule')->whereIn('group_id', $id)->count();
        if ($count > 0) {
            $this->error('该角色组下有管理员，无法删除');
        }

        return true;
    }

    /**
     * 获取详情后的钩子方法
     * @param \think\Model $info 记录信息
     * @return array|null
     */
    protected function afterInfo($info)
    {
        // 获取角色组的权限规则
        if (!empty($info['roles'])) {
            $info['rules'] = explode(',', $info['roles']);
        } else {
            $info['rules'] = [];
        }
        
        return $info;
    }

    /**
     * 修改角色组状态
     */
    public function status()
    {
        $id = $this->request->post('id');
        $status = $this->request->post('status');
        
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        $res = $this->model->where('id', $id)->update(['status' => $status]);
        if ($res) {
            $this->ok('修改成功');
        } else {
            $this->error('修改失败');
        }
    }

    /**
     * 获取所有角色组（用于下拉选择）
     */
    public function all()
    {
        $list = $this->model->where('status', 1)->field('id, name')->select();
        $this->ok('获取成功', $list);
    }
}