<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use think\App;

class Waybill extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'waybill';
        $this->configKey = 'waybill';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
        if (!isset($config['default_platform'])) {
            throw new \Exception('默认电子面单平台不能为空');
        }
        if (!isset($config['platforms']) || !is_array($config['platforms'])) {
            throw new \Exception('电子面单平台配置不能为空');
        }

        // 验证平台配置
        foreach ($config['platforms'] as $platform => $settings) {
            switch ($platform) {
                case 'kdniao':
                    if (!isset($settings['app_id'])) {
                        throw new \Exception('快递鸟APP ID不能为空');
                    }
                    if (!isset($settings['app_key'])) {
                        throw new \Exception('快递鸟APP Key不能为空');
                    }
                    break;
                case 'kd100':
                    if (!isset($settings['customer'])) {
                        throw new \Exception('快递100 Customer不能为空');
                    }
                    if (!isset($settings['key'])) {
                        throw new \Exception('快递100 Key不能为空');
                    }
                    break;
                case 'cainiao':
                    if (!isset($settings['app_key'])) {
                        throw new \Exception('菜鸟APP Key不能为空');
                    }
                    if (!isset($settings['secret_key'])) {
                        throw new \Exception('菜鸟Secret Key不能为空');
                    }
                    break;
                default:
                    throw new \Exception('不支持的电子面单平台：' . $platform);
            }
        }
    }

    /**
     * 处理配置数据
     * @param array $config 当前配置
     * @param array $data 新配置数据
     * @return array 处理后的配置数据
     */
    protected function processConfig(array $config, array $data): array
    {
        // 合并平台配置
        if (isset($data['platforms']) && is_array($data['platforms'])) {
            foreach ($data['platforms'] as $platform => $settings) {
                if (isset($config['platforms'][$platform]) && is_array($config['platforms'][$platform])) {
                    $data['platforms'][$platform] = array_merge($config['platforms'][$platform], $settings);
                }
            }
        }

        // 合并快递公司编码映射
        if (isset($data['express_codes']) && is_array($data['express_codes'])) {
            foreach ($data['express_codes'] as $platform => $codes) {
                if (isset($config['express_codes'][$platform]) && is_array($config['express_codes'][$platform])) {
                    $data['express_codes'][$platform] = array_merge($config['express_codes'][$platform], $codes);
                }
            }
        }

        return array_merge($config, $data);
    }
}