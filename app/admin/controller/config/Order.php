<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use think\App;

class Order extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'order';
        $this->configKey = 'order';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
//        if (!isset($config['expire_time'])) {
//            throw new \Exception('订单过期时间不能为空');
//        }
//        if (!isset($config['auto_receive_days'])) {
//            throw new \Exception('自动收货天数不能为空');
//        }
//        if (!isset($config['auto_comment_days'])) {
//            throw new \Exception('自动评价天数不能为空');
//        }
    }
}