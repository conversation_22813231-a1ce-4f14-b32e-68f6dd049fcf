<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use think\App;

class Message extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'message';
        $this->configKey = 'message';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
    }

    /**
     * 转换配置数据结构
     * @param array $data 原始配置数据
     * @return array 转换后的配置数据
     */
    protected function transformConfig(array $data): array
    {
        // 处理provider到default_provider的转换
        if (isset($data['email']['providers']['smtp']['provider'])) {
            $data['email']['default_provider'] = $data['email']['providers']['smtp']['provider'];
            unset($data['email']['providers']['smtp']['provider']);
        }
        return $data;
    }

    /**
     * 处理配置数据
     * @param array $config 当前配置
     * @param array $data 新配置数据
     * @return array 处理后的配置数据
     */
    protected function processConfig(array $config, array $data): array
    {
        // 如果存在provider相关配置，将其放入providers数组中
        if (isset($data['email']['providers']['smtp']['provider'])) {
            $providerName = $data['email']['providers']['smtp']['provider'];
            $providerConfig = [];
            
            // 提取provider相关配置
            $fields = ['host', 'port', 'username', 'password', 'from_name', 'from', 'secure'];
            foreach ($fields as $field) {
                if (isset( $data['email']['providers']['smtp'][$field])) {
                    $providerConfig[$field] =  $data['email']['providers']['smtp'][$field];
                    unset( $data['email']['providers']['smtp'][$field]);
                }
            }
        }
        
        return array_merge($config, $data);
    }
}