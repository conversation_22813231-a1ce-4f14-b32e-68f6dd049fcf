<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use think\App;

class Notification extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'notification';
        $this->configKey = 'notification';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
        if (!isset($config['default'])) {
            throw new \Exception('默认通知驱动不能为空');
        }
        if (!isset($config['channels']) || !is_array($config['channels'])) {
            throw new \Exception('通知渠道配置不能为空');
        }
    }
}