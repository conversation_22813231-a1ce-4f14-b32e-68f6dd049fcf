<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use think\App;

class Captcha extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'captcha';
        $this->configKey = 'captcha';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
        if (!isset($config['length'])) {
            throw new \Exception('验证码长度不能为空');
        }
        if (!isset($config['expire'])) {
            throw new \Exception('验证码有效期不能为空');
        }
    }
}