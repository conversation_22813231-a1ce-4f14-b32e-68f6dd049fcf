<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use app\common\controller\BaseController;
use app\common\ApiCode;
use think\App;
use think\facade\Config;

abstract class BaseConfigController extends BaseController
{
    /**
     * 配置文件路径
     * @var string
     */
    protected $configPath;

    /**
     * 配置文件名
     * @var string
     */
    protected $configFile;

    /**
     * 配置项键名
     * @var string
     */
    protected $configKey;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->initConfig();
    }

    /**
     * 初始化配置
     * @return void
     */
    abstract protected function initConfig(): void;

    /**
     * 获取配置列表
     * @return \think\Response
     */
    public function index()
    {
        try {
            $config = $this->getConfig();
        } catch (\Exception $e) {
            $this->fail($e->getMessage());
        }
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), ['config' => $config]);
    }

    /**
     * 更新配置
     * @return \think\Response
     */
    public function update()
    {
        $data = $this->request->put();
        try {
            $result = $this->setConfig($data);
            if ($result) {
            } else {
                $this->fail(ApiCode::getMessage(ApiCode::OPERATION_FAILED));
            }
        } catch (\Exception $e) {
            $this->fail($e->getMessage());
        }
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS));

    }

    /**
     * 获取配置
     * @return array
     */
    protected function getConfig(): array
    {
        return Config::get($this->configKey, []);
    }

    /**
     * 设置配置
     * @param array $data
     * @return bool
     */
    /**
     * 转换配置数据结构
     * @param array $data 原始配置数据
     * @return array 转换后的配置数据
     */
    protected function transformConfig(array $data): array
    {
        return $data;
    }

    /**
     * 处理配置数据
     * @param array $config 当前配置
     * @param array $data 新配置数据
     * @return array 处理后的配置数据
     */
    protected function processConfig(array $config, array $data): array
    {
        return array_merge($config, $data);
    }

    /**
     * 设置配置
     * @param array $data
     * @return bool
     */
    protected function setConfig(array $data): bool
    {
        try {
            // 获取当前配置
            $config = $this->getConfig();

            // 验证新配置
            $this->validateConfig($data);
            
            // 转换配置数据结构
            $transformedData = $this->transformConfig($data);

            // 处理配置数据
            $newConfig = $this->processConfig($config, $transformedData);
            
            // 备份当前配置
            $this->backupConfig();
            
            // 写入新配置
            $this->writeConfig($newConfig);
            
            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     */
    protected function validateConfig(array $config): void
    {
        // 子类实现具体的验证逻辑
    }

    /**
     * 备份配置
     * @return void
     */
    protected function backupConfig(): void
    {
        $configFile = $this->configPath . $this->configFile . '.php';
        $backupDir = runtime_path() . 'config/';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        $backupFile = $backupDir . $this->configFile . '.backup.' . date('YmdHis') . '.php';
        if (file_exists($configFile)) {
            copy($configFile, $backupFile);
        }
    }

    /**
     * 写入配置
     * @param array $config
     * @return void
     */
    protected function writeConfig(array $config): void
    {
        $configFile = $this->configPath . $this->configFile . '.php';
        $configContent = "<?php\n\nreturn " . var_export($config, true) . ";\n";
        file_put_contents($configFile, $configContent);
    }
}