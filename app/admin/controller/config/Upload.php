<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use think\App;

class Upload extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'upload';
        $this->configKey = 'upload';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
        if (!isset($config['default_type'])) {
            throw new \Exception('上传驱动不能为空');
        }
    }
}