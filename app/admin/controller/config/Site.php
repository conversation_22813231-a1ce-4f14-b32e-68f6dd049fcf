<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use think\App;

class Site extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = root_path() . 'config/';
        $this->configFile = 'site';
        $this->configKey = 'site';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
        if (!isset($config['siteName'])) {
            throw new \Exception('站点名称不能为空');
        }
        if (!isset($config['adminPath'])) {
            throw new \Exception('自定义后台入口不能为空');
        }
        if (!isset($config['recordNumber'])) {
            throw new \Exception('备案号不能为空');
        }
        if (!isset($config['version'])) {
            throw new \Exception('版本号不能为空');
        }
        if (!isset($config['timezone'])) {
            throw new \Exception('时区不能为空');
        }
        if (!isset($config['bannedIPs'])) {
            throw new \Exception('禁止访问IP不能为空');
        }
    }
}