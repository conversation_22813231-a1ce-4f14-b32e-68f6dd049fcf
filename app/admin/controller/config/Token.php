<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use think\App;

class Token extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'token';
        $this->configKey = 'token';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
        if (!isset($config['key'])) {
            throw new \Exception('令牌密钥不能为空');
        }
        if (!isset($config['expire'])) {
            throw new \Exception('令牌有效期不能为空');
        }
        if (!isset($config['refresh_expire'])) {
            throw new \Exception('刷新令牌有效期不能为空');
        }
    }
}