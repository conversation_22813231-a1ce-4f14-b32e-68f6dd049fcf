<?php

namespace app\admin\controller\config;

use app\common\controller\BaseController;
use think\App;
use think\facade\Config as ThinkConfig;
use think\facade\Request;

/**
 * 通用配置管理控制器
 * 用于管理app/config目录下的自定义配置文件
 */
class Config extends BaseController
{
    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 显示指定配置
     * @param string $name 配置名称
     */
    public function index($name = '')
    {
        if (empty($name)) {
            $this->error('配置名称不能为空');
        }
        
        $config = ThinkConfig::get($name);
        if (empty($config)) {
            $this->error('配置不存在');
        }
        
        $this->success('获取成功', $config);
    }

    /**
     * 编辑指定配置
     * @param string $name 配置名称
     */
    public function edit($name = '')
    {
        if (empty($name)) {
            $this->error('配置名称不能为空');
        }
        
        $config = ThinkConfig::get($name);
        if (empty($config)) {
            $this->error('配置不存在');
        }
        
        $this->success('获取成功', $config);
    }

    /**
     * 更新指定配置
     * @param string $name 配置名称
     */
    public function update($name = '')
    {
        if (empty($name)) {
            $this->error('配置名称不能为空');
        }
        
        $params = Request::except(['name']);
        
        // 验证参数
        if (empty($params)) {
            $this->error('参数不能为空');
        }
        
        try {
            // 获取原配置
            $config = ThinkConfig::get($name);
            if (empty($config)) {
                $this->error('配置不存在');
            }
            
            // 更新配置
            $newConfig = array_merge($config, $params);
            
            // 保存配置到文件
            $configFile = config_path() . $name . '.php';
            if (!file_exists($configFile)) {
                $this->error('配置文件不存在');
            }
            
            // 读取原文件获取注释信息
            $fileContent = file_get_contents($configFile);
            $headerComment = '';
            if (preg_match('/\/\/\s*\+{6,}[\s\S]*?\+{6,}/', $fileContent, $matches)) {
                $headerComment = $matches[0];
            } else {
                $headerComment = "// +----------------------------------------------------------------------\n// | 配置文件\n// +----------------------------------------------------------------------";
            }
            
            $content = "<?php\n\n{$headerComment}\n\nreturn " . var_export($newConfig, true) . ";\n";
            
            file_put_contents($configFile, $content);
            
            // 清除配置缓存
            ThinkConfig::set($newConfig, $name);
            
            $this->success('更新成功');
        } catch (\Exception $e) {
            $this->error('更新失败：' . $e->getMessage());
        }
    }
}