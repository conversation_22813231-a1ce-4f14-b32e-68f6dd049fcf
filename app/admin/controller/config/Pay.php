<?php
declare (strict_types = 1);

namespace app\admin\controller\config;

use think\App;

class Pay extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'pay';
        $this->configKey = 'pay';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
        if (!isset($config['default'])) {
            throw new \Exception('默认支付驱动不能为空');
        }
        if (!isset($config['drivers']) || !is_array($config['drivers'])) {
            throw new \Exception('支付驱动配置不能为空');
        }
    }
}