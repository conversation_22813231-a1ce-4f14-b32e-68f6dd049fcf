<?php

namespace app\admin\controller;

use app\common\ApiCode;
use app\common\controller\BaseController;
use app\common\login\LoginService;
use think\facade\Request;
use think\facade\Db;

class Login extends BaseController
{
    public function login()
    {
        $username = Request::post('username');
        $password = Request::post('password', '', null);
        $param = [];
        $result = LoginService::login($username, $password, $param, 'password');

        if ($result === false) {
            // 登录失败，获取错误信息
            $error = LoginService::getError();
            $code = LoginService::getCode();
            $this->ok($error, [], $code);
        }

        unset($result['user']['password']);
        unset($result['user']['salt']);

        $result['role'] = [
            'id' => '',
            'name' => '',
            'roles' => ''
        ];

        // 获取用户角色组信息
        $userGroup = Db::table('sys_admin_group')
            ->alias('g')
            ->join('sys_admin_group_rule r', 'r.group_id = g.id')
            ->where('r.user_id', $result['user']['id'])
            ->field('g.id, g.name, g.roles')
            ->find();

        if ($userGroup) {
            $result['role'] = [
                'id' => $userGroup['id'],
                'name' => $userGroup['name'],
                'roles' => $userGroup['roles']
            ];
        }

        // 登录成功，返回结果（包含用户信息、角色和token）
        $this->ok(ApiCode::getMessage(ApiCode::SUCCESS), $result, ApiCode::SUCCESS);

    }
}
