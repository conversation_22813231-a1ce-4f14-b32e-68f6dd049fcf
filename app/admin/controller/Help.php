<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\controller\BaseAdminController;
use think\App;

class Help extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'title|帮助标题' => 'require|max:100',
        'content|帮助内容' => 'require',
        'category|帮助分类' => 'require|max:50',
        'sort|排序' => 'require|number',
        'enabled|状态' => 'require|in:0,1'
    ];

    protected $allowFields = [
        'title',
        'content',
        'category',
        'sort',
        'enabled'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new \app\model\Help();
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array
     */
    protected function beforeAdd($data)
    {
        return $data;
    }

    /**
     * 更新前的钩子方法
     * @param array $data
     * @param $id
     * @return array
     */
    protected function beforeEdit($data, $id)
    {
        return $data;
    }
    
    /**
     * 查询后的钩子方法
     * @param \think\Paginator $data 分页对象
     * @return array
     */
    protected function afterIndex($data)
    {

        if ($data instanceof \think\paginator\driver\Bootstrap) {

            $items = $data->items();

            if (empty($items)) {
                return ['record' => [], 'count' => 0];
            }

            $categoryIds = array_column($items, 'category');
            $categories = \app\model\Category::where('id', 'in', $categoryIds)
                ->column('name', 'id');

            foreach ($items as &$item) {
                $item['category_name'] = $categories[$item['category']] ?? '';
            }

        } else {
            return ['record' => [], 'count' => 0];
        }

        return ['record' => $items, 'count' => $data->total()];
    }
}