<?php
declare (strict_types = 1);

namespace app\admin\controller\order;

use app\common\controller\BaseAdminController;
use app\model\order\OrderRefund as OrderRefundModel;
use app\model\order\Order as OrderModel;
use app\model\order\OrderLog;
use think\facade\App;
use think\facade\Db;

/**
 * 订单退款维权管理API控制器
 */
class OrderRefund extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'order_id|订单ID' => 'require|number',
        'order_no|订单号' => 'require|max:32',
        'user_id|用户ID' => 'require|number',
        'refund_amount|退款金额' => 'require|float|gt:0',
        'refund_type|退款类型' => 'require|number|in:1,2,3', // 1=仅退款 2=退货退款 3=换货
        'refund_reason|退款原因' => 'require|max:255',
        'refund_desc|退款说明' => 'max:1000',
        'evidence_images|凭证图片' => 'array',
        'status|退款状态' => 'require|number|in:0,1,2,3,4,5', // 0=待处理 1=已同意 2=已拒绝 3=退款中 4=已完成 5=已取消
        'admin_note|处理备注' => 'max:255'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new OrderRefundModel();
    }

    /**
     * 退款申请列表
     */
    public function index()
    {
        $params = $this->request->get();
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 10;

        $query = $this->model->withSearch(['status', 'order_no', 'user_id', 'refund_type', 'date_range'], $params);

        $total = $query->count();
        $list = $query->with(['order', 'user'])
            ->page($page, $pageSize)
            ->order('create_time', 'desc')
            ->select();

        return $this->success('获取成功', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ]);
    }

    /**
     * 退款申请详情
     */
    public function detail($id = null)
    {
        if (empty($id)) {
            return $this->error('退款申请ID不能为空');
        }

        $refund = $this->model->with(['order', 'user', 'logs'])
            ->where('id', $id)
            ->find();

        if (empty($refund)) {
            return $this->error('退款申请不存在');
        }

        return $this->success('获取成功', $refund);
    }

    /**
     * 审核退款申请
     */
    public function audit()
    {
        $data = $this->request->post();
        
        // 验证数据
        $validate = $this->validate($data, [
            'id|退款申请ID' => 'require|number',
            'status|处理状态' => 'require|number|in:1,2', // 1=同意 2=拒绝
            'admin_note|处理备注' => 'max:255'
        ]);

        if (true !== $validate) {
            return $this->error($validate);
        }

        Db::startTrans();
        try {
            $refund = $this->model->find($data['id']);
            if (empty($refund)) {
                throw new \Exception('退款申请不存在');
            }

            // 检查状态是否可以审核
            if ($refund->status !== 0) {
                throw new \Exception('该退款申请已处理');
            }

            // 更新退款申请状态
            $refund->status = $data['status'];
            $refund->admin_note = $data['admin_note'] ?? '';
            $refund->audit_time = time();
            $refund->admin_id = $this->adminId;
            $refund->save();

            // 如果同意退款，更新订单状态
            if ($data['status'] === 1) {
                $order = OrderModel::find($refund->order_id);
                if ($order) {
                    $order->status = 5; // 更新为已退款状态
                    $order->save();

                    // 记录订单日志
                    OrderLog::create([
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'user_id' => $this->adminId,
                        'action' => 'refund_approved',
                        'status' => 5,
                        'note' => $data['admin_note'] ?? '同意退款',
                        'create_time' => time()
                    ]);
                }
            }

            // 记录退款处理日志
            $refund->logs()->save([
                'refund_id' => $refund->id,
                'order_id' => $refund->order_id,
                'user_id' => $this->adminId,
                'action' => $data['status'] === 1 ? 'approve' : 'reject',
                'status' => $data['status'],
                'note' => $data['admin_note'] ?? '',
                'create_time' => time()
            ]);

            Db::commit();
            return $this->success('处理成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('处理失败: ' . $e->getMessage());
        }
    }

    /**
     * 确认退款完成
     */
    public function complete($id = null)
    {
        if (empty($id)) {
            return $this->error('退款申请ID不能为空');
        }

        Db::startTrans();
        try {
            $refund = $this->model->find($id);
            if (empty($refund)) {
                throw new \Exception('退款申请不存在');
            }

            // 检查状态是否可以完成
            if ($refund->status !== 3) {
                throw new \Exception('当前状态不能完成退款');
            }

            // 更新退款申请状态
            $refund->status = 4; // 更新为已完成状态
            $refund->complete_time = time();
            $refund->save();

            // 记录退款处理日志
            $refund->logs()->save([
                'refund_id' => $refund->id,
                'order_id' => $refund->order_id,
                'user_id' => $this->adminId,
                'action' => 'complete',
                'status' => 4,
                'note' => '退款已完成',
                'create_time' => time()
            ]);

            Db::commit();
            return $this->success('操作成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取退款统计数据
     */
    public function statistics()
    {
        $today = strtotime('today');
        $month = strtotime('first day of this month');

        // 今日退款统计
        $todayStats = $this->model->where('status', 4)
            ->where('complete_time', '>=', $today)
            ->field([
                'count(*) as count',
                'sum(refund_amount) as amount'
            ])
            ->find();

        // 本月退款统计
        $monthStats = $this->model->where('status', 4)
            ->where('complete_time', '>=', $month)
            ->field([
                'count(*) as count',
                'sum(refund_amount) as amount'
            ])
            ->find();

        // 待处理退款
        $pendingCount = $this->model->where('status', 0)->count();

        // 退款中数量
        $processingCount = $this->model->where('status', 3)->count();

        return $this->success('获取成功', [
            'today' => [
                'count' => intval($todayStats->count),
                'amount' => floatval($todayStats->amount)
            ],
            'month' => [
                'count' => intval($monthStats->count),
                'amount' => floatval($monthStats->amount)
            ],
            'pending_count' => $pendingCount,
            'processing_count' => $processingCount
        ]);
    }
}