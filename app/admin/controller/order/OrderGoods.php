<?php
declare (strict_types = 1);

namespace app\admin\controller\order;

use app\common\controller\BaseAdminController;
use app\model\order\Order as OrderGoodsModel;
use think\App;

/**
 * 订单商品管理API控制器
 */
class OrderGoods extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'order_id|订单ID' => 'require|number',
        'goods_id|商品ID' => 'require|number',
        'goods_name|商品名称' => 'require|max:255',
        'goods_image|商品图片' => 'max:255',
        'goods_spec|商品规格' => 'max:65535',
        'goods_price|商品单价' => 'require|float|gt:0',
        'quantity|购买数量' => 'require|number|gt:0',
        'total_amount|商品总金额' => 'require|float|gt:0',
        'shipping_company|物流公司' => 'max:50',
        'shipping_no|物流单号' => 'max:50',
        'shipping_time|发货时间' => 'number'
    ];
    
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new OrderGoodsModel();
    }
    
    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit') {
            return [
                'goods_name|商品名称' => 'max:255',
                'goods_image|商品图片' => 'max:255',
                'goods_spec|商品规格' => 'max:65535',
                'goods_price|商品单价' => 'float|gt:0',
                'quantity|购买数量' => 'number|gt:0',
                'total_amount|商品总金额' => 'float|gt:0',
                'shipping_company|物流公司' => 'max:50',
                'shipping_no|物流单号' => 'max:50'
            ];
        }
        
        return $this->validateRule;
    }
    
    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 设置创建和更新时间
        $data['createtime'] = $data['updatetime'] = time();
        
        // 计算商品总金额
        if (!isset($data['total_amount'])) {
            $data['total_amount'] = $data['goods_price'] * $data['quantity'];
        }
        
        return $data;
    }
    
    /**
     * 查询前的钩子方法
     * @param array $where 查询条件
     * @param array $sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 按订单ID搜索
        $orderId = $this->request->param('order_id', '');
        if (!empty($orderId)) {
            $where[] = ['order_id', '=', intval($orderId)];
        }
        
        // 按商品名称搜索
        $goodsName = $this->request->param('goods_name', '');
        if (!empty($goodsName)) {
            $where[] = ['goods_name', 'like', '%' . $goodsName . '%'];
        }
        
        // 按商品ID搜索
        $goodsId = $this->request->param('goods_id', '');
        if (!empty($goodsId)) {
            $where[] = ['goods_id', '=', intval($goodsId)];
        }
        
        // 按物流单号搜索
        $shippingNo = $this->request->param('shipping_no', '');
        if (!empty($shippingNo)) {
            $where[] = ['shipping_no', '=', $shippingNo];
        }
        
        // 设置默认排序
        if (empty($sort)) {
            $sort = ['id' => 'desc'];
        }
    }
    
    /**
     * 发货操作
     * @return \think\Response
     */
    public function ship()
    {
        // 获取请求数据
        $data = $this->request->post();
        
        // 验证数据
        $validate = $this->validate($data, [
            'order_id|订单ID' => 'require|number',
            'shipping_company|物流公司' => 'require|max:50',
            'shipping_no|物流单号' => 'require|max:50'
        ]);
        
        if (true !== $validate) {
            return $this->error($validate);
        }
        
        // 查询订单商品
        $orderGoods = $this->model->where('order_id', $data['order_id'])->find();
        if (!$orderGoods) {
            return $this->error('订单商品不存在');
        }
        
        // 更新发货信息
        $updateData = [
            'shipping_company' => $data['shipping_company'],
            'shipping_no' => $data['shipping_no'],
            'shipping_time' => time(),
            'updatetime' => time()
        ];
        
        if ($orderGoods->save($updateData)) {
            return $this->success('发货成功');
        } else {
            return $this->error('发货失败');
        }
    }
}