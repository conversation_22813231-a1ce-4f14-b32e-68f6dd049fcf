<?php
declare (strict_types = 1);

namespace app\admin\controller\order;

use app\common\controller\BaseAdminController;
use app\model\order\Order as OrderModel;
use app\model\order\OrderGoods;
use app\model\LogisticsCompany;
use think\facade\App;
use think\facade\Db;

/**
 * 订单发货配送管理API控制器
 */
class OrderShipping extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'order_id|订单ID' => 'require|number',
        'order_no|订单号' => 'require|max:32',
        'logistics_company_id|物流公司ID' => 'require|number',
        'shipping_no|物流单号' => 'require|max:50',
        'shipping_time|发货时间' => 'number',
        'shipping_status|发货状态' => 'number|in:0,1,2', // 0=待发货 1=已发货 2=已签收
        'receiver_name|收货人姓名' => 'require|max:50',
        'receiver_phone|收货人电话' => 'require|max:20',
        'receiver_province|省份' => 'require|max:50',
        'receiver_city|城市' => 'require|max:50',
        'receiver_district|区县' => 'require|max:50',
        'receiver_address|详细地址' => 'require|max:255',
        'note|备注' => 'max:255'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {   
        parent::__construct($app);
        $this->model = new OrderModel();
    }

    /**
     * 发货订单列表
     */
    public function index()
    {
        $params = $this->request->get();
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 10;

        $query = $this->model->withSearch(['shipping_status', 'order_no', 'shipping_no', 'receiver_name', 'receiver_phone', 'date_range'], $params);

        $total = $query->count();
        $list = $query->with(['goods', 'logistics'])
            ->page($page, $pageSize)
            ->order('create_time', 'desc')
            ->select();

        return $this->success('获取成功', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ]);
    }

    /**
     * 订单发货
     */
    public function ship()
    {
        $data = $this->request->post();
        
        // 验证数据
        $validate = $this->validate($data, [
            'order_id|订单ID' => 'require|number',
            'logistics_company_id|物流公司ID' => 'require|number',
            'shipping_no|物流单号' => 'require|max:50'
        ]);

        if (true !== $validate) {
            return $this->error($validate);
        }

        Db::startTrans();
        try {
            $order = $this->model->find($data['order_id']);
            if (empty($order)) {
                throw new \Exception('订单不存在');
            }

            // 检查订单状态是否可以发货
            if ($order->status !== 1) {
                throw new \Exception('当前订单状态不可发货');
            }

            // 检查物流公司是否存在
            $logisticsCompany = LogisticsCompany::where('id', $data['logistics_company_id'])
                ->where('status', 1)
                ->find();
            if (empty($logisticsCompany)) {
                throw new \Exception('物流公司不存在或已禁用');
            }

            // 更新订单状态和物流信息
            $order->status = 2; // 更新为待收货状态
            $order->shipping_status = 1; // 更新为已发货状态
            $order->logistics_company_id = $data['logistics_company_id'];
            $order->shipping_no = $data['shipping_no'];
            $order->shipping_time = time();
            $order->save();

            // 更新订单商品的物流信息
            OrderModel::where('order_id', $order->id)
                ->update([
                    'shipping_company' => $logisticsCompany->name,
                    'shipping_no' => $data['shipping_no'],
                    'ship_time' => time(),
                    'updatetime' => time()
                ]);

            // 记录订单日志
            $order->logs()->save([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $this->adminId,
                'action' => 'ship',
                'status' => 2,
                'note' => sprintf('订单已发货，物流公司：%s，物流单号：%s', $logisticsCompany->name, $data['shipping_no']),
                'create_time' => time()
            ]);

            Db::commit();
            return $this->success('发货成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('发货失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取物流公司列表
     */
    public function getLogisticsCompanies()
    {
        $companies = LogisticsCompany::getEnabledCompanies();
        return $this->success('获取成功', $companies);
    }

    /**
     * 更新物流信息
     */
    public function updateShipping()
    {
        $data = $this->request->post();
        
        // 验证数据
        $validate = $this->validate($data, [
            'order_id|订单ID' => 'require|number',
            'logistics_company_id|物流公司ID' => 'require|number',
            'shipping_no|物流单号' => 'require|max:50'
        ]);

        if (true !== $validate) {
            return $this->error($validate);
        }

        Db::startTrans();
        try {
            $order = $this->model->find($data['order_id']);
            if (empty($order)) {
                throw new \Exception('订单不存在');
            }

            // 检查订单状态
            if (!in_array($order->status, [1, 2])) {
                throw new \Exception('当前订单状态不可修改物流信息');
            }

            // 检查物流公司是否存在
            $logisticsCompany = LogisticsCompany::where('id', $data['logistics_company_id'])
                ->where('status', 1)
                ->find();
            if (empty($logisticsCompany)) {
                throw new \Exception('物流公司不存在或已禁用');
            }

            // 更新订单物流信息
            $order->logistics_company_id = $data['logistics_company_id'];
            $order->shipping_no = $data['shipping_no'];
            $order->save();

            // 更新订单商品的物流信息
            OrderModel::where('order_id', $order->id)
                ->update([
                    'shipping_company' => $logisticsCompany->name,
                    'shipping_no' => $data['shipping_no'],
                    'updatetime' => time()
                ]);

            // 记录订单日志
            $order->logs()->save([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $this->adminId,
                'action' => 'update_shipping',
                'status' => $order->status,
                'note' => sprintf('更新物流信息，物流公司：%s，物流单号：%s', $logisticsCompany->name, $data['shipping_no']),
                'create_time' => time()
            ]);

            Db::commit();
            return $this->success('更新成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 确认收货
     */
    public function confirm($id = null)
    {
        if (empty($id)) {
            return $this->error('订单ID不能为空');
        }

        Db::startTrans();
        try {
            $order = $this->model->find($id);
            if (empty($order)) {
                throw new \Exception('订单不存在');
            }

            // 检查订单状态
            if ($order->status !== 2) {
                throw new \Exception('当前订单状态不可确认收货');
            }

            // 更新订单状态
            $order->status = 3; // 更新为已完成状态
            $order->shipping_status = 2; // 更新为已签收状态
            $order->confirm_time = time();
            $order->save();

            // 记录订单日志
            $order->logs()->save([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $this->adminId,
                'action' => 'confirm',
                'status' => 3,
                'note' => '确认收货，订单完成',
                'create_time' => time()
            ]);

            Db::commit();
            return $this->success('确认收货成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('确认收货失败: ' . $e->getMessage());
        }
    }
}