<?php
declare (strict_types = 1);

namespace app\admin\controller\order;

use app\common\controller\BaseAdminController;
use app\model\order\OrderDetail as OrderDetailModel;
use think\App;

/**
 * 订单详情管理API控制器
 */
class OrderDetail extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'order_id|订单ID' => 'require|number',
        'order_no|订单号' => 'require|max:32',
        'user_id|用户ID' => 'require|number',
        'payment_method|支付方式' => 'number|in:1,2,3',
        'payment_no|支付交易号' => 'max:64',
        'coupon_id|优惠券ID' => 'number',
        'coupon_amount|优惠金额' => 'float|egt:0',
        'pay_amount|实际支付金额' => 'require|float|gt:0',
        'note|备注' => 'max:255'
    ];
    
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new OrderDetailModel();
    }
    
    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit') {
            return [
                'payment_method|支付方式' => 'number|in:1,2,3',
                'payment_no|支付交易号' => 'max:64',
                'coupon_id|优惠券ID' => 'number',
                'coupon_amount|优惠金额' => 'float|egt:0',
                'pay_amount|实际支付金额' => 'float|gt:0',
                'note|备注' => 'max:255'
            ];
        }
        
        return $this->validateRule;
    }
    
    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 设置创建和更新时间
        $data['createtime'] = $data['updatetime'] = time();
        
        // 设置实际支付金额
        if (!isset($data['pay_amount'])) {
            $data['pay_amount'] = $data['total_amount'];
            // 如果有优惠券，减去优惠金额
            if (isset($data['coupon_amount']) && $data['coupon_amount'] > 0) {
                $data['pay_amount'] -= $data['coupon_amount'];
            }
        }
        
        return $data;
    }
    
    /**
     * 查询前的钩子方法
     * @param array $where 查询条件
     * @param array $sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 按订单号搜索
        $orderNo = $this->request->param('order_no', '');
        if (!empty($orderNo)) {
            $where[] = ['order_no', '=', $orderNo];
        }
        
        // 按订单ID搜索
        $orderId = $this->request->param('order_id', '');
        if (!empty($orderId)) {
            $where[] = ['order_id', '=', intval($orderId)];
        }
        
        // 按用户ID搜索
        $userId = $this->request->param('user_id', '');
        if (!empty($userId)) {
            $where[] = ['user_id', '=', intval($userId)];
        }
        
        // 按支付方式搜索
        $paymentMethod = $this->request->param('payment_method', '');
        if (!empty($paymentMethod)) {
            $where[] = ['payment_method', '=', intval($paymentMethod)];
        }
        
        // 设置默认排序
        if (empty($sort)) {
            $sort = ['id' => 'desc'];
        }
    }
}