<?php
declare (strict_types = 1);

namespace app\admin\controller\order;

use app\common\controller\BaseAdminController;
use app\model\order\Order as OrderModel;
use app\model\order\OrderLog;
use think\App;
use think\facade\Db;

/**
 * 订单管理API控制器
 */
class Order extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'order_no|订单号' => 'require|max:32',
        'user_id|用户ID' => 'require|number',
        'total_amount|订单总金额' => 'require|float|gt:0',
        'payment_method|支付方式' => 'number|in:1,2,3',
        'payment_no|支付交易号' => 'max:64',
        'coupon_id|优惠券ID' => 'number',
        'coupon_amount|优惠金额' => 'float|egt:0',
        'pay_amount|实际支付金额' => 'require|float|gt:0',
        'status|订单状态' => 'require|number|in:0,1,2,3,4,5',
        'note|备注' => 'max:255'
    ];

    /**
     * 允许修改的字段
     * @var array
     */
    protected $allowFields = [
        'order_no',
        'user_id',
        'total_amount',
        'payment_method',
        'payment_no',
        'coupon_id',
        'coupon_amount',
        'pay_amount',
        'status',
        'note'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new OrderModel();
    }

    /**
     * 查询前的处理
     * @param array $where 查询条件
     * @param array $sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 设置默认排序
        if (empty($sort)) {
            $sort = ['create_time' => 'desc'];
        }

        // 处理搜索条件
        $params = $this->request->get();
        
        // 订单状态
        if (isset($params['status']) && $params['status'] !== '') {
            $where[] = ['status', '=', intval($params['status'])];
        }

        // 订单号
        if (!empty($params['order_no'])) {
            $where[] = ['order_no', 'like', '%' . $params['order_no'] . '%'];
        }

        // 用户ID
        if (!empty($params['user_id'])) {
            $where[] = ['user_id', '=', intval($params['user_id'])];
        }

        // 支付方式
        if (isset($params['payment_method']) && $params['payment_method'] !== '') {
            $where[] = ['payment_method', '=', intval($params['payment_method'])];
        }

        // 日期范围
        if (!empty($params['date_range'])) {
            $dateRange = explode(',', $params['date_range']);
            if (count($dateRange) == 2) {
                $where[] = ['create_time', 'between', [strtotime($dateRange[0]), strtotime($dateRange[1])]];
            }
        }
    }

    /**
     * 查询后的处理
     * @param \think\Paginator $list
     * @return array
     */
    protected function afterIndex($list)
    {
        $records = $list->items();
        
        // 关联商品信息
        foreach ($records as &$record) {
            $record->append(['goods']);
        }

        return ['record' => $records, 'count' => $list->total()];
    }

    /**
     * 更新订单状态
     */
    public function updateStatus()
    {
        if (!$this->request->isPost()) {
            $this->fail('请求方法不正确');
        }

        $data = $this->request->post();
        
        // 验证数据
        $validate = $this->validate($data, [
            'id|订单ID' => 'require|number',
            'status|订单状态' => 'require|number|in:0,1,2,3,4,5',
            'note|备注' => 'max:255'
        ]);

        if (true !== $validate) {
            $this->fail($validate);
        }

        Db::startTrans();
        try {
            $order = $this->model->find($data['id']);
            if (empty($order)) {
                throw new \Exception('订单不存在');
            }

            // 检查状态变更是否合法
            if (!$this->checkStatusChange($order->status, $data['status'])) {
                throw new \Exception('非法的状态变更');
            }

            // 更新订单状态
            $order->status = $data['status'];
            $order->save();

            // 记录操作日志
            OrderLog::create([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $this->adminId,
                'action' => 'update_status',
                'status' => $data['status'],
                'note' => $data['note'] ?? '',
                'create_time' => time()
            ]);

            Db::commit();
            $this->ok('操作成功');
        } catch (\Exception $e) {
            Db::rollback();
            $this->fail('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查订单状态变更是否合法
     * @param int $oldStatus 原状态
     * @param int $newStatus 新状态
     * @return bool
     */
    protected function checkStatusChange($oldStatus, $newStatus)
    {
        // 状态定义：0=待付款 1=待发货 2=待收货 3=已完成 4=已取消 5=已退款
        $allowedChanges = [
            0 => [1, 4], // 待付款可变更为待发货或已取消
            1 => [2, 4, 5], // 待发货可变更为待收货、已取消或已退款
            2 => [3, 4, 5], // 待收货可变更为已完成、已取消或已退款
            3 => [5], // 已完成可变更为已退款
            4 => [], // 已取消不可变更
            5 => [] // 已退款不可变更
        ];

        return in_array($newStatus, $allowedChanges[$oldStatus] ?? []);
    }

    /**
     * 删除前的处理
     * @param int $id 记录ID
     * @return bool
     */
    protected function beforeDelete($id)
    {
        $order = $this->model->find($id);
        if (!$order) {
            $this->fail('订单不存在');
        }

        // 只允许删除已取消或已退款的订单
        if (!in_array($order->status, [4, 5])) {
            $this->fail('只能删除已取消或已退款的订单');
        }

        return true;
    }

    /**
     * 删除后的处理
     * @param int $id 记录ID
     */
    protected function afterDelete($id)
    {
        // 删除订单商品和日志
        Db::name('order_goods')->where('order_id', $id)->delete();
        Db::name('order_log')->where('order_id', $id)->delete();
    }
}