<?php

namespace app\admin\controller\user;

use app\common\controller\BaseAdminController;
use app\common\IdGenerator;
use app\common\login\util\Password;
use app\model\system\User as UserModel;
use app\model\system\UserGroupRule;
use think\App;
use think\facade\Db;

class User extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'username|用户名' => 'require|unique:qi_users',
        'password|密码' => 'require|min:6',
        'group_id|角色组' => 'require|number',
    ];
    private $groups;

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new UserModel();
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit' && $id) {
            return [
                'username|用户名' => 'require|unique:qi_users,username,' . $id,
                'password|密码' => 'min:6',
                'group_id|角色组' => 'require|number',
            ];
        }
        
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 处理密码
        try {
            // 处理密码
            $salt = Password::generateSalt($data['password']);
            $data['salt'] = $salt;
            $data['password'] = Password::encryptPassword($data['password'], $salt);
            $data['user_no'] = IdGenerator::generateIdFromDb();
        } catch (\Exception $e){
        }
        
        return $data;
    }

    private function generateSalt($password)
    {
        $hashed = password_hash($password, PASSWORD_DEFAULT);
        return $hashed;
    }

    /**
     * 加密密码
     * @param string $password 原始密码
     * @param string $salt 盐值
     * @return string 加密后的密码
     */
    private function encryptPassword($password, $salt = '')
    {
        return password_hash($password . $salt, PASSWORD_DEFAULT);
    }

    /**
     * 添加后的钩子方法
     * @param int $id 新增记录的ID
     * @param array $data 表单数据
     */
    protected function afterAdd($id, $data)
    {
        // 保存用户与角色组的关联
        // 保存管理员与用户组的关联
        $groupRuleData = [];
        if (!$this->groups) {
            return;
        }
        foreach ($this->groups as $groupId) {
            $groupRuleData[] = [
                'user_id' => $id,
                'group_id' => $groupId
            ];
        }

        if (!empty($groupRuleData)) {
            try {
                (new UserGroupRule())->saveAll($groupRuleData);
            } catch (\Exception $e) {
                throw $e;
            }
        }
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 处理密码
        if (!empty($data['password'])) {
            $salt = $this->generateSalt($data['password']);
            $data['salt'] = $salt;
            $data['password'] = $this->encryptPassword($data['password'], $salt);
        } else {
            unset($data['password']);
        }
        
        return $data;
    }

    /**
     * 编辑后的钩子方法
     * @param int $id 记录ID
     * @param array $data 表单数据
     */
    protected function afterEdit($id, $data)
    {
        // 更新用户与角色组的关联
        if (!empty($data['group_id'])) {
            $groupRule = Db::table('qi_user_group_rule')->where('user_id', $id)->find();
            if ($groupRule) {
                Db::table('qi_user_group_rule')->where('user_id', $id)->update([
                    'group_id' => $data['group_id'],
                    'update_time' => time()
                ]);
            } else {
                Db::table('qi_user_group_rule')->insert([
                    'user_id' => $id,
                    'group_id' => $data['group_id'],
                ]);
            }
        }
    }

    /**
     * 删除前的钩子方法
     * @param int $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        // 删除用户与角色组的关联
        Db::table('qi_user_group_rule')->where('user_id', $id)->delete();
        return true;
    }

    /**
     * 获取详情后的钩子方法
     * @param \think\Model $info 记录信息
     * @return array|null
     */
    protected function afterInfo($info)
    {
        // 获取用户的角色组信息
        $groupRule = Db::name('qi_user_group_rule')->where('user_id', $info['id'])->find();
        if ($groupRule) {
            $info['group_id'] = $groupRule['group_id'];
        }
        
        // 移除密码字段
        unset($info['password']);
        
        return $info;
    }

    /**
     * 修改用户状态
     */
    public function status()
    {
        $id = $this->request->post('id');
        $status = $this->request->post('status');
        
        if (empty($id)) {
            $this->error('参数错误');
        }
        
        $res = $this->model->where('id', $id)->update(['status' => $status]);
        if ($res) {
            $this->ok('修改成功');
        } else {
            $this->error('修改失败');
        }
    }

    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        $items = $list->items();
        if (empty($items)) {
            return ['record' => [], 'count' => 0];
        }

        // 获取所有用户ID
        $userIds = array_column($items, 'id');

        // 查询用户与角色组的关联关系
        $groupRules = UserGroupRule::where('user_id', 'in', $userIds)
            ->field('user_id, group_id')
            ->select()
            ->toArray();

        // 获取所有角色组ID
        $groupIds = array_unique(array_column($groupRules, 'group_id'));

        // 查询角色组信息
        $groups = [];
        if (!empty($groupIds)) {
            $groups = Db::table('qi_user_group')
                ->where('id', 'in', $groupIds)
                ->column('name', 'id');
        }

        // 按用户ID分组整理角色组信息
        $userGroups = [];
        foreach ($groupRules as $rule) {
            $userId = $rule['user_id'];
            $groupId = $rule['group_id'];
            if (isset($groups[$groupId])) {
                $userGroups[$userId][] = [
                    'id' => $groupId,
                    'name' => $groups[$groupId]
                ];
            }
        }

        // 将角色组信息添加到用户数据中
        foreach ($items as &$item) {
            $item['groups'] = $userGroups[$item['id']] ?? [];
        }

        return ['record' => $items, 'count' => $list->total()];
    }
}