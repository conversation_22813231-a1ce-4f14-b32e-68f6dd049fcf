<?php

namespace app\admin\controller\user;

use app\admin\entity\UserEntity;
use app\common\controller\BaseAdminController;
use app\model\system\UserGroup as UserGroupModel;
use app\model\system\UserRule as UserRuleModel;
use think\App;
use think\facade\Db;

class UserAuth extends BaseAdminController
{
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 根据角色组ID获取权限
     * @param int $groupId 角色组ID
     * @return \think\Response
     */
    public function getAuthByGroup($groupId)
    {
        if (!$groupId) {
            $this->error('角色组ID不能为空');
        }

        // 获取角色组信息
        $group = UserGroupModel::where('id', $groupId)->where('enabled', 1)->find();
        if (!$group) {
            $this->error('角色组不存在或已禁用');
        }

        // 获取角色组的权限规则ID列表
        $ruleIds = $group->roles ?? [];
        if (empty($ruleIds)) {
            $this->ok('获取成功', []);
        }

        // 获取权限规则信息
        $rules = UserRuleModel::where('enabled', 1)
            ->whereIn('id', $ruleIds)
            ->field('id, name, role, pid, weigh, type, ismenu, enabled, description')
            ->select()
            ->toArray();

        // 构建树形结构
        $tree = $this->buildTree($rules);

        $this->ok('获取成功', $tree);
    }

    /**
     * 根据用户ID获取权限
     * @param int $userId 用户ID
     * @return \think\Response
     */
    public function getAuthByUserId($user_id)
    {
        if (!$user_id) {
            $this->error('用户ID不能为空');
        }

        // 获取用户所属的角色组ID列表
        $groupIds = Db::table('qi_user_group_rule')
            ->where('user_id', $user_id)
            ->column('group_id');

        if (empty($groupIds)) {
            $this->ok('获取成功', []);
        }

        // 获取所有角色组的权限规则ID列表
        $allRuleIds = [];
        foreach ($groupIds as $groupId) {
            $group = UserGroupModel::where('id', $groupId)->where('enabled', 1)->find();
            if ($group && !empty($group->rules)) {
                $allRuleIds = array_merge($allRuleIds, $group->rules);
            }
        }

        // 去重权限规则ID
        $allRuleIds = array_unique($allRuleIds);
        if (empty($allRuleIds)) {
            $this->ok('获取成功', []);
        }

        // 获取权限规则信息
        $rules = UserRuleModel::where('enabled', 1)
            ->whereIn('id', $allRuleIds)
            ->field('id, name, role, pid, weigh, type, ismenu, enabled, description')
            ->select()
            ->toArray();

        // 构建树形结构
        $tree = $this->buildTree($rules);

        $this->ok('获取成功', $tree);
    }

    /**
     * 根据用户ID获取权限
     * @param int $userId 用户ID
     * @return \think\Response
     */
    public function getGroupByUserId($user_id)
    {
        if (!$user_id) {
            $this->error('用户ID不能为空');
        }

        // 获取用户所属的角色组ID列表
        $groupIds = Db::table('qi_user_group_rule')
            ->where('user_id', $user_id)
            ->column('group_id');

        if (empty($groupIds)) {
            $this->ok('获取成功', []);
        }

        // 获取所有角色组的权限规则ID列表
        $group = UserGroupModel::whereIn('id', $groupIds)->where('enabled', 1)->select();

        // 构建树形结构
        $tree = $this->buildTree($group);

        $this->ok('获取成功', $tree);
    }

    /**
     * 根据用户ID获取权限
     * @param int $userId 用户ID
     * @return \think\Response
     */
    public function getAuth(UserEntity $userEntity)
    {
        if (!$userEntity->id) {
            $this->error('用户ID不能为空');
        }

        $userId = $userEntity->id;

        // 获取用户所属的角色组ID列表
        $groupIds = Db::table('qi_user_group_rule')
            ->where('user_id', $userId)
            ->column('group_id');

        if (empty($groupIds)) {
            $this->ok('获取成功', []);
        }

        // 获取所有角色组的权限规则ID列表
        $allRuleIds = [];
        foreach ($groupIds as $groupId) {
            $group = UserGroupModel::where('id', $groupId)->where('enabled', 1)->find();
            if ($group && !empty($group->rules)) {
                $allRuleIds = array_merge($allRuleIds, $group->rules);
            }
        }

        // 去重权限规则ID
        $allRuleIds = array_unique($allRuleIds);
        if (empty($allRuleIds)) {
            $this->ok('获取成功', []);
        }

        // 获取权限规则信息
        $rules = UserRuleModel::where('enabled', 1)
            ->whereIn('id', $allRuleIds)
            ->field('id, name, role, pid, weigh, type, ismenu, enabled, description')
            ->select()
            ->toArray();

        // 构建树形结构
        $tree = $this->buildTree($rules);

        $this->ok('获取成功', $tree);
    }

    /**
     * 更新用户组权限规则
     * @param int $groupId 用户组ID
     * @param mixed $ruleIds 权限规则ID，可以是字符串或数组
     * @return \think\Response
     */
    public function assignGroupRules()
    {
        $group_id = $this->request->param('group_id');
        $rule_ids = $this->request->param('rule_ids');
        if (!$group_id) {
            $this->error('用户组ID不能为空');
        }

        if (empty($rule_ids)) {
            $this->error('权限规则ID不能为空');
        }

        // 获取用户组信息
        $group = UserGroupModel::where('id', $group_id)->where('enabled', 1)->find();
        if (!$group) {
            $this->error('用户组不存在或已禁用');
        }

        // 处理权限规则ID格式
        if (is_string($rule_ids)) {
            $rule_ids = explode(',', $rule_ids);
        } elseif (is_array($rule_ids)) {
        }

        // 验证权限规则是否存在且启用
        $validRules = UserRuleModel::where('enabled', 1)
            ->whereIn('id', $rule_ids)
            ->column('id');

        if (empty($validRules)) {
            $this->error('未找到有效的权限规则');
        }

        Db::startTrans();
        try {
            // 更新用户组的权限规则
            UserGroupModel::where('id', $group_id)
                ->update(['roles' => implode(',', $validRules)]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('更新权限规则失败：' . $e->getMessage());
        }

        $this->ok('更新权限规则成功');
    }

    /**
     * 给用户分配角色
     * @param int $userId 用户ID
     * @param mixed $groupIds 角色组ID，可以是字符串或数组
     * @return \think\Response
     */
    public function assignRole()
    {
        $user_id = $this->request->param('user_id');

        if (!$user_id) {
            $this->error('用户ID不能为空');
        }

        $group_ids = $this->request->param('group_ids');

        if (empty($group_ids)) {
            $this->error('角色组ID不能为空');
        }

        // 如果groupIds是字符串，转换为数组
        if (is_string($group_ids)) {
            $group_ids = explode(',', $group_ids);
        } elseif (is_array($group_ids)) {
        }

        // 验证角色组是否存在且启用
        $validGroups = UserGroupModel::where('enabled', 1)
            ->whereIn('id', $group_ids)
            ->column('id');

        if (empty($validGroups)) {
            $this->error('未找到有效的角色组');
        }

        Db::startTrans();
        try {
            // 先删除用户原有的角色关联
            Db::table('qi_user_group_rule')
                ->where('user_id', $user_id)
                ->delete();

            // 批量插入新的角色关联
            $insertData = [];
            foreach ($validGroups as $groupId) {
                $insertData[] = [
                    'user_id' => $user_id,
                    'group_id' => $groupId
                ];
            }
            Db::table('qi_user_group_rule')->insertAll($insertData);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('角色分配失败：' . $e->getMessage());
        }

        $this->ok('角色分配成功');
    }
}