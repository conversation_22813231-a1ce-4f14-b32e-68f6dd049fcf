<?php

namespace app\admin\controller\user;

use app\common\controller\BaseAdminController;
use app\model\system\UserRule as UserRuleModel;
use think\App;
use think\facade\Db;

class UserRule extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|规则名称' => 'require',
        'role|权限标识' => 'require|unique:qi_user_rule',
        'weigh|权重' => 'require|number',
    ];
    
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new UserRuleModel();
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit' && $id) {
            return [
                'name|规则名称' => 'require',
                'role|权限标识' => 'require|unique:qi_user_rule,role,' . $id,
                'weigh|权重' => 'require|number',
            ];
        }
        
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        return [
            'pid' => $data['pid'],
            'name' => $data['name'],
            'role' => $data['role'],
            'ismenu' => $data['type'] == 'menu' ? 1 : 0,
            'type' => $data['type'],
            'weigh' => $data['weigh']
        ];
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        return [
            'pid' => $data['pid'],
            'name' => $data['name'],
            'role' => $data['role'],
            'ismenu' => $data['type'] == 'menu' ? 1 : 0,
            'type' => $data['type'],
            'weigh' => $data['weigh']
        ];
    }

    /**
     * 删除前的钩子方法
     * @param int $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        // 检查是否有角色组使用该权限规则
        $groups = Db::table('qi_user_group')->where('rules', 'like', '%' . $id . '%')->count();
        if ($groups > 0) {
            $this->fail('该权限规则已被角色组使用，无法删除');
        }
        
        return true;
    }

    /**
     * 获取所有权限规则（用于下拉选择）
     */
    public function all()
    {
        $list = $this->model->where('enabled', 1)->field('id, name, role, pid')->select();
        $this->ok('获取成功', $list);
    }

    /**
     * 获取权限规则树形结构
     */
    public function tree()
    {
        list($page, $pageSize, $sort, $where) = $this->buildParames();
        $list = $this->model->where('enabled', 1)
            ->field('id, name, role, pid, weigh, type, ismenu, enabled, description')
            ->where($where)
            ->order($sort)
            ->select()->toArray();
        
        // 构建树形结构
        $tree = $this->buildTree($list);
        
        $this->ok('获取成功', $tree);
    }

    /**
     * 构建树形结构
     */
    protected function buildTree($list, $pid = 0)
    {
        $tree = [];
        foreach ($list as $item) {
            if ($item['pid'] == $pid) {
                $children = $this->buildTree($list, $item['id']);
                if (!empty($children)) {
                    $item['children'] = $children;
                }
                $tree[] = $item;
            }
        }
        return $tree;
    }
}