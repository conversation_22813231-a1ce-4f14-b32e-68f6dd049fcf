<?php

namespace app\admin\controller\payment;

use app\model\payment\PaymentRecord as PaymentRecordModel;
use think\App;
use app\common\controller\BaseAdminController;

class PaymentRecord extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'record_no|记录编号' => 'require|unique:payment_record',
        'type|类型' => 'require|in:1,2',
        'amount|金额' => 'require|float|gt:0',
        'category|分类' => 'require|in:1,2,3,4,5,6,7,8',
        'pay_method|支付方式' => 'require|in:1,2,3,4',
        'operator_name|经办人姓名' => 'require|max:50',
        'remark|备注' => 'max:255'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new PaymentRecordModel();
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array
     */
    protected function beforeAdd($data)
    {
        // 设置创建时间
        $data['createtime'] = date('Y-m-d H:i:s');
        // 设置初始状态为待确认
        $data['enabled'] = 2;
        
        return $data;
    }

    /**
     * 更新前的钩子方法
     * @param array $data 表单数据
     * @return array
     */
    protected function beforeUpdate($data)
    {
        // 如果状态变更为已确认，设置确认时间
        if (isset($data['enabled']) && $data['status'] == 1) {
            $data['confirm_time'] = date('Y-m-d H:i:s');
        }
        
        return $data;
    }

    /**
     * 确认支付记录
     * @param int $id 记录ID
     * @return \think\Response
     */
    public function confirm($id)
    {
        $record = $this->model->find($id);
        if (!$record) {
            $this->fail('记录不存在');
        }

        if ($record->status != 2) {
            $this->fail('只有待确认的记录可以确认');
        }

        $record->status = 1;
        $record->confirm_time = date('Y-m-d H:i:s');
        
        if ($record->save()) {
            $this->ok('确认成功');
        }
        
        $this->fail('确认失败');
    }

    /**
     * 取消支付记录
     * @param int $id 记录ID
     * @return \think\Response
     */
    public function cancel($id)
    {
        $record = $this->model->find($id);
        if (!$record) {
            $this->fail('记录不存在');
        }

        if ($record->status == 1) {
            $this->fail('已确认的记录不能取消');
        }

        $record->status = 3;
        
        if ($record->save()) {
            $this->ok('取消成功');
        }
        
        $this->fail('取消失败');
    }
}