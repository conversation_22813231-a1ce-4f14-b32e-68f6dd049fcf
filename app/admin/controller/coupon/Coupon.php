<?php
declare (strict_types = 1);

namespace app\admin\controller\coupon;

use app\common\controller\BaseAdminController;
use think\facade\Db;

/**
 * 优惠券管理
 */
class Coupon extends BaseAdminController
{
    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->table = 'coupon';
        $this->model = new \app\common\model\Coupon();
        
        // 定义验证规则
        $this->validateRule = [
            'name' => 'require|max:100',
            'type' => 'require|in:1,2',
            'use_scope' => 'require|in:1,2',
            'value' => 'require|float|min:0',
            'min_order_amount' => 'float|min:0',
            'stock' => 'require|integer|min:0',
            'per_limit' => 'require|integer|min:1',
            'description' => 'max:255',
            'code' => 'max:50',
            'start_time' => 'require|date',
            'end_time' => 'require|date|gt:start_time',
        ];
        
        // 允许修改的字段
        $this->allowFields = [
            'name', 'type', 'value', 'min_order_amount', 'stock',
            'per_limit', 'description', 'start_time', 'end_time', 'code', 'use_scope'
        ];
    }
    
    /**
     * 获取优惠券列表
     */
    /**
     * 列表查询前的处理
     * @param array $where 查询条件
     * @param array $sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort): void
    {
        // 按类型筛选
        if ($type = $this->request->param('type')) {
            $where[] = ['type', '=', intval($type)];
        }
        
        // 按状态筛选
        if ($status = $this->request->param('status')) {
            $where[] = ['status', '=', intval($status)];
        }
        
        // 按时间范围筛选
        $startTime = $this->request->param('start_time');
        $endTime = $this->request->param('end_time');
        if ($startTime && $endTime) {
            $where[] = ['start_time', '>=', strtotime($startTime)];
            $where[] = ['end_time', '<=', strtotime($endTime)];
        }
        
        // 设置默认排序
        if (empty($sort)) {
            $sort = ['id' => 'desc'];
        }
    }
    
    /**
     * 添加优惠券前的数据验证
     */
    protected function beforeAdd($data)
    {
        // 转换时间戳
        $data['start_time'] = strtotime($data['start_time']);
        $data['end_time'] = strtotime($data['end_time']);
        
        // 生成优惠券码
        $data['code'] = $data['code'] ? $data['code'] : strtoupper(substr(md5(uniqid((string)mt_rand(), true)), 0, 12));

        // 验证时间范围
        if ($data['start_time'] >= $data['end_time']) {
            $this->fail('结束时间必须大于开始时间');
        }

        return $data;
    }
    
    /**
     * 修改优惠券前的数据验证
     * @param array $data
     * @param $id
     */
    protected function beforeEdit($data, $id)
    {
        // 转换时间戳
        if (isset($data['start_time'])) {
            $data['start_time'] = strtotime($data['start_time']);
        }
        if (isset($data['end_time'])) {
            $data['end_time'] = strtotime($data['end_time']);
        }
        
        // 验证时间范围
        if (isset($data['start_time']) && isset($data['end_time'])) {
            if ($data['start_time'] >= $data['end_time']) {
                $this->fail('结束时间必须大于开始时间');
            }
        }
        
        return $data;
    }
}