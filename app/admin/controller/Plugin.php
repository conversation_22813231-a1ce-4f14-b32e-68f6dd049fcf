<?php

namespace app\admin\controller;

use app\common\ApiCode;
use app\common\controller\BaseAdminController;
use app\plugin\core\PluginManager;
use think\App;
use think\facade\Db;
use think\facade\Request;

/**
 * 插件管理控制器
 */
class Plugin extends BaseAdminController
{
    /**
     * 插件管理器
     * @var PluginManager
     */
    protected $pluginManager;

    protected $plugins;

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->pluginManager = new PluginManager();

        $this->plugins = $this->pluginManager->loadPlugins(false);
    }

    /**
     * 获取插件列表
     * @return \think\Response
     * @throws \Exception
     */
    public function index()
    {
        // 获取插件名称参数
        $pluginName = Request::param('name');
        $author = Request::param('author');
        $enabled = Request::param('enabled');

        // 获取所有插件信息
        $plugins = $this->plugins;

        $pluginList = [];
        foreach ($plugins as $plugin) {
            $info = $plugin->getInfo();
            // 如果指定了插件名称，则进行筛选
            if (!empty($pluginName) && stripos($info['name'], $pluginName) === false) {
                continue;
            }
            if (!empty($author) && stripos($info['author'], $author) === false) {
                continue;
            }
            if (!is_null($enabled) && $info['config']['enabled'] !== ($enabled == 1 ? true : false)) {
                continue;
            }
            $info['enabled'] = $info['config']['enabled'];
            if (isset($info['upgrade']) && isset($info['upgrade']['version'])) {
                $info['newVersion'] = $info['upgrade']['version'];
            }
            $pluginList[] = $info;
        }

        $this->ok('获取插件列表成功', $pluginList);
    }

    /**
     * 检查插件是否已安装
     * @return \think\Response
     */
    protected function isInstalled($name)
    {

        // 检查数据库中是否存在插件配置表
        try {
            $tableName = 'plugin_' . $name . '_config';
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");

        } catch (\Exception $e) {
            return false;
        }

        if(!empty($result)){
            return true;
        } else {
            return false;
        }
    }

    /**
     * 检查插件是否已启用
     * @return \think\Response
     */
    protected function isEnabled($name)
    {

        $config = $this->pluginManager->getPluginConfig($name);
        $isEnabled = !empty($config) && isset($config['enabled']) && $config['enabled'];

        return $isEnabled;
    }

    /**
     * 安装插件
     * @return \think\Response
     */
    public function install()
    {
        $pluginName = Request::param('name');
        if (empty($pluginName)) {
            $this->error('插件名称不能为空');
        }

        if ($this->isInstalled($pluginName)) {
            $this->ok('插件已安装', [], ApiCode::CODE_ERROR);
        }

        if ($this->pluginManager->installPlugin($pluginName)) {
            $this->ok('安装成功', [], ApiCode::SUCCESS);
        } else {
            $this->ok('安装失败', [], ApiCode::CODE_ERROR);
        }

    }

    /**
     * 更新插件
     * @return \think\Response
     */
    public function upgrade()
    {
        $pluginName = Request::param('name');
        $pluginVersion = Request::param('version');
        if (empty($pluginName)) {
            $this->error('插件名称不能为空');
        }

        if (empty($pluginVersion)) {
            $this->error('插件版本不能为空');
        }

        if ($this->pluginManager->upgradePlugin($pluginName, $pluginVersion)) {
            $this->ok('插件更新成功', [], ApiCode::SUCCESS);
        } else {
            $this->ok('插件更新失败', [], ApiCode::CODE_ERROR);
        }
    }

    /**
     * 卸载插件
     * @return \think\Response
     */
    public function uninstall()
    {
        $pluginName = Request::param('name');
        if (empty($pluginName)) {
            $this->error('插件名称不能为空');
        }

        if (!$this->isInstalled($pluginName)) {
            $this->ok('插件未安装', [], ApiCode::CODE_ERROR);
        }

        if ($this->pluginManager->uninstallPlugin($pluginName)) {
            $this->ok('卸载成功', [], ApiCode::SUCCESS);
        } else {
            $this->ok('卸载失败', [], ApiCode::CODE_ERROR);
        }
    }

    /**
     * 启用插件
     * @return \think\Response
     */
    public function enable()
    {
        $pluginName = Request::param('name');
        if (empty($pluginName)) {
            $this->error('插件名称不能为空');
        }

        if ($this->isEnabled($pluginName)) {
            $this->ok('插件已经启用');
        }

        if ($this->pluginManager->enablePlugin($pluginName)) {
            $this->ok('插件启用成功');
        } else {
            $this->ok('插件启用失败', [], ApiCode::CODE_ERROR);
        }
    }

    /**
     * 禁用插件
     * @return \think\Response
     */
    public function disable()
    {
        $pluginName = Request::param('name');
        if (empty($pluginName)) {
            $this->error('插件名称不能为空');
        }

        if (!$this->isEnabled($pluginName)) {
            $this->ok('插件未启用');
        }

        if ($this->pluginManager->disablePlugin($pluginName)) {
            $this->ok('插件禁用成功');
        } else {
            $this->ok('插件启用失败', [], ApiCode::CODE_ERROR);
        }
    }

    /**
     * 上传并安装插件
     * @return \think\Response
     */
    public function uploadInstall()
    {
        $file = Request::file('file');
        if (empty($file) || !$file->isValid()) {
            $this->error('请选择有效的插件包文件');
        }

        // 验证文件格式
        $extension = strtolower($file->getOriginalExtension());
        if ($extension !== 'zip') {
            $this->error('插件包必须是zip格式');
        }

        // 验证文件大小（默认最大10MB）
        $maxSize = 10 * 1024 * 1024;
        if ($file->getSize() > $maxSize) {
            $this->error('插件包大小不能超过10MB');
        }

        // 创建临时目录
        $tempDir = runtime_path() . 'plugin_tmp/' . uniqid();
        $extractDir = $tempDir . '/extract';
        if (!is_dir($extractDir)) {
            mkdir($extractDir, 0755, true);
        }

        try {
            // 移动上传文件到临时目录
            $fileName = $file->getOriginalName();
            $zipFile = $tempDir . '/' . $fileName;
            $file->move($tempDir, $fileName);

            // 解压插件包
            $zip = new \ZipArchive();
            if ($zip->open($zipFile) !== true) {
                $this->error = '插件包解压失败';
                $this->code = ApiCode::CODE_ERROR;
                throw new \Exception('插件包解压失败');
            }

            // 解压到临时目录
            $zip->extractTo($extractDir);
            $zip->close();

            // 验证插件包
            $manifestFile = '';
            $items = scandir($extractDir);
            foreach ($items as $item) {
                if ($item === '.' || $item === '..') {
                    continue;
                }
                
                $itemPath = $extractDir . '/' . $item;
                if (is_dir($itemPath)) {
                    // 检查子目录中的manifest.json
                    $subManifestFile = $itemPath . '/manifest.json';
                    if (file_exists($subManifestFile)) {
                        $manifestFile = $subManifestFile;
                        $extractDir = $itemPath; // 更新解压目录为实际的插件目录
                        break;
                    }
                } else if ($item === 'manifest.json') {
                    // 检查当前目录的manifest.json
                    $manifestFile = $extractDir . '/manifest.json';
                    break;
                }
            }
            
            if (empty($manifestFile) || !file_exists($manifestFile)) {
                $this->error = '插件包缺少manifest.json文件';
                $this->code = ApiCode::CODE_ERROR;
                throw new \Exception('插件包缺少manifest.json文件');
            }

            $manifest = json_decode(file_get_contents($manifestFile), true);
            if ($manifest === null) {
                $this->error = 'manifest.json文件格式错误';
                $this->code = ApiCode::CODE_ERROR;
                throw new \Exception('manifest.json文件格式错误');
            }

            // 验证必要的配置项
            $requiredFields = ['name', 'version', 'title', 'description', 'author'];
            foreach ($requiredFields as $field) {
                if (!isset($manifest[$field]) || empty($manifest[$field])) {
                    $this->error = 'manifest.json缺少必要字段';
                    $this->code = ApiCode::CODE_ERROR;
                    throw new \Exception('manifest.json缺少必要字段');
                }
            }

            if ($this->isInstalled($manifest['name'])) {
                $this->error = '插件已经安装';
                $this->code = ApiCode::CODE_ERROR;
                throw new \Exception('插件已经安装');
            }

            $pluginName = $manifest['name'];
            $pluginDir = root_path() . 'apps/' . $pluginName;

            // 如果插件目录已存在，先卸载并删除
            if (is_dir($pluginDir)) {
                $this->pluginManager->uninstallPlugin($pluginName);
                $this->removeDirectory($pluginDir);
            }

            // 创建插件目录
            if (!is_dir($pluginDir)) {
                mkdir($pluginDir, 0755, true);
            }

            // 复制插件文件到目标目录，排除特定目录
            $excludes = ['config', 'data', '.git', '.svn', 'upgrade'];
            $this->copyPluginFiles($extractDir, $pluginDir, $excludes);

            // 安装插件
            if ($this->pluginManager->installPlugin($pluginName)) {
            } else {
                $this->error = '插件安装失败';
                $this->code = ApiCode::CODE_ERROR;
                throw new \Exception('插件安装失败');
            }

        } catch (\Exception $e) {
            // 清理临时文件
            if (is_dir($tempDir)) {
                $this->removeDirectory($tempDir);
            }
            $this->ok($e->getMessage(), [], ApiCode::CODE_ERROR);
        }

        if ($this->code) {
            $this->ok($this->error, '', $this->code);
        }

        $this->ok('插件上传安装成功');
    }

    /**
     * 递归复制插件文件（排除特定目录）
     */
    protected function copyPluginFiles(string $source, string $target, array $excludes): void
    {
        if (!is_dir($source)) {
            return;
        }

        if (!is_dir($target)) {
            mkdir($target, 0755, true);
        }

        $items = scandir($source);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') {
                continue;
            }

            $sourcePath = $source . '/' . $item;
            $targetPath = $target . '/' . $item;

            if (is_dir($sourcePath)) {
                if (!in_array($item, $excludes)) {
                    $this->copyPluginFiles($sourcePath, $targetPath, $excludes);
                }
            } else {
                copy($sourcePath, $targetPath);
            }
        }
    }

    /**
     * 删除目录
     */
    protected function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }

    /**
     * 获取插件配置
     * @return \think\Response
     */
    public function getConfig()
    {
        $pluginName = Request::param('name');
        if (empty($pluginName)) {
            $this->error('插件名称不能为空');
        }

        $config = $this->pluginManager->getPluginConfig($pluginName);
        if (empty($config)) {
            $this->ok('插件配置不存在', [], ApiCode::CODE_ERROR);
        }

        $this->ok('获取插件配置成功', $config);
    }

    /**
     * 设置插件配置
     * @return \think\Response
     */
    public function setConfig()
    {
        $pluginName = Request::param('name');
        $config = Request::param('config');
        if (empty($pluginName)) {
            $this->error('插件名称不能为空');
        }
        if (empty($config) || !is_array($config)) {
            $this->error('插件配置不能为空且必须是数组');
        }

        // 获取插件的manifest配置
        $plugin = $this->plugins[$pluginName] ?? null;
        if (!$plugin) {
            $this->ok('插件不存在', [], ApiCode::CODE_ERROR);
        }

        $manifest = $plugin->getInfo();
        $manifestConfig = $manifest['config'] ?? [];

        // 验证并转换配置项的类型
        foreach ($config as $key => $value) {
            if (!isset($manifestConfig[$key])) {
                continue;
            }

            if (isset($manifestConfig[$key]['value'])) {
                $manifestConfig[$key]['value'] = $value;
            }

        }

        if ($this->pluginManager->setPluginConfig($pluginName, $manifestConfig)) {
            $this->ok('插件配置更新成功');
        } else {
            $this->ok('插件配置更新失败', [], ApiCode::CODE_ERROR);
        }
    }
}