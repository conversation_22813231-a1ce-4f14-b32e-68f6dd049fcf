<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\common\controller\BaseAdminController;
use think\App;
use think\facade\Db;

class Announcement extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'title|公告标题' => 'require|max:100',
        'content|公告内容' => 'require',
        'type|公告类型' => 'require|in:1,2,3',
        'sort|排序' => 'require|number',
        'status|状态' => 'require|in:0,1',
        'start_time|开始时间' => 'require|number',
        'end_time|结束时间' => 'require|number|gt:start_time'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->table = 'sys_announcement';
        $this->model = Db::name($this->table);
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array
     */
    protected function beforeAdd($data)
    {
        $data['create_time'] = time();
        $data['update_time'] = time();
        return $data;
    }

    /**
     * 更新前的钩子方法
     * @param array $data 表单数据
     * @return array
     */
    protected function beforeUpdate($data)
    {
        $data['update_time'] = time();
        return $data;
    }

    /**
     * 查询前的钩子方法
     * @param array $where 查询条件
     * @param string|array $sort 排序规则
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 默认按排序和创建时间倒序排列
        if (empty($sort)) {
            $sort = ['sort' => 'desc', 'create_time' => 'desc'];
        }

        // 只显示在有效期内的公告
        $currentTime = time();
        $where[] = ['start_time', '<=', $currentTime];
        $where[] = ['end_time', '>=', $currentTime];
    }
}