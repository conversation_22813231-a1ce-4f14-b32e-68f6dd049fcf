<?php

namespace app\admin\controller\points;

use app\model\points\PointsOrder as PointsOrderModel;
use app\model\points\PointsGoods as PointsGoodsModel;
use think\App;
use app\common\controller\BaseAdminController;
use think\facade\Db;

class PointsOrder extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'order_no|订单编号' => 'require|unique:points_order',
        'user_id|用户ID' => 'require|integer',
        'goods_id|商品ID' => 'require|integer',
        'quantity|兑换数量' => 'require|integer|min:1',
        'receiver_name|收货人姓名' => 'require',
        'receiver_phone|收货人电话' => 'require|mobile',
        'receiver_address|收货地址' => 'require',
        'status|订单状态' => 'require|in:pending,delivered,completed'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new PointsOrderModel();
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 检查商品是否存在且有效
        $goods = PointsGoodsModel::where('id', $data['goods_id'])
            ->where('status', 1)
            ->where('stock', '>=', $data['quantity'])
            ->find();
        
        if (!$goods) {
            $this->error('商品不存在或库存不足');
        }

        // 设置商品信息
        $data['goods_name'] = $goods['name'];
        $data['points'] = $goods['points'] * $data['quantity'];
        
        // 生成订单编号
        $data['order_no'] = date('YmdHis') . mt_rand(1000, 9999);
        
        return $data;
    }

    /**
     * 添加后的钩子方法
     * @param int $id 新增记录的ID
     * @param array $data 表单数据
     */
    protected function afterAdd($id, $data)
    {
        // 扣减商品库存
        Db::name('points_goods')->where('id', $data['goods_id'])
            ->dec('stock', $data['quantity'])
            ->update();
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 订单号不允许修改
        if (isset($data['order_no'])) {
            unset($data['order_no']);
        }
        
        // 如果修改了状态为已发货，需要填写快递信息
        if (isset($data['status']) && $data['status'] == 'delivered') {
            if (empty($data['express_company']) || empty($data['express_no'])) {
                $this->error('请填写快递公司和快递单号');
            }
        }
        
        return $data;
    }

    /**
     * 删除前的钩子方法
     * @param int $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        $order = $this->model->find($id);
        if ($order && $order['status'] != 'pending') {
            $this->error('只能删除待发货的订单');
            return false;
        }
        return true;
    }

    /**
     * 删除后的钩子方法
     * @param int $id 记录ID
     */
    protected function afterDelete($id)
    {
        $order = $this->model->find($id);
        if ($order) {
            // 恢复商品库存
            Db::name('points_goods')->where('id', $order['goods_id'])
                ->inc('stock', $order['quantity'])
                ->update();
        }
    }

    /**
     * 发货处理
     * @param int $id 订单ID
     * @param string $express_company 快递公司
     * @param string $express_no 快递单号
     */
    public function deliver()
    {
        $id = $this->request->param('id/d');
        $express_company = $this->request->param('express_company/s');
        $express_no = $this->request->param('express_no/s');

        if (empty($express_company) || empty($express_no)) {
            $this->error('请填写快递公司和快递单号');
        }

        $order = $this->model->find($id);
        if (!$order) {
            $this->error('订单不存在');
        }

        if ($order['status'] != 'pending') {
            $this->error('只能发货待发货的订单');
        }

        try {
            $order->save([
                'status' => 'delivered',
                'express_company' => $express_company,
                'express_no' => $express_no
            ]);
            $this->ok('发货成功');
        } catch (\Exception $e) {
            $this->error('发货失败：' . $e->getMessage());
        }
    }
}