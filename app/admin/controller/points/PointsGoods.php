<?php

namespace app\admin\controller\points;

use app\model\points\PointsGoods as PointsGoodsModel;
use think\App;
use app\common\controller\BaseAdminController;

class PointsGoods extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|商品名称' => 'require',
        'points|所需积分' => 'require|integer|min:0',
        'stock|商品库存' => 'require|integer|min:0',
        'exchange_limit|兑换限制' => 'require|integer|min:0',
        'start_time|开始时间' => 'require|integer',
        'end_time|结束时间' => 'require|integer',
        'status|商品状态' => 'require|in:0,1'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new PointsGoodsModel();
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 验证时间
        if ($data['start_time'] >= $data['end_time']) {
            $this->error('开始时间必须小于结束时间');
        }
        return $data;
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 验证时间
        if ($data['start_time'] >= $data['end_time']) {
            $this->error('开始时间必须小于结束时间');
        }
        return $data;
    }

    /**
     * 删除前的钩子方法
     * @param int $id 记录ID
     * @return bool|null 返回false将阻止删除
     */
    protected function beforeDelete($id)
    {
        // 检查是否有关联的订单
        $orderCount = Db::name('points_order')->where('goods_id', $id)->count();
        if ($orderCount > 0) {
            $this->error('该商品已有关联订单，无法删除');
            return false;
        }
        return true;
    }

    /**
     * 获取所有积分商品（用于下拉选择）
     */
    public function all()
    {
        $list = $this->model->where('status', 1)
            ->where('stock', '>', 0)
            ->where('end_time', '>', time())
            ->field('id, name, points, stock')
            ->select();
        $this->ok('获取成功', $list);
    }
}