<?php
declare (strict_types = 1);

namespace app\admin\controller\seckill;

use app\common\controller\BaseAdminController;
use app\model\Seckill as SeckillModel;
use think\App;

/**
 * 限时秒杀管理API控制器
 */
class Seckill extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'title|活动名称' => 'require|max:255',
        'description|活动说明' => 'max:65535',
        'status|活动状态' => 'require|number|in:0,1,2',
        'start_time|开始时间' => 'require|number',
        'end_time|结束时间' => 'require|number|gt:start_time',
        'goods_id|商品ID' => 'require|number',
        'seckill_price|秒杀价格' => 'require|float|gt:0',
        'stock_quantity|秒杀商品数量' => 'require|number|gt:0',
        'limit_quantity|每人限购数量' => 'require|number|gt:0',
        'sold_quantity|已售数量' => 'number|egt:0'
    ];
    
    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new SeckillModel();
    }
    
    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit') {
            return [
                'title|活动名称' => 'max:255',
                'description|活动说明' => 'max:65535',
                'status|活动状态' => 'number|in:0,1,2',
                'start_time|开始时间' => 'number',
                'end_time|结束时间' => 'number|gt:start_time',
                'seckill_price|秒杀价格' => 'float|gt:0',
                'stock_quantity|秒杀商品数量' => 'number|gt:0',
                'limit_quantity|每人限购数量' => 'number|gt:0'
            ];
        }
        
        return $this->validateRule;
    }
    
    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 设置创建和更新时间
        $data['createtime'] = $data['updatetime'] = time();
        
        // 初始化已售数量
        $data['sold_quantity'] = 0;
        
        // 根据时间自动设置状态
        $time = time();
        if ($time >= $data['start_time'] && $time <= $data['end_time']) {
            $data['status'] = 1; // 进行中
        } elseif ($time > $data['end_time']) {
            $data['status'] = 2; // 已结束
        } else {
            $data['status'] = 0; // 未开始
        }
        
        return $data;
    }
    
    /**
     * 查询前的钩子方法
     * @param array $where 查询条件
     * @param array $sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 按活动名称搜索
        $title = $this->request->param('title', '');
        if (!empty($title)) {
            $where[] = ['title', 'like', '%' . $title . '%'];
        }
        
        // 按活动状态搜索
        $status = $this->request->param('status', '');
        if ($status !== '') {
            $where[] = ['status', '=', intval($status)];
        }
        
        // 按商品ID搜索
        $goodsId = $this->request->param('goods_id', '');
        if (!empty($goodsId)) {
            $where[] = ['goods_id', '=', intval($goodsId)];
        }
        
        // 按时间范围搜索
        $startTime = $this->request->param('start_time', '');
        if (!empty($startTime)) {
            $where[] = ['start_time', '>=', intval($startTime)];
        }
        
        $endTime = $this->request->param('end_time', '');
        if (!empty($endTime)) {
            $where[] = ['end_time', '<=', intval($endTime)];
        }
        
        // 设置默认排序
        if (empty($sort)) {
            $sort = ['id' => 'desc'];
        }
    }
    
    /**
     * 编辑前的钩子方法
     * @param array $data
     * @param $id
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 更新时间
        $data['updatetime'] = time();
        
        // 根据时间自动更新状态
        $time = time();
        if ($time >= $data['start_time'] && $time <= $data['end_time']) {
            $data['status'] = 1; // 进行中
        } elseif ($time > $data['end_time']) {
            $data['status'] = 2; // 已结束
        } else {
            $data['status'] = 0; // 未开始
        }
        
        return $data;
    }
}