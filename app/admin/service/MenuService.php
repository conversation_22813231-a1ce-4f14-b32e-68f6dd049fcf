<?php
namespace app\admin\service;

use app\common\controller\BaseAdminController;
use app\admin\entity\RoleEntity;
use think\facade\Db;
use think\facade\Cache;
use app\model\system\AdminRule;

class MenuService extends BaseAdminController
{
    /**
     * 缓存前缀
     */
    const CACHE_PREFIX = 'admin_menu_';
    
    /**
     * 缓存过期时间（秒）
     */
    const CACHE_EXPIRE = 3600;
    
    /**
     * 获取当前用户的菜单树
     */
    public function getMenuTree()
    {
        // 获取当前用户ID
        $userId = session('adminInfo.id');
        $cacheKey = self::CACHE_PREFIX . $userId;
        
        // 尝试从缓存获取
        $menuTree = Cache::get($cacheKey);
        if ($menuTree) {
            return $menuTree;
        }
        
        // 获取当前用户权限ID列表
        $permissionIds = $this->getUserPermissions();

        // 获取菜单基础数据
        $menus = Db::name('sys_admin_rule')
            ->where('status', 1)
            ->whereIn('id', $permissionIds)
            ->where('ismenu', 1)
            ->order('weigh desc,id asc')
            ->select();

        // 构建树形结构
        $menuTree = $this->buildTree($menus);
        
        // 存入缓存
        Cache::set($cacheKey, $menuTree, self::CACHE_EXPIRE);
        
        return $menuTree;
    }

    /**
     * 获取当前用户权限ID列表
     */
    private function getUserPermissions()
    {
        // 从三表关联获取权限信息
        $groupIds = session('adminInfo.group_ids');
        
        // 使用RoleEntity中的方法获取权限ID
        return RoleEntity::getGroupPermissions($groupIds);
    }
    
    /**
     * 清除用户菜单缓存
     * @param int|array $userIds 用户ID或用户ID数组
     */
    public function clearMenuCache($userIds = null)
    {
        if (is_null($userIds)) {
            // 清除当前用户缓存
            $userId = session('adminInfo.id');
            Cache::delete(self::CACHE_PREFIX . $userId);
        } elseif (is_array($userIds)) {
            // 清除多个用户缓存
            foreach ($userIds as $userId) {
                Cache::delete(self::CACHE_PREFIX . $userId);
            }
        } else {
            // 清除单个用户缓存
            Cache::delete(self::CACHE_PREFIX . $userIds);
        }
    }

    /**
     * 构建树形菜单
     */
    private function buildTree($items, $parentId = 0)
    {
        $tree = [];
        foreach ($items as $item) {
            if ($item['parent_id'] == $parentId) {
                $children = $this->buildTree($items, $item['id']);
                if ($children) {
                    $item['children'] = $children;
                }
                $tree[] = $this->formatMenu($item);
            }
        }
        return $tree;
    }

    /**
     * 格式化菜单项
     */
    private function formatMenu($menu)
    {
        return [
            'id' => $menu['id'],
            'title' => $menu['name'],
            'icon' => $this->parseIcon($menu['icon'] ?? ''),
            'path' => $this->parseRoute($menu['url'] ?? ''),
            'hidden' => !$menu['status'],
            'component' => $menu['component'] ?? ''
        ];
    }

    /**
     * 解析图标格式
     */
    private function parseIcon($icon)
    {
        if (strpos($icon, 'el-icon') !== false) {
            return ['type' => 'element', 'name' => $icon];
        }
        return ['type' => 'custom', 'name' => $icon];
    }

    /**
     * 解析路由路径
     */
    private function parseRoute($path)
    {
        return '/admin' . $path;
    }
}