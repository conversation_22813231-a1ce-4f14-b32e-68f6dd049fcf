<?php

namespace app\admin\entity;

use think\facade\Db;

class RoleEntity
{
    /**
     * 角色权限
     * @var array
     */
    public $role = [];

    /**
     * 获取权限组关联的权限ID
     */
    public static function getGroupPermissions($groupIds)
    {
        return Db::table('sys_admin_group_rule')
            ->alias('gr')
            ->join('sys_admin_rule r', 'gr.rule_id = r.id')
            ->whereIn('gr.group_id', $groupIds)
            ->where('r.status', 1)
            ->column('r.id');
    }

    public function getRole()
    {
        return $this->role;
    }
}
