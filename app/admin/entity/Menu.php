<?php

namespace app\admin\entity;

use think\Model;

class Menu extends Model
{
    protected $table = 'sys_admin_rule';

    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'pid'         => 'int',
        'name'        => 'string',
        'ismenu'      => 'int',
        'createtime'  => 'datetime',
        'updatetime'  => 'datetime',
        'weigh'       => 'int',
        'enabled'     => 'string',
        'role'        => 'string',
        'type'        => 'string',
        'platform'    => 'string',
        'icon'        => 'string',
        'description' => 'string'
    ];

    /**
     * 获取菜单列表
     * @return array
     */
    public function getMenuList()
    {
        return $this->where('type', 'menu')
            ->where('enabled', '1')
            ->field('id, pid, name as title, role as path, icon, weigh, description, ismenu as isMenu')
            ->order('weigh', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * 构建菜单树
     * @param array $list 菜单列表
     * @param int $pid 父ID
     * @return array
     */
    public function buildMenuTree($list, $pid = 0)
    {
        $tree = [];
        foreach ($list as $item) {
            if ($item['pid'] == $pid) {
                // 处理路径
                $item['path'] = $item['path'] ?: ('/' . strtolower(str_replace('/', '-', $item['title'])));
                
                // 设置meta信息
                $item['meta'] = [
                    'title' => $item['title'],
                    'icon' => $item['icon'],
                    'isMenu' => (bool)$item['isMenu']
                ];
                
                // 设置组件
                if ($item['path'] && strpos($item['path'], 'http') !== 0) {
                    $path = $item['path'];
                    if (strpos($path, '/') !== 0) {
                        $path = '/' . $path;
                    }
                    $item['component'] = '/pages' . $path;
                }
                
                // 递归处理子节点
                $children = $this->buildMenuTree($list, $item['id']);
                if (!empty($children)) {
                    $item['children'] = $children;
                } else {
                    $item['meta']['useLayout'] = true;
                }
                
                // 移除不需要的字段
                unset($item['id'], $item['pid'], $item['weigh'], $item['description']);
                
                $tree[] = $item;
            }
        }
        return $tree;
    }

    /**
     * 获取树形菜单
     * @return array
     */
    public function getTreeMenu()
    {
        $list = $this->getMenuList();
        return $this->buildMenuTree($list);
    }

    /**
     * 添加菜单
     * @param array $data 菜单数据
     * @return bool
     */
    public function addMenu($data)
    {
        $data['type'] = 'menu';
        return $this->save($data);
    }

    /**
     * 更新菜单
     * @param array $data 菜单数据
     * @return bool
     */
    public function updateMenu($data)
    {
        if (!isset($data['id'])) {
            return false;
        }
        return $this->where('id', $data['id'])
            ->where('type', 'menu')
            ->update($data);
    }

    /**
     * 删除菜单
     * @param int $id 菜单ID
     * @return bool
     */
    public function deleteMenu($id)
    {
        return $this->where('id', $id)
            ->where('type', 'menu')
            ->delete();
    }
}