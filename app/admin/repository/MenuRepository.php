<?php
declare (strict_types = 1);

namespace app\admin\repository;

use app\admin\entity\Menu;

class MenuRepository
{
    protected $menu;

    public function __construct(Menu $menu)
    {
        $this->menu = $menu;
    }

    /**
     * 获取菜单列表
     * @return array
     */
    public function getList(): array
    {
        return $this->menu->getMenuList();
    }

    /**
     * 获取树形菜单
     * @return array
     */
    public function getTree(): array
    {
        return $this->menu->getTreeMenu();
    }

    /**
     * 添加菜单
     * @param array $data
     * @return bool
     */
    public function add(array $data): bool
    {
        return $this->menu->addMenu($data);
    }

    /**
     * 更新菜单
     * @param array $data
     * @return bool
     */
    public function update(array $data): bool
    {
        return $this->menu->updateMenu($data);
    }

    /**
     * 删除菜单
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        return $this->menu->deleteMenu($id);
    }
}