<?php

namespace app\ai\monitoring;

use app\ai\utils\Logger;

/**
 * 指标收集器
 */
class MetricsCollector
{
    /**
     * 指标数据
     * @var array
     */
    protected array $metrics = [];

    /**
     * 计数器
     * @var array
     */
    protected array $counters = [];

    /**
     * 计时器
     * @var array
     */
    protected array $timers = [];

    /**
     * 直方图
     * @var array
     */
    protected array $histograms = [];

    /**
     * 记录计数器
     * @param string $name 指标名称
     * @param int $value 值
     * @param array $tags 标签
     */
    public function counter(string $name, int $value = 1, array $tags = []): void
    {
        $key = $this->generateKey($name, $tags);
        
        if (!isset($this->counters[$key])) {
            $this->counters[$key] = [
                'name' => $name,
                'value' => 0,
                'tags' => $tags,
                'type' => 'counter',
            ];
        }
        
        $this->counters[$key]['value'] += $value;
        $this->counters[$key]['last_updated'] = microtime(true);
    }

    /**
     * 记录计时器
     * @param string $name 指标名称
     * @param float $duration 持续时间（毫秒）
     * @param array $tags 标签
     */
    public function timer(string $name, float $duration, array $tags = []): void
    {
        $key = $this->generateKey($name, $tags);
        
        if (!isset($this->timers[$key])) {
            $this->timers[$key] = [
                'name' => $name,
                'values' => [],
                'tags' => $tags,
                'type' => 'timer',
            ];
        }
        
        $this->timers[$key]['values'][] = $duration;
        $this->timers[$key]['last_updated'] = microtime(true);
    }

    /**
     * 记录直方图
     * @param string $name 指标名称
     * @param float $value 值
     * @param array $tags 标签
     */
    public function histogram(string $name, float $value, array $tags = []): void
    {
        $key = $this->generateKey($name, $tags);
        
        if (!isset($this->histograms[$key])) {
            $this->histograms[$key] = [
                'name' => $name,
                'values' => [],
                'tags' => $tags,
                'type' => 'histogram',
            ];
        }
        
        $this->histograms[$key]['values'][] = $value;
        $this->histograms[$key]['last_updated'] = microtime(true);
    }

    /**
     * 记录AI请求指标
     * @param string $provider 提供商
     * @param string $model 模型
     * @param float $duration 持续时间
     * @param bool $success 是否成功
     * @param int $tokenUsage Token使用量
     */
    public function recordAiRequest(
        string $provider,
        string $model,
        float $duration,
        bool $success = true,
        int $tokenUsage = 0
    ): void {
        $tags = [
            'provider' => $provider,
            'model' => $model,
            'status' => $success ? 'success' : 'error',
        ];

        // 记录请求计数
        $this->counter('ai.requests.total', 1, $tags);
        
        // 记录请求时长
        $this->timer('ai.requests.duration', $duration, $tags);
        
        // 记录Token使用量
        if ($tokenUsage > 0) {
            $this->histogram('ai.requests.tokens', $tokenUsage, $tags);
        }
    }

    /**
     * 记录记忆操作指标
     * @param string $operation 操作类型
     * @param string $memoryType 记忆类型
     * @param float $duration 持续时间
     * @param bool $success 是否成功
     */
    public function recordMemoryOperation(
        string $operation,
        string $memoryType,
        float $duration,
        bool $success = true
    ): void {
        $tags = [
            'operation' => $operation,
            'memory_type' => $memoryType,
            'status' => $success ? 'success' : 'error',
        ];

        $this->counter('ai.memory.operations.total', 1, $tags);
        $this->timer('ai.memory.operations.duration', $duration, $tags);
    }

    /**
     * 记录缓存操作指标
     * @param string $operation 操作类型
     * @param bool $hit 是否命中
     */
    public function recordCacheOperation(string $operation, bool $hit = false): void
    {
        $tags = ['operation' => $operation];
        
        $this->counter('ai.cache.operations.total', 1, $tags);
        
        if ($operation === 'get') {
            $hitTags = array_merge($tags, ['result' => $hit ? 'hit' : 'miss']);
            $this->counter('ai.cache.hits.total', 1, $hitTags);
        }
    }

    /**
     * 获取所有指标
     * @return array
     */
    public function getAllMetrics(): array
    {
        return [
            'counters' => $this->counters,
            'timers' => $this->getTimerStats(),
            'histograms' => $this->getHistogramStats(),
            'timestamp' => microtime(true),
        ];
    }

    /**
     * 获取计时器统计
     * @return array
     */
    protected function getTimerStats(): array
    {
        $stats = [];
        
        foreach ($this->timers as $key => $timer) {
            $values = $timer['values'];
            $count = count($values);
            
            if ($count > 0) {
                sort($values);
                $stats[$key] = [
                    'name' => $timer['name'],
                    'tags' => $timer['tags'],
                    'count' => $count,
                    'min' => min($values),
                    'max' => max($values),
                    'avg' => array_sum($values) / $count,
                    'p50' => $this->percentile($values, 0.5),
                    'p95' => $this->percentile($values, 0.95),
                    'p99' => $this->percentile($values, 0.99),
                    'last_updated' => $timer['last_updated'],
                ];
            }
        }
        
        return $stats;
    }

    /**
     * 获取直方图统计
     * @return array
     */
    protected function getHistogramStats(): array
    {
        $stats = [];
        
        foreach ($this->histograms as $key => $histogram) {
            $values = $histogram['values'];
            $count = count($values);
            
            if ($count > 0) {
                sort($values);
                $stats[$key] = [
                    'name' => $histogram['name'],
                    'tags' => $histogram['tags'],
                    'count' => $count,
                    'min' => min($values),
                    'max' => max($values),
                    'avg' => array_sum($values) / $count,
                    'sum' => array_sum($values),
                    'last_updated' => $histogram['last_updated'],
                ];
            }
        }
        
        return $stats;
    }

    /**
     * 计算百分位数
     * @param array $values 值数组
     * @param float $percentile 百分位数
     * @return float
     */
    protected function percentile(array $values, float $percentile): float
    {
        $count = count($values);
        $index = $percentile * ($count - 1);
        
        if (floor($index) == $index) {
            return $values[(int)$index];
        }
        
        $lower = $values[(int)floor($index)];
        $upper = $values[(int)ceil($index)];
        
        return $lower + ($upper - $lower) * ($index - floor($index));
    }

    /**
     * 生成指标键
     * @param string $name 指标名称
     * @param array $tags 标签
     * @return string
     */
    protected function generateKey(string $name, array $tags): string
    {
        ksort($tags);
        $tagString = http_build_query($tags);
        return $name . '|' . $tagString;
    }

    /**
     * 重置所有指标
     */
    public function reset(): void
    {
        $this->counters = [];
        $this->timers = [];
        $this->histograms = [];
    }

    /**
     * 导出指标到日志
     */
    public function exportToLog(): void
    {
        $metrics = $this->getAllMetrics();
        Logger::info('AI Metrics Export', $metrics);
    }

    /**
     * 获取指标摘要
     * @return array
     */
    public function getSummary(): array
    {
        return [
            'counters_count' => count($this->counters),
            'timers_count' => count($this->timers),
            'histograms_count' => count($this->histograms),
            'total_requests' => $this->getTotalRequests(),
            'average_response_time' => $this->getAverageResponseTime(),
        ];
    }

    /**
     * 获取总请求数
     * @return int
     */
    protected function getTotalRequests(): int
    {
        $total = 0;
        foreach ($this->counters as $counter) {
            if ($counter['name'] === 'ai.requests.total') {
                $total += $counter['value'];
            }
        }
        return $total;
    }

    /**
     * 获取平均响应时间
     * @return float
     */
    protected function getAverageResponseTime(): float
    {
        $totalTime = 0;
        $totalCount = 0;
        
        foreach ($this->timers as $timer) {
            if ($timer['name'] === 'ai.requests.duration') {
                $values = $timer['values'];
                $totalTime += array_sum($values);
                $totalCount += count($values);
            }
        }
        
        return $totalCount > 0 ? $totalTime / $totalCount : 0;
    }
}
