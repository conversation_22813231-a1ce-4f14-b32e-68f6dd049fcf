<?php

namespace app\ai\container;

/**
 * 简化的服务容器
 */
class ServiceContainer
{
    protected static $instance;
    protected $bindings = [];
    protected $instances = [];
    protected $singletons = [];
    protected $aliases = [];

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 绑定服务
     * @param string $abstract
     * @param mixed $concrete
     * @param bool $singleton
     */
    public function bind($abstract, $concrete = null, $singleton = false)
    {
        $this->bindings[$abstract] = $concrete ?: $abstract;
        if ($singleton) {
            $this->singletons[$abstract] = true;
        }
    }

    /**
     * 绑定单例服务
     * @param string $abstract
     * @param mixed $concrete
     */
    public function singleton($abstract, $concrete = null)
    {
        $this->bind($abstract, $concrete, true);
    }

    /**
     * 绑定实例
     * @param string $abstract
     * @param mixed $instance
     */
    public function instance($abstract, $instance)
    {
        $this->instances[$abstract] = $instance;
    }

    /**
     * 设置别名
     * @param string $alias
     * @param string $abstract
     */
    public function alias($alias, $abstract)
    {
        $this->aliases[$alias] = $abstract;
    }

    /**
     * 检查服务是否已绑定
     * @param string $abstract
     * @return bool
     */
    public function bound($abstract)
    {
        return isset($this->bindings[$abstract]) ||
               isset($this->instances[$abstract]) ||
               isset($this->aliases[$abstract]);
    }

    /**
     * 解析服务
     * @param string $abstract
     * @param array $parameters
     * @return mixed
     */
    public function make($abstract, $parameters = [])
    {
        // 检查别名
        if (isset($this->aliases[$abstract])) {
            $abstract = $this->aliases[$abstract];
        }

        // 检查已实例化的服务
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        // 检查是否为单例且已创建
        if (isset($this->singletons[$abstract]) && isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        // 创建实例
        $instance = $this->createInstance($abstract, $parameters);

        // 如果是单例，保存实例
        if (isset($this->singletons[$abstract])) {
            $this->instances[$abstract] = $instance;
        }

        return $instance;
    }

    /**
     * 创建实例
     * @param string $abstract
     * @param array $parameters
     * @return mixed
     */
    protected function createInstance($abstract, $parameters = [])
    {
        // 检查绑定
        if (isset($this->bindings[$abstract])) {
            $concrete = $this->bindings[$abstract];
            if (is_callable($concrete)) {
                return $concrete($this, $parameters);
            }
            if (is_string($concrete) && class_exists($concrete)) {
                return new $concrete(...$parameters);
            }
        }

        // 简化的实例化逻辑
        switch ($abstract) {
            case "ai.basic":
                return new \app\ai\services\BasicAiService();
            case "ai.langchain":
                return new \app\ai\services\LangChainService();
            case "ai.unified":
                return new \app\ai\services\UnifiedAiService();
            case "ai.events":
                return new \app\ai\events\EventDispatcher();
            case "ai.cache":
                return new \app\ai\cache\CacheManager();
            case "ai.metrics":
                return new \app\ai\monitoring\MetricsCollector();
            case "ai.memory":
                return new \app\ai\memory\MySqlMemory();
            default:
                // 尝试直接实例化类
                if (class_exists($abstract)) {
                    return new $abstract(...$parameters);
                }
                throw new \Exception("Service not found: " . $abstract);
        }
    }

    /**
     * 清空容器
     */
    public function flush()
    {
        $this->bindings = [];
        $this->instances = [];
        $this->singletons = [];
        $this->aliases = [];
    }
}