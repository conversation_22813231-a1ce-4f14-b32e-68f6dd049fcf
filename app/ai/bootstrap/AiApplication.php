<?php

namespace app\ai\bootstrap;

use app\ai\container\ServiceContainer;
use app\ai\providers\AiServiceProvider;
use app\ai\providers\ServiceProvider;

/**
 * AI应用程序引导器
 */
class AiApplication
{
    /**
     * 服务容器
     * @var ServiceContainer
     */
    protected ServiceContainer $container;

    /**
     * 服务提供者
     * @var array
     */
    protected array $providers = [];

    /**
     * 已注册的服务提供者
     * @var array
     */
    protected array $registeredProviders = [];

    /**
     * 已启动的服务提供者
     * @var array
     */
    protected array $bootedProviders = [];

    /**
     * 是否已启动
     * @var bool
     */
    protected bool $booted = false;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->container = ServiceContainer::getInstance();
        $this->registerCoreProviders();
    }

    /**
     * 注册核心服务提供者
     */
    protected function registerCoreProviders(): void
    {
        $this->providers = [
            AiServiceProvider::class,
        ];
    }

    /**
     * 启动应用程序
     */
    public function boot(): void
    {
        if ($this->booted) {
            return;
        }

        // 注册所有服务提供者
        foreach ($this->providers as $provider) {
            $this->register($provider);
        }

        // 启动所有服务提供者
        foreach ($this->registeredProviders as $provider) {
            $this->bootProvider($provider);
        }

        $this->booted = true;
    }

    /**
     * 注册服务提供者
     * @param string|ServiceProvider $provider
     * @return ServiceProvider
     */
    public function register($provider): ServiceProvider
    {
        if (is_string($provider)) {
            $provider = new $provider($this->container);
        }

        $providerClass = get_class($provider);

        if (isset($this->registeredProviders[$providerClass])) {
            return $this->registeredProviders[$providerClass];
        }

        $provider->register();
        $this->registeredProviders[$providerClass] = $provider;

        if ($this->booted) {
            $this->bootProvider($provider);
        }

        return $provider;
    }

    /**
     * 启动服务提供者
     * @param ServiceProvider $provider
     */
    protected function bootProvider(ServiceProvider $provider): void
    {
        $providerClass = get_class($provider);

        if (isset($this->bootedProviders[$providerClass])) {
            return;
        }

        $provider->boot();
        $this->bootedProviders[$providerClass] = $provider;
    }

    /**
     * 获取服务容器
     * @return ServiceContainer
     */
    public function getContainer(): ServiceContainer
    {
        return $this->container;
    }

    /**
     * 从容器中解析服务
     * @param string $abstract
     * @param array $parameters
     * @return mixed
     */
    public function make(string $abstract, array $parameters = [])
    {
        return $this->container->make($abstract, $parameters);
    }

    /**
     * 检查服务是否已绑定
     * @param string $abstract
     * @return bool
     */
    public function bound(string $abstract): bool
    {
        return $this->container->bound($abstract);
    }

    /**
     * 绑定服务到容器
     * @param string $abstract
     * @param mixed $concrete
     * @param bool $singleton
     * @return $this
     */
    public function bind(string $abstract, $concrete = null, bool $singleton = false): self
    {
        $this->container->bind($abstract, $concrete, $singleton);
        return $this;
    }

    /**
     * 绑定单例服务
     * @param string $abstract
     * @param mixed $concrete
     * @return $this
     */
    public function singleton(string $abstract, $concrete = null): self
    {
        $this->container->singleton($abstract, $concrete);
        return $this;
    }

    /**
     * 绑定实例
     * @param string $abstract
     * @param mixed $instance
     * @return $this
     */
    public function instance(string $abstract, $instance): self
    {
        $this->container->instance($abstract, $instance);
        return $this;
    }

    /**
     * 设置别名
     * @param string $alias
     * @param string $abstract
     * @return $this
     */
    public function alias(string $alias, string $abstract): self
    {
        $this->container->alias($alias, $abstract);
        return $this;
    }

    /**
     * 获取应用程序版本
     * @return string
     */
    public function version(): string
    {
        return '2.0.0';
    }

    /**
     * 获取应用程序环境
     * @return string
     */
    public function environment(): string
    {
        return env('APP_ENV', 'production');
    }

    /**
     * 检查是否为调试模式
     * @return bool
     */
    public function isDebug(): bool
    {
        return env('APP_DEBUG', false);
    }

    /**
     * 获取应用程序状态
     * @return array
     */
    public function getStatus(): array
    {
        return [
            'version' => $this->version(),
            'environment' => $this->environment(),
            'debug' => $this->isDebug(),
            'booted' => $this->booted,
            'providers' => [
                'registered' => count($this->registeredProviders),
                'booted' => count($this->bootedProviders),
            ],
            'services' => [
                'basic_ai' => $this->bound('ai.basic'),
                'langchain' => $this->bound('ai.langchain'),
                'unified' => $this->bound('ai.unified'),
                'memory' => $this->bound('ai.memory'),
                'cache' => $this->bound('ai.cache'),
                'events' => $this->bound('ai.events'),
                'metrics' => $this->bound('ai.metrics'),
            ],
        ];
    }

    /**
     * 重置应用程序
     */
    public function reset(): void
    {
        $this->container->flush();
        $this->registeredProviders = [];
        $this->bootedProviders = [];
        $this->booted = false;
        $this->registerCoreProviders();
    }

    /**
     * 魔术方法：从容器获取服务
     * @param string $name
     * @return mixed
     */
    public function __get(string $name)
    {
        return $this->container->make($name);
    }

    /**
     * 魔术方法：向容器设置服务
     * @param string $name
     * @param mixed $value
     */
    public function __set(string $name, $value): void
    {
        $this->container->instance($name, $value);
    }
}
