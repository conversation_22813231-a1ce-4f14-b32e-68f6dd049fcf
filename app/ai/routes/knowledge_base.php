<?php

use think\facade\Route;

/**
 * AI知识库路由配置
 */

// AI知识库路由组
Route::group('ai/kb', function () {
    
    // 智能问答
    Route::post('ask', 'app\ai\controllers\KnowledgeBaseController@ask');
    
    // 批量问答
    Route::post('batch-ask', 'app\ai\controllers\KnowledgeBaseController@batchAsk');
    
    // 搜索问题
    Route::get('search', 'app\ai\controllers\KnowledgeBaseController@search');
    
    // 获取热门问题
    Route::get('popular', 'app\ai\controllers\KnowledgeBaseController@popular');
    
    // 按分类获取问题
    Route::get('category/:category_id', 'app\ai\controllers\KnowledgeBaseController@category');
    
    // 获取问题详情
    Route::get('detail/:id', 'app\ai\controllers\KnowledgeBaseController@detail');
    
    // 获取统计信息
    Route::get('stats', 'app\ai\controllers\KnowledgeBaseController@stats');
    
    // 清除会话
    Route::delete('session/:session_id', 'app\ai\controllers\KnowledgeBaseController@clearSession');
    
})->middleware([
    // 这里可以添加认证中间件
    // 'auth',
    // 'throttle:60,1' // 限流：每分钟60次请求
]);

// 兼容性路由（如果需要）
Route::group('help/ai', function () {
    
    Route::post('ask', 'app\ai\controllers\KnowledgeBaseController@ask');
    Route::get('popular', 'app\ai\controllers\KnowledgeBaseController@popular');
    Route::get('search', 'app\ai\controllers\KnowledgeBaseController@search');
    
});

// API文档路由
Route::get('ai/kb/docs', function () {
    $docs = [
        'title' => 'AI知识库API文档',
        'version' => '1.0.0',
        'description' => 'AI智能知识库系统接口文档',
        'base_url' => '/ai/kb',
        'endpoints' => [
            'query' => [
                'POST /ask' => [
                    'description' => '智能问答',
                    'parameters' => [
                        'question' => 'string, required - 用户问题',
                        'session_id' => 'string, optional - 会话ID',
                        'use_memory' => 'boolean, optional - 是否使用记忆功能'
                    ],
                    'response' => [
                        'answer' => '智能回答',
                        'sources' => '相关知识库来源',
                        'session_id' => '会话ID',
                        'confidence' => '置信度',
                        'suggestions' => '相关建议'
                    ]
                ],
                'POST /batch-ask' => [
                    'description' => '批量问答',
                    'parameters' => [
                        'questions' => 'array, required - 问题列表（最多10个）',
                        'session_id' => 'string, optional - 会话ID'
                    ]
                ],
                'GET /search' => [
                    'description' => '搜索问题',
                    'parameters' => [
                        'keyword' => 'string, required - 搜索关键词',
                        'limit' => 'integer, optional - 返回数量限制'
                    ]
                ]
            ],
            'browse' => [
                'GET /popular' => [
                    'description' => '获取热门问题',
                    'parameters' => [
                        'limit' => 'integer, optional - 返回数量限制（默认10）'
                    ]
                ],
                'GET /category/{category_id}' => [
                    'description' => '按分类获取问题',
                    'parameters' => [
                        'category_id' => 'integer, required - 分类ID',
                        'limit' => 'integer, optional - 返回数量限制（默认20）'
                    ]
                ],
                'GET /detail/{id}' => [
                    'description' => '获取问题详情',
                    'parameters' => [
                        'id' => 'integer, required - 问题ID'
                    ]
                ]
            ],
            'management' => [
                'GET /stats' => [
                    'description' => '获取知识库统计信息',
                    'response' => [
                        'total_questions' => '总问题数',
                        'total_categories' => '总分类数',
                        'popular_questions' => '热门问题',
                        'recent_updates' => '最近更新'
                    ]
                ],
                'DELETE /session/{session_id}' => [
                    'description' => '清除用户会话',
                    'parameters' => [
                        'session_id' => 'string, required - 会话ID'
                    ]
                ]
            ]
        ],
        'examples' => [
            'ask_question' => [
                'url' => 'POST /ai/kb/ask',
                'request' => [
                    'question' => '如何重置密码？',
                    'session_id' => 'user_12345',
                    'use_memory' => true
                ],
                'response' => [
                    'code' => 200,
                    'message' => '查询成功',
                    'data' => [
                        'answer' => '您可以通过以下步骤重置密码：1. 点击登录页面的"忘记密码"链接...',
                        'sources' => [
                            [
                                'id' => 1,
                                'title' => '密码重置指南',
                                'relevance_score' => 0.95
                            ]
                        ],
                        'session_id' => 'user_12345',
                        'confidence' => 0.9,
                        'suggestions' => [
                            ['title' => '账户安全设置', 'id' => 2],
                            ['title' => '登录问题解决', 'id' => 3]
                        ]
                    ]
                ]
            ],
            'search_questions' => [
                'url' => 'GET /ai/kb/search?keyword=密码&limit=5',
                'response' => [
                    'code' => 200,
                    'message' => '搜索成功',
                    'data' => [
                        'results' => [
                            ['id' => 1, 'title' => '密码重置指南'],
                            ['id' => 2, 'title' => '密码安全设置']
                        ],
                        'suggestions' => [
                            ['title' => '账户安全', 'id' => 3]
                        ]
                    ]
                ]
            ],
            'batch_ask' => [
                'url' => 'POST /ai/kb/batch-ask',
                'request' => [
                    'questions' => [
                        '如何重置密码？',
                        '如何修改个人信息？',
                        '如何联系客服？'
                    ],
                    'session_id' => 'batch_12345'
                ]
            ]
        ],
        'features' => [
            '智能问答：基于AI的自然语言理解和回答',
            '语义搜索：支持关键词和语义相似度搜索',
            '上下文记忆：保持对话上下文，提供连贯回答',
            '置信度评估：评估回答的可信度',
            '相关建议：提供相关问题建议',
            '批量处理：支持批量问题查询',
            '分类浏览：按分类浏览知识库',
            '热门问题：展示热门和常见问题',
            '实时统计：知识库使用统计',
            '会话管理：支持会话清除和管理'
        ],
        'integration' => [
            '与现有帮助系统无缝集成',
            '支持多种客户端接入',
            '提供RESTful API接口',
            '支持WebSocket实时通信',
            '兼容现有认证系统',
            '支持限流和安全控制'
        ],
        'response_format' => [
            'success' => [
                'code' => 200,
                'message' => '请求成功',
                'data' => '响应数据'
            ],
            'error' => [
                'code' => '错误码（400/404/500等）',
                'message' => '错误信息',
                'data' => null
            ]
        ]
    ];
    
    return json([
        'code' => 200,
        'message' => '获取成功',
        'data' => $docs
    ]);
});
