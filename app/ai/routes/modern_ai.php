<?php

use think\facade\Route;

/**
 * 现代化AI服务路由
 */

// AI服务路由组
Route::group('ai', function () {

    // 聊天相关接口
    Route::post('chat', 'app\ai\controllers\ModernAiController@chat');
    Route::post('chat/batch', 'app\ai\controllers\ModernAiController@batchChat');
    Route::post('chat/stream', 'app\ai\controllers\ModernAiController@streamChat');

    // 系统管理接口
    Route::get('status', 'app\ai\controllers\ModernAiController@status');
    Route::get('health', 'app\ai\controllers\ModernAiController@health');
    Route::get('metrics', 'app\ai\controllers\ModernAiController@metrics');

    // 缓存管理
    Route::delete('cache', 'app\ai\controllers\ModernAiController@clearCache');

})->middleware([
    'app\ai\middleware\AiServiceMiddleware'
]);

// 管理员专用路由
Route::group('ai/admin', function () {
    
    // 配置管理
    Route::get('config', function () {
        $config = \app\ai\config\ConfigManager::export();
        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $config
        ]);
    });
    
    Route::post('config', function () {
        $request = request();
        $config = $request->param('config', []);
        \app\ai\config\ConfigManager::import(['config' => $config]);
        
        return json([
            'code' => 200,
            'message' => '配置更新成功',
            'data' => null
        ]);
    });
    
    // 配置验证
    Route::get('config/validate', function () {
        $errors = \app\ai\config\ConfigManager::validate();
        
        return json([
            'code' => 200,
            'message' => '验证完成',
            'data' => [
                'valid' => empty($errors),
                'errors' => $errors
            ]
        ]);
    });
    
    // 应用管理
    Route::get('application/status', function () {
        $app = new \app\ai\bootstrap\AiApplication();
        $app->boot();
        
        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $app->getStatus()
        ]);
    });
    
    Route::post('application/reset', function () {
        $app = new \app\ai\bootstrap\AiApplication();
        $app->reset();
        $app->boot();
        
        return json([
            'code' => 200,
            'message' => '应用重置成功',
            'data' => null
        ]);
    });
    
    // 指标导出
    Route::get('metrics/export', function () {
        $app = new \app\ai\bootstrap\AiApplication();
        $app->boot();
        $metrics = $app->make('ai.metrics');
        $metrics->exportToLog();
        
        return json([
            'code' => 200,
            'message' => '指标导出成功',
            'data' => null
        ]);
    });
    
    // 缓存统计
    Route::get('cache/stats', function () {
        $app = new \app\ai\bootstrap\AiApplication();
        $app->boot();
        $cache = $app->make('ai.cache');
        
        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $cache->getStats()
        ]);
    });
    
})->middleware([
    'app\ai\middleware\AiServiceMiddleware',
    // 这里可以添加管理员权限验证中间件
]);

// 开发和测试路由
Route::group('ai/dev', function () {
    
    // 运行示例
    Route::get('example', function () {
        try {
            ob_start();
            $example = new \app\ai\examples\ModernArchitectureExample();
            $example->runAll();
            $output = ob_get_clean();
            
            return json([
                'code' => 200,
                'message' => '示例运行完成',
                'data' => ['output' => $output]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '示例运行失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    // 测试连接
    Route::get('test', function () {
        try {
            $app = new \app\ai\bootstrap\AiApplication();
            $app->boot();
            $aiService = $app->make('ai.unified');
            
            $response = $aiService->process('测试连接', [
                'provider' => 'deepseek',
                'cache' => false,
            ]);
            
            return json([
                'code' => 200,
                'message' => '测试成功',
                'data' => [
                    'response' => substr($response->content, 0, 100) . '...',
                    'service' => $response->service,
                    'duration' => $response->duration,
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '测试失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
})->middleware([
    'app\ai\middleware\AiServiceMiddleware'
]);

// API文档路由
Route::get('ai/docs', function () {
    $docs = [
        'version' => '2.0.0',
        'title' => 'AI服务API文档',
        'description' => '现代化AI服务接口文档',
        'endpoints' => [
            'main' => [
                'POST /ai/chat' => '智能聊天接口',
                'POST /ai/chat/batch' => '批量聊天接口',
                'POST /ai/chat/stream' => '流式聊天接口',
                'GET /ai/status' => '服务状态',
                'GET /ai/health' => '健康检查',
                'GET /ai/metrics' => '性能指标',
                'DELETE /ai/cache' => '清除缓存',
            ],
            'admin' => [
                'GET /ai/admin/config' => '获取配置',
                'POST /ai/admin/config' => '更新配置',
                'GET /ai/admin/config/validate' => '验证配置',
                'GET /ai/admin/application/status' => '应用状态',
                'POST /ai/admin/application/reset' => '重置应用',
            ],
            'dev' => [
                'GET /ai/dev/example' => '运行示例',
                'GET /ai/dev/test' => '测试连接',
            ]
        ],
        'features' => [
            '依赖注入容器',
            '事件驱动架构',
            '智能路由',
            '性能监控',
            '缓存管理',
            '配置管理',
            '中间件支持',
            '批量处理',
            '健康检查',
        ]
    ];
    
    return json([
        'code' => 200,
        'message' => '获取成功',
        'data' => $docs
    ]);
});
