<?php

namespace app\ai\memory;

use app\ai\interfaces\MemoryInterface;
use app\ai\config\ConfigManager;
use app\ai\utils\Logger;
use app\ai\services\UnifiedAiService;
use app\ai\exceptions\AiServiceException;

/**
 * 对话缓冲记忆实现
 * 管理对话历史和上下文信息
 */
class ConversationBufferMemory implements MemoryInterface
{
    /**
     * 记忆存储
     * @var array
     */
    protected array $memory = [];
    
    /**
     * 上下文存储
     * @var array
     */
    protected array $context = [];
    
    /**
     * 基础AI服务（用于总结）
     * @var BasicAiService|null
     */
    protected ?BasicAiService $aiService = null;
    
    /**
     * 构造函数
     * @param BasicAiService|null $aiService
     */
    public function __construct(?BasicAiService $aiService = null)
    {
        $this->aiService = $aiService;
    }
    
    /**
     * 保存对话消息
     * @param string $sessionId 会话ID
     * @param array $message 消息内容
     * @return void
     */
    public function saveMessage(string $sessionId, array $message): void
    {
        if (!isset($this->memory[$sessionId])) {
            $this->memory[$sessionId] = [];
        }
        
        $message['timestamp'] = $message['timestamp'] ?? time();
        $this->memory[$sessionId][] = $message;
        
        // 限制记忆长度，避免内存过大
        $maxMemoryLength = ConfigManager::get('memory.max_length', 50);
        if (count($this->memory[$sessionId]) > $maxMemoryLength) {
            $this->memory[$sessionId] = array_slice($this->memory[$sessionId], -$maxMemoryLength);
        }
        
        Logger::info('Message saved to memory', [
            'session_id' => $sessionId,
            'message_count' => count($this->memory[$sessionId])
        ]);
    }
    
    /**
     * 获取对话历史
     * @param string $sessionId 会话ID
     * @param int $limit 限制数量
     * @return array
     */
    public function getHistory(string $sessionId, int $limit = 10): array
    {
        $history = $this->memory[$sessionId] ?? [];
        
        if ($limit > 0 && count($history) > $limit) {
            $history = array_slice($history, -$limit);
        }
        
        return $history;
    }
    
    /**
     * 清除会话记忆
     * @param string $sessionId 会话ID
     * @return void
     */
    public function clearSession(string $sessionId): void
    {
        unset($this->memory[$sessionId]);
        unset($this->context[$sessionId]);
        
        Logger::info('Session memory cleared', ['session_id' => $sessionId]);
    }
    
    /**
     * 获取记忆变量
     * @param string $sessionId 会话ID
     * @return array
     */
    public function getMemoryVariables(string $sessionId): array
    {
        $history = $this->getHistory($sessionId);
        $context = $this->context[$sessionId] ?? [];
        
        $variables = [
            'history' => $history,
            'chat_history' => $this->formatChatHistory($history),
            'context' => $context
        ];
        
        // 添加最近的消息
        if (!empty($history)) {
            $lastMessage = end($history);
            $variables['last_message'] = $lastMessage;
            $variables['last_input'] = $lastMessage['input'] ?? '';
            $variables['last_output'] = $lastMessage['output'] ?? '';
        }
        
        return $variables;
    }
    
    /**
     * 保存上下文信息
     * @param string $sessionId 会话ID
     * @param string $key 键名
     * @param mixed $value 值
     * @return void
     */
    public function saveContext(string $sessionId, string $key, $value): void
    {
        if (!isset($this->context[$sessionId])) {
            $this->context[$sessionId] = [];
        }
        
        $this->context[$sessionId][$key] = $value;
        
        Logger::info('Context saved', [
            'session_id' => $sessionId,
            'key' => $key
        ]);
    }
    
    /**
     * 获取上下文信息
     * @param string $sessionId 会话ID
     * @param string $key 键名
     * @return mixed
     */
    public function getContext(string $sessionId, string $key)
    {
        return $this->context[$sessionId][$key] ?? null;
    }
    
    /**
     * 总结对话历史
     * @param string $sessionId 会话ID
     * @return string
     */
    public function summarizeHistory(string $sessionId): string
    {
        $history = $this->getHistory($sessionId);
        
        if (empty($history)) {
            return '暂无对话历史';
        }
        
        if ($this->aiService === null) {
            // 简单的文本总结
            return $this->simpleSummarize($history);
        }
        
        try {
            // 使用AI服务进行智能总结
            $conversationText = $this->formatChatHistory($history);
            $prompt = "请总结以下对话的主要内容和关键信息：\n\n{$conversationText}";
            
            return $this->aiService->generateText($prompt);
        } catch (AiServiceException $e) {
            Logger::error('AI summarization failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            
            return $this->simpleSummarize($history);
        }
    }
    
    /**
     * 格式化聊天历史
     * @param array $history
     * @return string
     */
    protected function formatChatHistory(array $history): string
    {
        $formatted = [];
        
        foreach ($history as $entry) {
            if (isset($entry['input'])) {
                $formatted[] = "用户: {$entry['input']}";
            }
            if (isset($entry['output'])) {
                $formatted[] = "助手: {$entry['output']}";
            }
        }
        
        return implode("\n", $formatted);
    }
    
    /**
     * 简单总结
     * @param array $history
     * @return string
     */
    protected function simpleSummarize(array $history): string
    {
        $messageCount = count($history);
        $topics = [];
        
        foreach ($history as $entry) {
            if (isset($entry['input'])) {
                // 提取关键词
                $words = explode(' ', $entry['input']);
                $topics = array_merge($topics, array_slice($words, 0, 3));
            }
        }
        
        $uniqueTopics = array_unique($topics);
        $topicsText = implode(', ', array_slice($uniqueTopics, 0, 5));
        
        return "对话包含 {$messageCount} 条消息，主要涉及: {$topicsText}";
    }
    
    /**
     * 获取会话统计信息
     * @param string $sessionId 会话ID
     * @return array
     */
    public function getSessionStats(string $sessionId): array
    {
        $history = $this->getHistory($sessionId, 0); // 获取全部历史
        $context = $this->context[$sessionId] ?? [];
        
        return [
            'message_count' => count($history),
            'context_keys' => array_keys($context),
            'first_message_time' => !empty($history) ? $history[0]['timestamp'] ?? null : null,
            'last_message_time' => !empty($history) ? end($history)['timestamp'] ?? null : null
        ];
    }
    
    /**
     * 清除所有记忆
     * @return void
     */
    public function clearAll(): void
    {
        $this->memory = [];
        $this->context = [];
        
        Logger::info('All memory cleared');
    }
}