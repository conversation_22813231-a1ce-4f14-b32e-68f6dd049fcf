<?php

namespace app\ai\controllers;

use app\ai\container\ServiceContainer;
use app\ai\services\UnifiedAiService;
use app\ai\events\EventDispatcher;
use app\ai\cache\CacheManager;
use app\ai\monitoring\MetricsCollector;
use app\ai\exceptions\AiServiceException;
use think\Request;
use think\Response;
use think\facade\Validate;

/**
 * 现代化AI控制器
 * 使用依赖注入和现代化架构
 */
class ModernAiController
{
    /**
     * 服务容器
     * @var ServiceContainer
     */
    protected ServiceContainer $container;

    /**
     * 统一AI服务
     * @var UnifiedAiService
     */
    protected UnifiedAiService $aiService;

    /**
     * 事件调度器
     * @var EventDispatcher
     */
    protected EventDispatcher $events;

    /**
     * 缓存管理器
     * @var CacheManager
     */
    protected CacheManager $cache;

    /**
     * 指标收集器
     * @var MetricsCollector
     */
    protected MetricsCollector $metrics;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->container = ServiceContainer::getInstance();
        $this->aiService = $this->container->make('ai.unified');
        $this->events = $this->container->make('ai.events');
        $this->cache = $this->container->make('ai.cache');
        $this->metrics = $this->container->make('ai.metrics');
    }

    /**
     * 智能聊天接口
     * @param Request $request
     * @return Response
     */
    public function chat(Request $request): Response
    {
        try {
            // 验证请求参数
            $validate = $this->validateChatRequest($request);
            if ($validate !== true) {
                return $this->errorResponse(400, $validate);
            }

            $input = $request->param('input');
            $options = $this->extractOptions($request);

            // 处理请求
            $response = $this->aiService->process($input, $options);

            return $this->successResponse([
                'content' => $response->content,
                'service' => $response->service,
                'duration' => $response->duration,
                'request_id' => $response->requestId,
            ]);

        } catch (AiServiceException $e) {
            return $this->errorResponse(500, 'AI服务请求失败：' . $e->getMessage());
        } catch (\Exception $e) {
            return $this->errorResponse(500, '服务器内部错误：' . $e->getMessage());
        }
    }

    /**
     * 批量聊天接口
     * @param Request $request
     * @return Response
     */
    public function batchChat(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'inputs' => 'require|array',
                'inputs.*' => 'require|string',
            ]);

            if (!$validate->check($request->param())) {
                return $this->errorResponse(400, '参数验证失败：' . $validate->getError());
            }

            $inputs = $request->param('inputs');
            $options = $this->extractOptions($request);

            // 批量处理
            $responses = $this->aiService->batchProcess($inputs, $options);

            $results = [];
            foreach ($responses as $index => $response) {
                $results[] = [
                    'index' => $index,
                    'content' => $response->content,
                    'service' => $response->service,
                    'duration' => $response->duration,
                    'request_id' => $response->requestId,
                    'success' => $response->isSuccess(),
                    'error' => $response->error,
                ];
            }

            return $this->successResponse([
                'results' => $results,
                'total' => count($results),
            ]);

        } catch (\Exception $e) {
            return $this->errorResponse(500, '批量处理失败：' . $e->getMessage());
        }
    }

    /**
     * 流式聊天接口
     * @param Request $request
     * @return Response
     */
    public function streamChat(Request $request): Response
    {
        try {
            $validate = $this->validateChatRequest($request);
            if ($validate !== true) {
                return $this->errorResponse(400, $validate);
            }

            $input = $request->param('input');
            $options = $this->extractOptions($request);

            // 设置SSE响应头
            $response = Response::create('', 'html', 200, [
                'Content-Type' => 'text/event-stream',
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',
                'Access-Control-Allow-Origin' => '*',
            ]);

            // 输出缓冲区处理
            if (ob_get_level()) {
                ob_end_clean();
            }

            // 发送连接事件
            echo "data: " . json_encode(['type' => 'connected']) . "\n\n";
            flush();

            // 这里需要实现流式处理逻辑
            // 由于当前的UnifiedAiService还不支持流式，这里先返回普通响应
            $aiResponse = $this->aiService->process($input, $options);
            
            echo "data: " . json_encode([
                'type' => 'content',
                'content' => $aiResponse->content
            ]) . "\n\n";
            flush();

            echo "data: " . json_encode(['type' => 'done']) . "\n\n";
            flush();

            return $response;

        } catch (\Exception $e) {
            echo "data: " . json_encode([
                'type' => 'error',
                'error' => $e->getMessage()
            ]) . "\n\n";
            flush();

            return Response::create('', 'html', 500);
        }
    }

    /**
     * 获取服务状态
     * @return Response
     */
    public function status(): Response
    {
        try {
            $status = $this->aiService->getStatus();
            $metrics = $this->metrics->getSummary();
            $cacheStats = $this->cache->getStats();

            return $this->successResponse([
                'services' => $status,
                'metrics' => $metrics,
                'cache' => $cacheStats,
                'timestamp' => time(),
            ]);

        } catch (\Exception $e) {
            return $this->errorResponse(500, '获取状态失败：' . $e->getMessage());
        }
    }

    /**
     * 获取指标数据
     * @return Response
     */
    public function metrics(): Response
    {
        try {
            $metrics = $this->metrics->getAllMetrics();
            return $this->successResponse($metrics);

        } catch (\Exception $e) {
            return $this->errorResponse(500, '获取指标失败：' . $e->getMessage());
        }
    }

    /**
     * 清除缓存
     * @return Response
     */
    public function clearCache(): Response
    {
        try {
            $this->cache->clearAiCache();
            return $this->successResponse(['message' => '缓存清除成功']);

        } catch (\Exception $e) {
            return $this->errorResponse(500, '清除缓存失败：' . $e->getMessage());
        }
    }

    /**
     * 健康检查
     * @return Response
     */
    public function health(): Response
    {
        try {
            return $this->successResponse([
                'status' => 'healthy',
                'timestamp' => time(),
                'version' => '2.0.0',
            ]);

        } catch (\Exception $e) {
            return $this->errorResponse(500, '健康检查失败：' . $e->getMessage());
        }
    }

    /**
     * 验证聊天请求
     * @param Request $request
     * @return bool|string
     */
    protected function validateChatRequest(Request $request)
    {
        $validate = Validate::rule([
            'input' => 'require|string',
            'provider' => 'string',
            'model' => 'string',
            'temperature' => 'float|between:0,2',
            'max_tokens' => 'integer|gt:0',
            'session_id' => 'string',
            'type' => 'in:simple,memory,tool,reasoning',
        ]);

        if (!$validate->check($request->param())) {
            return '参数验证失败：' . $validate->getError();
        }

        return true;
    }

    /**
     * 提取选项参数
     * @param Request $request
     * @return array
     */
    protected function extractOptions(Request $request): array
    {
        $options = [];
        
        $optionalParams = [
            'provider', 'model', 'temperature', 'max_tokens', 'top_p',
            'frequency_penalty', 'presence_penalty', 'session_id', 'type',
            'tools', 'cache', 'cache_ttl'
        ];

        foreach ($optionalParams as $param) {
            if ($request->has($param)) {
                $options[$param] = $request->param($param);
            }
        }

        return $options;
    }

    /**
     * 成功响应
     * @param mixed $data
     * @return Response
     */
    protected function successResponse($data): Response
    {
        return json([
            'code' => 200,
            'message' => '请求成功',
            'data' => $data,
            'timestamp' => time(),
        ]);
    }

    /**
     * 错误响应
     * @param int $code
     * @param string $message
     * @return Response
     */
    protected function errorResponse(int $code, string $message): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => null,
            'timestamp' => time(),
        ], $code);
    }
}
