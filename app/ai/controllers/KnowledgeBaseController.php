<?php

namespace app\ai\controllers;

use app\ai\services\KnowledgeBaseService;
use app\ai\utils\Logger;
use think\Request;
use think\Response;

/**
 * AI知识库控制器
 */
class KnowledgeBaseController
{
    /**
     * 知识库服务
     * @var KnowledgeBaseService
     */
    protected $knowledgeBaseService;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->knowledgeBaseService = new KnowledgeBaseService();
    }
    
    /**
     * 智能问答
     * @param Request $request
     * @return Response
     */
    public function ask(Request $request): Response
    {
        try {
            $question = $request->param('question', '');
            $sessionId = $request->param('session_id', '');
            $useMemory = $request->param('use_memory', true);
            
            if (empty($question)) {
                return json([
                    'code' => 400,
                    'message' => '问题不能为空',
                    'data' => null
                ]);
            }
            
            // 记录请求
            Logger::info('Knowledge base query', [
                'question' => $question,
                'session_id' => $sessionId,
                'ip' => $request->ip()
            ]);
            
            $result = $this->knowledgeBaseService->ask($question, [
                'session_id' => $sessionId,
                'use_memory' => $useMemory
            ]);
            
            if ($result['success']) {
                return json([
                    'code' => 200,
                    'message' => '查询成功',
                    'data' => [
                        'answer' => $result['content'],
                        'sources' => $result['sources'],
                        'session_id' => $result['session_id'],
                        'confidence' => $result['confidence'],
                        'suggestions' => $result['suggestions']
                    ]
                ]);
            } else {
                return json([
                    'code' => 500,
                    'message' => '查询失败',
                    'data' => [
                        'answer' => $result['content'],
                        'error' => $result['error'] ?? null
                    ]
                ]);
            }
            
        } catch (\Exception $e) {
            Logger::error('Knowledge base query error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return json([
                'code' => 500,
                'message' => '系统错误',
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取热门问题
     * @param Request $request
     * @return Response
     */
    public function popular(Request $request): Response
    {
        try {
            $limit = (int)$request->param('limit', 10);
            $limit = min(50, max(1, $limit)); // 限制范围
            
            $questions = $this->knowledgeBaseService->getPopularQuestions($limit);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $questions
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Get popular questions error', [
                'error' => $e->getMessage()
            ]);
            
            return json([
                'code' => 500,
                'message' => '获取失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 按分类获取问题
     * @param Request $request
     * @return Response
     */
    public function category(Request $request): Response
    {
        try {
            $categoryId = (int)$request->param('category_id', 0);
            $limit = (int)$request->param('limit', 20);
            $limit = min(100, max(1, $limit));
            
            if ($categoryId <= 0) {
                return json([
                    'code' => 400,
                    'message' => '分类ID无效',
                    'data' => []
                ]);
            }
            
            $questions = $this->knowledgeBaseService->getQuestionsByCategory($categoryId, $limit);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $questions
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Get questions by category error', [
                'category_id' => $request->param('category_id'),
                'error' => $e->getMessage()
            ]);
            
            return json([
                'code' => 500,
                'message' => '获取失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 获取问题详情
     * @param Request $request
     * @return Response
     */
    public function detail(Request $request): Response
    {
        try {
            $helpId = (int)$request->param('id', 0);
            
            if ($helpId <= 0) {
                return json([
                    'code' => 400,
                    'message' => '问题ID无效',
                    'data' => null
                ]);
            }
            
            $detail = $this->knowledgeBaseService->getQuestionDetail($helpId);
            
            if (!$detail) {
                return json([
                    'code' => 404,
                    'message' => '问题不存在',
                    'data' => null
                ]);
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $detail
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Get question detail error', [
                'help_id' => $request->param('id'),
                'error' => $e->getMessage()
            ]);
            
            return json([
                'code' => 500,
                'message' => '获取失败',
                'data' => null
            ]);
        }
    }
    
    /**
     * 搜索问题
     * @param Request $request
     * @return Response
     */
    public function search(Request $request): Response
    {
        try {
            $keyword = $request->param('keyword', '');
            $limit = (int)$request->param('limit', 10);
            $limit = min(50, max(1, $limit));
            
            if (empty($keyword)) {
                return json([
                    'code' => 400,
                    'message' => '搜索关键词不能为空',
                    'data' => []
                ]);
            }
            
            // 使用知识库服务的搜索功能
            $result = $this->knowledgeBaseService->ask($keyword, [
                'session_id' => 'search_' . uniqid(),
                'use_memory' => false
            ]);
            
            return json([
                'code' => 200,
                'message' => '搜索成功',
                'data' => [
                    'results' => $result['sources'] ?? [],
                    'suggestions' => $result['suggestions'] ?? []
                ]
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Search questions error', [
                'keyword' => $request->param('keyword'),
                'error' => $e->getMessage()
            ]);
            
            return json([
                'code' => 500,
                'message' => '搜索失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 获取知识库统计信息
     * @param Request $request
     * @return Response
     */
    public function stats(Request $request): Response
    {
        try {
            $stats = [
                'total_questions' => \app\model\Help::where('enabled', 1)->count(),
                'total_categories' => \app\model\Category::where('enabled', 1)->count(),
                'popular_questions' => $this->knowledgeBaseService->getPopularQuestions(5),
                'recent_updates' => \app\model\Help::where('enabled', 1)
                    ->order('updatetime', 'desc')
                    ->limit(5)
                    ->field('id,title,updatetime')
                    ->select()
                    ->toArray()
            ];
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Get knowledge base stats error', [
                'error' => $e->getMessage()
            ]);
            
            return json([
                'code' => 500,
                'message' => '获取失败',
                'data' => null
            ]);
        }
    }
    
    /**
     * 批量问答
     * @param Request $request
     * @return Response
     */
    public function batchAsk(Request $request): Response
    {
        try {
            $questions = $request->param('questions', []);
            $sessionId = $request->param('session_id', 'batch_' . uniqid());
            
            if (empty($questions) || !is_array($questions)) {
                return json([
                    'code' => 400,
                    'message' => '问题列表不能为空',
                    'data' => []
                ]);
            }
            
            if (count($questions) > 10) {
                return json([
                    'code' => 400,
                    'message' => '批量问题数量不能超过10个',
                    'data' => []
                ]);
            }
            
            $results = [];
            foreach ($questions as $index => $question) {
                if (empty($question)) {
                    continue;
                }
                
                $result = $this->knowledgeBaseService->ask($question, [
                    'session_id' => $sessionId . '_' . $index,
                    'use_memory' => false
                ]);
                
                $results[] = [
                    'question' => $question,
                    'answer' => $result['content'],
                    'confidence' => $result['confidence'] ?? 0,
                    'sources' => $result['sources'] ?? []
                ];
            }
            
            return json([
                'code' => 200,
                'message' => '批量查询成功',
                'data' => $results
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Batch ask error', [
                'error' => $e->getMessage()
            ]);
            
            return json([
                'code' => 500,
                'message' => '批量查询失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 清除会话
     * @param Request $request
     * @return Response
     */
    public function clearSession(Request $request): Response
    {
        try {
            $sessionId = $request->param('session_id', '');
            
            if (empty($sessionId)) {
                return json([
                    'code' => 400,
                    'message' => '会话ID不能为空',
                    'data' => null
                ]);
            }
            
            // 清除会话记忆
            $memory = new \app\ai\memory\MySqlMemory();
            $memory->clear($sessionId);
            
            return json([
                'code' => 200,
                'message' => '会话已清除',
                'data' => null
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Clear session error', [
                'session_id' => $request->param('session_id'),
                'error' => $e->getMessage()
            ]);
            
            return json([
                'code' => 500,
                'message' => '清除会话失败',
                'data' => null
            ]);
        }
    }
}
