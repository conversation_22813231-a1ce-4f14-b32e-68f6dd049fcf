<?php

namespace app\ai\middleware;

use app\ai\container\ServiceContainer;
use app\ai\events\EventDispatcher;
use app\ai\monitoring\MetricsCollector;
use think\Request;
use think\Response;
use think\facade\Config;
use think\facade\Cache;
use app\ai\utils\Logger;
use app\ai\exceptions\AiRateLimitException;
use app\ai\exceptions\AiContentFilterException;

/**
 * AI服务中间件
 * 提供请求验证、限流、内容过滤等功能
 */
class AiServiceMiddleware
{
    /**
     * 服务容器
     * @var ServiceContainer
     */
    protected ServiceContainer $container;

    /**
     * 中间件管道
     * @var array
     */
    protected array $middlewares = [];

    /**
     * 构造函数
     * @param ServiceContainer $container
     */
    public function __construct(ServiceContainer $container)
    {
        $this->container = $container;
    }

    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        $startTime = microtime(true);

        try {
            // 执行中间件管道
            $response = $this->runMiddlewarePipeline($request, $next);

            // 记录成功指标
            $duration = (microtime(true) - $startTime) * 1000;
            $this->recordMetrics($request, $response, $duration, true);

            return $response;

        } catch (\Exception $e) {
            // 记录失败指标
            $duration = (microtime(true) - $startTime) * 1000;
            $this->recordMetrics($request, null, $duration, false);

            Logger::error('AI Service Middleware Error', [
                'error' => $e->getMessage(),
                'request_uri' => $request->url(),
                'request_method' => $request->method(),
                'client_ip' => $request->ip(),
                'duration' => $duration,
            ]);

            return json([
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 运行中间件管道
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    protected function runMiddlewarePipeline(Request $request, \Closure $next): Response
    {
        $pipeline = array_reduce(
            array_reverse($this->middlewares),
            function ($stack, $middleware) {
                return function ($request) use ($stack, $middleware) {
                    return $middleware($request, $stack);
                };
            },
            $next
        );

        return $pipeline($request);
    }

    /**
     * 记录指标
     * @param Request $request
     * @param Response|null $response
     * @param float $duration
     * @param bool $success
     */
    protected function recordMetrics(Request $request, ?Response $response, float $duration, bool $success): void
    {
        try {
            $metrics = $this->container->make('ai.metrics');
            $metrics->timer('ai.middleware.duration', $duration, [
                'method' => $request->method(),
                'path' => $request->pathinfo(),
                'status' => $success ? 'success' : 'error',
            ]);

            $metrics->counter('ai.middleware.requests', 1, [
                'method' => $request->method(),
                'status' => $success ? 'success' : 'error',
            ]);
        } catch (\Exception $e) {
            // 忽略指标记录错误
        }
    }

    /**
     * 注册默认中间件
     */
    public function registerDefaultMiddleware(): void
    {
        $this->middlewares = [
            [$this, 'rateLimitMiddleware'],
            [$this, 'contentFilterMiddleware'],
            [$this, 'requestValidationMiddleware'],
            [$this, 'loggingMiddleware'],
        ];
    }

    /**
     * 添加中间件
     * @param callable $middleware
     */
    public function addMiddleware(callable $middleware): void
    {
        $this->middlewares[] = $middleware;
    }

    /**
     * 限流中间件
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function rateLimitMiddleware(Request $request, \Closure $next): Response
    {
        $this->checkRateLimit($request);
        return $next($request);
    }

    /**
     * 内容过滤中间件
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function contentFilterMiddleware(Request $request, \Closure $next): Response
    {
        $this->filterContent($request);
        return $next($request);
    }

    /**
     * 请求验证中间件
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function requestValidationMiddleware(Request $request, \Closure $next): Response
    {
        $this->validateRequestSize($request);
        return $next($request);
    }

    /**
     * 日志中间件
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function loggingMiddleware(Request $request, \Closure $next): Response
    {
        $this->logRequest($request);
        $response = $next($request);
        $this->logResponse($request, $response);
        return $response;
    }

    /**
     * 检查限流
     * @param Request $request
     * @throws AiRateLimitException
     */
    protected function checkRateLimit(Request $request): void
    {
        if (!Config::get('ai.rate_limit.enabled', true)) {
            return;
        }
        
        $clientIp = $request->ip();
        $userId = $request->header('user-id', $clientIp); // 可以根据实际情况调整用户标识
        
        $rpmLimit = Config::get('ai.rate_limit.requests_per_minute', 60);
        $rphLimit = Config::get('ai.rate_limit.requests_per_hour', 1000);
        
        // 检查每分钟限制
        $minuteKey = 'ai_rate_limit:minute:' . $userId . ':' . date('Y-m-d-H-i');
        $minuteCount = Cache::get($minuteKey, 0);
        
        if ($minuteCount >= $rpmLimit) {
            throw new AiRateLimitException(
                '请求频率过高，请稍后再试',
                '',
                60 - date('s')
            );
        }
        
        // 检查每小时限制
        $hourKey = 'ai_rate_limit:hour:' . $userId . ':' . date('Y-m-d-H');
        $hourCount = Cache::get($hourKey, 0);
        
        if ($hourCount >= $rphLimit) {
            throw new AiRateLimitException(
                '小时请求限制已达上限，请稍后再试',
                '',
                3600 - (time() % 3600)
            );
        }
        
        // 增加计数
        Cache::set($minuteKey, $minuteCount + 1, 60);
        Cache::set($hourKey, $hourCount + 1, 3600);
    }
    
    /**
     * 内容过滤
     * @param Request $request
     * @throws AiContentFilterException
     */
    protected function filterContent(Request $request): void
    {
        if (!Config::get('ai.security.content_filter.enabled', true)) {
            return;
        }
        
        $blockedKeywords = Config::get('ai.security.content_filter.blocked_keywords', []);
        
        if (empty($blockedKeywords)) {
            return;
        }
        
        $messages = $request->param('messages', []);
        
        foreach ($messages as $message) {
            if (!isset($message['content'])) {
                continue;
            }
            
            $content = strtolower($message['content']);
            
            foreach ($blockedKeywords as $keyword) {
                if (strpos($content, strtolower($keyword)) !== false) {
                    throw new AiContentFilterException(
                        '消息内容包含不当词汇，请修改后重试',
                        '',
                        $message['content']
                    );
                }
            }
        }
    }
    
    /**
     * 验证请求大小
     * @param Request $request
     * @throws \Exception
     */
    protected function validateRequestSize(Request $request): void
    {
        $maxMessageLength = Config::get('ai.security.max_message_length', 10000);
        $maxMessagesCount = Config::get('ai.security.max_messages_count', 50);
        
        $messages = $request->param('messages', []);
        
        // 检查消息数量
        if (count($messages) > $maxMessagesCount) {
            throw new \Exception("消息数量超过限制，最多允许 {$maxMessagesCount} 条消息");
        }
        
        // 检查单条消息长度
        foreach ($messages as $message) {
            if (isset($message['content']) && mb_strlen($message['content']) > $maxMessageLength) {
                throw new \Exception("单条消息长度超过限制，最多允许 {$maxMessageLength} 个字符");
            }
        }
    }
    
    /**
     * 记录请求日志
     * @param Request $request
     */
    protected function logRequest(Request $request): void
    {
        if (!Config::get('ai.logging.enabled', true) || !Config::get('ai.logging.log_requests', true)) {
            return;
        }
        
        $logData = [
            'type' => 'request',
            'method' => $request->method(),
            'uri' => $request->url(),
            'client_ip' => $request->ip(),
            'user_agent' => $request->header('user-agent'),
            'timestamp' => date('Y-m-d H:i:s'),
        ];
        
        // 根据日志级别决定是否记录详细参数
        $logLevel = Config::get('ai.logging.level', 'info');
        if (in_array($logLevel, ['debug'])) {
            $logData['params'] = $request->param();
        }
        
        Logger::info('AI Service Request', $logData);
    }
    
    /**
     * 记录响应日志
     * @param Request $request
     * @param Response $response
     */
    protected function logResponse(Request $request, Response $response): void
    {
        if (!Config::get('ai.logging.enabled', true) || !Config::get('ai.logging.log_responses', false)) {
            return;
        }
        
        $logData = [
            'type' => 'response',
            'method' => $request->method(),
            'uri' => $request->url(),
            'status_code' => $response->getCode(),
            'timestamp' => date('Y-m-d H:i:s'),
        ];
        
        // 根据配置决定是否记录响应内容
        $logLevel = Config::get('ai.logging.level', 'info');
        if (in_array($logLevel, ['debug']) && Config::get('ai.logging.log_responses', false)) {
            $logData['response'] = $response->getData();
        }
        
        Logger::info('AI Service Response', $logData);
    }
}