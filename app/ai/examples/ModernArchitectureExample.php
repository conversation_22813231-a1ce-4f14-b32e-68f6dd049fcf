<?php

namespace app\ai\examples;

use app\ai\bootstrap\AiApplication;
use app\ai\config\ConfigManager;
use app\ai\services\UnifiedAiService;
use app\ai\events\EventDispatcher;
use app\ai\cache\CacheManager;
use app\ai\monitoring\MetricsCollector;

/**
 * 现代化架构使用示例
 */
class ModernArchitectureExample
{
    /**
     * AI应用程序
     * @var AiApplication
     */
    protected AiApplication $app;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->app = new AiApplication();
        $this->app->boot();
    }

    /**
     * 基础使用示例
     */
    public function basicUsage(): void
    {
        echo "=== 现代化AI架构基础使用示例 ===\n\n";

        try {
            // 1. 配置管理
            echo "1. 配置管理示例:\n";
            ConfigManager::loadFromEnv();
            ConfigManager::set('default_provider', 'deepseek');
            echo "默认提供商: " . ConfigManager::get('default_provider') . "\n";
            echo "缓存启用: " . (ConfigManager::get('cache.enabled') ? 'true' : 'false') . "\n\n";

            // 2. 依赖注入
            echo "2. 依赖注入示例:\n";
            $aiService = $this->app->make('ai.unified');
            $events = $this->app->make('ai.events');
            $cache = $this->app->make('ai.cache');
            $metrics = $this->app->make('ai.metrics');
            echo "服务已通过容器注入\n\n";

            // 3. 智能聊天
            echo "3. 智能聊天示例:\n";
            $response = $aiService->process('你好，请介绍一下你自己', [
                'provider' => 'deepseek',
                'cache' => true,
            ]);
            echo "响应: " . substr($response->content, 0, 100) . "...\n";
            echo "服务: " . $response->service . "\n";
            echo "耗时: " . $response->duration . "ms\n\n";

            // 4. 带记忆的对话
            echo "4. 带记忆的对话示例:\n";
            $sessionId = 'user_123_session';
            
            $response1 = $aiService->process('我的名字是张三', [
                'session_id' => $sessionId,
                'type' => 'memory',
            ]);
            echo "第一次对话: " . substr($response1->content, 0, 50) . "...\n";

            $response2 = $aiService->process('你还记得我的名字吗？', [
                'session_id' => $sessionId,
                'type' => 'memory',
            ]);
            echo "第二次对话: " . substr($response2->content, 0, 50) . "...\n\n";

            // 5. 批量处理
            echo "5. 批量处理示例:\n";
            $inputs = [
                '1+1等于多少？',
                '今天天气怎么样？',
                '推荐一本好书',
            ];
            
            $responses = $aiService->batchProcess($inputs, [
                'provider' => 'deepseek',
            ]);
            
            foreach ($responses as $index => $response) {
                echo "问题 {$index}: " . substr($response->content, 0, 30) . "...\n";
            }
            echo "\n";

            // 6. 事件系统
            echo "6. 事件系统示例:\n";
            $events->listen('ai.request.completed', function ($event, $payload) {
                echo "事件触发: AI请求完成，耗时 {$payload['duration']}ms\n";
            });
            
            // 触发一个请求来演示事件
            $aiService->process('测试事件系统', ['provider' => 'deepseek']);
            echo "\n";

            // 7. 缓存系统
            echo "7. 缓存系统示例:\n";
            $cacheKey = 'test_cache_key';
            $cache->set($cacheKey, 'cached_value', 300);
            $cachedValue = $cache->get($cacheKey);
            echo "缓存值: " . $cachedValue . "\n";
            echo "缓存统计: " . json_encode($cache->getStats()) . "\n\n";

            // 8. 指标收集
            echo "8. 指标收集示例:\n";
            $metrics->counter('example.requests', 1, ['type' => 'demo']);
            $metrics->timer('example.duration', 150.5, ['type' => 'demo']);
            $summary = $metrics->getSummary();
            echo "指标摘要: " . json_encode($summary) . "\n\n";

            // 9. 应用状态
            echo "9. 应用状态示例:\n";
            $status = $this->app->getStatus();
            echo "应用状态: " . json_encode($status, JSON_PRETTY_PRINT) . "\n\n";

        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 高级功能示例
     */
    public function advancedUsage(): void
    {
        echo "=== 高级功能示例 ===\n\n";

        try {
            $aiService = $this->app->make('ai.unified');

            // 1. 智能路由
            echo "1. 智能路由示例:\n";
            
            // 简单对话 - 会路由到BasicAiService
            $simple = $aiService->process('你好');
            echo "简单对话路由到: " . $simple->service . "\n";
            
            // 需要记忆的对话 - 会路由到LangChainService
            $memory = $aiService->process('记住我的名字是李四', [
                'session_id' => 'test_session'
            ]);
            echo "记忆对话路由到: " . $memory->service . "\n";
            
            // 需要工具的对话 - 会路由到LangChainService
            $tool = $aiService->process('帮我计算 123 + 456');
            echo "工具对话路由到: " . $tool->service . "\n\n";

            // 2. 自定义中间件
            echo "2. 自定义中间件示例:\n";
            $middleware = $this->app->make('ai.middleware');
            
            // 添加自定义中间件
            $middleware->addMiddleware(function ($request, $next) {
                echo "自定义中间件: 请求前处理\n";
                $response = $next($request);
                echo "自定义中间件: 请求后处理\n";
                return $response;
            });
            echo "自定义中间件已添加\n\n";

            // 3. 配置验证
            echo "3. 配置验证示例:\n";
            $errors = ConfigManager::validate();
            if (empty($errors)) {
                echo "配置验证通过\n";
            } else {
                echo "配置错误:\n";
                foreach ($errors as $error) {
                    echo "- " . $error . "\n";
                }
            }
            echo "\n";

            // 4. 性能监控
            echo "4. 性能监控示例:\n";
            $metrics = $this->app->make('ai.metrics');
            
            // 模拟一些请求
            for ($i = 0; $i < 5; $i++) {
                $start = microtime(true);
                $aiService->process("测试请求 {$i}");
                $duration = (microtime(true) - $start) * 1000;
                $metrics->timer('test.requests', $duration);
            }
            
            $allMetrics = $metrics->getAllMetrics();
            echo "性能指标: " . json_encode($allMetrics['timers'], JSON_PRETTY_PRINT) . "\n\n";

            // 5. 事件驱动架构
            echo "5. 事件驱动架构示例:\n";
            $events = $this->app->make('ai.events');
            
            // 注册多个监听器
            $events->listen('custom.event', function ($event, $payload) {
                echo "监听器1: 处理自定义事件\n";
            }, 10); // 高优先级
            
            $events->listen('custom.event', function ($event, $payload) {
                echo "监听器2: 处理自定义事件\n";
            }, 5); // 低优先级
            
            // 触发事件
            $events->dispatch('custom.event', ['data' => 'test']);
            echo "\n";

        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 运行所有示例
     */
    public function runAll(): void
    {
        $this->basicUsage();
        $this->advancedUsage();
        
        echo "=== 示例运行完成 ===\n";
        echo "现代化AI架构提供了以下特性:\n";
        echo "- 依赖注入容器\n";
        echo "- 服务提供者模式\n";
        echo "- 事件驱动架构\n";
        echo "- 智能路由和缓存\n";
        echo "- 性能监控和指标收集\n";
        echo "- 中间件管道\n";
        echo "- 配置管理\n";
        echo "- 现代化的API设计\n";
    }
}

// 如果直接运行此文件
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $example = new ModernArchitectureExample();
    $example->runAll();
}
