<?php

namespace app\ai\models;

use think\Model;

/**
 * AI聊天上下文模型
 * 基于MySqlMemory类的实际表结构
 */
class AiChatContext extends Model
{
    // 表名
    protected $name = 'ai_chat_contexts';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'context_value' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 允许写入的字段
    protected $field = [
        'session_id', 'context_key', 'context_value'
    ];

    // JSON字段
    protected $json = ['context_value'];

    /**
     * 关联会话
     * @return \think\model\relation\BelongsTo
     */
    public function session()
    {
        return $this->belongsTo(AiChatSession::class, 'session_id', 'session_id');
    }

    /**
     * 按会话查询
     * @param \think\db\Query $query
     * @param string $sessionId
     * @return \think\db\Query
     */
    public function scopeBySession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }



    /**
     * 获取会话的所有上下文
     * @param string $sessionId
     * @return \think\Collection
     */
    public static function getSessionContexts($sessionId)
    {
        return self::where('session_id', $sessionId)
            ->order('created_at', 'asc')
            ->select();
    }

    /**
     * 设置上下文
     * @param string $sessionId
     * @param string $key
     * @param mixed $value
     * @return AiChatContext
     */
    public static function setContext($sessionId, $key, $value)
    {
        $context = self::where('session_id', $sessionId)
            ->where('context_key', $key)
            ->find();

        if (!$context) {
            $context = new self();
        }

        $data = [
            'session_id' => $sessionId,
            'context_key' => $key,
            'context_value' => $value,
        ];

        $context->save($data);
        return $context;
    }

    /**
     * 获取上下文值
     * @param string $sessionId
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getContext($sessionId, $key, $default = null)
    {
        $context = self::where('session_id', $sessionId)
            ->where('context_key', $key)
            ->find();

        return $context ? $context->context_value : $default;
    }

    /**
     * 删除上下文
     * @param string $sessionId
     * @param string $key
     * @return bool
     */
    public static function removeContext($sessionId, $key = null)
    {
        $query = self::where('session_id', $sessionId);

        if ($key) {
            $query->where('context_key', $key);
        }

        return $query->delete();
    }

    /**
     * 检查上下文是否存在
     * @param string $sessionId
     * @param string $key
     * @return bool
     */
    public static function hasContext($sessionId, $key)
    {
        return self::where('session_id', $sessionId)
            ->where('context_key', $key)
            ->count() > 0;
    }
}
