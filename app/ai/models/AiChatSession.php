<?php

namespace app\ai\models;

use think\Model;

/**
 * AI聊天会话模型
 * 基于MySqlMemory类的实际表结构
 */
class AiChatSession extends Model
{
    // 表名
    protected $name = 'ai_chat_sessions';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'status' => 'boolean',
        'message_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'last_activity' => 'datetime',
    ];

    // 允许写入的字段
    protected $field = [
        'session_id', 'status', 'message_count', 'last_activity'
    ];

    // 字段默认值
    protected $insert = [
        'status' => 1,
        'message_count' => 0,
    ];

    /**
     * 关联聊天消息
     * @return \think\model\relation\HasMany
     */
    public function messages()
    {
        return $this->hasMany(AiChatMessage::class, 'session_id', 'session_id')
            ->order('created_at', 'asc');
    }

    /**
     * 关联上下文
     * @return \think\model\relation\HasMany
     */
    public function contexts()
    {
        return $this->hasMany(AiChatContext::class, 'session_id', 'session_id');
    }

    /**
     * 获取活跃会话
     * @param \think\db\Query $query
     * @return \think\db\Query
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 更新最后活跃时间
     * @return bool
     */
    public function updateLastActivity()
    {
        return $this->save(['last_activity' => date('Y-m-d H:i:s')]);
    }

    /**
     * 增加消息数量
     * @param int $count
     * @return bool
     */
    public function incrementMessageCount($count = 1)
    {
        $this->inc('message_count', $count);
        return $this->save();
    }

    /**
     * 结束会话
     * @return bool
     */
    public function endSession()
    {
        return $this->save(['status' => 0]);
    }

    /**
     * 获取会话统计信息
     * @return array
     */
    public function getStats()
    {
        return [
            'session_id' => $this->session_id,
            'message_count' => $this->message_count,
            'duration' => $this->created_at->diffInMinutes($this->last_activity),
            'status' => $this->status ? 'active' : 'ended',
        ];
    }

    /**
     * 清理过期会话
     * @param int $hours 过期小时数
     * @return int 清理数量
     */
    public static function cleanExpiredSessions($hours = 24)
    {
        $expiredTime = date('Y-m-d H:i:s', time() - $hours * 3600);

        return self::where('last_activity', '<', $expiredTime)
            ->where('status', 1)
            ->count();
    }

    /**
     * 创建新会话
     * @param array $data
     * @return AiChatSession
     */
    public static function createSession($data)
    {
        $session = new self();
        $session->save($data);
        return $session;
    }
}
