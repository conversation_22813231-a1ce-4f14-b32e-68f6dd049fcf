<?php

namespace app\ai\models;

use think\Model;

/**
 * AI聊天消息模型
 * 基于MySqlMemory类的实际表结构
 */
class AiChatMessage extends Model
{
    // 表名
    protected $name = 'ai_chat_messages';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'token_count' => 'integer',
        'metadata' => 'json',
        'created_at' => 'datetime',
    ];

    // 允许写入的字段
    protected $field = [
        'session_id', 'message_type', 'content', 'metadata', 'token_count'
    ];

    // JSON字段
    protected $json = ['metadata'];

    /**
     * 关联会话
     * @return \think\model\relation\BelongsTo
     */
    public function session()
    {
        return $this->belongsTo(AiChatSession::class, 'session_id', 'session_id');
    }

    /**
     * 输入消息查询
     * @param \think\db\Query $query
     * @return \think\db\Query
     */
    public function scopeInput($query)
    {
        return $query->where('message_type', 'input');
    }

    /**
     * 输出消息查询
     * @param \think\db\Query $query
     * @return \think\db\Query
     */
    public function scopeOutput($query)
    {
        return $query->where('message_type', 'output');
    }

    /**
     * 按会话查询
     * @param \think\db\Query $query
     * @param string $sessionId
     * @return \think\db\Query
     */
    public function scopeBySession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }



    /**
     * 获取会话的消息历史
     * @param string $sessionId
     * @param int $limit
     * @param int $offset
     * @return \think\Collection
     */
    public static function getSessionHistory($sessionId, $limit = 20, $offset = 0)
    {
        return self::where('session_id', $sessionId)
            ->order('created_at', 'asc')
            ->limit($limit, $offset)
            ->select();
    }

    /**
     * 获取最近的消息
     * @param string $sessionId
     * @param int $count
     * @return \think\Collection
     */
    public static function getRecentMessages($sessionId, $count = 10)
    {
        return self::where('session_id', $sessionId)
            ->order('created_at', 'desc')
            ->limit($count)
            ->select();
    }

    /**
     * 添加输入消息
     * @param string $sessionId
     * @param string $content
     * @param array $metadata
     * @return AiChatMessage
     */
    public static function addInputMessage($sessionId, $content, $metadata = [])
    {
        $message = new self();
        $message->save([
            'session_id' => $sessionId,
            'message_type' => 'input',
            'content' => $content,
            'metadata' => $metadata,
        ]);
        
        return $message;
    }

    /**
     * 添加输出消息
     * @param string $sessionId
     * @param string $content
     * @param array $options
     * @return AiChatMessage
     */
    public static function addOutputMessage($sessionId, $content, $options = [])
    {
        $message = new self();
        $message->save([
            'session_id' => $sessionId,
            'message_type' => 'output',
            'content' => $content,
            'token_count' => $options['token_count'] ?? 0,
            'metadata' => $options['metadata'] ?? [],
        ]);

        return $message;
    }

    /**
     * 获取Token统计
     * @param string $sessionId
     * @return array
     */
    public static function getTokenStats($sessionId)
    {
        $stats = self::where('session_id', $sessionId)
            ->field('
                SUM(token_count) as total_tokens,
                AVG(token_count) as avg_tokens,
                MAX(token_count) as max_tokens,
                COUNT(*) as message_count
            ')
            ->find();

        return $stats ? $stats->toArray() : [
            'total_tokens' => 0,
            'avg_tokens' => 0,
            'max_tokens' => 0,
            'message_count' => 0,
        ];
    }

    /**
     * 清理旧消息
     * @param int $days 保留天数
     * @return int 清理数量
     */
    public static function cleanOldMessages($days = 30)
    {
        $expiredTime = date('Y-m-d H:i:s', time() - $days * 24 * 3600);

        return self::where('created_at', '<', $expiredTime)->delete();
    }
}
