<?php

namespace app\ai\exceptions;

/**
 * AI服务异常基类
 */
class AiServiceException extends \Exception
{
    /**
     * 服务提供商名称
     * @var string
     */
    protected string $provider;

    /**
     * 错误代码
     * @var string
     */
    protected string $errorCode;

    /**
     * 原始错误数据
     * @var array
     */
    protected array $rawError;

    public function __construct(
        string $message = '',
        string $provider = '',
        string $errorCode = '',
        array $rawError = [],
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->provider = $provider;
        $this->errorCode = $errorCode;
        $this->rawError = $rawError;
    }

    /**
     * 获取服务提供商名称
     * @return string
     */
    public function getProvider(): string
    {
        return $this->provider;
    }

    /**
     * 获取错误代码
     * @return string
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    /**
     * 获取原始错误数据
     * @return array
     */
    public function getRawError(): array
    {
        return $this->rawError;
    }

    /**
     * 转换为数组
     * @return array
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'provider' => $this->provider,
            'error_code' => $this->errorCode,
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'raw_error' => $this->rawError,
        ];
    }
}

/**
 * 配置异常
 */
class AiConfigException extends AiServiceException
{
    //
}

/**
 * 认证异常
 */
class AiAuthException extends AiServiceException
{
    //
}

/**
 * 限流异常
 */
class AiRateLimitException extends AiServiceException
{
    /**
     * 重试时间（秒）
     * @var int
     */
    protected int $retryAfter;

    public function __construct(
        string $message = '',
        string $provider = '',
        int $retryAfter = 0,
        string $errorCode = '',
        array $rawError = [],
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $provider, $errorCode, $rawError, $code, $previous);
        $this->retryAfter = $retryAfter;
    }

    /**
     * 获取重试时间
     * @return int
     */
    public function getRetryAfter(): int
    {
        return $this->retryAfter;
    }
}

/**
 * 网络异常
 */
class AiNetworkException extends AiServiceException
{
    //
}

/**
 * 服务不可用异常
 */
class AiServiceUnavailableException extends AiServiceException
{
    //
}

/**
 * 内容过滤异常
 */
class AiContentFilterException extends AiServiceException
{
    /**
     * 被过滤的内容
     * @var string
     */
    protected string $filteredContent;

    public function __construct(
        string $message = '',
        string $provider = '',
        string $filteredContent = '',
        string $errorCode = '',
        array $rawError = [],
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $provider, $errorCode, $rawError, $code, $previous);
        $this->filteredContent = $filteredContent;
    }

    /**
     * 获取被过滤的内容
     * @return string
     */
    public function getFilteredContent(): string
    {
        return $this->filteredContent;
    }
}