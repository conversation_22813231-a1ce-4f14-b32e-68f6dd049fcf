# 现代化AI服务架构

## 概述

这是一个现代化的AI服务架构，采用依赖注入、事件驱动、智能路由等现代设计模式，提供高性能、可扩展的AI服务。

## 核心特性

- **依赖注入容器**: 自动依赖解析和管理
- **智能路由**: 根据请求类型自动选择最适合的服务
- **事件驱动**: 解耦组件，提高可扩展性
- **性能监控**: 实时指标收集和分析
- **智能缓存**: 多层缓存策略
- **统一配置**: 环境变量和代码配置统一管理

## 快速开始

### 1. 初始化应用

```php
use app\ai\bootstrap\AiApplication;

$app = new AiApplication();
$app->boot();
```

### 2. 基础聊天

```php
$aiService = $app->make('ai.unified');
$response = $aiService->process('你好，请介绍一下你自己');
echo $response->content;
```

### 3. 带记忆的对话

```php
$response = $aiService->process('我的名字是张三', [
    'session_id' => 'user_123',
    'type' => 'memory'
]);
```

### 4. 批量处理

```php
$inputs = ['问题1', '问题2', '问题3'];
$responses = $aiService->batchProcess($inputs);
```

## API接口

### 聊天接口
- `POST /ai/chat` - 智能聊天
- `POST /ai/chat/batch` - 批量聊天
- `POST /ai/chat/stream` - 流式聊天

### 系统接口
- `GET /ai/status` - 服务状态
- `GET /ai/health` - 健康检查
- `GET /ai/metrics` - 性能指标
- `DELETE /ai/cache` - 清除缓存

### 管理接口
- `GET /ai/admin/config` - 配置管理
- `GET /ai/admin/application/status` - 应用状态

## 配置

### 环境变量

```bash
AI_DEFAULT_PROVIDER=deepseek
AI_CACHE_ENABLED=true
AI_MEMORY_TYPE=mysql
DEEPSEEK_API_KEY=your_api_key
```

### 代码配置

```php
use app\ai\config\ConfigManager;

ConfigManager::set('default_provider', 'deepseek');
ConfigManager::set('cache.ttl', 3600);
```

## 架构组件

### 核心服务
- `ServiceContainer` - 依赖注入容器
- `UnifiedAiService` - 统一AI服务入口
- `EventDispatcher` - 事件调度器
- `CacheManager` - 缓存管理器
- `MetricsCollector` - 指标收集器
- `ConfigManager` - 配置管理器

### 服务提供者
- `AiServiceProvider` - AI服务注册和启动

### 中间件
- `AiServiceMiddleware` - 请求处理中间件

## 监控和调试

### 性能指标
```php
$metrics = $app->make('ai.metrics');
$summary = $metrics->getSummary();
```

### 事件监听
```php
$events = $app->make('ai.events');
$events->listen('ai.request.completed', function($event, $payload) {
    // 处理事件
});
```

### 应用状态
```php
$status = $app->getStatus();
```

## 扩展开发

### 添加事件监听器
```php
$events->listen('custom.event', CustomEventListener::class);
```

### 添加中间件
```php
$middleware->addMiddleware(CustomMiddleware::class);
```

### 自定义配置
```php
ConfigManager::set('custom.setting', 'value');
```

## 版本信息

- 版本: 2.0.0
- 架构: 现代化微服务架构
- 兼容性: PHP 8.0+
- 框架: ThinkPHP 8.0+

## 许可证

MIT License
