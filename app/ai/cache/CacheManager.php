<?php

namespace app\ai\cache;

use think\facade\Cache;
use app\ai\config\ConfigManager;

/**
 * 缓存管理器
 */
class CacheManager
{
    /**
     * 缓存前缀
     * @var string
     */
    protected string $prefix;

    /**
     * 默认TTL
     * @var int
     */
    protected int $defaultTtl;

    /**
     * 是否启用缓存
     * @var bool
     */
    protected bool $enabled;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->prefix = ConfigManager::get('cache.key_prefix', 'ai_');
        $this->defaultTtl = ConfigManager::get('cache.ttl', 3600);
        $this->enabled = ConfigManager::get('cache.enabled', true);
    }

    /**
     * 获取缓存
     * @param string $key 缓存键
     * @param mixed $default 默认值
     * @return mixed
     */
    public function get(string $key, $default = null)
    {
        if (!$this->enabled) {
            return $default;
        }

        $fullKey = $this->prefix . $key;
        return Cache::get($fullKey, $default);
    }

    /**
     * 设置缓存
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int|null $ttl 过期时间
     * @return bool
     */
    public function set(string $key, $value, ?int $ttl = null): bool
    {
        if (!$this->enabled) {
            return false;
        }

        $fullKey = $this->prefix . $key;
        $ttl = $ttl ?? $this->defaultTtl;
        
        return Cache::set($fullKey, $value, $ttl);
    }

    /**
     * 删除缓存
     * @param string $key 缓存键
     * @return bool
     */
    public function delete(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }

        $fullKey = $this->prefix . $key;
        return Cache::delete($fullKey);
    }

    /**
     * 检查缓存是否存在
     * @param string $key 缓存键
     * @return bool
     */
    public function has(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }

        $fullKey = $this->prefix . $key;
        return Cache::has($fullKey);
    }

    /**
     * 记忆化函数调用
     * @param string $key 缓存键
     * @param callable $callback 回调函数
     * @param int|null $ttl 过期时间
     * @return mixed
     */
    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        if (!$this->enabled) {
            return $callback();
        }

        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }

        $value = $callback();
        $this->set($key, $value, $ttl);
        
        return $value;
    }

    /**
     * 生成AI请求的缓存键
     * @param string $provider 提供商
     * @param string $model 模型
     * @param array $messages 消息
     * @param array $options 选项
     * @return string
     */
    public function generateAiRequestKey(string $provider, string $model, array $messages, array $options = []): string
    {
        $data = [
            'provider' => $provider,
            'model' => $model,
            'messages' => $messages,
            'options' => $options,
        ];
        
        return 'ai_request_' . md5(json_encode($data));
    }

    /**
     * 缓存AI响应
     * @param string $provider 提供商
     * @param string $model 模型
     * @param array $messages 消息
     * @param array $options 选项
     * @param string $response 响应
     * @param int|null $ttl 过期时间
     * @return bool
     */
    public function cacheAiResponse(
        string $provider,
        string $model,
        array $messages,
        array $options,
        string $response,
        ?int $ttl = null
    ): bool {
        $key = $this->generateAiRequestKey($provider, $model, $messages, $options);
        return $this->set($key, $response, $ttl);
    }

    /**
     * 获取缓存的AI响应
     * @param string $provider 提供商
     * @param string $model 模型
     * @param array $messages 消息
     * @param array $options 选项
     * @return string|null
     */
    public function getCachedAiResponse(
        string $provider,
        string $model,
        array $messages,
        array $options
    ): ?string {
        $key = $this->generateAiRequestKey($provider, $model, $messages, $options);
        return $this->get($key);
    }

    /**
     * 清除所有AI缓存
     * @return bool
     */
    public function clearAiCache(): bool
    {
        if (!$this->enabled) {
            return false;
        }

        // 这里需要根据具体的缓存驱动实现
        // ThinkPHP的Cache门面可能不支持按前缀清除
        // 可以考虑使用标签或其他方式
        return true;
    }

    /**
     * 获取缓存统计信息
     * @return array
     */
    public function getStats(): array
    {
        return [
            'enabled' => $this->enabled,
            'prefix' => $this->prefix,
            'default_ttl' => $this->defaultTtl,
        ];
    }

    /**
     * 启用缓存
     */
    public function enable(): void
    {
        $this->enabled = true;
    }

    /**
     * 禁用缓存
     */
    public function disable(): void
    {
        $this->enabled = false;
    }

    /**
     * 设置缓存前缀
     * @param string $prefix 前缀
     */
    public function setPrefix(string $prefix): void
    {
        $this->prefix = $prefix;
    }

    /**
     * 设置默认TTL
     * @param int $ttl TTL
     */
    public function setDefaultTtl(int $ttl): void
    {
        $this->defaultTtl = $ttl;
    }
}
