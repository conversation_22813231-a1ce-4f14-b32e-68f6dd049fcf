<?php

namespace app\ai\prompts;

use app\ai\exceptions\AiServiceException;
use app\ai\utils\Logger;

/**
 * 模板初始化器
 * 负责加载和注册预定义的提示模板
 */
class TemplateInitializer
{
    /**
     * 模板目录
     * @var string
     */
    protected static string $templateDir = __DIR__ . '/templates';
    
    /**
     * 已初始化标志
     * @var bool
     */
    protected static bool $initialized = false;
    
    /**
     * 初始化所有模板
     * @return void
     */
    public static function initialize(): void
    {
        if (self::$initialized) {
            return;
        }
        
        try {
            self::loadTemplatesFromDirectory(self::$templateDir);
            self::registerBuiltInTemplates();
            
            self::$initialized = true;
            Logger::info('Template initializer completed');
        } catch (\Exception $e) {
            Logger::error('Template initialization failed', [
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("Template initialization failed: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 从目录加载模板
     * @param string $directory
     * @return void
     */
    protected static function loadTemplatesFromDirectory(string $directory): void
    {
        if (!is_dir($directory)) {
            Logger::warning('Template directory not found', ['directory' => $directory]);
            return;
        }
        
        $files = glob($directory . '/*.json');
        
        foreach ($files as $file) {
            $templateName = pathinfo($file, PATHINFO_FILENAME);
            
            try {
                $template = PromptTemplateManager::loadFromFile($file);
                PromptTemplateManager::register($templateName, $template);
                
                Logger::info('Template loaded from file', [
                    'name' => $templateName,
                    'file' => $file
                ]);
            } catch (\Exception $e) {
                Logger::error('Failed to load template from file', [
                    'file' => $file,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
    
    /**
     * 注册内置模板
     * @return void
     */
    protected static function registerBuiltInTemplates(): void
    {
        // 简单问答模板
        $qaTemplate = new BasePromptTemplate(
            "问题：{question}\n\n请提供详细的回答：",
            ['question']
        );
        $qaTemplate->setSystemMessage('你是一个知识渊博的助手，请提供准确和有用的回答。');
        PromptTemplateManager::register('qa_simple', $qaTemplate);
        
        // 总结模板
        $summaryTemplate = new BasePromptTemplate(
            "请总结以下内容：\n\n{content}\n\n总结要求：\n- 保留关键信息\n- 简洁明了\n- 逻辑清晰\n\n总结：",
            ['content']
        );
        $summaryTemplate->setSystemMessage('你是一个专业的内容总结助手。');
        PromptTemplateManager::register('summarizer', $summaryTemplate);
        
        // 聊天模板
        $chatTemplate = new ChatPromptTemplate();
        $chatTemplate->setSystemMessage('你是一个友好的AI助手，请与用户进行自然的对话。');
        $chatTemplate->addUserMessage('{message}');
        PromptTemplateManager::register('chat_friendly', $chatTemplate);
        
        // 代码生成模板
        $codeTemplate = new BasePromptTemplate(
            "请生成{language}代码来实现以下功能：\n\n功能描述：{description}\n\n要求：\n- 代码要清晰易读\n- 添加必要的注释\n- 遵循最佳实践\n\n代码：",
            ['language', 'description']
        );
        $codeTemplate->setSystemMessage('你是一个专业的程序员，精通多种编程语言。');
        PromptTemplateManager::register('code_generator', $codeTemplate);
        
        // 错误分析模板
        $errorTemplate = new BasePromptTemplate(
            "请分析以下错误信息：\n\n错误信息：{error_message}\n\n相关代码：\n```{language}\n{code}\n```\n\n请提供：\n1. 错误原因分析\n2. 解决方案\n3. 预防措施\n\n分析结果：",
            ['error_message', 'language', 'code']
        );
        $errorTemplate->setSystemMessage('你是一个经验丰富的调试专家。');
        PromptTemplateManager::register('error_analyzer', $errorTemplate);
        
        // 学习计划模板
        $learningTemplate = new BasePromptTemplate(
            "请为学习{subject}制定一个学习计划：\n\n学习者水平：{level}\n学习时间：{duration}\n学习目标：{goals}\n\n请提供：\n1. 学习路径\n2. 时间安排\n3. 学习资源推荐\n4. 实践项目建议\n\n学习计划：",
            ['subject', 'level', 'duration', 'goals']
        );
        $learningTemplate->setSystemMessage('你是一个专业的学习顾问，擅长制定个性化学习计划。');
        PromptTemplateManager::register('learning_planner', $learningTemplate);
        
        Logger::info('Built-in templates registered', [
            'count' => 6
        ]);
    }
    
    /**
     * 重新初始化
     * @return void
     */
    public static function reinitialize(): void
    {
        self::$initialized = false;
        PromptTemplateManager::clearCache();
        self::initialize();
    }
    
    /**
     * 设置模板目录
     * @param string $directory
     * @return void
     */
    public static function setTemplateDirectory(string $directory): void
    {
        self::$templateDir = $directory;
        Logger::info('Template directory updated', ['directory' => $directory]);
    }
    
    /**
     * 获取模板目录
     * @return string
     */
    public static function getTemplateDirectory(): string
    {
        return self::$templateDir;
    }
    
    /**
     * 检查是否已初始化
     * @return bool
     */
    public static function isInitialized(): bool
    {
        return self::$initialized;
    }
    
    /**
     * 获取可用模板列表
     * @return array
     */
    public static function getAvailableTemplates(): array
    {
        if (!self::$initialized) {
            self::initialize();
        }
        
        return PromptTemplateManager::getTemplateNames();
    }
}