<?php

namespace app\ai\prompts;

use app\ai\interfaces\PromptTemplateInterface;
use app\ai\exceptions\AiServiceException;

/**
 * 条件提示模板
 * 根据条件选择不同的模板内容
 */
class ConditionalPromptTemplate implements PromptTemplateInterface
{
    /**
     * 条件配置
     * @var array
     */
    protected array $conditions;
    
    /**
     * 默认模板
     * @var string
     */
    protected string $defaultTemplate;
    
    /**
     * 输入变量
     * @var array
     */
    protected array $inputVariables;
    
    /**
     * 示例数组
     * @var array
     */
    protected array $examples = [];
    
    /**
     * 系统消息
     * @var string
     */
    protected string $systemMessage = '';
    
    /**
     * 构造函数
     * @param array $conditions 条件配置
     * @param string $defaultTemplate 默认模板
     * @param array $inputVariables 输入变量
     */
    public function __construct(array $conditions, string $defaultTemplate, array $inputVariables = [])
    {
        $this->conditions = $conditions;
        $this->defaultTemplate = $defaultTemplate;
        $this->inputVariables = $inputVariables;
    }
    
    /**
     * 格式化提示模板
     * @param array $variables
     * @return string
     */
    public function format(array $variables = []): string
    {
        if (!$this->validateVariables($variables)) {
            throw new AiServiceException('Missing required variables for conditional prompt template');
        }
        
        $template = $this->selectTemplate($variables);
        
        // 替换变量
        foreach ($variables as $key => $value) {
            $template = str_replace('{' . $key . '}', $value, $template);
        }
        
        // 添加系统消息
        if (!empty($this->systemMessage)) {
            $template = $this->systemMessage . "\n\n" . $template;
        }
        
        // 添加示例
        if (!empty($this->examples)) {
            $exampleText = $this->formatExamples();
            $template = $exampleText . "\n\n" . $template;
        }
        
        return $template;
    }
    
    /**
     * 选择模板
     * @param array $variables
     * @return string
     */
    protected function selectTemplate(array $variables): string
    {
        foreach ($this->conditions as $condition) {
            if ($this->evaluateCondition($condition, $variables)) {
                return $condition['template'];
            }
        }
        
        return $this->defaultTemplate;
    }
    
    /**
     * 评估条件
     * @param array $condition
     * @param array $variables
     * @return bool
     */
    protected function evaluateCondition(array $condition, array $variables): bool
    {
        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? '==';
        $value = $condition['value'] ?? '';
        
        if (!isset($variables[$field])) {
            return false;
        }
        
        $fieldValue = $variables[$field];
        
        switch ($operator) {
            case '==':
                return $fieldValue == $value;
            case '!=':
                return $fieldValue != $value;
            case '>':
                return $fieldValue > $value;
            case '<':
                return $fieldValue < $value;
            case '>=':
                return $fieldValue >= $value;
            case '<=':
                return $fieldValue <= $value;
            case 'in':
                return in_array($fieldValue, (array)$value);
            case 'not_in':
                return !in_array($fieldValue, (array)$value);
            case 'contains':
                return strpos($fieldValue, $value) !== false;
            case 'not_contains':
                return strpos($fieldValue, $value) === false;
            case 'starts_with':
                return strpos($fieldValue, $value) === 0;
            case 'ends_with':
                return substr($fieldValue, -strlen($value)) === $value;
            case 'regex':
                return preg_match($value, $fieldValue);
            case 'empty':
                return empty($fieldValue);
            case 'not_empty':
                return !empty($fieldValue);
            default:
                return false;
        }
    }
    
    /**
     * 获取模板字符串
     * @return string
     */
    public function getTemplate(): string
    {
        return json_encode([
            'conditions' => $this->conditions,
            'default' => $this->defaultTemplate
        ], JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * 获取输入变量
     * @return array
     */
    public function getInputVariables(): array
    {
        return $this->inputVariables;
    }
    
    /**
     * 验证变量
     * @param array $variables
     * @return bool
     */
    public function validateVariables(array $variables): bool
    {
        foreach ($this->inputVariables as $required) {
            if (!isset($variables[$required])) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 添加示例
     * @param array $examples
     * @return self
     */
    public function addExamples(array $examples): self
    {
        $this->examples = array_merge($this->examples, $examples);
        return $this;
    }
    
    /**
     * 设置系统消息
     * @param string $systemMessage
     * @return self
     */
    public function setSystemMessage(string $systemMessage): self
    {
        $this->systemMessage = $systemMessage;
        return $this;
    }
    
    /**
     * 添加条件
     * @param string $field 字段名
     * @param string $operator 操作符
     * @param mixed $value 值
     * @param string $template 模板
     * @return self
     */
    public function addCondition(string $field, string $operator, $value, string $template): self
    {
        $this->conditions[] = [
            'field' => $field,
            'operator' => $operator,
            'value' => $value,
            'template' => $template
        ];
        
        return $this;
    }
    
    /**
     * 设置默认模板
     * @param string $template
     * @return self
     */
    public function setDefaultTemplate(string $template): self
    {
        $this->defaultTemplate = $template;
        return $this;
    }
    
    /**
     * 格式化示例
     * @return string
     */
    protected function formatExamples(): string
    {
        if (empty($this->examples)) {
            return '';
        }
        
        $formatted = "Here are some examples:\n";
        foreach ($this->examples as $index => $example) {
            $formatted .= "Example " . ($index + 1) . ":\n";
            if (isset($example['input'])) {
                $formatted .= "Input: " . $example['input'] . "\n";
            }
            if (isset($example['output'])) {
                $formatted .= "Output: " . $example['output'] . "\n";
            }
            $formatted .= "\n";
        }
        
        return $formatted;
    }
    
    /**
     * 获取条件统计
     * @return array
     */
    public function getConditionStats(): array
    {
        return [
            'condition_count' => count($this->conditions),
            'operators_used' => array_unique(array_column($this->conditions, 'operator')),
            'fields_used' => array_unique(array_column($this->conditions, 'field'))
        ];
    }
}