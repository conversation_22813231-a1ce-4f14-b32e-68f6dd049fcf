{"type": "conditional", "conditions": [{"field": "user_type", "operator": "==", "value": "beginner", "template": "作为初学者，我来为你详细解释{topic}。\n\n{topic}是什么：\n{explanation}\n\n简单来说：\n{simple_explanation}\n\n入门建议：\n1. 从基础开始\n2. 多练习\n3. 不要急于求成\n\n需要更多帮助吗？"}, {"field": "user_type", "operator": "==", "value": "expert", "template": "关于{topic}的高级分析：\n\n技术细节：\n{explanation}\n\n最佳实践：\n{best_practices}\n\n性能考虑：\n{performance_notes}\n\n相关资源：\n{resources}"}], "default_template": "关于{topic}：\n\n{explanation}\n\n希望这个回答对你有帮助。如果需要更详细的信息，请告诉我你的技术水平。", "input_variables": ["user_type", "topic", "explanation", "simple_explanation", "best_practices", "performance_notes", "resources"]}