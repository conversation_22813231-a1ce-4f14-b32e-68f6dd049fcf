<?php

namespace app\ai\prompts;

use app\ai\interfaces\PromptTemplateInterface;
use app\ai\exceptions\AiServiceException;
use app\ai\utils\Logger;

/**
 * 提示模板管理器
 * 提供模板注册、加载和管理功能
 */
class PromptTemplateManager
{
    /**
     * 注册的模板
     * @var array
     */
    protected static array $templates = [];
    
    /**
     * 模板缓存
     * @var array
     */
    protected static array $cache = [];
    
    /**
     * 注册模板
     * @param string $name 模板名称
     * @param PromptTemplateInterface $template 模板实例
     * @return void
     */
    public static function register(string $name, PromptTemplateInterface $template): void
    {
        self::$templates[$name] = $template;
        Logger::info('Template registered', ['name' => $name]);
    }
    
    /**
     * 获取模板
     * @param string $name 模板名称
     * @return PromptTemplateInterface
     * @throws AiServiceException
     */
    public static function get(string $name): PromptTemplateInterface
    {
        if (!isset(self::$templates[$name])) {
            throw new AiServiceException("Template '{$name}' not found");
        }
        
        return self::$templates[$name];
    }
    
    /**
     * 检查模板是否存在
     * @param string $name 模板名称
     * @return bool
     */
    public static function exists(string $name): bool
    {
        return isset(self::$templates[$name]);
    }
    
    /**
     * 获取所有模板名称
     * @return array
     */
    public static function getTemplateNames(): array
    {
        return array_keys(self::$templates);
    }
    
    /**
     * 从配置创建模板
     * @param array $config 配置数组
     * @return PromptTemplateInterface
     * @throws AiServiceException
     */
    public static function createFromConfig(array $config): PromptTemplateInterface
    {
        $type = $config['type'] ?? 'base';
        
        switch ($type) {
            case 'base':
                return new BasePromptTemplate(
                    $config['template'] ?? '',
                    $config['input_variables'] ?? []
                );
            
            case 'chat':
                $template = new ChatPromptTemplate($config['messages'] ?? []);
                
                if (isset($config['system'])) {
                    $template->setSystemMessage($config['system']);
                }
                
                if (isset($config['examples'])) {
                    $template->addExamples($config['examples']);
                }
                
                return $template;
            
            case 'few_shot':
                return self::createFewShotTemplate($config);
            
            case 'conditional':
                return self::createConditionalTemplate($config);
            
            default:
                throw new AiServiceException("Unsupported template type: {$type}");
        }
    }
    
    /**
     * 从文件加载模板
     * @param string $filePath 文件路径
     * @param string $type 模板类型
     * @return PromptTemplateInterface
     * @throws AiServiceException
     */
    public static function loadFromFile(string $filePath, string $type = 'base'): PromptTemplateInterface
    {
        if (!file_exists($filePath)) {
            throw new AiServiceException("Template file not found: {$filePath}");
        }
        
        $cacheKey = md5($filePath . $type . filemtime($filePath));
        
        if (isset(self::$cache[$cacheKey])) {
            return self::$cache[$cacheKey];
        }
        
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        
        switch ($extension) {
            case 'json':
                $config = json_decode(file_get_contents($filePath), true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new AiServiceException("Invalid JSON in template file: {$filePath}");
                }
                $template = self::createFromConfig($config);
                break;
            
            case 'txt':
            case 'md':
                $content = file_get_contents($filePath);
                $variables = [];
                
                // 提取变量
                preg_match_all('/\{([^}]+)\}/', $content, $matches);
                if (!empty($matches[1])) {
                    $variables = array_unique($matches[1]);
                }
                
                if ($type === 'chat') {
                    $template = new ChatPromptTemplate();
                    $template->addUserMessage($content);
                } else {
                    $template = new BasePromptTemplate($content, $variables);
                }
                break;
            
            default:
                throw new AiServiceException("Unsupported template file format: {$extension}");
        }
        
        self::$cache[$cacheKey] = $template;
        return $template;
    }
    
    /**
     * 创建Few-Shot模板
     * @param array $config
     * @return PromptTemplateInterface
     */
    protected static function createFewShotTemplate(array $config): PromptTemplateInterface
    {
        $template = $config['template'] ?? '';
        $examples = $config['examples'] ?? [];
        $inputVariables = $config['input_variables'] ?? [];
        
        $promptTemplate = new BasePromptTemplate($template, $inputVariables);
        
        if (!empty($examples)) {
            $promptTemplate->addExamples($examples);
        }
        
        if (isset($config['system'])) {
            $promptTemplate->setSystemMessage($config['system']);
        }
        
        return $promptTemplate;
    }
    
    /**
     * 创建条件模板
     * @param array $config
     * @return PromptTemplateInterface
     */
    protected static function createConditionalTemplate(array $config): PromptTemplateInterface
    {
        return new ConditionalPromptTemplate(
            $config['conditions'] ?? [],
            $config['default_template'] ?? '',
            $config['input_variables'] ?? []
        );
    }
    
    /**
     * 批量注册模板
     * @param array $templates 模板数组
     * @return void
     */
    public static function registerBatch(array $templates): void
    {
        foreach ($templates as $name => $template) {
            if ($template instanceof PromptTemplateInterface) {
                self::register($name, $template);
            } elseif (is_array($template)) {
                self::register($name, self::createFromConfig($template));
            }
        }
    }
    
    /**
     * 清除缓存
     * @return void
     */
    public static function clearCache(): void
    {
        self::$cache = [];
        Logger::info('Template cache cleared');
    }
    
    /**
     * 获取模板统计信息
     * @return array
     */
    public static function getStats(): array
    {
        return [
            'registered_templates' => count(self::$templates),
            'cached_templates' => count(self::$cache),
            'template_names' => array_keys(self::$templates)
        ];
    }
}