<?php

namespace app\ai\prompts;

use app\ai\interfaces\PromptTemplateInterface;
use app\ai\exceptions\AiServiceException;

/**
 * 聊天提示模板
 * 专门用于对话场景的提示模板
 */
class ChatPromptTemplate implements PromptTemplateInterface
{
    /**
     * 消息列表
     * @var array
     */
    protected array $messages = [];
    
    /**
     * 输入变量
     * @var array
     */
    protected array $inputVariables = [];
    
    /**
     * 构造函数
     * @param array $messages
     */
    public function __construct(array $messages = [])
    {
        $this->messages = $messages;
        $this->extractInputVariables();
    }
    
    /**
     * 添加系统消息
     * @param string $content
     * @return self
     */
    public function addSystemMessage(string $content): self
    {
        $this->messages[] = [
            'role' => 'system',
            'content' => $content
        ];
        $this->extractInputVariables();
        return $this;
    }
    
    /**
     * 添加用户消息
     * @param string $content
     * @return self
     */
    public function addUserMessage(string $content): self
    {
        $this->messages[] = [
            'role' => 'user',
            'content' => $content
        ];
        $this->extractInputVariables();
        return $this;
    }
    
    /**
     * 添加助手消息
     * @param string $content
     * @return self
     */
    public function addAssistantMessage(string $content): self
    {
        $this->messages[] = [
            'role' => 'assistant',
            'content' => $content
        ];
        $this->extractInputVariables();
        return $this;
    }
    
    /**
     * 格式化提示模板
     * @param array $variables
     * @return string
     */
    public function format(array $variables = []): string
    {
        if (!$this->validateVariables($variables)) {
            throw new AiServiceException('Missing required variables for chat prompt template');
        }
        
        $formattedMessages = [];
        
        foreach ($this->messages as $message) {
            $content = $message['content'];
            
            // 替换变量
            foreach ($variables as $key => $value) {
                $content = str_replace('{' . $key . '}', $value, $content);
            }
            
            $formattedMessages[] = [
                'role' => $message['role'],
                'content' => $content
            ];
        }
        
        return json_encode($formattedMessages, JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * 获取格式化的消息数组
     * @param array $variables
     * @return array
     */
    public function formatMessages(array $variables = []): array
    {
        if (!$this->validateVariables($variables)) {
            throw new AiServiceException('Missing required variables for chat prompt template');
        }
        
        $formattedMessages = [];
        
        foreach ($this->messages as $message) {
            $content = $message['content'];
            
            // 替换变量
            foreach ($variables as $key => $value) {
                $content = str_replace('{' . $key . '}', $value, $content);
            }
            
            $formattedMessages[] = [
                'role' => $message['role'],
                'content' => $content
            ];
        }
        
        return $formattedMessages;
    }
    
    /**
     * 获取模板字符串
     * @return string
     */
    public function getTemplate(): string
    {
        return json_encode($this->messages, JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * 获取输入变量
     * @return array
     */
    public function getInputVariables(): array
    {
        return $this->inputVariables;
    }
    
    /**
     * 验证变量
     * @param array $variables
     * @return bool
     */
    public function validateVariables(array $variables): bool
    {
        foreach ($this->inputVariables as $required) {
            if (!isset($variables[$required])) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 添加示例
     * @param array $examples
     * @return self
     */
    public function addExamples(array $examples): self
    {
        foreach ($examples as $example) {
            if (isset($example['input']) && isset($example['output'])) {
                $this->addUserMessage($example['input']);
                $this->addAssistantMessage($example['output']);
            }
        }
        return $this;
    }
    
    /**
     * 设置系统消息
     * @param string $systemMessage
     * @return self
     */
    public function setSystemMessage(string $systemMessage): self
    {
        // 移除现有的系统消息
        $this->messages = array_filter($this->messages, function($msg) {
            return $msg['role'] !== 'system';
        });
        
        // 在开头添加新的系统消息
        array_unshift($this->messages, [
            'role' => 'system',
            'content' => $systemMessage
        ]);
        
        $this->extractInputVariables();
        return $this;
    }
    
    /**
     * 提取输入变量
     */
    protected function extractInputVariables(): void
    {
        $variables = [];
        
        foreach ($this->messages as $message) {
            preg_match_all('/\{([^}]+)\}/', $message['content'], $matches);
            if (!empty($matches[1])) {
                $variables = array_merge($variables, $matches[1]);
            }
        }
        
        $this->inputVariables = array_unique($variables);
    }
    
    /**
     * 从配置创建模板
     * @param array $config
     * @return static
     */
    public static function fromConfig(array $config): self
    {
        $template = new static();
        
        if (isset($config['system'])) {
            $template->setSystemMessage($config['system']);
        }
        
        if (isset($config['messages'])) {
            foreach ($config['messages'] as $message) {
                $template->messages[] = $message;
            }
        }
        
        if (isset($config['examples'])) {
            $template->addExamples($config['examples']);
        }
        
        $template->extractInputVariables();
        return $template;
    }
}