<?php

namespace app\ai\prompts;

use app\ai\interfaces\PromptTemplateInterface;
use app\ai\exceptions\AiServiceException;

/**
 * 基础提示模板实现
 */
class BasePromptTemplate implements PromptTemplateInterface
{
    /**
     * 模板字符串
     * @var string
     */
    protected string $template;
    
    /**
     * 输入变量
     * @var array
     */
    protected array $inputVariables;
    
    /**
     * 系统消息
     * @var string
     */
    protected string $systemMessage = '';
    
    /**
     * 示例数组
     * @var array
     */
    protected array $examples = [];
    
    /**
     * 构造函数
     * @param string $template
     * @param array $inputVariables
     */
    public function __construct(string $template, array $inputVariables = [])
    {
        $this->template = $template;
        $this->inputVariables = $inputVariables;
    }
    
    /**
     * 格式化提示模板
     * @param array $variables
     * @return string
     */
    public function format(array $variables = []): string
    {
        if (!$this->validateVariables($variables)) {
            throw new AiServiceException('Missing required variables for prompt template');
        }
        
        $formatted = $this->template;
        
        // 替换变量
        foreach ($variables as $key => $value) {
            $formatted = str_replace('{' . $key . '}', $value, $formatted);
        }
        
        // 添加系统消息
        if (!empty($this->systemMessage)) {
            $formatted = $this->systemMessage . "\n\n" . $formatted;
        }
        
        // 添加示例
        if (!empty($this->examples)) {
            $exampleText = $this->formatExamples();
            $formatted = $exampleText . "\n\n" . $formatted;
        }
        
        return $formatted;
    }
    
    /**
     * 获取模板字符串
     * @return string
     */
    public function getTemplate(): string
    {
        return $this->template;
    }
    
    /**
     * 获取输入变量
     * @return array
     */
    public function getInputVariables(): array
    {
        return $this->inputVariables;
    }
    
    /**
     * 验证变量
     * @param array $variables
     * @return bool
     */
    public function validateVariables(array $variables): bool
    {
        foreach ($this->inputVariables as $required) {
            if (!isset($variables[$required])) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 添加示例
     * @param array $examples
     * @return self
     */
    public function addExamples(array $examples): self
    {
        $this->examples = array_merge($this->examples, $examples);
        return $this;
    }
    
    /**
     * 设置系统消息
     * @param string $systemMessage
     * @return self
     */
    public function setSystemMessage(string $systemMessage): self
    {
        $this->systemMessage = $systemMessage;
        return $this;
    }
    
    /**
     * 格式化示例
     * @return string
     */
    protected function formatExamples(): string
    {
        if (empty($this->examples)) {
            return '';
        }
        
        $formatted = "Here are some examples:\n";
        foreach ($this->examples as $index => $example) {
            $formatted .= "Example " . ($index + 1) . ":\n";
            if (isset($example['input'])) {
                $formatted .= "Input: " . $example['input'] . "\n";
            }
            if (isset($example['output'])) {
                $formatted .= "Output: " . $example['output'] . "\n";
            }
            $formatted .= "\n";
        }
        
        return $formatted;
    }
    
    /**
     * 从文件加载模板
     * @param string $filePath
     * @return static
     */
    public static function fromFile(string $filePath): self
    {
        if (!file_exists($filePath)) {
            throw new AiServiceException("Template file not found: {$filePath}");
        }
        
        $content = file_get_contents($filePath);
        $variables = [];
        
        // 提取变量
        preg_match_all('/\{([^}]+)\}/', $content, $matches);
        if (!empty($matches[1])) {
            $variables = array_unique($matches[1]);
        }
        
        return new static($content, $variables);
    }
}