<?php

namespace app\ai\tools;

use app\ai\exceptions\AiServiceException;

/**
 * 计算器工具
 */
class CalculatorTool extends BaseTool
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct('calculator', '执行基本的数学计算');
        
        $this->parameterSchema = [
            'type' => 'object',
            'properties' => [
                'expression' => [
                    'type' => 'string',
                    'description' => '要计算的数学表达式，支持 +, -, *, /, %, ^, sqrt, sin, cos, tan, log 等'
                ]
            ],
            'required' => ['expression']
        ];
        
        $this->examples = [
            [
                'description' => '基本算术运算',
                'input' => ['expression' => '2 + 3 * 4'],
                'output' => 14
            ],
            [
                'description' => '平方根计算',
                'input' => ['expression' => 'sqrt(16)'],
                'output' => 4
            ],
            [
                'description' => '三角函数',
                'input' => ['expression' => 'sin(30)'],
                'output' => 0.5
            ]
        ];
    }
    
    /**
     * 执行计算
     * @param array $parameters
     * @param array $context
     * @return mixed
     */
    protected function run(array $parameters = [], array $context = [])
    {
        $expression = $parameters['expression'];
        
        try {
            // 清理和验证表达式
            $cleanExpression = $this->sanitizeExpression($expression);
            
            // 执行计算
            $result = $this->evaluate($cleanExpression);
            
            return [
                'expression' => $expression,
                'result' => $result,
                'formatted_result' => $this->formatResult($result)
            ];
            
        } catch (\Exception $e) {
            throw new AiServiceException('计算错误: ' . $e->getMessage());
        }
    }
    
    /**
     * 清理表达式
     * @param string $expression
     * @return string
     */
    protected function sanitizeExpression(string $expression): string
    {
        // 移除空格
        $expression = preg_replace('/\s+/', '', $expression);
        
        // 替换常用函数
        $expression = str_replace([
            'sqrt(',
            'sin(',
            'cos(',
            'tan(',
            'log(',
            'ln(',
            'abs(',
            'ceil(',
            'floor(',
            'round('
        ], [
            'sqrt(',
            'sin(',
            'cos(',
            'tan(',
            'log10(',
            'log(',
            'abs(',
            'ceil(',
            'floor(',
            'round('
        ], $expression);
        
        // 替换幂运算
        $expression = str_replace('^', '**', $expression);
        
        // 验证表达式安全性
        if (!$this->isExpressionSafe($expression)) {
            throw new AiServiceException('不安全的表达式');
        }
        
        return $expression;
    }
    
    /**
     * 验证表达式安全性
     * @param string $expression
     * @return bool
     */
    protected function isExpressionSafe(string $expression): bool
    {
        // 只允许数字、运算符和安全的函数
        $allowedPattern = '/^[0-9+\-*\/().,\s^sqrt|sin|cos|tan|log10|log|abs|ceil|floor|round|pi|e]+$/i';
        
        if (!preg_match($allowedPattern, $expression)) {
            return false;
        }
        
        // 检查危险函数
        $dangerousFunctions = ['eval', 'exec', 'system', 'shell_exec', 'file', 'include', 'require'];
        foreach ($dangerousFunctions as $func) {
            if (stripos($expression, $func) !== false) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 计算表达式
     * @param string $expression
     * @return float
     */
    protected function evaluate(string $expression): float
    {
        // 替换常量
        $expression = str_replace(['pi', 'e'], [M_PI, M_E], $expression);
        
        // 使用安全的计算方法
        try {
            // 这里使用简单的eval，实际项目中建议使用更安全的数学表达式解析器
            $result = eval("return $expression;");
            
            if ($result === false || !is_numeric($result)) {
                throw new AiServiceException('计算结果无效');
            }
            
            return (float) $result;
            
        } catch (\ParseError $e) {
            throw new AiServiceException('表达式语法错误: ' . $e->getMessage());
        } catch (\Error $e) {
            throw new AiServiceException('计算错误: ' . $e->getMessage());
        }
    }
    
    /**
     * 格式化结果
     * @param float $result
     * @return string
     */
    protected function formatResult(float $result): string
    {
        // 如果是整数，显示为整数
        if ($result == intval($result)) {
            return (string) intval($result);
        }
        
        // 保留适当的小数位数
        return number_format($result, 6, '.', '');
    }
    
    /**
     * 获取支持的函数列表
     * @return array
     */
    public function getSupportedFunctions(): array
    {
        return [
            'sqrt' => '平方根',
            'sin' => '正弦函数（弧度）',
            'cos' => '余弦函数（弧度）',
            'tan' => '正切函数（弧度）',
            'log' => '自然对数',
            'log10' => '常用对数',
            'abs' => '绝对值',
            'ceil' => '向上取整',
            'floor' => '向下取整',
            'round' => '四舍五入',
            'pi' => '圆周率常量',
            'e' => '自然常数'
        ];
    }
    
    /**
     * 获取支持的运算符列表
     * @return array
     */
    public function getSupportedOperators(): array
    {
        return [
            '+' => '加法',
            '-' => '减法',
            '*' => '乘法',
            '/' => '除法',
            '%' => '取模',
            '^' => '幂运算',
            '()' => '括号（改变运算优先级）'
        ];
    }
}