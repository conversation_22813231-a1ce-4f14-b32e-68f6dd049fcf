<?php

namespace app\ai\tools;

use app\ai\exceptions\AiServiceException;

/**
 * HTTP请求工具
 */
class HttpRequestTool extends BaseTool
{
    /**
     * 允许的域名列表
     * @var array
     */
    protected array $allowedDomains = [];
    
    /**
     * 请求超时时间（秒）
     * @var int
     */
    protected int $timeout = 30;
    
    /**
     * 最大重定向次数
     * @var int
     */
    protected int $maxRedirects = 5;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct('http_request', '发送HTTP请求获取网页内容或API数据');
        
        $this->parameterSchema = [
            'type' => 'object',
            'properties' => [
                'url' => [
                    'type' => 'string',
                    'description' => '要请求的URL地址'
                ],
                'method' => [
                    'type' => 'string',
                    'description' => 'HTTP方法',
                    'enum' => ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
                    'default' => 'GET'
                ],
                'headers' => [
                    'type' => 'object',
                    'description' => '请求头',
                    'default' => []
                ],
                'data' => [
                    'type' => 'object',
                    'description' => '请求数据（POST/PUT等）',
                    'default' => []
                ],
                'timeout' => [
                    'type' => 'integer',
                    'description' => '超时时间（秒）',
                    'default' => 30
                ],
                'follow_redirects' => [
                    'type' => 'boolean',
                    'description' => '是否跟随重定向',
                    'default' => true
                ]
            ],
            'required' => ['url']
        ];
        
        $this->examples = [
            [
                'description' => 'GET请求获取网页内容',
                'input' => [
                    'url' => 'https://api.github.com/users/octocat',
                    'method' => 'GET'
                ],
                'output' => 'JSON响应数据'
            ],
            [
                'description' => 'POST请求发送数据',
                'input' => [
                    'url' => 'https://httpbin.org/post',
                    'method' => 'POST',
                    'data' => ['key' => 'value']
                ],
                'output' => 'POST响应数据'
            ]
        ];
        
        // 设置默认允许的域名（可以通过配置修改）
        $this->allowedDomains = [
            'api.github.com',
            'httpbin.org',
            'jsonplaceholder.typicode.com'
        ];
    }
    
    /**
     * 执行HTTP请求
     * @param array $parameters
     * @param array $context
     * @return mixed
     */
    protected function run(array $parameters = [], array $context = [])
    {
        $url = $parameters['url'];
        $method = strtoupper($parameters['method'] ?? 'GET');
        $headers = $parameters['headers'] ?? [];
        $data = $parameters['data'] ?? [];
        $timeout = $parameters['timeout'] ?? $this->timeout;
        $followRedirects = $parameters['follow_redirects'] ?? true;
        
        try {
            // 验证URL
            if (!$this->isUrlAllowed($url)) {
                throw new AiServiceException('URL不在允许的域名列表中');
            }
            
            // 发送请求
            $response = $this->sendRequest($url, $method, $headers, $data, $timeout, $followRedirects);
            
            return [
                'url' => $url,
                'method' => $method,
                'status_code' => $response['status_code'],
                'headers' => $response['headers'],
                'body' => $response['body'],
                'content_type' => $response['content_type'],
                'size' => strlen($response['body']),
                'execution_time' => $response['execution_time']
            ];
            
        } catch (\Exception $e) {
            throw new AiServiceException('HTTP请求失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 验证URL是否被允许
     * @param string $url
     * @return bool
     */
    protected function isUrlAllowed(string $url): bool
    {
        $parsedUrl = parse_url($url);
        
        if (!$parsedUrl || !isset($parsedUrl['host'])) {
            return false;
        }
        
        $host = strtolower($parsedUrl['host']);
        
        // 如果没有设置允许的域名，则允许所有HTTPS请求
        if (empty($this->allowedDomains)) {
            return isset($parsedUrl['scheme']) && $parsedUrl['scheme'] === 'https';
        }
        
        // 检查是否在允许的域名列表中
        foreach ($this->allowedDomains as $allowedDomain) {
            if ($host === strtolower($allowedDomain) || 
                str_ends_with($host, '.' . strtolower($allowedDomain))) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 发送HTTP请求
     * @param string $url
     * @param string $method
     * @param array $headers
     * @param array $data
     * @param int $timeout
     * @param bool $followRedirects
     * @return array
     */
    protected function sendRequest(
        string $url, 
        string $method, 
        array $headers, 
        array $data, 
        int $timeout, 
        bool $followRedirects
    ): array {
        $startTime = microtime(true);
        
        // 初始化cURL
        $ch = curl_init();
        
        // 设置基本选项
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_FOLLOWLOCATION => $followRedirects,
            CURLOPT_MAXREDIRS => $this->maxRedirects,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_USERAGENT => 'AI-Tool-HttpRequest/1.0',
            CURLOPT_HEADER => false,
            CURLOPT_HEADERFUNCTION => function($curl, $header) use (&$responseHeaders) {
                $responseHeaders[] = trim($header);
                return strlen($header);
            }
        ]);
        
        $responseHeaders = [];
        
        // 设置请求方法
        switch ($method) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                    $headers['Content-Type'] = 'application/json';
                }
                break;
                
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                    $headers['Content-Type'] = 'application/json';
                }
                break;
                
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
                
            case 'PATCH':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                    $headers['Content-Type'] = 'application/json';
                }
                break;
        }
        
        // 设置请求头
        if (!empty($headers)) {
            $headerArray = [];
            foreach ($headers as $key => $value) {
                $headerArray[] = "$key: $value";
            }
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
        }
        
        // 执行请求
        $body = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        $executionTime = (microtime(true) - $startTime) * 1000;
        
        if ($body === false || !empty($error)) {
            throw new AiServiceException('cURL错误: ' . $error);
        }
        
        // 解析响应头
        $parsedHeaders = [];
        foreach ($responseHeaders as $header) {
            if (strpos($header, ':') !== false) {
                list($key, $value) = explode(':', $header, 2);
                $parsedHeaders[trim($key)] = trim($value);
            }
        }
        
        return [
            'status_code' => $statusCode,
            'headers' => $parsedHeaders,
            'body' => $body,
            'content_type' => $contentType,
            'execution_time' => $executionTime
        ];
    }
    
    /**
     * 设置允许的域名
     * @param array $domains
     * @return self
     */
    public function setAllowedDomains(array $domains): self
    {
        $this->allowedDomains = $domains;
        return $this;
    }
    
    /**
     * 添加允许的域名
     * @param string $domain
     * @return self
     */
    public function addAllowedDomain(string $domain): self
    {
        $this->allowedDomains[] = $domain;
        return $this;
    }
    
    /**
     * 设置超时时间
     * @param int $timeout
     * @return self
     */
    public function setTimeout(int $timeout): self
    {
        $this->timeout = $timeout;
        return $this;
    }
    
    /**
     * 设置最大重定向次数
     * @param int $maxRedirects
     * @return self
     */
    public function setMaxRedirects(int $maxRedirects): self
    {
        $this->maxRedirects = $maxRedirects;
        return $this;
    }
    
    /**
     * 获取允许的域名列表
     * @return array
     */
    public function getAllowedDomains(): array
    {
        return $this->allowedDomains;
    }
}