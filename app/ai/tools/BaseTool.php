<?php

namespace app\ai\tools;

use app\ai\interfaces\ToolInterface;
use app\ai\exceptions\AiServiceException;
use app\ai\utils\Logger;

/**
 * 基础工具实现
 */
abstract class BaseTool implements ToolInterface
{
    /**
     * 工具名称
     * @var string
     */
    protected string $name;
    
    /**
     * 工具描述
     * @var string
     */
    protected string $description;
    
    /**
     * 参数模式
     * @var array
     */
    protected array $parameterSchema = [];
    
    /**
     * 工具类型
     * @var string
     */
    protected string $type = 'function';
    
    /**
     * 是否需要认证
     * @var bool
     */
    protected bool $requiresAuth = false;
    
    /**
     * 示例
     * @var array
     */
    protected array $examples = [];
    
    /**
     * 是否启用详细日志
     * @var bool
     */
    protected bool $verbose = false;
    
    /**
     * 构造函数
     * @param string $name
     * @param string $description
     */
    public function __construct(string $name = '', string $description = '')
    {
        $this->name = $name ?: static::class;
        $this->description = $description;
    }
    
    /**
     * 执行工具
     * @param array $parameters
     * @param array $context
     * @return mixed
     */
    public function execute(array $parameters = [], array $context = []): array
    {
        try {
            // 验证参数
            if (!$this->validateParameters($parameters)) {
                throw new AiServiceException('Invalid parameters for tool: ' . $this->name);
            }
            
            // 检查认证
            if ($this->requiresAuth && !$this->checkAuth($context)) {
                throw new AiServiceException('Authentication required for tool: ' . $this->name);
            }
            
            // 记录开始执行
            if ($this->verbose) {
                Logger::info('Tool execution started', [
                    'tool' => $this->name,
                    'parameters' => $parameters,
                    'context' => $context
                ]);
            }
            
            $startTime = microtime(true);
            
            // 执行具体逻辑
            $result = $this->run($parameters, $context);
            
            $executionTime = (microtime(true) - $startTime) * 1000;
            
            // 记录执行完成
            if ($this->verbose) {
                Logger::info('Tool execution completed', [
                    'tool' => $this->name,
                    'execution_time' => $executionTime . 'ms',
                    'result' => $result
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Logger::error('Tool execution failed', [
                'tool' => $this->name,
                'error' => $e->getMessage(),
                'parameters' => $parameters,
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * 执行具体逻辑（子类实现）
     * @param array $parameters
     * @param array $context
     * @return mixed
     */
    abstract protected function run(array $parameters = [], array $context = []);
    
    /**
     * 获取工具名称
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }
    
    /**
     * 获取工具描述
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }
    
    /**
     * 获取参数模式
     * @return array
     */
    public function getParameterSchema(): array
    {
        return $this->parameterSchema;
    }
    
    /**
     * 验证参数
     * @param array $parameters
     * @return bool
     */
    public function validateParameters(array $parameters): bool
    {
        // 检查必需参数
        if (isset($this->parameterSchema['required'])) {
            foreach ($this->parameterSchema['required'] as $required) {
                if (!isset($parameters[$required])) {
                    return false;
                }
            }
        }
        
        // 检查参数类型
        if (isset($this->parameterSchema['properties'])) {
            foreach ($parameters as $key => $value) {
                if (isset($this->parameterSchema['properties'][$key])) {
                    $schema = $this->parameterSchema['properties'][$key];
                    if (!$this->validateParameterType($value, $schema)) {
                        return false;
                    }
                }
            }
        }
        
        return true;
    }
    
    /**
     * 验证参数类型
     * @param mixed $value
     * @param array $schema
     * @return bool
     */
    protected function validateParameterType($value, array $schema): bool
    {
        if (!isset($schema['type'])) {
            return true;
        }
        
        switch ($schema['type']) {
            case 'string':
                return is_string($value);
            case 'integer':
                return is_int($value);
            case 'number':
                return is_numeric($value);
            case 'boolean':
                return is_bool($value);
            case 'array':
                return is_array($value);
            case 'object':
                return is_array($value) || is_object($value);
            default:
                return true;
        }
    }
    
    /**
     * 获取工具类型
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }
    
    /**
     * 是否需要认证
     * @return bool
     */
    public function requiresAuth(): bool
    {
        return $this->requiresAuth;
    }
    
    /**
     * 获取示例
     * @return array
     */
    public function getExamples(): array
    {
        return $this->examples;
    }
    
    /**
     * 检查认证
     * @param array $context
     * @return bool
     */
    protected function checkAuth(array $context): bool
    {
        // 默认实现，子类可以重写
        return isset($context['auth']) || isset($context['user_id']);
    }
    
    /**
     * 设置详细日志
     * @param bool $verbose
     * @return self
     */
    public function setVerbose(bool $verbose): self
    {
        $this->verbose = $verbose;
        return $this;
    }
    
    /**
     * 设置参数模式
     * @param array $schema
     * @return self
     */
    public function setParameterSchema(array $schema): self
    {
        $this->parameterSchema = $schema;
        return $this;
    }
    
    /**
     * 添加示例
     * @param array $example
     * @return self
     */
    public function addExample(array $example): self
    {
        $this->examples[] = $example;
        return $this;
    }
    
    /**
     * 获取工具的JSON Schema描述
     * @return array
     */
    public function toSchema(): array
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'type' => $this->type,
            'parameters' => $this->parameterSchema,
            'requires_auth' => $this->requiresAuth,
            'examples' => $this->examples
        ];
    }
    
    /**
     * 从配置创建工具
     * @param array $config
     * @return static
     */
    public static function fromConfig(array $config): self
    {
        $instance = new static(
            $config['name'] ?? '',
            $config['description'] ?? ''
        );
        
        if (isset($config['parameter_schema'])) {
            $instance->setParameterSchema($config['parameter_schema']);
        }
        
        if (isset($config['type'])) {
            $instance->type = $config['type'];
        }
        
        if (isset($config['requires_auth'])) {
            $instance->requiresAuth = $config['requires_auth'];
        }
        
        if (isset($config['examples'])) {
            $instance->examples = $config['examples'];
        }
        
        if (isset($config['verbose'])) {
            $instance->setVerbose($config['verbose']);
        }
        
        return $instance;
    }
    
    /**
     * 异步执行工具
     * @param array $parameters
     * @param array $context
     * @param callable|null $callback
     * @return mixed
     */
    public function asyncExecute(array $parameters = [], array $context = [], ?callable $callback = null)
    {
        // 简单的异步实现
        $result = $this->execute($parameters, $context);
        
        if ($callback) {
            call_user_func($callback, $result);
        }
        
        return $result;
    }
}