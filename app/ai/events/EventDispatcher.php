<?php

namespace app\ai\events;

use Closure;

/**
 * 事件调度器
 */
class EventDispatcher
{
    /**
     * 事件监听器
     * @var array
     */
    protected array $listeners = [];

    /**
     * 通配符监听器
     * @var array
     */
    protected array $wildcards = [];

    /**
     * 监听事件
     * @param string $event 事件名称
     * @param callable $listener 监听器
     * @param int $priority 优先级
     * @return $this
     */
    public function listen(string $event, callable $listener, int $priority = 0): self
    {
        if (str_contains($event, '*')) {
            $this->wildcards[$event][] = ['listener' => $listener, 'priority' => $priority];
        } else {
            $this->listeners[$event][] = ['listener' => $listener, 'priority' => $priority];
        }

        return $this;
    }

    /**
     * 触发事件
     * @param string $event 事件名称
     * @param mixed $payload 事件数据
     * @param bool $halt 是否在第一个监听器返回非null时停止
     * @return array|mixed
     */
    public function dispatch(string $event, $payload = [], bool $halt = false)
    {
        $responses = [];

        // 获取直接监听器
        $listeners = $this->getListeners($event);

        // 获取通配符监听器
        $wildcardListeners = $this->getWildcardListeners($event);

        // 合并并排序
        $allListeners = array_merge($listeners, $wildcardListeners);
        usort($allListeners, function ($a, $b) {
            return $b['priority'] <=> $a['priority'];
        });

        foreach ($allListeners as $listenerData) {
            $listener = $listenerData['listener'];
            
            if ($listener instanceof Closure) {
                $response = $listener($event, $payload);
            } else {
                $response = call_user_func($listener, $event, $payload);
            }

            if ($halt && $response !== null) {
                return $response;
            }

            if ($response !== null) {
                $responses[] = $response;
            }
        }

        return $halt ? null : $responses;
    }

    /**
     * 获取事件监听器
     * @param string $event 事件名称
     * @return array
     */
    protected function getListeners(string $event): array
    {
        return $this->listeners[$event] ?? [];
    }

    /**
     * 获取通配符监听器
     * @param string $event 事件名称
     * @return array
     */
    protected function getWildcardListeners(string $event): array
    {
        $listeners = [];

        foreach ($this->wildcards as $pattern => $patternListeners) {
            if ($this->matchesWildcard($pattern, $event)) {
                $listeners = array_merge($listeners, $patternListeners);
            }
        }

        return $listeners;
    }

    /**
     * 检查事件是否匹配通配符模式
     * @param string $pattern 模式
     * @param string $event 事件名称
     * @return bool
     */
    protected function matchesWildcard(string $pattern, string $event): bool
    {
        $pattern = str_replace('*', '.*', $pattern);
        return preg_match('/^' . $pattern . '$/', $event) === 1;
    }

    /**
     * 移除监听器
     * @param string $event 事件名称
     * @return $this
     */
    public function forget(string $event): self
    {
        unset($this->listeners[$event]);
        unset($this->wildcards[$event]);
        return $this;
    }

    /**
     * 注册默认监听器
     */
    public function registerDefaultListeners(): void
    {
        // AI请求开始事件
        $this->listen('ai.request.started', function ($event, $payload) {
            // 记录请求开始时间
            $payload['start_time'] = microtime(true);
        });

        // AI请求完成事件
        $this->listen('ai.request.completed', function ($event, $payload) {
            // 计算请求耗时
            if (isset($payload['start_time'])) {
                $duration = microtime(true) - $payload['start_time'];
                // 记录到指标收集器
            }
        });

        // AI请求失败事件
        $this->listen('ai.request.failed', function ($event, $payload) {
            // 记录错误日志
            error_log("AI Request Failed: " . json_encode($payload));
        });

        // 记忆保存事件
        $this->listen('ai.memory.saved', function ($event, $payload) {
            // 可以在这里添加记忆保存后的处理逻辑
        });

        // 记忆清除事件
        $this->listen('ai.memory.cleared', function ($event, $payload) {
            // 可以在这里添加记忆清除后的处理逻辑
        });
    }
}
