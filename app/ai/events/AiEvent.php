<?php

namespace app\ai\events;

/**
 * AI事件基类
 */
abstract class AiEvent
{
    /**
     * 事件时间戳
     * @var float
     */
    public float $timestamp;

    /**
     * 事件ID
     * @var string
     */
    public string $eventId;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->timestamp = microtime(true);
        $this->eventId = uniqid('ai_event_', true);
    }

    /**
     * 获取事件名称
     * @return string
     */
    abstract public function getEventName(): string;

    /**
     * 获取事件数据
     * @return array
     */
    abstract public function getEventData(): array;
}

/**
 * AI请求开始事件
 */
class AiRequestStartedEvent extends AiEvent
{
    public string $provider;
    public string $model;
    public array $parameters;
    public string $requestId;

    public function __construct(string $provider, string $model, array $parameters, string $requestId)
    {
        parent::__construct();
        $this->provider = $provider;
        $this->model = $model;
        $this->parameters = $parameters;
        $this->requestId = $requestId;
    }

    public function getEventName(): string
    {
        return 'ai.request.started';
    }

    public function getEventData(): array
    {
        return [
            'provider' => $this->provider,
            'model' => $this->model,
            'parameters' => $this->parameters,
            'request_id' => $this->requestId,
            'timestamp' => $this->timestamp,
        ];
    }
}

/**
 * AI请求完成事件
 */
class AiRequestCompletedEvent extends AiEvent
{
    public string $provider;
    public string $model;
    public string $response;
    public string $requestId;
    public float $duration;
    public int $tokenUsage;

    public function __construct(
        string $provider,
        string $model,
        string $response,
        string $requestId,
        float $duration,
        int $tokenUsage = 0
    ) {
        parent::__construct();
        $this->provider = $provider;
        $this->model = $model;
        $this->response = $response;
        $this->requestId = $requestId;
        $this->duration = $duration;
        $this->tokenUsage = $tokenUsage;
    }

    public function getEventName(): string
    {
        return 'ai.request.completed';
    }

    public function getEventData(): array
    {
        return [
            'provider' => $this->provider,
            'model' => $this->model,
            'response_length' => strlen($this->response),
            'request_id' => $this->requestId,
            'duration' => $this->duration,
            'token_usage' => $this->tokenUsage,
            'timestamp' => $this->timestamp,
        ];
    }
}

/**
 * AI请求失败事件
 */
class AiRequestFailedEvent extends AiEvent
{
    public string $provider;
    public string $model;
    public string $error;
    public string $requestId;
    public float $duration;

    public function __construct(
        string $provider,
        string $model,
        string $error,
        string $requestId,
        float $duration
    ) {
        parent::__construct();
        $this->provider = $provider;
        $this->model = $model;
        $this->error = $error;
        $this->requestId = $requestId;
        $this->duration = $duration;
    }

    public function getEventName(): string
    {
        return 'ai.request.failed';
    }

    public function getEventData(): array
    {
        return [
            'provider' => $this->provider,
            'model' => $this->model,
            'error' => $this->error,
            'request_id' => $this->requestId,
            'duration' => $this->duration,
            'timestamp' => $this->timestamp,
        ];
    }
}

/**
 * 记忆保存事件
 */
class MemorySavedEvent extends AiEvent
{
    public string $sessionId;
    public array $message;
    public string $memoryType;

    public function __construct(string $sessionId, array $message, string $memoryType)
    {
        parent::__construct();
        $this->sessionId = $sessionId;
        $this->message = $message;
        $this->memoryType = $memoryType;
    }

    public function getEventName(): string
    {
        return 'ai.memory.saved';
    }

    public function getEventData(): array
    {
        return [
            'session_id' => $this->sessionId,
            'message' => $this->message,
            'memory_type' => $this->memoryType,
            'timestamp' => $this->timestamp,
        ];
    }
}

/**
 * 记忆清除事件
 */
class MemoryClearedEvent extends AiEvent
{
    public string $sessionId;
    public string $memoryType;

    public function __construct(string $sessionId, string $memoryType)
    {
        parent::__construct();
        $this->sessionId = $sessionId;
        $this->memoryType = $memoryType;
    }

    public function getEventName(): string
    {
        return 'ai.memory.cleared';
    }

    public function getEventData(): array
    {
        return [
            'session_id' => $this->sessionId,
            'memory_type' => $this->memoryType,
            'timestamp' => $this->timestamp,
        ];
    }
}
