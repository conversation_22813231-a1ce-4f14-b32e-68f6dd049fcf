<?php

namespace app\ai\events;

class AiRequestFailedEvent
{
    protected $data;

    public function __construct($provider, $model, $error, $requestId, $duration)
    {
        $this->data = [
            'provider' => $provider,
            'model' => $model,
            'error' => $error,
            'requestId' => $requestId,
            'duration' => $duration
        ];
    }

    public function getEventName()
    {
        return strtolower(str_replace('Event', '', 'AiRequestFailedEvent'));
    }

    public function getEventData()
    {
        return $this->data;
    }
}