<?php

namespace app\ai\events;

class AiRequestCompletedEvent
{
    protected $data;

    public function __construct($provider, $model, $response, $requestId, $duration)
    {
        $this->data = [
            'provider' => $provider,
            'model' => $model,
            'response' => $response,
            'requestId' => $requestId,
            'duration' => $duration
        ];
    }

    public function getEventName()
    {
        return strtolower(str_replace('Event', '', 'AiRequestCompletedEvent'));
    }

    public function getEventData()
    {
        return $this->data;
    }
}