<?php

namespace app\ai\events;

class AiRequestStartedEvent
{
    protected $data;

    public function __construct($provider, $model, $options, $requestId)
    {
        $this->data = [
            'provider' => $provider,
            'model' => $model,
            'options' => $options,
            'requestId' => $requestId
        ];
    }

    public function getEventName()
    {
        return strtolower(str_replace('Event', '', 'AiRequestStartedEvent'));
    }

    public function getEventData()
    {
        return $this->data;
    }
}