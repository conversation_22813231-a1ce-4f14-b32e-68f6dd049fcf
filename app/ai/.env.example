# AI服务配置示例
# 复制此文件为 .env 并填入实际的配置值

# ===========================================
# AI服务基础配置
# ===========================================

# 默认AI服务提供商
AI_DEFAULT_PROVIDER=deepseek

# 主要服务提供商
AI_PRIMARY_PROVIDER=deepseek

# 启用故障转移
AI_FAILOVER_ENABLED=true

# 启用负载均衡
AI_LOAD_BALANCING_ENABLED=false

# ===========================================
# DeepSeek配置
# ===========================================

# DeepSeek API密钥 (必填)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# DeepSeek API基础URL
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 默认模型
DEEPSEEK_DEFAULT_MODEL=deepseek-chat

# 请求超时时间（秒）
DEEPSEEK_TIMEOUT=30

# 最大重试次数
DEEPSEEK_MAX_RETRIES=3

# ===========================================
# OpenAI配置（可选）
# ===========================================

# OpenAI API密钥
OPENAI_API_KEY=your_openai_api_key_here

# OpenAI API基础URL
OPENAI_BASE_URL=https://api.openai.com/v1

# 默认模型
OPENAI_DEFAULT_MODEL=gpt-3.5-turbo

# 请求超时时间（秒）
OPENAI_TIMEOUT=30

# 最大重试次数
OPENAI_MAX_RETRIES=3

# OpenAI组织ID（可选）
OPENAI_ORGANIZATION=

# ===========================================
# Claude配置（可选）
# ===========================================

# Claude API密钥
CLAUDE_API_KEY=your_claude_api_key_here

# Claude API基础URL
CLAUDE_BASE_URL=https://api.anthropic.com

# 默认模型
CLAUDE_DEFAULT_MODEL=claude-3-sonnet-20240229

# 请求超时时间（秒）
CLAUDE_TIMEOUT=30

# 最大重试次数
CLAUDE_MAX_RETRIES=3

# Claude API版本
CLAUDE_VERSION=2023-06-01

# ===========================================
# 限流配置
# ===========================================

# 启用限流
AI_RATE_LIMIT_ENABLED=true

# 每分钟请求限制
AI_RATE_LIMIT_RPM=60

# 每小时请求限制
AI_RATE_LIMIT_RPH=1000

# ===========================================
# 日志配置
# ===========================================

# 启用日志
AI_LOGGING_ENABLED=true

# 日志级别 (debug, info, warning, error)
AI_LOGGING_LEVEL=info

# 记录请求日志
AI_LOG_REQUESTS=true

# 记录响应日志（注意：可能包含敏感信息）
AI_LOG_RESPONSES=false

# ===========================================
# 缓存配置
# ===========================================

# 启用缓存
AI_CACHE_ENABLED=true

# 缓存时间（秒）
AI_CACHE_TTL=3600

# 缓存前缀
AI_CACHE_PREFIX=ai_service_

# ===========================================
# 安全配置
# ===========================================

# 启用内容过滤
AI_CONTENT_FILTER_ENABLED=true

# 单条消息最大长度
AI_MAX_MESSAGE_LENGTH=10000

# 最大消息数量
AI_MAX_MESSAGES_COUNT=50

# ===========================================
# 使用说明
# ===========================================

# 1. 复制此文件为项目根目录的 .env 文件
# 2. 填入实际的API密钥和配置值
# 3. 根据需要调整其他配置参数
# 4. 确保 .env 文件不被提交到版本控制系统

# 获取API密钥的方法：
# - DeepSeek: https://platform.deepseek.com/
# - OpenAI: https://platform.openai.com/api-keys
# - Claude: https://console.anthropic.com/

# 注意事项：
# - API密钥是敏感信息，请妥善保管
# - 不同的AI服务提供商有不同的定价策略
# - 建议先在测试环境中验证配置
# - 定期检查API使用量和费用