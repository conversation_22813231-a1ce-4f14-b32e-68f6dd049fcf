<?php

namespace app\ai\factories;

use app\ai\memory\MySqlMemory;
use app\ai\interfaces\MemoryInterface;
use app\ai\exceptions\AiServiceException;
use app\ai\utils\Logger;
use think\facade\Config;

/**
 * MySQL记忆存储器工厂类
 * 负责创建和管理MySQL存储器实例
 */
class MySqlMemoryFactory
{
    /**
     * 存储器实例缓存
     * @var array
     */
    protected static array $instances = [];
    
    /**
     * 默认配置环境
     * @var string
     */
    protected static string $defaultEnvironment = 'default';
    
    /**
     * 配置缓存
     * @var array
     */
    protected static array $configCache = [];
    
    /**
     * 创建MySQL存储器实例
     * @param string $environment 环境配置名称
     * @param array $customConfig 自定义配置（可选）
     * @return MemoryInterface
     * @throws AiServiceException
     */
    public static function create(string $environment = null, array $customConfig = []): MemoryInterface
    {
        $environment = $environment ?: static::$defaultEnvironment;
        $cacheKey = $environment . '_' . md5(serialize($customConfig));
        
        // 检查实例缓存
        if (isset(static::$instances[$cacheKey])) {
            Logger::debug('Returning cached MySQL memory instance', [
                'environment' => $environment,
                'cache_key' => $cacheKey
            ]);
            return static::$instances[$cacheKey];
        }
        
        try {
            // 获取配置
            $config = static::getConfig($environment, $customConfig);
            
            // 验证配置
            static::validateConfig($config);
            
            // 创建实例
            $instance = new MySqlMemory($config);
            
            // 缓存实例
            static::$instances[$cacheKey] = $instance;
            
            Logger::info('MySQL memory instance created', [
                'environment' => $environment,
                'connection' => $config['connection'] ?? 'default',
                'max_history_length' => $config['max_history_length'] ?? 100
            ]);
            
            return $instance;
        } catch (\Exception $e) {
            Logger::error('Failed to create MySQL memory instance', [
                'environment' => $environment,
                'error' => $e->getMessage()
            ]);
            throw new AiServiceException("Failed to create MySQL memory instance: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 创建开发环境存储器
     * @param array $customConfig
     * @return MemoryInterface
     */
    public static function createForDevelopment(array $customConfig = []): MemoryInterface
    {
        return static::create('development', $customConfig);
    }
    
    /**
     * 创建生产环境存储器
     * @param array $customConfig
     * @return MemoryInterface
     */
    public static function createForProduction(array $customConfig = []): MemoryInterface
    {
        return static::create('production', $customConfig);
    }
    
    /**
     * 创建测试环境存储器
     * @param array $customConfig
     * @return MemoryInterface
     */
    public static function createForTesting(array $customConfig = []): MemoryInterface
    {
        return static::create('testing', $customConfig);
    }
    
    /**
     * 创建高性能存储器
     * @param array $customConfig
     * @return MemoryInterface
     */
    public static function createHighPerformance(array $customConfig = []): MemoryInterface
    {
        return static::create('high_performance', $customConfig);
    }
    
    /**
     * 创建分片存储器
     * @param array $customConfig
     * @return MemoryInterface
     */
    public static function createSharded(array $customConfig = []): MemoryInterface
    {
        return static::create('sharded', $customConfig);
    }
    
    /**
     * 根据应用环境自动创建存储器
     * @param array $customConfig
     * @return MemoryInterface
     */
    public static function createAuto(array $customConfig = []): MemoryInterface
    {
        $appEnv = env('APP_ENV', 'development');
        
        $environmentMap = [
            'development' => 'development',
            'testing' => 'testing',
            'production' => 'production',
            'staging' => 'production',
        ];
        
        $environment = $environmentMap[$appEnv] ?? 'default';
        
        Logger::info('Auto-creating MySQL memory instance', [
            'app_env' => $appEnv,
            'memory_env' => $environment
        ]);
        
        return static::create($environment, $customConfig);
    }
    
    /**
     * 获取配置
     * @param string $environment
     * @param array $customConfig
     * @return array
     */
    protected static function getConfig(string $environment, array $customConfig = []): array
    {
        $cacheKey = $environment . '_' . md5(serialize($customConfig));
        
        if (isset(static::$configCache[$cacheKey])) {
            return static::$configCache[$cacheKey];
        }
        
        // 加载基础配置
        $configFile = app()->getAppPath() . 'ai/config/mysql_memory_config.php';
        
        if (!file_exists($configFile)) {
            throw new AiServiceException("MySQL memory config file not found: {$configFile}");
        }
        
        $allConfigs = include $configFile;
        
        if (!isset($allConfigs[$environment])) {
            throw new AiServiceException("MySQL memory config not found for environment: {$environment}");
        }
        
        $config = $allConfigs[$environment];
        
        // 合并自定义配置
        if (!empty($customConfig)) {
            $config = array_merge_recursive($config, $customConfig);
        }
        
        // 处理表名配置
        if (isset($config['tables'])) {
            $config['session_table'] = $config['tables']['sessions'];
            $config['message_table'] = $config['tables']['messages'];
            $config['context_table'] = $config['tables']['contexts'];
        }
        
        // 处理记忆配置
        if (isset($config['memory']['max_history_length'])) {
            $config['max_history_length'] = $config['memory']['max_history_length'];
        }
        
        // 缓存配置
        static::$configCache[$cacheKey] = $config;
        
        return $config;
    }
    
    /**
     * 验证配置
     * @param array $config
     * @throws AiServiceException
     */
    protected static function validateConfig(array $config): void
    {
        $requiredKeys = ['connection'];
        
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key])) {
                throw new AiServiceException("Missing required config key: {$key}");
            }
        }
        
        // 验证连接配置
        $dbConfig = Config::get('database.connections.' . $config['connection']);
        if (empty($dbConfig)) {
            throw new AiServiceException("Database connection config not found: {$config['connection']}");
        }
        
        // 验证表名
        $tableKeys = ['session_table', 'message_table', 'context_table'];
        foreach ($tableKeys as $key) {
            if (isset($config[$key]) && empty($config[$key])) {
                throw new AiServiceException("Invalid table name for {$key}");
            }
        }
        
        // 验证数值配置
        if (isset($config['max_history_length']) && $config['max_history_length'] < 1) {
            throw new AiServiceException('max_history_length must be greater than 0');
        }
    }
    
    /**
     * 清除实例缓存
     * @param string $environment 指定环境，为空则清除所有
     */
    public static function clearCache(string $environment = null): void
    {
        if ($environment) {
            // 清除指定环境的缓存
            $keysToRemove = [];
            foreach (static::$instances as $key => $instance) {
                if (strpos($key, $environment . '_') === 0) {
                    $keysToRemove[] = $key;
                }
            }
            
            foreach ($keysToRemove as $key) {
                unset(static::$instances[$key]);
            }
            
            Logger::info('MySQL memory cache cleared for environment', [
                'environment' => $environment,
                'cleared_count' => count($keysToRemove)
            ]);
        } else {
            // 清除所有缓存
            $count = count(static::$instances);
            static::$instances = [];
            static::$configCache = [];
            
            Logger::info('All MySQL memory cache cleared', [
                'cleared_count' => $count
            ]);
        }
    }
    
    /**
     * 获取所有缓存的实例
     * @return array
     */
    public static function getCachedInstances(): array
    {
        return static::$instances;
    }
    
    /**
     * 设置默认环境
     * @param string $environment
     */
    public static function setDefaultEnvironment(string $environment): void
    {
        static::$defaultEnvironment = $environment;
        Logger::info('Default MySQL memory environment changed', [
            'environment' => $environment
        ]);
    }
    
    /**
     * 获取默认环境
     * @return string
     */
    public static function getDefaultEnvironment(): string
    {
        return static::$defaultEnvironment;
    }
    
    /**
     * 检查环境配置是否存在
     * @param string $environment
     * @return bool
     */
    public static function hasEnvironment(string $environment): bool
    {
        try {
            static::getConfig($environment);
            return true;
        } catch (AiServiceException $e) {
            return false;
        }
    }
    
    /**
     * 获取所有可用的环境配置
     * @return array
     */
    public static function getAvailableEnvironments(): array
    {
        $configFile = app()->getAppPath() . 'ai/config/mysql_memory_config.php';
        
        if (!file_exists($configFile)) {
            return [];
        }
        
        $allConfigs = include $configFile;
        return array_keys($allConfigs);
    }
    
    /**
     * 创建带有自定义表前缀的存储器
     * @param string $prefix 表前缀
     * @param string $environment 环境
     * @return MemoryInterface
     */
    public static function createWithTablePrefix(string $prefix, string $environment = null): MemoryInterface
    {
        $customConfig = [
            'tables' => [
                'sessions' => $prefix . 'ai_chat_sessions',
                'messages' => $prefix . 'ai_chat_messages',
                'contexts' => $prefix . 'ai_chat_contexts',
            ]
        ];
        
        return static::create($environment, $customConfig);
    }
    
    /**
     * 创建只读存储器（用于数据分析等场景）
     * @param string $environment
     * @return MemoryInterface
     */
    public static function createReadOnly(string $environment = null): MemoryInterface
    {
        $customConfig = [
            'connection' => 'mysql_readonly',  // 假设有只读连接配置
            'memory' => [
                'auto_cleanup' => false,
            ]
        ];
        
        return static::create($environment, $customConfig);
    }
}