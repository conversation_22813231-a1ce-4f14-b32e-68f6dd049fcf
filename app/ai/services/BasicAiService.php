<?php

namespace app\ai\services;

use app\ai\config\ConfigManager;
use app\ai\exceptions\AiServiceException;
use app\ai\utils\Logger;
use app\ai\utils\HttpClient;

/**
 * 基础AI服务
 * 提供简单直接的AI调用功能
 */
class BasicAiService
{
    /**
     * 默认服务提供商
     * @var string
     */
    protected string $defaultProvider;
    
    /**
     * 支持的服务提供商配置
     * @var array
     */
    protected array $providers = [];
    
    /**
     * 构造函数
     * 
     * @param string|null $defaultProvider 默认服务提供商
     */
    public function __construct(?string $defaultProvider = null)
    {
        $this->defaultProvider = $defaultProvider ?? ConfigManager::get('default_provider', 'deepseek');
        $this->loadProviders();

        Logger::info('BasicAiService initialized', [
            'default_provider' => $this->defaultProvider
        ]);
    }
    
    /**
     * 加载服务提供商配置
     */
    protected function loadProviders(): void
    {
        $this->providers = [
            'deepseek' => [
                'api_key' => ConfigManager::get('providers.deepseek.api_key'),
                'base_url' => ConfigManager::get('providers.deepseek.base_url', 'https://api.deepseek.com'),
                'model' => ConfigManager::get('providers.deepseek.model', 'deepseek-chat'),
                'timeout' => ConfigManager::get('providers.deepseek.timeout', 30)
            ],
            'openai' => [
                'api_key' => ConfigManager::get('providers.openai.api_key'),
                'base_url' => ConfigManager::get('providers.openai.base_url', 'https://api.openai.com/v1'),
                'model' => ConfigManager::get('providers.openai.model', 'gpt-3.5-turbo'),
                'timeout' => ConfigManager::get('providers.openai.timeout', 30)
            ],
            'claude' => [
                'api_key' => ConfigManager::get('providers.claude.api_key'),
                'base_url' => ConfigManager::get('providers.claude.base_url', 'https://api.anthropic.com'),
                'model' => ConfigManager::get('providers.claude.model', 'claude-3-sonnet-20240229'),
                'timeout' => ConfigManager::get('providers.claude.timeout', 30)
            ]
        ];
    }
    
    /**
     * 聊天对话
     * 
     * @param mixed $messages 消息内容（字符串或消息数组）
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return string
     * @throws AiServiceException
     */
    public function chat($messages, array $options = [], ?string $provider = null): string
    {
        $provider = $provider ?? $this->defaultProvider;

        Logger::info('Chat request started: '.  $provider);
        
        if (!isset($this->providers[$provider])) {
            throw new AiServiceException("Unsupported provider: {$provider}");
        }
        
        $config = $this->providers[$provider];

        Logger::info('Chat request started', $config);
        
        if (empty($config['api_key'])) {
            throw new AiServiceException("API key not configured for provider: {$provider}");
        }
        
        // 标准化消息格式
        if (is_string($messages)) {
            $messages = [['role' => 'user', 'content' => $messages]];
        }
        
        $startTime = microtime(true);
        
        try {
            $response = $this->makeApiRequest($provider, $config, $messages, $options);
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            Logger::info('Chat request completed', [
                'provider' => $provider,
                'response_time' => $responseTime,
                'message_count' => count($messages)
            ]);
            
            return $response;
            
        } catch (\Exception $e) {
            Logger::error('Chat request failed', [
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);
            
            throw new AiServiceException("Chat request failed: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 流式聊天对话
     * 
     * @param mixed $messages 消息内容
     * @param callable $callback 回调函数
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return void
     * @throws AiServiceException
     */
    public function streamChat($messages, callable $callback, array $options = [], ?string $provider = null): void
    {
        $provider = $provider ?? $this->defaultProvider;
        
        if (!isset($this->providers[$provider])) {
            throw new AiServiceException("Unsupported provider: {$provider}");
        }
        
        $config = $this->providers[$provider];
        
        if (empty($config['api_key'])) {
            throw new AiServiceException("API key not configured for provider: {$provider}");
        }
        
        // 标准化消息格式
        if (is_string($messages)) {
            $messages = [['role' => 'user', 'content' => $messages]];
        }
        
        try {
            $this->makeStreamApiRequest($provider, $config, $messages, $options, $callback);
            
        } catch (\Exception $e) {
            Logger::error('Stream chat request failed', [
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);
            
            throw new AiServiceException("Stream chat request failed: {$e->getMessage()}", 0, $e);
        }
    }
    
    /**
     * 文本生成
     * 
     * @param string $prompt 提示文本
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return string
     * @throws AiServiceException
     */
    public function generateText(string $prompt, array $options = [], ?string $provider = null): string
    {
        return $this->chat($prompt, $options, $provider);
    }
    
    /**
     * 文本分析
     * 
     * @param string $text 待分析文本
     * @param string $type 分析类型（sentiment, summary, keywords等）
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return string
     * @throws AiServiceException
     */
    public function analyzeText(string $text, string $type = 'summary', array $options = [], ?string $provider = null): string
    {
        $prompts = [
            'sentiment' => "请分析以下文本的情感倾向（积极/消极/中性）：\n\n{$text}",
            'summary' => "请总结以下文本的主要内容：\n\n{$text}",
            'keywords' => "请提取以下文本的关键词：\n\n{$text}",
            'classification' => "请对以下文本进行分类：\n\n{$text}"
        ];
        
        $prompt = $prompts[$type] ?? $prompts['summary'];
        
        return $this->chat($prompt, $options, $provider);
    }
    
    /**
     * 文本翻译
     * 
     * @param string $text 待翻译文本
     * @param string $targetLang 目标语言
     * @param string $sourceLang 源语言（可选）
     * @param array $options 选项参数
     * @param string|null $provider 指定服务提供商
     * @return string
     * @throws AiServiceException
     */
    public function translateText(string $text, string $targetLang, string $sourceLang = 'auto', array $options = [], ?string $provider = null): string
    {
        $prompt = "请将以下文本翻译为{$targetLang}：\n\n{$text}";
        
        if ($sourceLang !== 'auto') {
            $prompt = "请将以下{$sourceLang}文本翻译为{$targetLang}：\n\n{$text}";
        }
        
        return $this->chat($prompt, $options, $provider);
    }
    
    /**
     * 发起API请求
     * 
     * @param string $provider 服务提供商
     * @param array $config 配置信息
     * @param array $messages 消息数组
     * @param array $options 选项参数
     * @return string
     * @throws \Exception
     */
    protected function makeApiRequest(string $provider, array $config, array $messages, array $options): string
    {
        $url = rtrim($config['base_url'], '/') . '/chat/completions';

        Logger::info('Making API request: ' . $url);
        
        $data = [
            'model' => $options['model'] ?? $config['model'],
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 2000,
            'stream' => false
        ];
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $config['api_key']
        ];
        
        // 特殊处理不同提供商的请求头
        if ($provider === 'claude') {
            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $config['api_key'],
                'anthropic-version: 2023-06-01'
            ];
            $url = rtrim($config['base_url'], '/') . '/v1/messages';
            $data = [
                'model' => $options['model'] ?? $config['model'],
                'messages' => $messages,
                'max_tokens' => $options['max_tokens'] ?? 2000
            ];
        }
        
        // 转换headers格式为关联数组
        $headerArray = [];
        foreach ($headers as $header) {
            if (strpos($header, ':') !== false) {
                list($key, $value) = explode(':', $header, 2);
                $headerArray[trim($key)] = trim($value);
            }
        }

        // 创建HTTP客户端，设置更短的超时时间用于快速失败
        $httpClient = new HttpClient([
            'timeout' => min($config['timeout'], 30), // 最大30秒
            'connect_timeout' => 10, // 连接超时10秒
            'debug' => ConfigManager::get('debug', false),
            'max_retries' => 2, // 减少重试次数
            'retry_delay' => 2
        ]);

        Logger::info('Making API request', [
            'provider' => $provider,
            'url' => $url,
            'model' => $data['model'],
            'message_count' => count($messages)
        ]);

        try {
            $result = $httpClient->post($url, $data, $headerArray);
            $responseData = $result['data'];

            Logger::info('API request successful', [
                'provider' => $provider,
                'execution_time' => $result['execution_time'],
                'response_size' => $result['response_size']
            ]);

        } catch (AiServiceException $e) {
            Logger::error('API request failed', [
                'provider' => $provider,
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            throw new \Exception("API request failed: {$e->getMessage()}");
        }

        // 解析不同提供商的响应格式
        if ($provider === 'claude') {
            if (isset($responseData['content'][0]['text'])) {
                return $responseData['content'][0]['text'];
            }
        } else {
            if (isset($responseData['choices'][0]['message']['content'])) {
                return $responseData['choices'][0]['message']['content'];
            }
        }

        throw new \Exception("Unexpected response format: " . json_encode($responseData));
    }
    
    /**
     * 发起流式API请求
     * 
     * @param string $provider 服务提供商
     * @param array $config 配置信息
     * @param array $messages 消息数组
     * @param array $options 选项参数
     * @param callable $callback 回调函数
     * @return void
     * @throws \Exception
     */
    protected function makeStreamApiRequest(string $provider, array $config, array $messages, array $options, callable $callback): void
    {
        $url = rtrim($config['base_url'], '/') . '/chat/completions';
        
        $data = [
            'model' => $options['model'] ?? $config['model'],
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 2000,
            'stream' => true
        ];
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $config['api_key']
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => false,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => $config['timeout'],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_WRITEFUNCTION => function($ch, $data) use ($callback) {
                $lines = explode("\n", $data);
                foreach ($lines as $line) {
                    if (strpos($line, 'data: ') === 0) {
                        $json = substr($line, 6);
                        if ($json === '[DONE]') {
                            break;
                        }
                        $decoded = json_decode($json, true);
                        if (isset($decoded['choices'][0]['delta']['content'])) {
                            $callback($decoded['choices'][0]['delta']['content']);
                        }
                    }
                }
                return strlen($data);
            }
        ]);
        
        $result = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception("CURL error: {$error}");
        }
        
        if ($result === false) {
            throw new \Exception("Stream request failed");
        }
    }
    
    /**
     * 获取支持的服务提供商列表
     * 
     * @return array
     */
    public function getSupportedProviders(): array
    {
        return array_keys($this->providers);
    }
    
    /**
     * 检查服务提供商是否可用
     * 
     * @param string $provider 服务提供商
     * @return bool
     */
    public function isProviderAvailable(string $provider): bool
    {
        return isset($this->providers[$provider]) && !empty($this->providers[$provider]['api_key']);
    }
    
    /**
     * 获取默认服务提供商
     * 
     * @return string
     */
    public function getDefaultProvider(): string
    {
        return $this->defaultProvider;
    }
    
    /**
     * 设置默认服务提供商
     * 
     * @param string $provider 服务提供商
     * @return self
     * @throws AiServiceException
     */
    public function setDefaultProvider(string $provider): self
    {
        if (!isset($this->providers[$provider])) {
            throw new AiServiceException("Unsupported provider: {$provider}");
        }
        
        $this->defaultProvider = $provider;
        
        Logger::info('Default provider changed', [
            'new_provider' => $provider
        ]);
        
        return $this;
    }
}