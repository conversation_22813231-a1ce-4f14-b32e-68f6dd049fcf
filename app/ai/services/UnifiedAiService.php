<?php

namespace app\ai\services;

use app\ai\container\ServiceContainer;
use app\ai\events\EventDispatcher;
use app\ai\cache\CacheManager;
use app\ai\monitoring\MetricsCollector;
use app\ai\exceptions\AiServiceException;
use app\ai\events\AiRequestStartedEvent;
use app\ai\events\AiRequestCompletedEvent;
use app\ai\events\AiRequestFailedEvent;

/**
 * 统一AI服务
 * 提供智能路由、缓存、监控等高级功能
 */
class UnifiedAiService
{
    /**
     * 服务容器
     * @var ServiceContainer
     */
    protected ServiceContainer $container;

    /**
     * 基础AI服务
     * @var BasicAiService
     */
    protected BasicAiService $basicAi;

    /**
     * LangChain服务
     * @var LangChainService
     */
    protected LangChainService $langChain;

    /**
     * 事件调度器
     * @var EventDispatcher
     */
    protected EventDispatcher $events;

    /**
     * 缓存管理器
     * @var CacheManager
     */
    protected CacheManager $cache;

    /**
     * 指标收集器
     * @var MetricsCollector
     */
    protected MetricsCollector $metrics;

    /**
     * 构造函数
     * @param BasicAiService|null $basicAi
     * @param LangChainService|null $langChain
     */
    public function __construct(?BasicAiService $basicAi = null, ?LangChainService $langChain = null)
    {
        $this->container = ServiceContainer::getInstance();
        $this->basicAi = $basicAi ?? $this->container->make('ai.basic');
        $this->langChain = $langChain ?? $this->container->make('ai.langchain');
        $this->events = $this->container->make('ai.events');
        $this->cache = $this->container->make('ai.cache');
        $this->metrics = $this->container->make('ai.metrics');
    }

    /**
     * 智能处理请求
     * @param string $input 输入内容
     * @param array $options 选项
     * @return AiResponse
     */
    public function process(string $input, array $options = []): AiResponse
    {
        $requestId = uniqid('ai_req_', true);
        $startTime = microtime(true);
        
        try {
            // 分析请求类型
            $requestType = $this->analyzeRequestType($input, $options);
            
            // 检查缓存
            if ($this->shouldUseCache($options)) {
                $cachedResponse = $this->getCachedResponse($input, $options);
                if ($cachedResponse !== null) {
                    $this->metrics->recordCacheOperation('get', true);
                    return new AiResponse($cachedResponse, 'cache', 0, $requestId);
                }
                $this->metrics->recordCacheOperation('get', false);
            }

            // 选择合适的服务
            $service = $this->selectService($requestType, $options);
            $provider = $options['provider'] ?? 'auto';
            $model = $options['model'] ?? 'auto';

            // 触发请求开始事件
            $event = new AiRequestStartedEvent($provider, $model, $options, $requestId);
            $this->events->dispatch($event->getEventName(), $event->getEventData());

            // 执行请求
            $response = $this->executeRequest($service, $input, $options, $requestType);
            
            $duration = (microtime(true) - $startTime) * 1000; // 转换为毫秒

            // 缓存响应
            if ($this->shouldUseCache($options)) {
                $this->cacheResponse($input, $options, $response);
                $this->metrics->recordCacheOperation('set');
            }

            // 记录指标
            $this->metrics->recordAiRequest($provider, $model, $duration, true);

            // 触发请求完成事件
            $event = new AiRequestCompletedEvent($provider, $model, $response, $requestId, $duration);
            $this->events->dispatch($event->getEventName(), $event->getEventData());

            return new AiResponse($response, $service, $duration, $requestId);

        } catch (\Exception $e) {
            $duration = (microtime(true) - $startTime) * 1000;
            
            // 记录失败指标
            $provider = $options['provider'] ?? 'unknown';
            $model = $options['model'] ?? 'unknown';
            $this->metrics->recordAiRequest($provider, $model, $duration, false);

            // 触发请求失败事件
            $event = new AiRequestFailedEvent($provider, $model, $e->getMessage(), $requestId, $duration);
            $this->events->dispatch($event->getEventName(), $event->getEventData());

            throw $e;
        }
    }

    /**
     * 分析请求类型
     * @param string $input 输入内容
     * @param array $options 选项
     * @return string
     */
    protected function analyzeRequestType(string $input, array $options): string
    {
        // 如果明确指定了类型
        if (isset($options['type'])) {
            return $options['type'];
        }

        // 基于输入内容分析
        $input = strtolower($input);
        
        // 检查是否需要记忆功能
        if (isset($options['session_id']) || 
            str_contains($input, '记住') || 
            str_contains($input, '之前') ||
            str_contains($input, 'remember')) {
            return 'memory';
        }

        // 检查是否需要工具调用
        if (str_contains($input, '计算') || 
            str_contains($input, '搜索') ||
            str_contains($input, '查询') ||
            str_contains($input, 'calculate') ||
            str_contains($input, 'search')) {
            return 'tool';
        }

        // 检查是否需要复杂推理
        if (str_contains($input, '分析') || 
            str_contains($input, '推理') ||
            str_contains($input, '步骤') ||
            str_contains($input, 'analyze') ||
            str_contains($input, 'step by step')) {
            return 'reasoning';
        }

        // 默认为简单对话
        return 'simple';
    }

    /**
     * 选择合适的服务
     * @param string $requestType 请求类型
     * @param array $options 选项
     * @return string
     */
    protected function selectService(string $requestType, array $options): string
    {
        switch ($requestType) {
            case 'memory':
            case 'tool':
            case 'reasoning':
                return 'langchain';
            case 'simple':
            default:
                return 'basic';
        }
    }

    /**
     * 执行请求
     * @param string $service 服务类型
     * @param string $input 输入内容
     * @param array $options 选项
     * @param string $requestType 请求类型
     * @return string
     */
    protected function executeRequest(string $service, string $input, array $options, string $requestType): string
    {
        switch ($service) {
            case 'langchain':
                return $this->executeLangChainRequest($input, $options, $requestType);
            case 'basic':
            default:
                return $this->executeBasicRequest($input, $options);
        }
    }

    /**
     * 执行基础AI请求
     * @param string $input 输入内容
     * @param array $options 选项
     * @return string
     */
    protected function executeBasicRequest(string $input, array $options): string
    {
        $messages = [['role' => 'user', 'content' => $input]];
        return $this->basicAi->chat($messages, $options);
    }

    /**
     * 执行LangChain请求
     * @param string $input 输入内容
     * @param array $options 选项
     * @param string $requestType 请求类型
     * @return string
     */
    protected function executeLangChainRequest(string $input, array $options, string $requestType): string
    {
        switch ($requestType) {
            case 'memory':
                $sessionId = $options['session_id'] ?? 'default';
                return $this->langChain->executeWithMemory($input, $sessionId, $options);
                
            case 'tool':
                $tools = $options['tools'] ?? ['calculator', 'http_request'];
                return $this->langChain->createReActAgent($input, $tools, $options);
                
            case 'reasoning':
                $prompt = "请逐步分析以下问题：{input}";
                return $this->langChain->createLLMChain($prompt, ['input' => $input], $options);
                
            default:
                return $this->langChain->executeWithMemory($input, 'default', $options);
        }
    }

    /**
     * 是否应该使用缓存
     * @param array $options 选项
     * @return bool
     */
    protected function shouldUseCache(array $options): bool
    {
        return $options['cache'] ?? true;
    }

    /**
     * 获取缓存的响应
     * @param string $input 输入内容
     * @param array $options 选项
     * @return string|null
     */
    protected function getCachedResponse(string $input, array $options): ?string
    {
        $provider = $options['provider'] ?? 'auto';
        $model = $options['model'] ?? 'auto';
        $messages = [['role' => 'user', 'content' => $input]];
        
        return $this->cache->getCachedAiResponse($provider, $model, $messages, $options);
    }

    /**
     * 缓存响应
     * @param string $input 输入内容
     * @param array $options 选项
     * @param string $response 响应
     */
    protected function cacheResponse(string $input, array $options, string $response): void
    {
        $provider = $options['provider'] ?? 'auto';
        $model = $options['model'] ?? 'auto';
        $messages = [['role' => 'user', 'content' => $input]];
        $ttl = $options['cache_ttl'] ?? null;
        
        $this->cache->cacheAiResponse($provider, $model, $messages, $options, $response, $ttl);
    }

    /**
     * 获取服务状态
     * @return array
     */
    public function getStatus(): array
    {
        return [
            'basic_ai' => 'active',
            'langchain' => 'active',
            'cache' => $this->cache->getStats(),
            'metrics' => $this->metrics->getSummary(),
        ];
    }

    /**
     * 批量处理请求
     * @param array $inputs 输入数组
     * @param array $options 选项
     * @return array
     */
    public function batchProcess(array $inputs, array $options = []): array
    {
        $results = [];
        
        foreach ($inputs as $index => $input) {
            try {
                $results[$index] = $this->process($input, $options);
            } catch (\Exception $e) {
                $results[$index] = new AiResponse('', 'error', 0, '', $e->getMessage());
            }
        }
        
        return $results;
    }
}

/**
 * AI响应类
 */
class AiResponse
{
    public string $content;
    public string $service;
    public float $duration;
    public string $requestId;
    public ?string $error;

    public function __construct(
        string $content,
        string $service,
        float $duration,
        string $requestId = '',
        ?string $error = null
    ) {
        $this->content = $content;
        $this->service = $service;
        $this->duration = $duration;
        $this->requestId = $requestId ?: uniqid('ai_resp_', true);
        $this->error = $error;
    }

    public function isSuccess(): bool
    {
        return $this->error === null;
    }

    public function toArray(): array
    {
        return [
            'content' => $this->content,
            'service' => $this->service,
            'duration' => $this->duration,
            'request_id' => $this->requestId,
            'error' => $this->error,
            'success' => $this->isSuccess(),
        ];
    }
}
