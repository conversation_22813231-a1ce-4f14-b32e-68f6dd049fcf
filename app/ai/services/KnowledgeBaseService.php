<?php

namespace app\ai\services;

use app\ai\memory\MySqlMemory;
use app\ai\utils\Logger;
use app\common\goee_segmenta\MultiLanguageSegmentationService;
use app\model\Help;
use think\facade\Cache;

/**
 * AI知识库服务
 * 整合帮助文档与AI功能，提供智能问答
 */
class KnowledgeBaseService
{
    /**
     * AI服务
     * @var UnifiedAiService
     */
    protected $aiService;
    
    /**
     * 记忆服务
     * @var MySqlMemory
     */
    protected $memory;

    /**
     * 中文分词服务
     * @var MultiLanguageSegmentationService
     */
    protected $segmentationService;

    /**
     * 缓存前缀
     * @var string
     */
    protected $cachePrefix = 'ai_kb_';
    
    /**
     * 回复模式配置
     * @var array
     */
    protected array $responseConfig = [
        'mode' => 'simple', // simple, formal, detailed
        'include_sources' => false,
        'include_suggestions' => true,
        'include_fallback_message' => true,
        'max_content_length' => 500
    ];

    /**
     * 构造函数
     */
    public function __construct(array $config = [])
    {
        $this->aiService = new UnifiedAiService();
        $this->memory = new MySqlMemory();
        $this->segmentationService = new MultiLanguageSegmentationService();

        // 合并配置
        if (!empty($config)) {
            $this->responseConfig = array_merge($this->responseConfig, $config);
        }
    }
    
    /**
     * 智能问答
     * @param string $question 用户问题
     * @param array $options 选项
     * @return array
     */
    public function ask(string $question, array $options = []): array
    {
        try {
            $sessionId = $options['session_id'] ?? 'kb_' . uniqid();
            $useMemory = $options['use_memory'] ?? true;
            
            // 1. 搜索相关帮助文档
            $relevantHelps = $this->searchRelevantHelps($question);
            
            // 2. 构建上下文
            $context = $this->buildContext($question, $relevantHelps, $sessionId, $useMemory);
            
            // 3. 生成AI回复
            $response = $this->generateAiResponse($question, $context, $sessionId);

            Logger::info('Knowledge base query', $response);
            
            // 4. 保存对话记录
            if ($useMemory) {
                $this->saveConversation($sessionId, $question, $response['content'], $relevantHelps);
            }
            
            return [
                'success' => true,
                'content' => $response['content'],
                'sources' => $this->responseConfig['include_sources'] ?? $relevantHelps,
                'session_id' => $sessionId,
                'confidence' => $this->calculateConfidence($relevantHelps),
                'suggestions' => $this->generateSuggestions($question, $relevantHelps)
            ];
            
        } catch (\Exception $e) {
            Logger::error('Knowledge base query failed', [
                'question' => $question,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'content' => '抱歉，我暂时无法回答您的问题，请稍后再试或联系人工客服。',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 搜索相关帮助文档
     * @param string $question
     * @return array
     */
    protected function searchRelevantHelps(string $question): array
    {
        // 缓存键
        $cacheKey = $this->cachePrefix . 'search_' . md5($question);
        $cached = $this->safeGetCache($cacheKey);
        Logger::info('Search relevant helps from cache', ['cached' => $cached]);

        if ($cached) {
            return $cached;
        }
        
        // 1. 关键词匹配搜索
        $keywordResults = $this->keywordSearch($question);
        
        // 2. 语义相似度搜索（如果有向量数据库）
        $semanticResults = $this->semanticSearch($question);
        
        // 3. 合并和排序结果
        $results = $this->mergeAndRankResults($keywordResults, $semanticResults);
        
        // 缓存结果（5分钟）
        $this->safeSetCache($cacheKey, $results, 300);
        
        return $results;
    }
    
    /**
     * 关键词搜索
     * @param string $question
     * @return array
     */
    protected function keywordSearch(string $question): array
    {
        // 提取关键词
        $keywords = $this->extractKeywords($question);

        Logger::info('Keyword search', ['keywords' => $keywords]);
        
        if (empty($keywords)) {
            return [];
        }
        
        // 构建搜索条件
        $query = Help::where('enabled', 1);
        
        foreach ($keywords as $keyword) {
            $query->whereOr(function($q) use ($keyword) {
                $q->where('title', 'like', "%{$keyword}%")
                  ->whereOr('content', 'like', "%{$keyword}%");
            });
        }
        
        $helps = $query->with(['category'])
            ->field('id,title,content,category,sort')
            ->order('sort', 'desc')
            ->limit(10)
            ->select()
            ->toArray();
        Logger::info('Keyword search', ['helps' => $helps]);
        
        // 计算相关性分数
        foreach ($helps as &$help) {
            $help['relevance_score'] = $this->calculateKeywordRelevance($question, $help);
            $help['search_type'] = 'keyword';
        }
        
        return $helps;
    }
    
    /**
     * 语义搜索（简化版本）
     * @param string $question
     * @return array
     */
    protected function semanticSearch(string $question): array
    {
        // 这里可以集成向量数据库进行语义搜索
        // 目前使用简化的语义匹配
        
        $helps = Help::where('enabled', 1)
            ->field('id,title,content,category,sort')
            ->select()
            ->toArray();
        
        $results = [];
        foreach ($helps as $help) {
            $similarity = $this->calculateSemanticSimilarity($question, $help['title'] . ' ' . $help['content']);
            Logger::info('Semantic search', ['similarity' => $similarity, 'help' => $help]);
            if ($similarity > 0.3) { // 阈值
                $help['relevance_score'] = $similarity;
                $help['search_type'] = 'semantic';
                $results[] = $help;
            }
        }
        
        // 按相关性排序
        usort($results, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });

        Logger::info('Semantic search', ['results' => $results]);
        
        return array_slice($results, 0, 5);
    }
    
    /**
     * 合并和排序搜索结果
     * @param array $keywordResults
     * @param array $semanticResults
     * @return array
     */
    protected function mergeAndRankResults(array $keywordResults, array $semanticResults): array
    {
        $merged = [];
        $seen = [];
        
        // 合并结果，避免重复
        foreach (array_merge($keywordResults, $semanticResults) as $result) {
            if (!isset($seen[$result['id']])) {
                $merged[] = $result;
                $seen[$result['id']] = true;
            }
        }
        
        // 按相关性分数排序
        usort($merged, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });
        
        return array_slice($merged, 0, 5);
    }
    
    /**
     * 构建AI上下文
     * @param string $question
     * @param array $relevantHelps
     * @param string $sessionId
     * @param bool $useMemory
     * @return array
     */
    protected function buildContext(string $question, array $relevantHelps, string $sessionId, bool $useMemory): array
    {
        $context = [
            'question' => $question,
            'knowledge_base' => $this->formatKnowledgeBase($relevantHelps),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // 添加历史对话上下文
        if ($useMemory) {
            $history = $this->memory->getHistory($sessionId);
            $context['conversation_history'] = $this->formatConversationHistory($history);
        }
        
        return $context;
    }
    
    /**
     * 生成AI回复
     * @param string $question
     * @param array $context
     * @param string $sessionId
     * @return array
     */
    protected function generateAiResponse(string $question, array $context, string $sessionId): array
    {
        // 构建提示词
        $prompt = $this->buildPrompt($question, $context);

        Logger::info('Generate AI response', ['prompt' => $prompt]);
        
        // 调用AI服务
        $response = $this->aiService->process($prompt, [
            'session_id' => $sessionId,
            'type' => 'knowledge_base',
            'temperature' => 0.7,
            'max_tokens' => 800
        ]);

        Logger::info('AI response', ['response' => $response]);
        
        return [
            'content' => $response->content ?? $response,
            'tokens' => $response->tokens ?? 0,
            'model' => $response->model ?? 'unknown'
        ];
    }
    
    /**
     * 构建AI提示词
     * @param string $question
     * @param array $context
     * @return string
     */
    protected function buildPrompt(string $question, array $context): string
    {
        $mode = $this->responseConfig['mode'];

        switch ($mode) {
            case 'simple':
                return $this->buildSimplePrompt($question, $context);
            case 'formal':
                return $this->buildFormalPrompt($question, $context);
            case 'detailed':
                return $this->buildDetailedPrompt($question, $context);
            default:
                return $this->buildSimplePrompt($question, $context);
        }
    }

    /**
     * 构建简洁模式提示词
     * @param string $question
     * @param array $context
     * @return string
     */
    protected function buildSimplePrompt(string $question, array $context): string
    {
        $prompt = "你是一个智能客服助手，请直接、简洁地回答用户问题。\n\n";

        // 添加知识库内容
        if (!empty($context['knowledge_base'])) {
            $prompt .= "参考信息：\n";
            foreach ($context['knowledge_base'] as $index => $kb) {
                $prompt .= "• {$kb['title']}：{$kb['content']}\n";
            }
            $prompt .= "\n";
        }

        $prompt .= "用户问题：{$question}\n\n";
        $prompt .= "回答要求：\n";
        $prompt .= "- 直接回答问题，不要添加多余的格式\n";
        $prompt .= "- 基于参考信息回答，如果没有相关信息就说不知道\n";
        $prompt .= "- 回答要简洁明了，不超过200字\n";
        $prompt .= "- 不要添加\"根据知识库内容\"等前缀\n";
        $prompt .= "- 不要添加联系客服的建议\n\n";
        $prompt .= "回答：";

        return $prompt;
    }

    /**
     * 构建正式模式提示词
     * @param string $question
     * @param array $context
     * @return string
     */
    protected function buildFormalPrompt(string $question, array $context): string
    {
        $prompt = "您好，我是智能客服助手，很高兴为您服务。\n\n";

        // 添加知识库内容
        if (!empty($context['knowledge_base'])) {
            $prompt .= "相关帮助信息：\n";
            foreach ($context['knowledge_base'] as $index => $kb) {
                $prompt .= ($index + 1) . ". {$kb['title']}\n   {$kb['content']}\n\n";
            }
        }

        // 添加对话历史
        if (!empty($context['conversation_history'])) {
            $prompt .= "对话记录：\n{$context['conversation_history']}\n\n";
        }

        $prompt .= "您的问题：{$question}\n\n";
        $prompt .= "回答要求：\n";
        $prompt .= "- 使用正式、礼貌的语言\n";
        $prompt .= "- 基于帮助信息提供准确回答\n";
        $prompt .= "- 如果信息不足，建议联系人工客服\n";
        $prompt .= "- 可以提供相关建议\n\n";
        $prompt .= "回答：";

        return $prompt;
    }

    /**
     * 构建详细模式提示词
     * @param string $question
     * @param array $context
     * @return string
     */
    protected function buildDetailedPrompt(string $question, array $context): string
    {
        $prompt = "你是一个专业的客服助手，需要提供详细、全面的回答。\n\n";

        // 添加知识库内容
        if (!empty($context['knowledge_base'])) {
            $prompt .= "知识库内容：\n";
            foreach ($context['knowledge_base'] as $index => $kb) {
                $prompt .= "【{$kb['title']}】\n{$kb['content']}\n\n";
            }
        }

        // 添加对话历史
        if (!empty($context['conversation_history'])) {
            $prompt .= "对话历史：\n{$context['conversation_history']}\n\n";
        }

        $prompt .= "用户问题：{$question}\n\n";
        $prompt .= "请提供详细回答，包括：\n";
        $prompt .= "1. 直接回答用户问题\n";
        $prompt .= "2. 提供相关的背景信息\n";
        $prompt .= "3. 给出具体的操作步骤（如适用）\n";
        $prompt .= "4. 提供相关建议或注意事项\n";
        $prompt .= "5. 如果需要进一步帮助，建议联系人工客服\n\n";
        $prompt .= "回答：";

        return $prompt;
    }

    /**
     * 设置回复配置
     * @param array $config
     * @return self
     */
    public function setResponseConfig(array $config): self
    {
        $this->responseConfig = array_merge($this->responseConfig, $config);
        return $this;
    }

    /**
     * 获取回复配置
     * @return array
     */
    public function getResponseConfig(): array
    {
        return $this->responseConfig;
    }

    /**
     * 设置回复模式
     * @param string $mode simple|formal|detailed
     * @return self
     */
    public function setResponseMode(string $mode): self
    {
        $this->responseConfig['mode'] = $mode;
        return $this;
    }

    /**
     * 设置配置（别名方法）
     * @param array $config
     * @return self
     */
    public function setConfig(array $config): self
    {
        return $this->setResponseConfig($config);
    }

    /**
     * 安全获取缓存
     * @param string $key
     * @return mixed
     */
    protected function safeGetCache(string $key)
    {
        try {
            return Cache::get($key);
        } catch (\Exception $e) {
            Logger::warning('Cache get failed', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 安全设置缓存
     * @param string $key
     * @param mixed $value
     * @param int $ttl
     * @return bool
     */
    protected function safeSetCache(string $key, $value, int $ttl = 300): bool
    {
        try {
            return Cache::set($key, $value, $ttl);
        } catch (\Exception $e) {
            Logger::warning('Cache set failed', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 提取关键词（使用专业分词服务）
     * @param string $text
     * @return array
     */
    protected function extractKeywords(string $text): array
    {
        try {
            // 使用专业的中文分词服务
            $keywords = $this->segmentationService->extractKeywords($text, 10);

            Logger::info('Keywords extracted', [
                'text' => $text,
                'keywords' => $keywords
            ]);

            return $keywords;
        } catch (\Exception $e) {
            Logger::error('Failed to extract keywords', [
                'text' => $text,
                'error' => $e->getMessage()
            ]);

            // 降级到简单分词
            return $this->fallbackExtractKeywords($text);
        }
    }

    /**
     * 降级关键词提取方法
     * @param string $text
     * @return array
     */
    protected function fallbackExtractKeywords(string $text): array
    {
        // 简化的关键词提取（作为备用方案）
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        $words = preg_split('/\s+/u', trim($text));

        // 基础停用词
        $stopWords = ['的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那', '有', '没', '不', '要', '会', '能', '可以', '怎么', '什么', '为什么', '如何'];
        $keywords = [];

        foreach ($words as $word) {
            if (mb_strlen($word) >= 2 && !in_array($word, $stopWords)) {
                $keywords[] = $word;
            }
        }

        return array_unique($keywords);
    }
    
    /**
     * 计算关键词相关性（改进版）
     * @param string $question
     * @param array $help
     * @return float
     */
    protected function calculateKeywordRelevance(string $question, array $help): float
    {
        $questionKeywords = $this->extractKeywords($question);
        $helpText = $help['title'] . ' ' . $help['content'];
        $helpKeywords = $this->extractKeywords($helpText);

        if (empty($questionKeywords)) {
            return 0.0;
        }

        // 改进的相关性计算
        $score = 0.0;
        $titleWeight = 2.0;  // 标题匹配权重更高
        $contentWeight = 1.0; // 内容匹配权重

        // 1. 直接字符串匹配（最重要）
        $questionLower = mb_strtolower($question);
        $titleLower = mb_strtolower($help['title']);
        $contentLower = mb_strtolower($help['content']);

        // 标题匹配检查
        if (strpos($titleLower, $questionLower) !== false) {
            $score += 0.8; // 问题在标题中
        } elseif (strpos($questionLower, $titleLower) !== false) {
            $score += 0.7; // 标题在问题中
        }

        // 内容匹配检查
        if (strpos($contentLower, $questionLower) !== false) {
            $score += 0.6; // 问题在内容中
        }

        // 部分关键词匹配检查
        foreach ($questionKeywords as $keyword) {
            $keywordLower = mb_strtolower($keyword);
            if (strpos($titleLower, $keywordLower) !== false) {
                $score += 0.3; // 关键词在标题中
            } elseif (strpos($contentLower, $keywordLower) !== false) {
                $score += 0.2; // 关键词在内容中
            }
        }

        // 2. 关键词匹配
        if (!empty($helpKeywords)) {
            $matchedKeywords = array_intersect($questionKeywords, $helpKeywords);
            $keywordScore = count($matchedKeywords) / count($questionKeywords);
            $score += $keywordScore * 0.4;
        }

        // 3. 标题关键词匹配（额外加分）
        $titleKeywords = $this->extractKeywords($help['title']);
        if (!empty($titleKeywords)) {
            $titleMatches = array_intersect($questionKeywords, $titleKeywords);
            if (!empty($titleMatches)) {
                $score += (count($titleMatches) / count($questionKeywords)) * 0.3;
            }
        }

        // 4. 长度惩罚（避免过长文档稀释相关性）
        $lengthPenalty = min(1.0, 500 / max(1, mb_strlen($help['content'])));
        $score *= $lengthPenalty;

        return min(1.0, $score);
    }
    
    /**
     * 计算语义相似度（简化版本）
     * @param string $text1
     * @param string $text2
     * @return float
     */
    protected function calculateSemanticSimilarity(string $text1, string $text2): float
    {
        // 这里可以使用更复杂的语义相似度算法
        // 目前使用简化的余弦相似度
        
        $words1 = $this->extractKeywords($text1);
        $words2 = $this->extractKeywords($text2);
        
        if (empty($words1) || empty($words2)) {
            return 0.0;
        }
        
        $intersection = array_intersect($words1, $words2);
        $magnitude1 = sqrt(count($words1));
        $magnitude2 = sqrt(count($words2));
        
        if ($magnitude1 == 0 || $magnitude2 == 0) {
            return 0.0;
        }
        
        return count($intersection) / ($magnitude1 * $magnitude2);
    }
    
    /**
     * 格式化知识库内容
     * @param array $helps
     * @return array
     */
    protected function formatKnowledgeBase(array $helps): array
    {
        $formatted = [];
        foreach ($helps as $help) {
            $formatted[] = [
                'id' => $help['id'],
                'title' => $help['title'],
                'content' => mb_substr(strip_tags($help['content']), 0, 500) . '...',
                'relevance_score' => $help['relevance_score'] ?? 0
            ];
        }
        return $formatted;
    }
    
    /**
     * 格式化对话历史
     * @param array $history
     * @return string
     */
    protected function formatConversationHistory(array $history): string
    {
        if (empty($history)) {
            return '';
        }
        
        $formatted = [];
        foreach (array_slice($history, -3) as $entry) { // 只取最近3条
            if (isset($entry['input']) && isset($entry['output'])) {
                $formatted[] = "用户：{$entry['input']}";
                $formatted[] = "助手：{$entry['output']}";
            }
        }
        
        return implode("\n", $formatted);
    }
    
    /**
     * 保存对话记录
     * @param string $sessionId
     * @param string $question
     * @param string $answer
     * @param array $sources
     */
    protected function saveConversation(string $sessionId, string $question, string $answer, array $sources): void
    {
        try {
            $this->memory->addMessage($sessionId, $question, 'input');
            $this->memory->addMessage($sessionId, $answer, 'output', [
                'sources' => array_column($sources, 'id'),
                'type' => 'knowledge_base'
            ]);
        } catch (\Exception $e) {
            Logger::error('Failed to save conversation', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 计算回答置信度（改进版）
     * @param array $relevantHelps
     * @return float
     */
    protected function calculateConfidence(array $relevantHelps): float
    {
        if (empty($relevantHelps)) {
            return 0.1;
        }

        $scores = array_column($relevantHelps, 'relevance_score');
        $maxScore = max($scores);
        $avgScore = array_sum($scores) / count($scores);
        $helpCount = count($relevantHelps);

        // 基础置信度：最高分和平均分的加权平均
        $baseConfidence = ($maxScore * 0.7) + ($avgScore * 0.3);

        // 数量加成：有多个相关文档时增加置信度
        $countBonus = min(0.2, ($helpCount - 1) * 0.05);

        // 质量阈值：如果最高分很低，大幅降低置信度
        if ($maxScore < 0.3) {
            $baseConfidence *= 0.5;
        } elseif ($maxScore > 0.7) {
            $baseConfidence += 0.1; // 高质量匹配加分
        }

        $finalConfidence = $baseConfidence + $countBonus;

        // 确保置信度在合理范围内
        $finalConfidence = max(0.1, min(0.95, $finalConfidence));

        Logger::info('Confidence calculation', [
            'max_score' => $maxScore,
            'avg_score' => $avgScore,
            'help_count' => $helpCount,
            'base_confidence' => $baseConfidence,
            'final_confidence' => $finalConfidence
        ]);

        return $finalConfidence;
    }
    
    /**
     * 生成相关建议
     * @param string $question
     * @param array $relevantHelps
     * @return array
     */
    protected function generateSuggestions(string $question, array $relevantHelps): array
    {
        $suggestions = [];

        if ($this->responseConfig['include_suggestions']) {
            try {
                // 1. 基于相关帮助文档生成基础建议
                $documentSuggestions = [];
                foreach (array_slice($relevantHelps, 0, 3) as $help) {
                    $documentSuggestions[] = [
                        'title' => $help['title'],
                        'id' => $help['id'],
                        'type' => 'document'
                    ];
                }

                // 2. 使用AI生成智能建议
                $aiSuggestions = $this->generateAiSuggestions($question, $relevantHelps);

                // 3. 合并建议并去重
                $suggestions = array_merge($documentSuggestions, $aiSuggestions);

                // 4. 限制建议数量
                $suggestions = array_slice($suggestions, 0, 5);

            } catch (\Exception $e) {
                Logger::error('Failed to generate suggestions', [
                    'question' => $question,
                    'error' => $e->getMessage()
                ]);

                // 降级到基础建议
                foreach (array_slice($relevantHelps, 0, 3) as $help) {
                    $suggestions[] = [
                        'title' => $help['title'],
                        'id' => $help['id'],
                        'type' => 'document'
                    ];
                }
            }
        }

        return $suggestions;
    }

    /**
     * 使用AI生成智能建议
     * @param string $question
     * @param array $relevantHelps
     * @return array
     */
    protected function generateAiSuggestions(string $question, array $relevantHelps): array
    {
        // 构建AI提示词用于生成建议
        $prompt = $this->buildSuggestionPrompt($question, $relevantHelps);

        try {
            // 调用AI服务生成建议
            $response = $this->aiService->process($prompt, [
                'type' => 'suggestion_generation',
                'temperature' => 0.8,
                'max_tokens' => 300
            ]);

            $content = $response->content ?? $response;

            // 解析AI生成的建议
            return $this->parseAiSuggestions($content);

        } catch (\Exception $e) {
            Logger::error('AI suggestion generation failed', [
                'question' => $question,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * 构建建议生成的AI提示词
     * @param string $question
     * @param array $relevantHelps
     * @return string
     */
    protected function buildSuggestionPrompt(string $question, array $relevantHelps): string
    {
        $prompt = "基于用户问题，生成3-5个相关的后续问题建议。\n\n";
        $prompt .= "用户问题：{$question}\n\n";

        // 添加相关文档信息
        if (!empty($relevantHelps)) {
            $prompt .= "相关文档：\n";
            foreach (array_slice($relevantHelps, 0, 3) as $help) {
                $prompt .= "- {$help['title']}\n";
            }
            $prompt .= "\n";
        }

        $prompt .= "请生成用户可能感兴趣的相关问题建议，每个建议一行，格式如下：\n";
        $prompt .= "建议1: [具体问题]\n";
        $prompt .= "建议2: [具体问题]\n";
        $prompt .= "建议3: [具体问题]\n";
        $prompt .= "\n要求：\n";
        $prompt .= "1. 建议应该与原问题相关但有所扩展\n";
        $prompt .= "2. 建议应该是用户可能真正关心的问题\n";
        $prompt .= "3. 建议应该简洁明了，易于理解\n";
        $prompt .= "4. 避免重复原问题的内容\n";

        return $prompt;
    }

    /**
     * 解析AI生成的建议内容
     * @param string $content
     * @return array
     */
    protected function parseAiSuggestions(string $content): array
    {
        $suggestions = [];

        // 按行分割内容
        $lines = explode("\n", trim($content));

        foreach ($lines as $line) {
            $line = trim($line);

            // 匹配建议格式：建议X: [问题内容]
            if (preg_match('/^建议\d+[:：]\s*(.+)$/u', $line, $matches)) {
                $suggestionText = trim($matches[1]);
                if (!empty($suggestionText)) {
                    $suggestions[] = [
                        'title' => $suggestionText,
                        'type' => 'ai_generated',
                        'id' => null
                    ];
                }
            }
            // 也支持简单的列表格式
            elseif (preg_match('/^[-*]\s*(.+)$/u', $line, $matches)) {
                $suggestionText = trim($matches[1]);
                if (!empty($suggestionText)) {
                    $suggestions[] = [
                        'title' => $suggestionText,
                        'type' => 'ai_generated',
                        'id' => null
                    ];
                }
            }
        }

        return $suggestions;
    }

    /**
     * 获取热门问题
     * @param int $limit
     * @return array
     */
    public function getPopularQuestions(int $limit = 10): array
    {
        $cacheKey = $this->cachePrefix . 'popular_questions';
        $cached = $this->safeGetCache($cacheKey);

        if ($cached !== false) {
            return $cached;
        }

        // 从帮助文档中获取热门问题
        $popular = Help::where('enabled', 1)
            ->field('id,title,category')
            ->order('sort', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();

        $this->safeSetCache($cacheKey, $popular, 1800); // 缓存30分钟
        
        return $popular;
    }
    
    /**
     * 获取分类下的问题
     * @param int $categoryId
     * @param int $limit
     * @return array
     */
    public function getQuestionsByCategory(int $categoryId, int $limit = 20): array
    {
        return Help::where('enabled', 1)
            ->where('category', $categoryId)
            ->field('id,title,content')
            ->order('sort', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    
    /**
     * 获取问题详情
     * @param int $helpId
     * @return array|null
     */
    public function getQuestionDetail(int $helpId): ?array
    {
        $help = Help::where('id', $helpId)
            ->where('enabled', 1)
            ->find();
            
        if (!$help) {
            return null;
        }
        
        return $help->toArray();
    }
}
