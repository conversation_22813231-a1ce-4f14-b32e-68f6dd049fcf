<?php

namespace app\ai\services;

use app\ai\memory\MySqlMemory;
use app\ai\utils\Logger;
use app\ai\config\BusinessScopeConfig;
use app\common\goee_segmenta\MultiLanguageSegmentationService;
use app\model\Help;
use think\facade\Cache;

/**
 * AI知识库服务
 * 整合帮助文档与AI功能，提供智能问答
 */
class KnowledgeBaseService
{
    /**
     * AI服务
     * @var UnifiedAiService
     */
    protected $aiService;
    
    /**
     * 记忆服务
     * @var MySqlMemory
     */
    protected $memory;

    /**
     * 中文分词服务
     * @var MultiLanguageSegmentationService
     */
    protected $segmentationService;

    /**
     * 缓存前缀
     * @var string
     */
    protected $cachePrefix = 'ai_kb_';
    
    /**
     * 回复模式配置
     * @var array
     */
    protected array $responseConfig = [
        'mode' => 'simple', // simple, formal, detailed
        'include_sources' => false,
        'include_suggestions' => true,
        'include_fallback_message' => true,
        'max_content_length' => 500
    ];

    /**
     * 构造函数
     */
    public function __construct(array $config = [])
    {
        $this->aiService = new UnifiedAiService();
        $this->memory = new MySqlMemory();
        $this->segmentationService = new MultiLanguageSegmentationService();

        // 合并配置
        if (!empty($config)) {
            $this->responseConfig = array_merge($this->responseConfig, $config);
        }
    }
    
    /**
     * 智能问答
     * @param string $question 用户问题
     * @param array $options 选项
     * @return array
     */
    public function ask(string $question, array $options = []): array
    {
        try {
            $sessionId = $options['session_id'] ?? 'kb_' . uniqid();
            $useMemory = $options['use_memory'] ?? true;

            // 0. 预检查：验证问题是否在业务范围内
            $scopeCheck = BusinessScopeConfig::checkScope($question);
            if ($scopeCheck['allowed'] === false) {
                Logger::info('问题超出业务范围', [
                    'question' => $question,
                    'scope' => $scopeCheck['scope'],
                    'reason' => $scopeCheck['reason']
                ]);

                return [
                    'success' => true,
                    'content' => BusinessScopeConfig::getSafeReply('out_of_scope'),
                    'sources' => [],
                    'session_id' => $sessionId,
                    'confidence' => 0.1,
                    'suggestions' => [
                        ['title' => '如何联系客服？', 'id' => null, 'type' => 'general'],
                        ['title' => '网站使用指南', 'id' => null, 'type' => 'general'],
                        ['title' => '常见问题解答', 'id' => null, 'type' => 'general']
                    ]
                ];
            }

            // 1. 搜索相关帮助文档
            $relevantHelps = $this->searchRelevantHelps($question);

            // 2. 构建上下文
            $context = $this->buildContext($question, $relevantHelps, $sessionId, $useMemory);

            // 3. 生成AI回复（包含建议）
            $response = $this->generateAiResponse($question, $context, $sessionId, $relevantHelps);

            Logger::info('Knowledge base query', $response);

            // 4. 保存对话记录
            if ($useMemory) {
                $this->saveConversation($sessionId, $question, $response['content'], $relevantHelps);
            }

            return [
                'success' => true,
                'content' => $response['content'],
                'sources' => $this->responseConfig['include_sources'] ?? $relevantHelps,
                'session_id' => $sessionId,
                'confidence' => $this->calculateConfidence($relevantHelps),
                'suggestions' => $response['suggestions'] ?? []
            ];

        } catch (\Exception $e) {
            Logger::error('Knowledge base query failed', [
                'question' => $question,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'content' => BusinessScopeConfig::getSafeReply('need_human_service'),
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 搜索相关帮助文档
     * @param string $question
     * @return array
     */
    protected function searchRelevantHelps(string $question): array
    {
        // 缓存键
        $cacheKey = $this->cachePrefix . 'search_' . md5($question);
        $cached = $this->safeGetCache($cacheKey);
        Logger::info('Search relevant helps from cache', ['cached' => $cached]);

        if ($cached) {
            return $cached;
        }
        
        // 1. 关键词匹配搜索
        $keywordResults = $this->keywordSearch($question);
        
        // 2. 语义相似度搜索（如果有向量数据库）
        $semanticResults = $this->semanticSearch($question);
        
        // 3. 合并和排序结果
        $results = $this->mergeAndRankResults($keywordResults, $semanticResults);
        
        // 缓存结果（5分钟）
        $this->safeSetCache($cacheKey, $results, 300);
        
        return $results;
    }
    
    /**
     * 关键词搜索
     * @param string $question
     * @return array
     */
    protected function keywordSearch(string $question): array
    {
        // 提取关键词
        $keywords = $this->extractKeywords($question);

        Logger::info('Keyword search', ['keywords' => $keywords]);
        
        if (empty($keywords)) {
            return [];
        }
        
        // 构建搜索条件
        $query = Help::where('enabled', 1);
        
        foreach ($keywords as $keyword) {
            $query->whereOr(function($q) use ($keyword) {
                $q->where('title', 'like', "%{$keyword}%")
                  ->whereOr('content', 'like', "%{$keyword}%");
            });
        }
        
        $helps = $query->with(['category'])
            ->field('id,title,content,category,sort')
            ->order('sort', 'desc')
            ->limit(10)
            ->select()
            ->toArray();
        Logger::info('Keyword search', ['helps' => $helps]);
        
        // 计算相关性分数
        foreach ($helps as &$help) {
            $help['relevance_score'] = $this->calculateKeywordRelevance($question, $help);
            $help['search_type'] = 'keyword';
        }
        
        return $helps;
    }
    
    /**
     * 语义搜索（简化版本）
     * @param string $question
     * @return array
     */
    protected function semanticSearch(string $question): array
    {
        // 这里可以集成向量数据库进行语义搜索
        // 目前使用简化的语义匹配
        
        $helps = Help::where('enabled', 1)
            ->field('id,title,content,category,sort')
            ->select()
            ->toArray();
        
        $results = [];
        foreach ($helps as $help) {
            $similarity = $this->calculateSemanticSimilarity($question, $help['title'] . ' ' . $help['content']);
            Logger::info('Semantic search', ['similarity' => $similarity, 'help' => $help]);
            if ($similarity > 0.3) { // 阈值
                $help['relevance_score'] = $similarity;
                $help['search_type'] = 'semantic';
                $results[] = $help;
            }
        }
        
        // 按相关性排序
        usort($results, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });

        Logger::info('Semantic search', ['results' => $results]);
        
        return array_slice($results, 0, 5);
    }
    
    /**
     * 合并和排序搜索结果
     * @param array $keywordResults
     * @param array $semanticResults
     * @return array
     */
    protected function mergeAndRankResults(array $keywordResults, array $semanticResults): array
    {
        $merged = [];
        $seen = [];
        
        // 合并结果，避免重复
        foreach (array_merge($keywordResults, $semanticResults) as $result) {
            if (!isset($seen[$result['id']])) {
                $merged[] = $result;
                $seen[$result['id']] = true;
            }
        }
        
        // 按相关性分数排序
        usort($merged, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });
        
        return array_slice($merged, 0, 5);
    }
    
    /**
     * 构建AI上下文
     * @param string $question
     * @param array $relevantHelps
     * @param string $sessionId
     * @param bool $useMemory
     * @return array
     */
    protected function buildContext(string $question, array $relevantHelps, string $sessionId, bool $useMemory): array
    {
        $context = [
            'question' => $question,
            'knowledge_base' => $this->formatKnowledgeBase($relevantHelps),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // 添加历史对话上下文
        if ($useMemory) {
            $history = $this->memory->getHistory($sessionId);
            $context['conversation_history'] = $this->formatConversationHistory($history);
        }
        
        return $context;
    }
    
    /**
     * 生成AI回复（包含建议）
     * @param string $question
     * @param array $context
     * @param string $sessionId
     * @param array $relevantHelps
     * @return array
     */
    protected function generateAiResponse(string $question, array $context, string $sessionId, array $relevantHelps): array
    {
        // 构建提示词（包含建议生成）
        $prompt = $this->buildPrompt($question, $context, $relevantHelps);

        Logger::info('Generate AI response', ['prompt' => $prompt]);

        // 调用AI服务
        $response = $this->aiService->process($prompt, [
            'session_id' => $sessionId,
            'type' => 'knowledge_base',
            'temperature' => 0.7,
            'max_tokens' => 1200  // 增加token数量以容纳建议
        ]);

        Logger::info('AI response', ['response' => $response]);

        // 解析回复内容和建议
        $parsedResponse = $this->parseAiResponseWithSuggestions($response->content ?? $response);

        // 安全检查：验证回复是否超出业务范围
        $validatedResponse = $this->validateBusinessScope($parsedResponse, $relevantHelps);

        return [
            'content' => $validatedResponse['content'],
            'suggestions' => $validatedResponse['suggestions'],
            'tokens' => $response->tokens ?? 0,
            'model' => $response->model ?? 'unknown'
        ];
    }
    
    /**
     * 构建AI提示词
     * @param string $question
     * @param array $context
     * @param array $relevantHelps
     * @return string
     */
    protected function buildPrompt(string $question, array $context, array $relevantHelps = []): string
    {
        $mode = $this->responseConfig['mode'];
        $includeSuggestions = $this->responseConfig['include_suggestions'] ?? false;

        switch ($mode) {
            case 'simple':
                return $this->buildSimplePrompt($question, $context, $includeSuggestions);
            case 'formal':
                return $this->buildFormalPrompt($question, $context, $includeSuggestions);
            case 'detailed':
                return $this->buildDetailedPrompt($question, $context, $includeSuggestions);
            default:
                return $this->buildSimplePrompt($question, $context, $includeSuggestions);
        }
    }

    /**
     * 构建简洁模式提示词
     * @param string $question
     * @param array $context
     * @param bool $includeSuggestions
     * @return string
     */
    protected function buildSimplePrompt(string $question, array $context, bool $includeSuggestions = false): string
    {
        $prompt = $this->buildBusinessScopePrompt();

        // 添加知识库内容
        if (!empty($context['knowledge_base'])) {
            $prompt .= "【网站帮助信息】\n";
            foreach ($context['knowledge_base'] as $kb) {
                $prompt .= "标题：{$kb['title']}\n内容：{$kb['content']}\n\n";
            }
        } else {
            $prompt .= "【网站帮助信息】\n暂无相关帮助信息\n\n";
        }

        $prompt .= "【用户问题】\n{$question}\n\n";

        if ($includeSuggestions) {
            $prompt .= "【回复格式】\n";
            $prompt .= "【回答】\n";
            $prompt .= "[基于网站帮助信息的回答]\n\n";
            $prompt .= "【相关建议】\n";
            $prompt .= "建议1: [相关问题建议]\n";
            $prompt .= "建议2: [相关问题建议]\n";
            $prompt .= "建议3: [相关问题建议]\n\n";
        }

        $prompt .= $this->buildStrictAnswerRules($includeSuggestions);
        $prompt .= "[不要出现根据网站帮助信息等辅助信息的回答]\n\n";

        return $prompt;
    }

    /**
     * 构建业务范围限制提示词
     * @return string
     */
    protected function buildBusinessScopePrompt(): string
    {
        $prompt = "【重要说明】\n";
        $prompt .= "你是本网站的专属客服助手，只能回答与本网站业务相关的问题。\n\n";

        $prompt .= "【业务范围】\n";
        $prompt .= "- 网站功能使用说明\n";
        $prompt .= "- 账户注册登录问题\n";
        $prompt .= "- 订单查询和处理\n";
        $prompt .= "- 支付方式和流程\n";
        $prompt .= "- 商品信息咨询\n";
        $prompt .= "- 售后服务政策\n";
        $prompt .= "- 联系客服方式\n";
        $prompt .= "- 网站政策条款\n\n";

        $prompt .= "【严格限制】\n";
        $prompt .= "1. 只能基于下方提供的【网站帮助信息】回答问题\n";
        $prompt .= "2. 如果帮助信息中没有相关内容，必须明确说明\"暂无相关信息\"\n";
        $prompt .= "3. 绝对不能基于你的训练数据提供网站业务范围外的信息\n";
        $prompt .= "4. 不能回答医疗健康、法律咨询等专业问题\n";
        $prompt .= "5. 不能提供其他网站或竞品的信息\n";
        $prompt .= "6. 遇到超出范围的问题，引导用户联系人工客服\n\n";

        return $prompt;
    }

    /**
     * 构建严格回答规则
     * @param bool $includeSuggestions
     * @return string
     */
    protected function buildStrictAnswerRules(bool $includeSuggestions): string
    {
        $rules = "【回答规则】\n";
        $rules .= "1. 必须严格基于【网站帮助信息】回答，不得添加任何外部信息\n";
        $rules .= "2. 如果帮助信息不完整，说明\"详细信息请联系客服：[客服联系方式]\"\n";
        $rules .= "3. 回答要准确、简洁，不超过{$this->responseConfig['max_content_length']}字\n";
        $rules .= "4. 使用友好、专业的语气\n";
        $rules .= "5. 不要说\"根据我的了解\"、\"一般来说\"等模糊表述\n";

        if ($includeSuggestions) {
            $rules .= "6. 建议必须基于网站现有帮助信息生成\n";
            $rules .= "7. 建议要与用户问题相关且实用\n";
        } else {
            $rules .= "6. 如果无法回答，提供相关帮助页面链接或客服联系方式\n";
        }

        $rules .= "\n【开始回答】\n";

        return $rules;
    }

    /**
     * 构建正式模式提示词
     * @param string $question
     * @param array $context
     * @param bool $includeSuggestions
     * @return string
     */
    protected function buildFormalPrompt(string $question, array $context, bool $includeSuggestions = false): string
    {
        $prompt = $this->buildBusinessScopePrompt();
        $prompt .= "【服务态度】\n您好，很高兴为您服务。\n\n";

        // 添加知识库内容
        if (!empty($context['knowledge_base'])) {
            $prompt .= "【网站帮助信息】\n";
            foreach ($context['knowledge_base'] as $index => $kb) {
                $prompt .= ($index + 1) . ". 标题：{$kb['title']}\n   内容：{$kb['content']}\n\n";
            }
        } else {
            $prompt .= "【网站帮助信息】\n暂无相关帮助信息\n\n";
        }

        // 添加对话历史
        if (!empty($context['conversation_history'])) {
            $prompt .= "【对话记录】\n{$context['conversation_history']}\n\n";
        }

        $prompt .= "【用户问题】\n{$question}\n\n";

        if ($includeSuggestions) {
            $prompt .= "【回复格式】\n";
            $prompt .= "【回答】\n";
            $prompt .= "[基于网站帮助信息的正式回答]\n\n";
            $prompt .= "【相关建议】\n";
            $prompt .= "建议1: [相关问题建议]\n";
            $prompt .= "建议2: [相关问题建议]\n";
            $prompt .= "建议3: [相关问题建议]\n\n";
        }

        $prompt .= $this->buildStrictAnswerRules($includeSuggestions);
        $prompt .= "请使用正式、礼貌的语言回答。\n\n";
        $prompt .= "[不要出现根据网站帮助信息等辅助信息的回答]\n\n";

        return $prompt;
    }

    /**
     * 构建详细模式提示词
     * @param string $question
     * @param array $context
     * @param bool $includeSuggestions
     * @return string
     */
    protected function buildDetailedPrompt(string $question, array $context, bool $includeSuggestions = false): string
    {
        $prompt = $this->buildBusinessScopePrompt();
        $prompt .= "【服务模式】\n作为专业的网站客服助手，我将为您提供详细、全面的解答。\n\n";

        // 添加知识库内容
        if (!empty($context['knowledge_base'])) {
            $prompt .= "【网站帮助信息】\n";
            foreach ($context['knowledge_base'] as $kb) {
                $prompt .= "标题：【{$kb['title']}】\n内容：{$kb['content']}\n\n";
            }
        } else {
            $prompt .= "【网站帮助信息】\n暂无相关帮助信息\n\n";
        }

        // 添加对话历史
        if (!empty($context['conversation_history'])) {
            $prompt .= "【对话历史】\n{$context['conversation_history']}\n\n";
        }

        $prompt .= "【用户问题】\n{$question}\n\n";

        if ($includeSuggestions) {
            $prompt .= "【回复格式】\n";
            $prompt .= "【回答】\n";
            $prompt .= "[基于网站帮助信息的详细回答，包括背景、步骤、注意事项]\n\n";
            $prompt .= "【相关建议】\n";
            $prompt .= "建议1: [相关深入问题建议]\n";
            $prompt .= "建议2: [相关深入问题建议]\n";
            $prompt .= "建议3: [相关深入问题建议]\n\n";
        }

        $prompt .= $this->buildStrictAnswerRules($includeSuggestions);
        $prompt .= "请提供详细回答，包括：背景信息、具体步骤、注意事项等。\n\n";
        $prompt .= "[不要出现根据网站帮助信息等辅助信息的回答]\n\n";

        return $prompt;
    }

    /**
     * 设置回复配置
     * @param array $config
     * @return self
     */
    public function setResponseConfig(array $config): self
    {
        $this->responseConfig = array_merge($this->responseConfig, $config);
        return $this;
    }

    /**
     * 获取回复配置
     * @return array
     */
    public function getResponseConfig(): array
    {
        return $this->responseConfig;
    }

    /**
     * 设置回复模式
     * @param string $mode simple|formal|detailed
     * @return self
     */
    public function setResponseMode(string $mode): self
    {
        $this->responseConfig['mode'] = $mode;
        return $this;
    }

    /**
     * 设置配置（别名方法）
     * @param array $config
     * @return self
     */
    public function setConfig(array $config): self
    {
        return $this->setResponseConfig($config);
    }

    /**
     * 安全获取缓存
     * @param string $key
     * @return mixed
     */
    protected function safeGetCache(string $key)
    {
        try {
            return Cache::get($key);
        } catch (\Exception $e) {
            Logger::warning('Cache get failed', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 安全设置缓存
     * @param string $key
     * @param mixed $value
     * @param int $ttl
     * @return bool
     */
    protected function safeSetCache(string $key, $value, int $ttl = 300): bool
    {
        try {
            return Cache::set($key, $value, $ttl);
        } catch (\Exception $e) {
            Logger::warning('Cache set failed', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 提取关键词（使用专业分词服务）
     * @param string $text
     * @return array
     */
    protected function extractKeywords(string $text): array
    {
        try {
            // 使用专业的中文分词服务
            $keywords = $this->segmentationService->extractKeywords($text, 10);

            Logger::info('Keywords extracted', [
                'text' => $text,
                'keywords' => $keywords
            ]);

            return $keywords;
        } catch (\Exception $e) {
            Logger::error('Failed to extract keywords', [
                'text' => $text,
                'error' => $e->getMessage()
            ]);

            // 降级到简单分词
            return $this->fallbackExtractKeywords($text);
        }
    }

    /**
     * 降级关键词提取方法
     * @param string $text
     * @return array
     */
    protected function fallbackExtractKeywords(string $text): array
    {
        // 简化的关键词提取（作为备用方案）
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $text);
        $words = preg_split('/\s+/u', trim($text));

        // 基础停用词
        $stopWords = ['的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那', '有', '没', '不', '要', '会', '能', '可以', '怎么', '什么', '为什么', '如何'];
        $keywords = [];

        foreach ($words as $word) {
            if (mb_strlen($word) >= 2 && !in_array($word, $stopWords)) {
                $keywords[] = $word;
            }
        }

        return array_unique($keywords);
    }
    
    /**
     * 计算关键词相关性（改进版）
     * @param string $question
     * @param array $help
     * @return float
     */
    protected function calculateKeywordRelevance(string $question, array $help): float
    {
        $questionKeywords = $this->extractKeywords($question);
        $helpText = $help['title'] . ' ' . $help['content'];
        $helpKeywords = $this->extractKeywords($helpText);

        if (empty($questionKeywords)) {
            return 0.0;
        }

        // 改进的相关性计算
        $score = 0.0;
        $titleWeight = 2.0;  // 标题匹配权重更高
        $contentWeight = 1.0; // 内容匹配权重

        // 1. 直接字符串匹配（最重要）
        $questionLower = mb_strtolower($question);
        $titleLower = mb_strtolower($help['title']);
        $contentLower = mb_strtolower($help['content']);

        // 标题匹配检查
        if (strpos($titleLower, $questionLower) !== false) {
            $score += 0.8; // 问题在标题中
        } elseif (strpos($questionLower, $titleLower) !== false) {
            $score += 0.7; // 标题在问题中
        }

        // 内容匹配检查
        if (strpos($contentLower, $questionLower) !== false) {
            $score += 0.6; // 问题在内容中
        }

        // 部分关键词匹配检查
        foreach ($questionKeywords as $keyword) {
            $keywordLower = mb_strtolower($keyword);
            if (strpos($titleLower, $keywordLower) !== false) {
                $score += 0.3; // 关键词在标题中
            } elseif (strpos($contentLower, $keywordLower) !== false) {
                $score += 0.2; // 关键词在内容中
            }
        }

        // 2. 关键词匹配
        if (!empty($helpKeywords)) {
            $matchedKeywords = array_intersect($questionKeywords, $helpKeywords);
            $keywordScore = count($matchedKeywords) / count($questionKeywords);
            $score += $keywordScore * 0.4;
        }

        // 3. 标题关键词匹配（额外加分）
        $titleKeywords = $this->extractKeywords($help['title']);
        if (!empty($titleKeywords)) {
            $titleMatches = array_intersect($questionKeywords, $titleKeywords);
            if (!empty($titleMatches)) {
                $score += (count($titleMatches) / count($questionKeywords)) * 0.3;
            }
        }

        // 4. 长度惩罚（避免过长文档稀释相关性）
        $lengthPenalty = min(1.0, 500 / max(1, mb_strlen($help['content'])));
        $score *= $lengthPenalty;

        return min(1.0, $score);
    }
    
    /**
     * 计算语义相似度（简化版本）
     * @param string $text1
     * @param string $text2
     * @return float
     */
    protected function calculateSemanticSimilarity(string $text1, string $text2): float
    {
        // 这里可以使用更复杂的语义相似度算法
        // 目前使用简化的余弦相似度
        
        $words1 = $this->extractKeywords($text1);
        $words2 = $this->extractKeywords($text2);
        
        if (empty($words1) || empty($words2)) {
            return 0.0;
        }
        
        $intersection = array_intersect($words1, $words2);
        $magnitude1 = sqrt(count($words1));
        $magnitude2 = sqrt(count($words2));
        
        if ($magnitude1 == 0 || $magnitude2 == 0) {
            return 0.0;
        }
        
        return count($intersection) / ($magnitude1 * $magnitude2);
    }
    
    /**
     * 格式化知识库内容
     * @param array $helps
     * @return array
     */
    protected function formatKnowledgeBase(array $helps): array
    {
        $formatted = [];
        foreach ($helps as $help) {
            $formatted[] = [
                'id' => $help['id'],
                'title' => $help['title'],
                'content' => mb_substr(strip_tags($help['content']), 0, 500) . '...',
                'relevance_score' => $help['relevance_score'] ?? 0
            ];
        }
        return $formatted;
    }
    
    /**
     * 格式化对话历史
     * @param array $history
     * @return string
     */
    protected function formatConversationHistory(array $history): string
    {
        if (empty($history)) {
            return '';
        }
        
        $formatted = [];
        foreach (array_slice($history, -3) as $entry) { // 只取最近3条
            if (isset($entry['input']) && isset($entry['output'])) {
                $formatted[] = "用户：{$entry['input']}";
                $formatted[] = "助手：{$entry['output']}";
            }
        }
        
        return implode("\n", $formatted);
    }
    
    /**
     * 保存对话记录
     * @param string $sessionId
     * @param string $question
     * @param string $answer
     * @param array $sources
     */
    protected function saveConversation(string $sessionId, string $question, string $answer, array $sources): void
    {
        try {
            $this->memory->addMessage($sessionId, $question, 'input');
            $this->memory->addMessage($sessionId, $answer, 'output', [
                'sources' => array_column($sources, 'id'),
                'type' => 'knowledge_base'
            ]);
        } catch (\Exception $e) {
            Logger::error('Failed to save conversation', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 计算回答置信度（改进版）
     * @param array $relevantHelps
     * @return float
     */
    protected function calculateConfidence(array $relevantHelps): float
    {
        if (empty($relevantHelps)) {
            return 0.1;
        }

        $scores = array_column($relevantHelps, 'relevance_score');
        $maxScore = max($scores);
        $avgScore = array_sum($scores) / count($scores);
        $helpCount = count($relevantHelps);

        // 基础置信度：最高分和平均分的加权平均
        $baseConfidence = ($maxScore * 0.7) + ($avgScore * 0.3);

        // 数量加成：有多个相关文档时增加置信度
        $countBonus = min(0.2, ($helpCount - 1) * 0.05);

        // 质量阈值：如果最高分很低，大幅降低置信度
        if ($maxScore < 0.3) {
            $baseConfidence *= 0.5;
        } elseif ($maxScore > 0.7) {
            $baseConfidence += 0.1; // 高质量匹配加分
        }

        $finalConfidence = $baseConfidence + $countBonus;

        // 确保置信度在合理范围内
        $finalConfidence = max(0.1, min(0.95, $finalConfidence));

        Logger::info('Confidence calculation', [
            'max_score' => $maxScore,
            'avg_score' => $avgScore,
            'help_count' => $helpCount,
            'base_confidence' => $baseConfidence,
            'final_confidence' => $finalConfidence
        ]);

        return $finalConfidence;
    }
    
    /**
     * 生成相关建议（降级方法，当AI建议生成失败时使用）
     * @param string $question
     * @param array $relevantHelps
     * @return array
     */
    protected function generateSuggestions(string $question, array $relevantHelps): array
    {
        $suggestions = [];

        if ($this->responseConfig['include_suggestions']) {
            // 基于相关帮助文档生成基础建议
            foreach (array_slice($relevantHelps, 0, 3) as $help) {
                $suggestions[] = [
                    'title' => $help['title'],
                    'id' => $help['id'],
                    'type' => 'document'
                ];
            }
        }

        return $suggestions;
    }



    /**
     * 解析AI生成的建议内容
     * @param string $content
     * @return array
     */
    protected function parseAiSuggestions(string $content): array
    {
        $suggestions = [];

        // 按行分割内容
        $lines = explode("\n", trim($content));

        foreach ($lines as $line) {
            $line = trim($line);

            // 匹配建议格式：建议X: [问题内容]
            if (preg_match('/^建议\d+[:：]\s*(.+)$/u', $line, $matches)) {
                $suggestionText = trim($matches[1]);
                if (!empty($suggestionText)) {
                    $suggestions[] = [
                        'title' => $suggestionText,
                        'type' => 'ai_generated',
                        'id' => null
                    ];
                }
            }
            // 也支持简单的列表格式
            elseif (preg_match('/^[-*]\s*(.+)$/u', $line, $matches)) {
                $suggestionText = trim($matches[1]);
                if (!empty($suggestionText)) {
                    $suggestions[] = [
                        'title' => $suggestionText,
                        'type' => 'ai_generated',
                        'id' => null
                    ];
                }
            }
        }

        return $suggestions;
    }

    /**
     * 解析包含建议的AI回复
     * @param string $content
     * @return array
     */
    protected function parseAiResponseWithSuggestions(string $content): array
    {
        $result = [
            'content' => '',
            'suggestions' => []
        ];

        // 检查是否包含格式化的回答和建议
        if (preg_match('/【回答】\s*(.*?)\s*【相关建议】\s*(.*?)$/s', $content, $matches)) {
            // 提取回答部分
            $result['content'] = trim($matches[1]);

            // 提取建议部分
            $suggestionsText = trim($matches[2]);
            $result['suggestions'] = $this->parseAiSuggestions($suggestionsText);
        } else {
            // 如果没有格式化，整个内容作为回答
            $result['content'] = trim($content);
            $result['suggestions'] = [];
        }

        return $result;
    }

    /**
     * 验证AI回复是否超出业务范围
     * @param array $response
     * @param array $relevantHelps
     * @return array
     */
    protected function validateBusinessScope(array $response, array $relevantHelps): array
    {
        $content = $response['content'] ?? '';
        $suggestions = $response['suggestions'] ?? [];

        // 使用BusinessScopeConfig检查内容
        $hasViolation = false;
        $violationReason = '';

        // 检查是否包含禁止的关键词
        foreach (BusinessScopeConfig::$forbiddenScopes as $scope => $config) {
            foreach ($config['keywords'] as $keyword) {
                if (stripos($content, $keyword) !== false) {
                    $hasViolation = true;
                    $violationReason = $config['reason'];
                    Logger::warning('AI回复包含超范围内容', [
                        'scope' => $scope,
                        'keyword' => $keyword,
                        'content' => $content,
                        'reason' => $violationReason
                    ]);
                    break 2;
                }
            }
        }

        // 如果检测到违规内容或没有相关帮助信息，使用安全回复
        if ($hasViolation) {
            return [
                'content' => BusinessScopeConfig::getSafeReply('out_of_scope'),
                'suggestions' => $this->generateSafeSuggestions($relevantHelps)
            ];
        }

        if (empty($relevantHelps)) {
            return [
                'content' => BusinessScopeConfig::getSafeReply('no_relevant_info'),
                'suggestions' => $this->generateSafeSuggestions($relevantHelps)
            ];
        }

        // 验证建议是否合理
        $validatedSuggestions = $this->validateSuggestions($suggestions, $relevantHelps);

        return [
            'content' => $content,
            'suggestions' => $validatedSuggestions
        ];
    }

    /**
     * 生成安全回复（当AI回复超出范围时）
     * @param array $relevantHelps
     * @return string
     */
    protected function generateSafeReply(array $relevantHelps): string
    {
        if (!empty($relevantHelps)) {
            $help = $relevantHelps[0];
            return "根据我们的帮助信息，关于「{$help['title']}」：\n\n{$help['content']}\n\n如需更详细的信息，请联系我们的人工客服。";
        }

        return "抱歉，我只能回答与本网站业务相关的问题。您的问题可能超出了我的服务范围，建议您：\n\n1. 查看网站帮助中心\n2. 联系人工客服获取专业解答\n3. 或者重新描述您的问题\n\n感谢您的理解！";
    }

    /**
     * 生成安全建议
     * @param array $relevantHelps
     * @return array
     */
    protected function generateSafeSuggestions(array $relevantHelps): array
    {
        $suggestions = [];

        // 基于帮助文档生成安全建议
        foreach (array_slice($relevantHelps, 0, 3) as $help) {
            $suggestions[] = [
                'title' => $help['title'],
                'id' => $help['id'],
                'type' => 'document'
            ];
        }

        // 如果没有相关帮助，提供通用建议
        if (empty($suggestions)) {
            $suggestions = [
                ['title' => '如何联系客服？', 'id' => null, 'type' => 'general'],
                ['title' => '网站使用指南', 'id' => null, 'type' => 'general'],
                ['title' => '常见问题解答', 'id' => null, 'type' => 'general']
            ];
        }

        return $suggestions;
    }

    /**
     * 验证建议是否合理
     * @param array $suggestions
     * @param array $relevantHelps
     * @return array
     */
    protected function validateSuggestions(array $suggestions, array $relevantHelps): array
    {
        $validSuggestions = [];

        foreach ($suggestions as $suggestion) {
            $title = $suggestion['title'] ?? '';

            // 使用BusinessScopeConfig检查建议是否包含禁止内容
            $isDangerous = false;
            foreach (BusinessScopeConfig::$forbiddenScopes as $scope => $config) {
                foreach ($config['keywords'] as $keyword) {
                    if (stripos($title, $keyword) !== false) {
                        $isDangerous = true;
                        Logger::info('过滤危险建议', [
                            'suggestion' => $title,
                            'scope' => $scope,
                            'keyword' => $keyword
                        ]);
                        break 2;
                    }
                }
            }

            if (!$isDangerous && !empty($title)) {
                $validSuggestions[] = $suggestion;
            }
        }

        // 如果所有建议都被过滤，使用安全建议
        if (empty($validSuggestions)) {
            return $this->generateSafeSuggestions($relevantHelps);
        }

        return $validSuggestions;
    }

    /**
     * 获取热门问题
     * @param int $limit
     * @return array
     */
    public function getPopularQuestions(int $limit = 10): array
    {
        $cacheKey = $this->cachePrefix . 'popular_questions';
        $cached = $this->safeGetCache($cacheKey);

        if ($cached !== false) {
            return $cached;
        }

        // 从帮助文档中获取热门问题
        $popular = Help::where('enabled', 1)
            ->field('id,title,category')
            ->order('sort', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();

        $this->safeSetCache($cacheKey, $popular, 1800); // 缓存30分钟
        
        return $popular;
    }
    
    /**
     * 获取分类下的问题
     * @param int $categoryId
     * @param int $limit
     * @return array
     */
    public function getQuestionsByCategory(int $categoryId, int $limit = 20): array
    {
        return Help::where('enabled', 1)
            ->where('category', $categoryId)
            ->field('id,title,content')
            ->order('sort', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    
    /**
     * 获取问题详情
     * @param int $helpId
     * @return array|null
     */
    public function getQuestionDetail(int $helpId): ?array
    {
        $help = Help::where('id', $helpId)
            ->where('enabled', 1)
            ->find();
            
        if (!$help) {
            return null;
        }
        
        return $help->toArray();
    }
}
