<?php

namespace app\ai\interfaces;

/**
 * 链式调用接口
 * 定义链式处理的基本结构
 */
interface ChainInterface
{
    /**
     * 执行链式调用
     * @param array $inputs 输入数据
     * @param array $context 上下文信息
     * @return array 处理结果
     */
    public function run(array $inputs, array $context = []): array;
    
    /**
     * 获取链的名称
     * @return string
     */
    public function getName(): string;
    
    /**
     * 获取输入键
     * @return array
     */
    public function getInputKeys(): array;
    
    /**
     * 获取输出键
     * @return array
     */
    public function getOutputKeys(): array;
    
    /**
     * 验证输入
     * @param array $inputs
     * @return bool
     */
    public function validateInputs(array $inputs): bool;
}