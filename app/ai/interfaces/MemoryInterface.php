<?php

namespace app\ai\interfaces;

/**
 * 记忆管理接口
 * 用于处理对话历史和上下文记忆
 */
interface MemoryInterface
{
    /**
     * 保存对话消息
     * @param string $sessionId 会话ID
     * @param array $message 消息内容
     * @return void
     */
    public function saveMessage(string $sessionId, array $message): void;
    
    /**
     * 获取对话历史
     * @param string $sessionId 会话ID
     * @param int $limit 限制数量
     * @return array
     */
    public function getHistory(string $sessionId, int $limit = 10): array;
    
    /**
     * 清除会话记忆
     * @param string $sessionId 会话ID
     * @return void
     */
    public function clearSession(string $sessionId): void;
    
    /**
     * 获取记忆变量
     * @param string $sessionId 会话ID
     * @return array
     */
    public function getMemoryVariables(string $sessionId): array;
    
    /**
     * 保存上下文信息
     * @param string $sessionId 会话ID
     * @param string $key 键名
     * @param mixed $value 值
     * @return void
     */
    public function saveContext(string $sessionId, string $key, $value): void;
    
    /**
     * 获取上下文信息
     * @param string $sessionId 会话ID
     * @param string $key 键名
     * @return mixed
     */
    public function getContext(string $sessionId, string $key);
    
    /**
     * 总结对话历史
     * @param string $sessionId 会话ID
     * @return string
     */
    public function summarizeHistory(string $sessionId): string;
}