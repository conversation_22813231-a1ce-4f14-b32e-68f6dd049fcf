<?php

namespace app\ai\interfaces;

/**
 * 工具接口
 * 定义AI可以调用的外部工具和函数
 */
interface ToolInterface
{
    /**
     * 执行工具
     * @param array $params 参数
     * @return array 执行结果
     */
    public function execute(array $params = []): array;
    
    /**
     * 获取工具名称
     * @return string
     */
    public function getName(): string;
    
    /**
     * 获取工具描述
     * @return string
     */
    public function getDescription(): string;
    
    /**
     * 获取参数模式
     * @return array
     */
    public function getParameterSchema(): array;
    
    /**
     * 验证参数
     * @param array $params
     * @return bool
     */
    public function validateParameters(array $params): bool;
    
    /**
     * 获取工具类型
     * @return string
     */
    public function getType(): string;
    
    /**
     * 是否需要认证
     * @return bool
     */
    public function requiresAuth(): bool;
    
    /**
     * 获取使用示例
     * @return array
     */
    public function getExamples(): array;
}