<?php

namespace app\ai\interfaces;

/**
 * 代理接口
 * 用于智能决策和工具调用的高级抽象
 */
interface AgentInterface
{
    /**
     * 执行代理任务
     * @param string $input 输入内容
     * @param array $context 上下文
     * @return array 执行结果
     */
    public function run(string $input, array $context = []): array;
    
    /**
     * 添加工具
     * @param ToolInterface $tool
     * @return self
     */
    public function addTool(ToolInterface $tool): self;
    
    /**
     * 获取可用工具
     * @return array
     */
    public function getTools(): array;
    
    /**
     * 设置记忆管理器
     * @param MemoryInterface $memory
     * @return self
     */
    public function setMemory(MemoryInterface $memory): self;
    
    /**
     * 获取代理类型
     * @return string
     */
    public function getType(): string;
    
    /**
     * 设置最大迭代次数
     * @param int $maxIterations
     * @return self
     */
    public function setMaxIterations(int $maxIterations): self;
    
    /**
     * 获取执行计划
     * @param string $input
     * @return array
     */
    public function plan(string $input): array;
    
    /**
     * 执行单步操作
     * @param array $step
     * @return array
     */
    public function executeStep(array $step): array;
}