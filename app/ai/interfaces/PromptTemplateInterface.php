<?php

namespace app\ai\interfaces;

/**
 * 提示模板接口
 * 用于管理和格式化AI提示
 */
interface PromptTemplateInterface
{
    /**
     * 格式化提示模板
     * @param array $variables 变量数组
     * @return string 格式化后的提示
     */
    public function format(array $variables = []): string;
    
    /**
     * 获取模板字符串
     * @return string
     */
    public function getTemplate(): string;
    
    /**
     * 获取输入变量
     * @return array
     */
    public function getInputVariables(): array;
    
    /**
     * 验证变量
     * @param array $variables
     * @return bool
     */
    public function validateVariables(array $variables): bool;
    
    /**
     * 添加示例
     * @param array $examples
     * @return self
     */
    public function addExamples(array $examples): self;
    
    /**
     * 设置系统消息
     * @param string $systemMessage
     * @return self
     */
    public function setSystemMessage(string $systemMessage): self;
}