# AI MySQL存储器配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# ===========================================
# AI 记忆存储器配置
# ===========================================

# 存储器类型: buffer(内存) 或 mysql(数据库)
AI_MEMORY_TYPE=mysql

# MySQL存储器环境配置
# 可选值: development, production, testing, auto
AI_MEMORY_ENVIRONMENT=production

# ===========================================
# MySQL存储器详细配置
# ===========================================

# 自动清理过期会话
AI_MYSQL_AUTO_CLEANUP=true

# 自动清理天数阈值（超过此天数的会话将被清理）
AI_MYSQL_CLEANUP_DAYS=30

# 最大历史记录长度
AI_MYSQL_MAX_HISTORY=100

# 启用批量操作（提高性能）
AI_MYSQL_BATCH_OPERATIONS=true

# 批量操作大小
AI_MYSQL_BATCH_SIZE=50

# 启用查询缓存
AI_MYSQL_ENABLE_CACHE=true

# 缓存TTL（秒）
AI_MYSQL_CACHE_TTL=300

# ===========================================
# 提示模板配置
# ===========================================

# 启用模板缓存
AI_TEMPLATE_CACHE=true

# 模板缓存TTL（秒）
AI_TEMPLATE_CACHE_TTL=600

# 自动初始化预定义模板
AI_TEMPLATE_AUTO_INIT=true

# ===========================================
# 数据库连接配置（如果使用MySQL存储器）
# ===========================================

# 主数据库连接
DATABASE_TYPE=mysql
DATABASE_HOSTNAME=127.0.0.1
DATABASE_DATABASE=anchor_ai
DATABASE_USERNAME=root
DATABASE_PASSWORD=
DATABASE_HOSTPORT=3306
DATABASE_CHARSET=utf8mb4
DATABASE_PREFIX=

# ===========================================
# 性能和安全配置
# ===========================================

# 启用详细日志
AI_LOG_VERBOSE=false

# 日志级别: debug, info, warning, error
AI_LOG_LEVEL=info

# 记录性能指标
AI_LOG_PERFORMANCE=true

# 请求超时时间（秒）
AI_REQUEST_TIMEOUT=30

# 最大重试次数
AI_MAX_RETRIES=3

# 重试延迟（毫秒）
AI_RETRY_DELAY=1000

# 启用请求频率限制
AI_RATE_LIMITING=true

# 每分钟最大请求数
AI_MAX_REQUESTS_PER_MINUTE=60

# 启用内容过滤
AI_CONTENT_FILTERING=true

# ===========================================
# 使用说明
# ===========================================

# 1. 首先运行数据库迁移创建必要的表：
#    php think migrate:run

# 2. 初始化MySQL存储器：
#    php think ai:mysql-memory init --env=production

# 3. 查看存储统计：
#    php think ai:mysql-memory stats --env=production

# 4. 清理过期数据：
#    php think ai:mysql-memory cleanup --days=30 --force

# 5. 在代码中使用：
#    // 自动根据配置选择存储器类型
#    $controller = new AiController();
#    
#    // 或手动切换存储器类型
#    $langChain->switchToMySqlMemory('production');