<?php

namespace app\ai\config;

/**
 * AI客服业务范围配置
 * 定义AI客服可以回答的业务范围和限制规则
 */
class BusinessScopeConfig
{
    /**
     * 允许的业务范围
     * @var array
     */
    public static $allowedScopes = [
        'account' => [
            'name' => '账户相关',
            'keywords' => ['注册', '登录', '密码', '账户', '个人信息', '实名认证'],
            'description' => '用户注册、登录、密码重置、账户信息管理等'
        ],
        'order' => [
            'name' => '订单相关', 
            'keywords' => ['订单', '下单', '支付', '退款', '物流', '配送'],
            'description' => '订单查询、支付流程、退款政策、物流跟踪等'
        ],
        'product' => [
            'name' => '商品相关',
            'keywords' => ['商品', '价格', '库存', '规格', '参数', '详情'],
            'description' => '商品信息咨询、价格查询、库存状态等'
        ],
        'service' => [
            'name' => '客服相关',
            'keywords' => ['客服', '联系', '投诉', '建议', '反馈', '服务时间'],
            'description' => '客服联系方式、服务时间、投诉建议等'
        ],
        'website' => [
            'name' => '网站功能',
            'keywords' => ['使用', '功能', '操作', '设置', '帮助', '教程'],
            'description' => '网站功能使用、操作指南、设置说明等'
        ],
        'policy' => [
            'name' => '政策条款',
            'keywords' => ['政策', '条款', '协议', '规则', '制度', '说明'],
            'description' => '网站政策、用户协议、服务条款等'
        ]
    ];

    /**
     * 禁止的业务范围
     * @var array
     */
    public static $forbiddenScopes = [
        'medical' => [
            'name' => '医疗健康',
            'keywords' => ['诊断', '治疗', '药物', '病情', '症状', '医院', '医生', '健康', '疾病'],
            'reason' => '医疗健康问题需要专业医生诊断，请咨询医疗机构'
        ],
        'legal' => [
            'name' => '法律咨询',
            'keywords' => ['法律', '起诉', '律师', '法院', '合同纠纷', '违法', '诉讼', '维权'],
            'reason' => '法律问题需要专业律师解答，请咨询法律机构'
        ],
        'technical' => [
            'name' => '技术开发',
            'keywords' => ['代码', '编程', '开发', '数据库', '服务器', 'API', '算法', '架构'],
            'reason' => '技术开发问题超出客服范围，请联系技术支持'
        ],
        'competitor' => [
            'name' => '竞品信息',
            'keywords' => [],
            'reason' => '我只能提供本网站相关信息，其他网站请直接咨询'
        ],
        'financial' => [
            'name' => '投资理财',
            'keywords' => ['投资', '理财', '股票', '基金', '贷款', '借钱', '金融', '收益'],
            'reason' => '投资理财有风险，请咨询专业金融机构'
        ],
        'privacy' => [
            'name' => '隐私信息',
            'keywords' => ['身份证', '银行卡号', '密码', '手机号', '地址', '隐私'],
            'reason' => '请勿在聊天中透露个人隐私信息，保护您的信息安全'
        ]
    ];

    /**
     * 安全回复模板
     * @var array
     */
    public static $safeReplyTemplates = [
        'out_of_scope' => '抱歉，您的问题超出了我的服务范围。我只能回答与本网站业务相关的问题。',
        'need_human_service' => '这个问题需要人工客服为您详细解答，请联系我们的客服团队。',
        'check_help_center' => '建议您查看我们的帮助中心，或联系人工客服获取更准确的信息。',
        'privacy_warning' => '为了您的信息安全，请勿在聊天中透露个人隐私信息。',
        'no_relevant_info' => '抱歉，我暂时没有找到相关信息，建议您联系人工客服获取帮助。'
    ];

    /**
     * 客服联系方式
     * @var array
     */
    public static $contactInfo = [
        'phone' => '************',
        'email' => '<EMAIL>',
        'wechat' => 'Goee',
        'qq' => '743309260',
        'work_time' => '周一至周五 9:00-18:00，周六至周日 10:00-17:00'
    ];

    /**
     * 检查问题是否在允许范围内
     * @param string $question
     * @return array
     */
    public static function checkScope(string $question): array
    {
        $question = mb_strtolower($question);
        
        // 检查是否包含禁止内容
        foreach (self::$forbiddenScopes as $scope => $config) {
            foreach ($config['keywords'] as $keyword) {
                if (stripos($question, $keyword) !== false) {
                    return [
                        'allowed' => false,
                        'scope' => $scope,
                        'reason' => $config['reason'],
                        'type' => 'forbidden'
                    ];
                }
            }
        }
        
        // 检查是否属于允许范围
        foreach (self::$allowedScopes as $scope => $config) {
            foreach ($config['keywords'] as $keyword) {
                if (stripos($question, $keyword) !== false) {
                    return [
                        'allowed' => true,
                        'scope' => $scope,
                        'description' => $config['description'],
                        'type' => 'allowed'
                    ];
                }
            }
        }
        
        // 未明确分类，需要进一步判断
        return [
            'allowed' => null,
            'scope' => 'unknown',
            'reason' => '需要基于知识库内容判断',
            'type' => 'unknown'
        ];
    }

    /**
     * 获取安全回复
     * @param string $type
     * @param array $context
     * @return string
     */
    public static function getSafeReply(string $type, array $context = []): string
    {
        $template = self::$safeReplyTemplates[$type] ?? self::$safeReplyTemplates['out_of_scope'];
        
        $reply = $template . "\n\n";
        $reply .= "您可以通过以下方式获取帮助：\n";
        $reply .= "📞 客服热线：" . self::$contactInfo['phone'] . "\n";
        $reply .= "📧 邮箱：" . self::$contactInfo['email'] . "\n";
        $reply .= "⏰ 服务时间：" . self::$contactInfo['work_time'] . "\n";
        
        return $reply;
    }

    /**
     * 获取业务范围说明
     * @return string
     */
    public static function getScopeDescription(): string
    {
        $description = "我可以为您解答以下问题：\n\n";
        
        foreach (self::$allowedScopes as $scope => $config) {
            $description .= "• {$config['name']}：{$config['description']}\n";
        }
        
        $description .= "\n如有其他问题，请联系人工客服。";
        
        return $description;
    }
}
