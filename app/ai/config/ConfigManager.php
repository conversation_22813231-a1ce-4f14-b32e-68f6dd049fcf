<?php

namespace app\ai\config;

use app\ai\utils\Logger;
use think\facade\Config;

/**
 * 配置管理器
 * 统一管理AI相关配置
 */
class ConfigManager
{
    /**
     * 配置缓存
     * @var array
     */
    protected static array $cache = [];

    /**
     * 默认配置
     * @var array
     */
    protected static array $defaults = [
        // 基础配置
        'default_provider' => 'deepseek',
        'default_model' => 'auto',
        'timeout' => 30,
        'max_retries' => 3,
        'retry_delay' => 1000,

        // 缓存配置
        'cache' => [
            'enabled' => true,
            'ttl' => 3600,
            'key_prefix' => 'ai_',
        ],

        // 限流配置
        'rate_limit' => [
            'enabled' => true,
            'requests_per_minute' => 60,
            'requests_per_hour' => 1000,
        ],

        // 安全配置
        'security' => [
            'content_filter' => [
                'enabled' => true,
                'blocked_keywords' => [],
            ],
            'max_message_length' => 10000,
            'max_messages_count' => 50,
        ],

        // 日志配置
        'logging' => [
            'enabled' => true,
            'level' => 'info',
            'log_requests' => true,
            'log_responses' => false,
        ],

        // 记忆配置
        'memory' => [
            'type' => 'buffer', // buffer, mysql
            'environment' => 'auto',
            'ttl' => 86400, // 24小时
        ],

        // 监控配置
        'monitoring' => [
            'enabled' => true,
            'metrics_export_interval' => 300, // 5分钟
        ],

        // 事件配置
        'events' => [
            'enabled' => true,
            'async' => false,
        ],

        // 提供商配置
        'providers' => [
            'deepseek' => [
                'api_key' => '***********************************',
                'base_url' => 'https://api.deepseek.com',
                'model' => 'deepseek-chat',
                'timeout' => 30,
                'max_retries' => 3,
            ],
            'openai' => [
                'api_key' => '',
                'base_url' => 'https://api.openai.com/v1',
                'model' => 'gpt-3.5-turbo',
                'timeout' => 30,
                'max_retries' => 3,
            ],
            'claude' => [
                'api_key' => '',
                'base_url' => 'https://api.anthropic.com',
                'model' => 'claude-3-sonnet-20240229',
                'timeout' => 30,
                'max_retries' => 3,
            ],
        ],
    ];

    /**
     * 获取配置值
     * @param string $key 配置键，支持点号分隔
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        // 检查缓存
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }

        // 尝试从ThinkPHP配置获取（如果可用）
        if (class_exists('think\facade\Config')) {
            try {
                $value = Config::get('ai.' . $key);
                if ($value !== null) {
                    self::$cache[$key] = $value;
                    return $value;
                }
            } catch (\Exception $e) {
                // 忽略ThinkPHP配置错误
            }
        }

        // 从默认配置获取
        $value = self::getFromDefaults($key);
        
        if ($value !== null) {
            self::$cache[$key] = $value;
            return $value;
        }

        // 返回默认值
        return $default;
    }

    /**
     * 设置配置值
     * @param string $key 配置键
     * @param mixed $value 配置值
     */
    public static function set(string $key, $value): void
    {
        self::$cache[$key] = $value;

        // 尝试设置到ThinkPHP配置（如果可用）
        if (class_exists('think\facade\Config')) {
            try {
                // ThinkPHP的Config::set()需要数组格式
                Config::set(['ai.' . $key => $value]);
            } catch (\Exception $e) {
                // 忽略ThinkPHP配置错误，继续使用内部缓存
            }
        }
    }

    /**
     * 从默认配置获取值
     * @param string $key 配置键
     * @return mixed
     */
    protected static function getFromDefaults(string $key)
    {
        $keys = explode('.', $key);
        $value = self::$defaults;

        foreach ($keys as $segment) {
            if (!is_array($value) || !array_key_exists($segment, $value)) {
                return null;
            }
            $value = $value[$segment];
        }

        return $value;
    }

    /**
     * 检查配置是否存在
     * @param string $key 配置键
     * @return bool
     */
    public static function has(string $key): bool
    {
        return self::get($key) !== null;
    }

    /**
     * 获取所有配置
     * @return array
     */
    public static function all(): array
    {
        $config = self::$defaults;

        // 尝试从ThinkPHP配置获取（如果可用）
        if (class_exists('think\facade\Config')) {
            try {
                $thinkConfig = Config::get('ai', []);
                $config = array_merge($config, $thinkConfig);
            } catch (\Exception $e) {
                // 忽略ThinkPHP配置错误
            }
        }

        // 合并缓存的配置
        return array_merge($config, self::$cache);
    }

    /**
     * 获取提供商配置
     * @param string $provider 提供商名称
     * @return array|null
     */
    public static function getProvider(string $provider): ?array
    {
        return self::get("providers.{$provider}");
    }

    /**
     * 获取所有提供商配置
     * @return array
     */
    public static function getAllProviders(): array
    {
        return self::get('providers', []);
    }

    /**
     * 设置提供商配置
     * @param string $provider 提供商名称
     * @param array $config 配置
     */
    public static function setProvider(string $provider, array $config): void
    {
        self::set("providers.{$provider}", $config);
    }

    /**
     * 验证配置
     * @return array 验证结果
     */
    public static function validate(): array
    {
        $errors = [];
        $providers = self::getAllProviders();

        // 验证提供商配置
        foreach ($providers as $name => $config) {
            if (empty($config['api_key'])) {
                $errors[] = "Provider '{$name}' is missing API key";
            }
            
            if (empty($config['base_url'])) {
                $errors[] = "Provider '{$name}' is missing base URL";
            }
            
            if (empty($config['model'])) {
                $errors[] = "Provider '{$name}' is missing model";
            }
        }

        // 验证默认提供商
        $defaultProvider = self::get('default_provider');
        if (!isset($providers[$defaultProvider])) {
            $errors[] = "Default provider '{$defaultProvider}' is not configured";
        }

        // 验证缓存配置
        $cacheTtl = self::get('cache.ttl');
        if (!is_numeric($cacheTtl) || $cacheTtl < 0) {
            $errors[] = "Cache TTL must be a positive number";
        }

        // 验证限流配置
        $rateLimit = self::get('rate_limit');
        if ($rateLimit['enabled']) {
            if (!is_numeric($rateLimit['requests_per_minute']) || $rateLimit['requests_per_minute'] <= 0) {
                $errors[] = "Rate limit requests_per_minute must be a positive number";
            }
            
            if (!is_numeric($rateLimit['requests_per_hour']) || $rateLimit['requests_per_hour'] <= 0) {
                $errors[] = "Rate limit requests_per_hour must be a positive number";
            }
        }

        return $errors;
    }

    /**
     * 重置配置缓存
     */
    public static function clearCache(): void
    {
        self::$cache = [];
    }

    /**
     * 从环境变量加载配置
     */
    public static function loadFromEnv(): void
    {
        $envMappings = [
            'AI_DEFAULT_PROVIDER' => 'default_provider',
            'AI_CACHE_ENABLED' => 'cache.enabled',
            'AI_CACHE_TTL' => 'cache.ttl',
            'AI_RATE_LIMIT_ENABLED' => 'rate_limit.enabled',
            'AI_RATE_LIMIT_RPM' => 'rate_limit.requests_per_minute',
            'AI_RATE_LIMIT_RPH' => 'rate_limit.requests_per_hour',
            'AI_MEMORY_TYPE' => 'memory.type',
            'AI_MEMORY_ENVIRONMENT' => 'memory.environment',
            'AI_LOGGING_ENABLED' => 'logging.enabled',
            'AI_LOGGING_LEVEL' => 'logging.level',
            'DEEPSEEK_API_KEY' => 'providers.deepseek.api_key',
            'OPENAI_API_KEY' => 'providers.openai.api_key',
            'CLAUDE_API_KEY' => 'providers.claude.api_key',
        ];

        foreach ($envMappings as $envKey => $configKey) {
            $value = env($envKey);
            if ($value !== null) {
                // 转换布尔值
                if (in_array(strtolower($value), ['true', 'false'])) {
                    $value = strtolower($value) === 'true';
                }
                // 转换数字
                elseif (is_numeric($value)) {
                    $value = is_float($value) ? (float)$value : (int)$value;
                }
                
                self::set($configKey, $value);
            }
        }
    }

    /**
     * 导出配置到数组
     * @return array
     */
    public static function export(): array
    {
        return [
            'version' => '2.0.0',
            'timestamp' => time(),
            'config' => self::all(),
            'validation' => self::validate(),
        ];
    }

    /**
     * 从数组导入配置
     * @param array $config 配置数组
     */
    public static function import(array $config): void
    {
        if (isset($config['config'])) {
            foreach ($config['config'] as $key => $value) {
                self::set($key, $value);
            }
        }
    }
}
