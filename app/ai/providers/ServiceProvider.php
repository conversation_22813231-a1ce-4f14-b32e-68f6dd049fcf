<?php

namespace app\ai\providers;

use app\ai\container\ServiceContainer;

/**
 * 服务提供者基类
 */
abstract class ServiceProvider
{
    /**
     * 服务容器
     * @var ServiceContainer
     */
    protected ServiceContainer $container;

    /**
     * 延迟加载的服务
     * @var array
     */
    protected array $defer = [];

    /**
     * 构造函数
     * @param ServiceContainer $container
     */
    public function __construct(ServiceContainer $container)
    {
        $this->container = $container;
    }

    /**
     * 注册服务
     */
    abstract public function register(): void;

    /**
     * 启动服务
     */
    public function boot(): void
    {
        // 默认空实现
    }

    /**
     * 获取提供的服务
     * @return array
     */
    public function provides(): array
    {
        return $this->defer;
    }

    /**
     * 是否延迟加载
     * @return bool
     */
    public function isDeferred(): bool
    {
        return !empty($this->defer);
    }
}
