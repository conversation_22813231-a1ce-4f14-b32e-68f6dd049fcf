<?php

namespace app\ai\providers;

use app\ai\container\ServiceContainer;

/**
 * AI服务提供者
 */
class AiServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register(): void
    {
        $container = ServiceContainer::getInstance();

        // 注册AI基础服务
        $container->singleton('ai.basic', function() {
            return new \app\ai\services\BasicAiService();
        });

        // 注册LangChain服务
        $container->singleton('ai.langchain', function() {
            return new \app\ai\services\LangChainService();
        });

        // 注册统一AI服务
        $container->singleton('ai.unified', function() {
            return new \app\ai\services\UnifiedAiService();
        });

        // 注册知识库服务
        $container->singleton('ai.knowledge', function() {
            return new \app\ai\services\KnowledgeBaseService();
        });

        // 注册内存服务
        $container->singleton('ai.memory', function() {
            return new \app\ai\memory\MySqlMemory();
        });

        // 注册缓存管理器
        $container->singleton('ai.cache', function() {
            return new \app\ai\cache\CacheManager();
        });

        // 注册事件调度器
        $container->singleton('ai.events', function() {
            return new \app\ai\events\EventDispatcher();
        });

        // 注册指标收集器
        $container->singleton('ai.metrics', function() {
            return new \app\ai\monitoring\MetricsCollector();
        });

        // 设置别名
        $container->alias('ai', 'ai.unified');
        $container->alias('knowledge', 'ai.knowledge');
        $container->alias('memory', 'ai.memory');
        $container->alias('cache', 'ai.cache');
        $container->alias('events', 'ai.events');
        $container->alias('metrics', 'ai.metrics');
    }

    /**
     * 启动服务
     */
    public function boot(): void
    {
        // 初始化事件监听器
        $this->registerEventListeners();

        // 初始化缓存
        $this->initializeCache();

        // 初始化指标收集
        $this->initializeMetrics();
    }

    /**
     * 注册事件监听器
     */
    protected function registerEventListeners()
    {
        $container = ServiceContainer::getInstance();

        if ($container->bound('ai.events')) {
            $events = $container->make('ai.events');

            // 注册AI请求事件监听器
            $events->listen('ai.request.started', function($data) {
                // 记录请求开始
                if (ServiceContainer::getInstance()->bound('ai.metrics')) {
                    $metrics = ServiceContainer::getInstance()->make('ai.metrics');
                    // 可以在这里记录请求开始时间等
                }
            });

            $events->listen('ai.request.completed', function($data) {
                // 记录请求完成
                if (ServiceContainer::getInstance()->bound('ai.metrics')) {
                    $metrics = ServiceContainer::getInstance()->make('ai.metrics');
                    $metrics->recordAiRequest(
                        $data['provider'] ?? 'unknown',
                        $data['model'] ?? 'unknown',
                        $data['duration'] ?? 0,
                        true
                    );
                }
            });

            $events->listen('ai.request.failed', function($data) {
                // 记录请求失败
                if (ServiceContainer::getInstance()->bound('ai.metrics')) {
                    $metrics = ServiceContainer::getInstance()->make('ai.metrics');
                    $metrics->recordAiRequest(
                        $data['provider'] ?? 'unknown',
                        $data['model'] ?? 'unknown',
                        $data['duration'] ?? 0,
                        false
                    );
                }
            });
        }
    }

    /**
     * 初始化缓存
     */
    protected function initializeCache()
    {
        // 缓存初始化逻辑
    }

    /**
     * 初始化指标收集
     */
    protected function initializeMetrics()
    {
        // 指标收集初始化逻辑
    }
}