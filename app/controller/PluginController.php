<?php

namespace app\controller;

use app\plugin\core\PluginManager;
use think\facade\Request;
use think\response\Json;

/**
 * 插件控制器
 */
class PluginController
{
    /**
     * 插件管理器
     * @var PluginManager
     */
    protected $pluginManager;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->pluginManager = new PluginManager();
    }

    /**
     * 获取插件列表
     * @return Json
     */
    public function index(): Json
    {
        $plugins = $this->pluginManager->getPlugins();
        $list = [];
        foreach ($plugins as $name => $plugin) {
            $list[] = $plugin->getInfo();
        }
        return json(['code' => 0, 'msg' => 'success', 'data' => $list]);
    }

    /**
     * 安装插件
     * @return Json
     */
    public function install(): Json
    {
        $pluginName = Request::param('name');
        if (!$pluginName) {
            return json(['code' => 1, 'msg' => '插件名称不能为空']);
        }

        if ($this->pluginManager->installPlugin($pluginName)) {
            return json(['code' => 0, 'msg' => '安装成功']);
        }
        return json(['code' => 1, 'msg' => '安装失败']);
    }

    /**
     * 卸载插件
     * @return Json
     */
    public function uninstall(): Json
    {
        $pluginName = Request::param('name');
        if (!$pluginName) {
            return json(['code' => 1, 'msg' => '插件名称不能为空']);
        }

        if ($this->pluginManager->uninstallPlugin($pluginName)) {
            return json(['code' => 0, 'msg' => '卸载成功']);
        }
        return json(['code' => 1, 'msg' => '卸载失败']);
    }

    /**
     * 启用插件
     * @return Json
     */
    public function enable(): Json
    {
        $pluginName = Request::param('name');
        if (!$pluginName) {
            return json(['code' => 1, 'msg' => '插件名称不能为空']);
        }

        if ($this->pluginManager->enablePlugin($pluginName)) {
            return json(['code' => 0, 'msg' => '启用成功']);
        }
        return json(['code' => 1, 'msg' => '启用失败']);
    }

    /**
     * 禁用插件
     * @return Json
     */
    public function disable(): Json
    {
        $pluginName = Request::param('name');
        if (!$pluginName) {
            return json(['code' => 1, 'msg' => '插件名称不能为空']);
        }

        if ($this->pluginManager->disablePlugin($pluginName)) {
            return json(['code' => 0, 'msg' => '禁用成功']);
        }
        return json(['code' => 1, 'msg' => '禁用失败']);
    }

    /**
     * 获取插件配置
     * @return Json
     */
    public function getConfig(): Json
    {
        $pluginName = Request::param('name');
        if (!$pluginName) {
            return json(['code' => 1, 'msg' => '插件名称不能为空']);
        }

        $config = $this->pluginManager->getPluginConfig($pluginName);
        if ($config === null) {
            return json(['code' => 1, 'msg' => '插件不存在']);
        }

        return json(['code' => 0, 'msg' => 'success', 'data' => $config]);
    }

    /**
     * 设置插件配置
     * @return Json
     */
    public function setConfig(): Json
    {
        $pluginName = Request::param('name');
        $config = Request::param('config/a', []);
        
        if (!$pluginName) {
            return json(['code' => 1, 'msg' => '插件名称不能为空']);
        }

        if ($this->pluginManager->setPluginConfig($pluginName, $config)) {
            return json(['code' => 0, 'msg' => '设置成功']);
        }
        return json(['code' => 1, 'msg' => '设置失败']);
    }
} 