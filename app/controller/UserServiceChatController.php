<?php
declare(strict_types=1);

namespace app\controller;

use app\vchat\core\MessageProtocol;
use app\vchat\core\UserServiceChat;
use app\vchat\utils\Logger;
use think\Request;
use think\swoole\Websocket;
use think\swoole\websocket\socketio\EnginePacket;

/**
 * 用户端聊天控制器
 */
class UserServiceChatController
{
    /**
     * @var UserServiceChat
     */
    protected $chat;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->chat = new UserServiceChat();
        $this->logger = new Logger();
    }

    /**
     * 用户连接入口
     */
    public function index()
    {
        return \think\swoole\helper\websocket()
            ->onOpen(function (Websocket $websocket, $request) {
                try {
                    // 记录连接信息
                    $this->logger->info('用户端WebSocket连接建立', [
                        'fd' => $websocket->getSender(),
                        'time' => date('Y-m-d H:i:s'),
                        'request' => [
                            'get' => $request->get(),
                            'post' => $request->post(),
                            'header' => $request->header(),
                            'param' => $request->param(),
                            'url' => $request->url(true),
                            'path' => $request->pathinfo(),
                            'query' => $request->query()
                        ]
                    ]);

                    // 发送欢迎消息
                    $this->chat->onOpen($websocket, $request);
                } catch (\Exception $e) {
                    $this->logger->error('用户连接建立失败', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            })
            ->onMessage(function (Websocket $websocket, EnginePacket $enginePacket) {
                try {
                    // 记录 EnginePacket 信息
                    $this->logger->info('收到用户消息', [
                        'fd' => $websocket->getSender(),
                        'packet' => [
                            'type' => $enginePacket->type,
                            'data_length' => strlen($enginePacket->data),
                            'data_hex' => bin2hex($enginePacket->data),
                            'data' => $enginePacket->data
                        ]
                    ]);

                    // 只处理 MESSAGE 类型的包
                    if ($enginePacket->type != \think\swoole\websocket\socketio\EnginePacket::MESSAGE) {
                        $this->logger->info('跳过用户端非MESSAGE类型包', [
                            'received_type' => $enginePacket->type,
                            'expected_type' => \think\swoole\websocket\socketio\EnginePacket::MESSAGE
                        ]);
                        return;
                    }

                    // 解析 Socket.IO 消息内容
                    $socketPacket = \think\swoole\websocket\socketio\Packet::fromString($enginePacket->data);
                    $this->logger->info('用户端SocketPacket解析结果', [
                        'type' => $socketPacket->type,
                        'data' => $socketPacket->data
                    ]);

                    // 处理连接事件
                    if ($socketPacket->type == \think\swoole\websocket\socketio\Packet::CONNECT) {
                        $this->logger->info('收到用户端Socket.IO CONNECT事件');
                        // 处理连接认证
                        $token = $socketPacket->data['token'] ?? '';
                        $data = $socketPacket->data ?? [];
                        $this->logger->info('用户端连接认证token', ['token' => $token]);

                        if ($this->chat->handleAuth($websocket, $data)) {
                            $this->logger->info('用户端连接认证成功');
                            // 连接成功响应
                            return;
                        }
                        // 认证失败，发送错误响应
                        $response = \think\swoole\websocket\socketio\Packet::create(
                            \think\swoole\websocket\socketio\Packet::CONNECT_ERROR,
                            [
                                'data' => [
                                    'message' => '认证失败'
                                ]
                            ]
                        );
                        $enginePacket = \think\swoole\websocket\socketio\EnginePacket::message($response->toString());
                        $this->logger->error('用户端认证失败，准备推送错误消息并关闭连接', [
                            'fd' => $websocket->getSender(),
                            'response' => $response->toString()
                        ]);
                        $websocket->push($enginePacket->toString());
                        $websocket->close();
                        return;
                    }

                    // 只处理事件类型的消息
                    if ($socketPacket->type != \think\swoole\websocket\socketio\Packet::EVENT) {
                        $this->logger->info('跳过用户端非EVENT类型', ['type' => $socketPacket->type]);
                        return;
                    }

                    // 解析事件数据
                    $eventData = $socketPacket->data;
                    if (!$eventData || !isset($eventData[0])) {
                        $this->logger->error('用户端事件数据格式错误', ['data' => $eventData]);
                        return;
                    }

                    // 第一个元素是事件名，第二个元素是事件数据
                    $eventName = $eventData[0];
                    $data = $eventData[1] ?? [];

                    $this->logger->info('解析到用户端事件', [
                        'event' => $eventName,
                        'data' => $data
                    ]);

                    // 根据事件名处理不同的消息类型
                    switch ($eventName) {
                        case MessageProtocol::MESSAGE_AUTHORIZE:
                            $this->chat->handleAuth($websocket, $data);
                            break;
                        case MessageProtocol::MESSAGE_TYPE:
                            $this->chat->onMessage($websocket, $data);
                            break;
                        case MessageProtocol::MESSAGE_TYPING:
                            $this->chat->onMessage($websocket, array_merge($data, ['message_type' => MessageProtocol::MESSAGE_TYPING]));
                            break;
                        case MessageProtocol::MESSAGE_REQUEST_TYPE:
                            $this->chat->onMessage($websocket, array_merge($data, ['message_type' => MessageProtocol::MESSAGE_REQUEST_TYPE]));
                            break;
                        case MessageProtocol::MESSAGE_RATING:
                            $this->chat->onMessage($websocket, array_merge($data, ['message_type' => 'rating']));
                            break;
                        case MessageProtocol::MESSAGE_READ:
                            $this->chat->onMessage($websocket, array_merge($data, ['message_type' => MessageProtocol::MESSAGE_READ]));
                            break;
                        default:
                            $this->logger->info('未处理的事件类型', ['event' => $eventName]);
                            break;
                    }
                } catch (\Exception $e) {
                    $this->logger->error('处理用户消息失败', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            })
            ->onClose(function (Websocket $websocket,Request $request) {
                try {
                    $this->chat->onClose($websocket, $request);
                } catch (\Exception $e) {
                    $this->logger->error('用户连接关闭处理失败', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            });
    }
}