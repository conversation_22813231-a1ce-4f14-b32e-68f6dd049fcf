<?php

namespace app\im\controller;

use app\ai\bootstrap\AiApplication;
use app\ai\services\UnifiedAiService;
use app\ai\exceptions\AiServiceException;
use app\ai\config\ConfigManager;
use think\facade\Validate;
use think\Request;
use think\Response;

/**
 * AI控制器 (现代化版本)
 * 使用新的现代化架构提供AI服务的HTTP接口
 */
class AiController
{
    /**
     * AI应用程序
     * @var AiApplication
     */
    protected AiApplication $app;

    /**
     * 统一AI服务
     * @var UnifiedAiService
     */
    protected UnifiedAiService $aiService;

    public function __construct()
    {
        // 初始化现代化AI应用
        $this->app = new AiApplication();
        $this->app->boot();

        // 获取统一AI服务
        $this->aiService = $this->app->make('ai.unified');

        // 加载环境配置
        ConfigManager::loadFromEnv();
    }

    /**
     * 发送聊天消息 (现代化版本)
     * @param Request $request
     * @return Response
     */
    public function chat(Request $request): Response
    {
        try {
            // 验证请求参数
            $validate = Validate::rule([
                'input' => 'require|string',
                'provider' => 'string',
                'model' => 'string',
                'temperature' => 'float|between:0,2',
                'max_tokens' => 'integer|gt:0',
                'session_id' => 'string',
                'type' => 'in:simple,memory,tool,reasoning',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $input = $request->param('input');
            $options = [];

            // 可选参数
            $optionalParams = [
                'provider', 'model', 'temperature', 'max_tokens', 'top_p',
                'frequency_penalty', 'presence_penalty', 'session_id', 'type',
                'tools', 'cache', 'cache_ttl'
            ];
            foreach ($optionalParams as $param) {
                if ($request->has($param)) {
                    $options[$param] = $request->param($param);
                }
            }

            // 使用统一AI服务处理请求
            $response = $this->aiService->process($input, $options);

            return json([
                'code' => 200,
                'message' => '请求成功',
                'data' => [
                    'content' => $response->content,
                    'service' => $response->service,
                    'duration' => $response->duration,
                    'request_id' => $response->requestId,
                    'timestamp' => time()
                ]
            ]);

        } catch (AiServiceException $e) {
            return json([
                'code' => 500,
                'message' => 'AI服务请求失败：' . $e->getMessage(),
                'data' => null
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 批量聊天处理
     * @param Request $request
     * @return Response
     */
    public function batchChat(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'inputs' => 'require|array',
                'inputs.*' => 'require|string',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $inputs = $request->param('inputs');
            $options = [];

            $optionalParams = ['provider', 'model', 'temperature', 'max_tokens'];
            foreach ($optionalParams as $param) {
                if ($request->has($param)) {
                    $options[$param] = $request->param($param);
                }
            }

            // 批量处理
            $responses = $this->aiService->batchProcess($inputs, $options);

            $results = [];
            foreach ($responses as $index => $response) {
                $results[] = [
                    'index' => $index,
                    'content' => $response->content,
                    'service' => $response->service,
                    'duration' => $response->duration,
                    'request_id' => $response->requestId,
                    'success' => $response->isSuccess(),
                    'error' => $response->error,
                ];
            }

            return json([
                'code' => 200,
                'message' => '批量处理成功',
                'data' => [
                    'results' => $results,
                    'total' => count($results),
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '批量处理失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取服务状态
     * @return Response
     */
    public function status(): Response
    {
        try {
            $status = $this->aiService->getStatus();
            $appStatus = $this->app->getStatus();

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'ai_services' => $status,
                    'application' => $appStatus,
                    'timestamp' => time(),
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取状态失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取性能指标
     * @return Response
     */
    public function metrics(): Response
    {
        try {
            $metrics = $this->app->make('ai.metrics');
            $allMetrics = $metrics->getAllMetrics();

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $allMetrics
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取指标失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 清除缓存
     * @return Response
     */
    public function clearCache(): Response
    {
        try {
            $cache = $this->app->make('ai.cache');
            $cache->clearAiCache();

            return json([
                'code' => 200,
                'message' => '缓存清除成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '清除缓存失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 流式聊天 (简化版本)
     * @param Request $request
     * @return Response
     */
    public function streamChat(Request $request): Response
    {
        try {
            // 验证请求参数
            $validate = Validate::rule([
                'input' => 'require|string',
                'provider' => 'string',
                'model' => 'string',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $input = $request->param('input');
            $options = [];

            // 可选参数
            $optionalParams = ['provider', 'model', 'temperature', 'max_tokens', 'session_id'];
            foreach ($optionalParams as $param) {
                if ($request->has($param)) {
                    $options[$param] = $request->param($param);
                }
            }

            // 设置响应头
            header('Content-Type: text/event-stream');
            header('Cache-Control: no-cache');
            header('Connection: keep-alive');
            header('Access-Control-Allow-Origin: *');

            // 输出缓冲区处理
            if (ob_get_level()) {
                ob_end_clean();
            }

            // 发送连接事件
            echo "data: " . json_encode(['type' => 'connected']) . "\n\n";
            flush();

            // 处理请求 (目前使用普通响应，后续可扩展为真正的流式)
            $response = $this->aiService->process($input, $options);

            // 发送内容
            echo "data: " . json_encode([
                'type' => 'content',
                'content' => $response->content
            ]) . "\n\n";
            flush();

            // 发送结束事件
            echo "data: " . json_encode(['type' => 'done']) . "\n\n";
            flush();

            return Response::create('');

        } catch (\Exception $e) {
            echo "data: " . json_encode([
                'type' => 'error',
                'error' => $e->getMessage()
            ]) . "\n\n";
            flush();

            return Response::create('', 'html', 500);
        }
    }

    /**
     * 健康检查
     * @return Response
     */
    public function health(): Response
    {
        try {
            return json([
                'code' => 200,
                'message' => '服务正常',
                'data' => [
                    'status' => 'healthy',
                    'version' => '2.0.0',
                    'timestamp' => time(),
                    'services' => [
                        'unified_ai' => 'active',
                        'container' => 'active',
                        'events' => 'active',
                        'cache' => 'active',
                        'metrics' => 'active',
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '健康检查失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 生成文本
     * @param Request $request
     * @return Response
     */
    public function generateText(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'prompt' => 'require|string',
                'provider' => 'string',
                'model' => 'string',
                'max_tokens' => 'integer|gt:0',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $prompt = $request->param('prompt');
            $provider = $request->param('provider');
            $options = [];

            $optionalParams = ['model', 'temperature', 'max_tokens', 'top_p'];
            foreach ($optionalParams as $param) {
                if ($request->has($param)) {
                    $options[$param] = $request->param($param);
                }
            }

            $response = $this->basicAiService->generateText($prompt, $options, $provider);

            return json([
                'code' => 200,
                'message' => '生成成功',
                'data' => [
                    'text' => $response,
                    'provider' => $provider,
                    'timestamp' => time()
                ]
            ]);

        } catch (AiServiceException $e) {
            return json([
                'code' => 500,
                'message' => 'AI服务请求失败：' . $e->getMessage(),
                'data' => null
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 分析文本
     * @param Request $request
     * @return Response
     */
    public function analyzeText(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'text' => 'require|string',
                'analysis_type' => 'require|string',
                'provider' => 'string',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $text = $request->param('text');
            $analysisType = $request->param('analysis_type');
            $provider = $request->param('provider');
            $options = [];

            $optionalParams = ['model', 'temperature'];
            foreach ($optionalParams as $param) {
                if ($request->has($param)) {
                    $options[$param] = $request->param($param);
                }
            }

            $response = $this->basicAiService->analyzeText($text, $analysisType, $options, $provider);

            return json([
                'code' => 200,
                'message' => '分析成功',
                'data' => [
                    'analysis' => $response,
                    'type' => $analysisType,
                    'provider' => $provider,
                    'timestamp' => time()
                ]
            ]);

        } catch (AiServiceException $e) {
            return json([
                'code' => 500,
                'message' => 'AI服务请求失败：' . $e->getMessage(),
                'data' => null
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 翻译文本
     * @param Request $request
     * @return Response
     */
    public function translateText(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'text' => 'require|string',
                'target_language' => 'require|string',
                'source_language' => 'string',
                'provider' => 'string',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $text = $request->param('text');
            $targetLanguage = $request->param('target_language');
            $sourceLanguage = $request->param('source_language', 'auto');
            $provider = $request->param('provider');
            $options = [];

            $optionalParams = ['model', 'temperature'];
            foreach ($optionalParams as $param) {
                if ($request->has($param)) {
                    $options[$param] = $request->param($param);
                }
            }

            $response = $this->basicAiService->translateText(
                $text,
                $targetLanguage,
                $sourceLanguage,
                $options,
                $provider
            );

            return json([
                'code' => 200,
                'message' => '翻译成功',
                'data' => [
                    'translation' => $response,
                    'source_language' => $sourceLanguage,
                    'target_language' => $targetLanguage,
                    'provider' => $provider,
                    'timestamp' => time()
                ]
            ]);

        } catch (AiServiceException $e) {
            return json([
                'code' => 500,
                'message' => 'AI服务请求失败：' . $e->getMessage(),
                'data' => null
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建LangChain链
     * @param Request $request
     * @return Response
     */
    public function createChain(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'chain_type' => 'require|string',
                'provider' => 'string',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $chainType = $request->param('chain_type');
            $steps = $request->param('steps', []);
            $input = $request->param('input', []);
            $provider = $request->param('provider');
            $options = $request->param('options', []);

            switch ($chainType) {
                case 'llm':
                    $prompt = $steps['prompt'] ?? 'Process the following: {input}';
                    $variables = array_merge($input, $steps['variables'] ?? []);
                    $result = $this->langChainService->createLLMChain($prompt, $variables, $options, $provider);
                    break;
                case 'sequential':
                    $result = $this->langChainService->createSequentialChain($steps, $input, $options, $provider);
                    break;
                default:
                    throw new AiServiceException("Unsupported chain type: {$chainType}");
            }

            return json([
                'code' => 200,
                'message' => '链创建成功',
                'data' => [
                    'result' => $result,
                    'chain_type' => $chainType,
                    'provider' => $provider,
                    'timestamp' => time()
                ]
            ]);

        } catch (AiServiceException $e) {
            return json([
                'code' => 500,
                'message' => 'LangChain服务请求失败：' . $e->getMessage(),
                'data' => null
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建智能代理
     * @param Request $request
     * @return Response
     */
    public function createAgent(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'task' => 'require|string',
                'agent_type' => 'string',
                'provider' => 'string',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $task = $request->param('task');
            $agentType = $request->param('agent_type', 'react');
            $tools = $request->param('tools', []);
            $provider = $request->param('provider');
            $options = $request->param('options', []);

            $result = $this->langChainService->createReActAgent(
                $task,
                $tools,
                $options,
                $provider
            );

            return json([
                'code' => 200,
                'message' => '代理创建成功',
                'data' => [
                    'result' => $result,
                    'agent_type' => $agentType,
                    'tools_used' => $tools,
                    'provider' => $provider,
                    'timestamp' => time()
                ]
            ]);

        } catch (AiServiceException $e) {
            return json([
                'code' => 500,
                'message' => 'LangChain代理请求失败：' . $e->getMessage(),
                'data' => null
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 执行工作流
     * @param Request $request
     * @return Response
     */
    public function executeWorkflow(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'task' => 'require|string',
                'provider' => 'string',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $task = $request->param('task');
            $context = $request->param('context', []);
            $provider = $request->param('provider');
            $options = $request->param('options', []);

            $workflow = [
                'type' => $options['workflow_type'] ?? 'sequential',
                'task' => $task,
                'steps' => $options['steps'] ?? [],
                'tools' => $options['tools'] ?? []
            ];

            $result = $this->langChainService->executeWorkflow(
                $workflow,
                $context,
                $options,
                $provider
            );

            return json([
                'code' => 200,
                'message' => '工作流执行成功',
                'data' => [
                    'result' => $result,
                    'workflow_type' => $workflow['type'],
                    'provider' => $provider,
                    'timestamp' => time()
                ]
            ]);

        } catch (AiServiceException $e) {
            return json([
                'code' => 500,
                'message' => 'LangChain工作流请求失败：' . $e->getMessage(),
                'data' => null
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 带记忆的对话
     * @param Request $request
     * @return Response
     */
    public function chatWithMemory(Request $request): Response
    {
        try {
            $validate = Validate::rule([
                'input' => 'require|string',
                'session_id' => 'require|string',
                'provider' => 'string',
            ]);

            if (!$validate->check($request->param())) {
                return json([
                    'code' => 400,
                    'message' => '参数验证失败：' . $validate->getError(),
                    'data' => null
                ]);
            }

            $input = $request->param('input');
            $sessionId = $request->param('session_id');
            $provider = $request->param('provider');
            $options = $request->param('options', []);

            $response = $this->langChainService->executeWithMemory(
                $input,
                $sessionId,
                $options,
                $provider
            );

            return json([
                'code' => 200,
                'message' => '对话成功',
                'data' => [
                    'response' => $response,
                    'session_id' => $sessionId,
                    'provider' => $provider,
                    'timestamp' => time()
                ]
            ]);

        } catch (AiServiceException $e) {
            return json([
                'code' => 500,
                'message' => 'LangChain记忆对话请求失败：' . $e->getMessage(),
                'data' => null
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 清除会话记忆
     * @param Request $request
     * @return Response
     */
    public function clearMemory(Request $request): Response
    {
        try {
            $sessionId = $request->param('session_id');
            
            $this->langChainService->clearMemory($sessionId);

            return json([
                'code' => 200,
                'message' => '记忆清除成功',
                'data' => [
                    'session_id' => $sessionId,
                    'timestamp' => time()
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '记忆清除失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取支持的AI服务提供商列表
     * @return Response
     */
    public function getProviders(): Response
    {
        try {
            $providers = $this->basicAiService->getSupportedProviders();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $providers
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取可用工具列表
     * @return Response
     */
    public function getTools(): Response
    {
        try {
            $tools = $this->langChainService->getAvailableTools();
            
            $toolsInfo = [];
            foreach ($tools as $tool) {
                $toolsInfo[] = [
                    'name' => $tool,
                    'description' => $this->langChainService->getToolDescription($tool)
                ];
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $toolsInfo
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 健康检查
     * @return Response
     */
    public function health(): Response
    {
        try {
            return json([
                'code' => 200,
                'message' => '服务正常',
                'data' => [
                    'status' => 'healthy',
                    'timestamp' => time(),
                    'services' => [
                        'basic_ai' => 'active',
                        'langchain' => 'active'
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '健康检查失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}