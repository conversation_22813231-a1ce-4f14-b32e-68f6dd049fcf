<?php

declare(strict_types=1);

namespace app\im\controller\customer;

use app\admin\controller\config\BaseConfigController;
use think\App;

/**
 * 客服系统配置控制器
 */
class ServiceConfigController extends BaseConfigController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 初始化配置
     * @return void
     */
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'vchat';
        $this->configKey = 'vchat';
    }

    /**
     * 验证配置
     * @param array $config
     * @return void
     * @throws \Exception
     */
    protected function validateConfig(array $config): void
    {
        // 验证等待时间配置
        if (isset($config['waiting'])) {
            $waiting = $config['waiting'];
            if (!isset($waiting['base_time']) || $waiting['base_time'] <= 0) {
                throw new \Exception('基础等待时间必须大于0');
            }
            if (!isset($waiting['increment_per_user']) || $waiting['increment_per_user'] < 0) {
                throw new \Exception('每用户等待增量不能为负数');
            }
            if (!isset($waiting['max_time']) || $waiting['max_time'] <= $waiting['base_time']) {
                throw new \Exception('最大等待时间必须大于基础等待时间');
            }
        }

        // 验证会话超时配置
        if (isset($config['session_timeout'])) {
            $timeout = $config['session_timeout'];
            if (!isset($timeout['user_inactive_timeout']) || $timeout['user_inactive_timeout'] <= 0) {
                throw new \Exception('用户无响应超时时间必须大于0');
            }
            if (!isset($timeout['service_inactive_timeout']) || $timeout['service_inactive_timeout'] <= 0) {
                throw new \Exception('客服无响应超时时间必须大于0');
            }
            if (!isset($timeout['max_duration']) || $timeout['max_duration'] <= 0) {
                throw new \Exception('会话最大持续时间必须大于0');
            }
        }

        // 验证排队管理配置
        if (isset($config['queue'])) {
            $queue = $config['queue'];
            if (!isset($queue['max_global_queue']) || $queue['max_global_queue'] < 0) {
                throw new \Exception('全局最大排队人数不能为负数');
            }
            if (!isset($queue['max_per_service_queue']) || $queue['max_per_service_queue'] < 0) {
                throw new \Exception('单个客服最大排队人数不能为负数');
            }
            if (!isset($queue['strategy']) || !in_array($queue['strategy'], ['fifo', 'priority', 'vip'])) {
                throw new \Exception('无效的排队策略');
            }
        }

        // 验证客服分配策略
        if (isset($config['assignment'])) {
            $assign = $config['assignment'];
            if (!isset($assign['strategy']) || !in_array($assign['strategy'], ['round_robin', 'least_load', 'random'])) {
                throw new \Exception('无效的客服分配策略');
            }

            if (isset($assign['load_balance_weight'])) {
                $weights = $assign['load_balance_weight'];
                $total = array_sum($weights);
                
                if ($total <= 0 || $total > 1) {
                    throw new \Exception('负载均衡权重总和必须在0~1之间');
                }
            }
        }

        // 验证通知配置
        if (isset($config['notification'])) {
            $notify = $config['notification'];
            if (!isset($notify['queue_notification'])) {
                throw new \Exception('必须指定是否启用排队通知');
            }
            if (!isset($notify['timeout_warning'])) {
                throw new \Exception('必须指定是否启用超时警告');
            }
        }

        // 验证 Redis 配置
        if (isset($config['redis'])) {
            $redis = $config['redis'];
            if (!isset($redis['host']) || empty($redis['host'])) {
                throw new \Exception('Redis主机地址不能为空');
            }
            if (!isset($redis['port']) || $redis['port'] <= 0 || $redis['port'] > 65535) {
                throw new \Exception('Redis端口号必须在1~65535之间');
            }
        }

        // 验证日志配置
        if (isset($config['log'])) {
            $log = $config['log'];
            if (!isset($log['level']) || !in_array($log['level'], ['debug', 'info', 'warning', 'error'])) {
                throw new \Exception('日志级别必须是 debug/info/warning/error');
            }
        }
    }

    /**
     * 转换配置数据结构
     * @param array $data 原始配置数据
     * @return array 转换后的配置数据
     */
    protected function transformConfig(array $data): array
    {
        // 可以在这里做配置结构调整或默认值填充
        return $data;
    }

    /**
     * 处理配置数据
     * @param array $config 当前配置
     * @param array $data 新配置数据
     * @return array 处理后的配置数据
     */
    protected function processConfig(array $config, array $data): array
    {
        // 合并配置并保留未修改部分
        return array_merge_recursive($config, $data);
    }
}