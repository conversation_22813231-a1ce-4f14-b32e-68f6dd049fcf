<?php

declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\ChatSession;
use app\model\CustomerService;
use app\model\Task;
use think\App;
use think\facade\Request;
use think\facade\Log;
use think\facade\Db;
use think\facade\Cache;

/**
 * 满意度定时任务控制器
 */
class SatisfactionTaskController extends BaseAdminController
{
    protected $sessionModel;
    protected $serviceModel;
    protected $taskModel;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->sessionModel = new ChatSession();
        $this->serviceModel = new CustomerService();
        $this->taskModel = new Task();
    }

    /**
     * 生成满意度日报
     * 定时任务调用，生成每日满意度统计报告
     * @return \think\Response
     */
    public function generateDailyReport()
    {
        try {
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $startTime = strtotime($yesterday . ' 00:00:00');
            $endTime = strtotime($yesterday . ' 23:59:59');

            Log::info('开始生成满意度日报: ' . $yesterday);

            // 获取昨日满意度数据
            $dailyStats = $this->getDailyStats($startTime, $endTime);

            // 获取历史对比数据（前一天）
            $prevStartTime = strtotime('-2 day 00:00:00');
            $prevEndTime = strtotime('-2 day 23:59:59');
            $prevStats = $this->getDailyStats($prevStartTime, $prevEndTime);

            // 计算变化率
            $changes = $this->calculateChanges($dailyStats, $prevStats);

            // 生成报告数据
            $reportData = [
                'date' => $yesterday,
                'stats' => $dailyStats,
                'prev_stats' => $prevStats,
                'changes' => $changes,
                'generated_at' => date('Y-m-d H:i:s')
            ];

            // 存储报告到缓存
            $cacheKey = 'satisfaction_daily_report_' . str_replace('-', '', $yesterday);
            Cache::set($cacheKey, $reportData, 86400 * 7); // 保存7天

            // 记录到数据库（可选）
            $this->saveReportToDatabase('daily', $yesterday, $reportData);

            // 发送通知（如果配置了）
            $this->sendReportNotification('daily', $reportData);

            Log::info('满意度日报生成完成: ' . $yesterday);

            return $this->success([
                'message' => '日报生成成功',
                'date' => $yesterday,
                'report_data' => $reportData
            ]);

        } catch (\Exception $e) {
            Log::error('生成满意度日报失败: ' . $e->getMessage());
            return $this->fail('日报生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成满意度月报
     * 定时任务调用，生成每月满意度统计报告
     * @return \think\Response
     */
    public function generateMonthlyReport()
    {
        try {
            $lastMonth = date('Y-m', strtotime('-1 month'));
            $startTime = strtotime($lastMonth . '-01 00:00:00');
            $endTime = strtotime(date('Y-m-t 23:59:59', $startTime));

            Log::info('开始生成满意度月报: ' . $lastMonth);

            // 获取上月满意度数据
            $monthlyStats = $this->getMonthlyStats($startTime, $endTime);

            // 获取历史对比数据（前一个月）
            $prevStartTime = strtotime('-2 month first day of this month 00:00:00');
            $prevEndTime = strtotime('-2 month last day of this month 23:59:59');
            $prevStats = $this->getMonthlyStats($prevStartTime, $prevEndTime);

            // 计算变化率
            $changes = $this->calculateChanges($monthlyStats, $prevStats);

            // 获取每日趋势数据
            $dailyTrend = $this->getDailyTrendForMonth($startTime, $endTime);

            // 获取客服排名
            $serviceRanking = $this->getServiceRankingForMonth($startTime, $endTime);

            // 生成报告数据
            $reportData = [
                'month' => $lastMonth,
                'stats' => $monthlyStats,
                'prev_stats' => $prevStats,
                'changes' => $changes,
                'daily_trend' => $dailyTrend,
                'service_ranking' => $serviceRanking,
                'generated_at' => date('Y-m-d H:i:s')
            ];

            // 存储报告到缓存
            $cacheKey = 'satisfaction_monthly_report_' . str_replace('-', '', $lastMonth);
            Cache::set($cacheKey, $reportData, 86400 * 30); // 保存30天

            // 记录到数据库
            $this->saveReportToDatabase('monthly', $lastMonth, $reportData);

            // 发送通知
            $this->sendReportNotification('monthly', $reportData);

            Log::info('满意度月报生成完成: ' . $lastMonth);

            return $this->success([
                'message' => '月报生成成功',
                'month' => $lastMonth,
                'report_data' => $reportData
            ]);

        } catch (\Exception $e) {
            Log::error('生成满意度月报失败: ' . $e->getMessage());
            return $this->fail('月报生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 满意度预警检查
     * 定时任务调用，检查满意度是否低于阈值并发送预警
     * @return \think\Response
     */
    public function checkSatisfactionAlert()
    {
        try {
            // 配置预警阈值
            $alertThreshold = 3.5; // 满意度低于3.5分预警
            $minSessions = 10; // 最少会话数量
            $checkPeriod = 24; // 检查最近24小时

            $startTime = time() - ($checkPeriod * 3600);
            $endTime = time();

            Log::info('开始满意度预警检查');

            // 获取最近时间段的满意度数据
            $recentStats = $this->getRecentStats($startTime, $endTime);

            $alerts = [];

            // 检查整体满意度
            if ($recentStats['total_sessions'] >= $minSessions && $recentStats['avg_satisfaction'] < $alertThreshold) {
                $alerts[] = [
                    'type' => 'overall',
                    'level' => 'warning',
                    'message' => '整体满意度过低',
                    'current_value' => $recentStats['avg_satisfaction'],
                    'threshold' => $alertThreshold,
                    'sessions_count' => $recentStats['total_sessions'],
                    'period' => $checkPeriod . '小时'
                ];
            }

            // 检查各客服满意度
            $serviceStats = $this->getServiceStatsForAlert($startTime, $endTime, $minSessions);
            foreach ($serviceStats as $stat) {
                if ($stat['avg_satisfaction'] < $alertThreshold) {
                    $alerts[] = [
                        'type' => 'service',
                        'level' => 'warning',
                        'message' => '客服满意度过低',
                        'service_id' => $stat['service_id'],
                        'service_name' => $stat['service_name'],
                        'current_value' => $stat['avg_satisfaction'],
                        'threshold' => $alertThreshold,
                        'sessions_count' => $stat['sessions_count'],
                        'period' => $checkPeriod . '小时'
                    ];
                }
            }

            // 如果有预警，发送通知
            if (!empty($alerts)) {
                $this->sendAlertNotification($alerts);
                Log::warning('满意度预警触发', ['alerts' => $alerts]);
            }

            Log::info('满意度预警检查完成，发现 ' . count($alerts) . ' 个预警');

            return $this->success([
                'message' => '预警检查完成',
                'alerts_count' => count($alerts),
                'alerts' => $alerts,
                'check_period' => $checkPeriod . '小时',
                'threshold' => $alertThreshold
            ]);

        } catch (\Exception $e) {
            Log::error('满意度预警检查失败: ' . $e->getMessage());
            return $this->fail('预警检查失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取日统计数据
     * @param int $startTime
     * @param int $endTime
     * @return array
     */
    private function getDailyStats($startTime, $endTime)
    {
        $condition = [
            ['end_time', 'between', [$startTime, $endTime]],
            ['satisfaction', '>', 0]
        ];

        $stats = $this->sessionModel->where($condition)->field([
            'COUNT(*) as total_sessions',
            'AVG(satisfaction) as avg_satisfaction',
            'COUNT(CASE WHEN satisfaction >= 4 THEN 1 END) as high_satisfaction_count',
            'COUNT(CASE WHEN satisfaction <= 2 THEN 1 END) as low_satisfaction_count',
            'AVG(duration) as avg_duration'
        ])->find();

        return [
            'total_sessions' => (int)$stats['total_sessions'],
            'avg_satisfaction' => round((float)$stats['avg_satisfaction'], 2),
            'high_satisfaction_count' => (int)$stats['high_satisfaction_count'],
            'low_satisfaction_count' => (int)$stats['low_satisfaction_count'],
            'high_satisfaction_rate' => $stats['total_sessions'] > 0 ? round($stats['high_satisfaction_count'] / $stats['total_sessions'] * 100, 2) : 0,
            'low_satisfaction_rate' => $stats['total_sessions'] > 0 ? round($stats['low_satisfaction_count'] / $stats['total_sessions'] * 100, 2) : 0,
            'avg_duration' => round((float)$stats['avg_duration'], 2)
        ];
    }

    /**
     * 获取月统计数据
     * @param int $startTime
     * @param int $endTime
     * @return array
     */
    private function getMonthlyStats($startTime, $endTime)
    {
        return $this->getDailyStats($startTime, $endTime);
    }

    /**
     * 获取最近统计数据
     * @param int $startTime
     * @param int $endTime
     * @return array
     */
    private function getRecentStats($startTime, $endTime)
    {
        return $this->getDailyStats($startTime, $endTime);
    }

    /**
     * 计算变化率
     * @param array $current
     * @param array $previous
     * @return array
     */
    private function calculateChanges($current, $previous)
    {
        $changes = [];
        
        foreach ($current as $key => $value) {
            if (isset($previous[$key]) && is_numeric($value) && is_numeric($previous[$key])) {
                if ($previous[$key] != 0) {
                    $changes[$key] = round(($value - $previous[$key]) / $previous[$key] * 100, 2);
                } else {
                    $changes[$key] = $value > 0 ? 100 : 0;
                }
            } else {
                $changes[$key] = 0;
            }
        }
        
        return $changes;
    }

    /**
     * 获取月度每日趋势
     * @param int $startTime
     * @param int $endTime
     * @return array
     */
    private function getDailyTrendForMonth($startTime, $endTime)
    {
        $trend = [];
        $currentTime = $startTime;
        
        while ($currentTime <= $endTime) {
            $dayStart = $currentTime;
            $dayEnd = strtotime('+1 day', $currentTime) - 1;
            
            $dayStats = $this->getDailyStats($dayStart, $dayEnd);
            $trend[] = [
                'date' => date('Y-m-d', $currentTime),
                'avg_satisfaction' => $dayStats['avg_satisfaction'],
                'total_sessions' => $dayStats['total_sessions']
            ];
            
            $currentTime = strtotime('+1 day', $currentTime);
        }
        
        return $trend;
    }

    /**
     * 获取月度客服排名
     * @param int $startTime
     * @param int $endTime
     * @return array
     */
    private function getServiceRankingForMonth($startTime, $endTime)
    {
        $condition = [
            ['end_time', 'between', [$startTime, $endTime]],
            ['satisfaction', '>', 0]
        ];

        $ranking = $this->sessionModel
            ->where($condition)
            ->field([
                'service_id',
                'COUNT(*) as sessions_count',
                'AVG(satisfaction) as avg_satisfaction'
            ])
            ->group('service_id')
            ->having('sessions_count >= 5') // 至少5个会话
            ->order('avg_satisfaction desc')
            ->limit(20)
            ->select()
            ->toArray();

        // 获取客服信息
        $serviceIds = array_column($ranking, 'service_id');
        $services = $this->serviceModel->whereIn('id', $serviceIds)->column('nickname', 'id');

        return array_map(function($item) use ($services) {
            return [
                'service_id' => $item['service_id'],
                'service_name' => $services[$item['service_id']] ?? '未知客服',
                'sessions_count' => (int)$item['sessions_count'],
                'avg_satisfaction' => round((float)$item['avg_satisfaction'], 2)
            ];
        }, $ranking);
    }

    /**
     * 获取客服预警统计
     * @param int $startTime
     * @param int $endTime
     * @param int $minSessions
     * @return array
     */
    private function getServiceStatsForAlert($startTime, $endTime, $minSessions)
    {
        $condition = [
            ['end_time', 'between', [$startTime, $endTime]],
            ['satisfaction', '>', 0]
        ];

        $stats = $this->sessionModel
            ->where($condition)
            ->field([
                'service_id',
                'COUNT(*) as sessions_count',
                'AVG(satisfaction) as avg_satisfaction'
            ])
            ->group('service_id')
            ->having('sessions_count >= ' . $minSessions)
            ->select()
            ->toArray();

        // 获取客服信息
        $serviceIds = array_column($stats, 'service_id');
        $services = $this->serviceModel->whereIn('id', $serviceIds)->column('nickname', 'id');

        return array_map(function($item) use ($services) {
            return [
                'service_id' => $item['service_id'],
                'service_name' => $services[$item['service_id']] ?? '未知客服',
                'sessions_count' => (int)$item['sessions_count'],
                'avg_satisfaction' => round((float)$item['avg_satisfaction'], 2)
            ];
        }, $stats);
    }

    /**
     * 保存报告到数据库
     * @param string $type
     * @param string $period
     * @param array $data
     */
    private function saveReportToDatabase($type, $period, $data)
    {
        try {
            // 这里可以创建一个专门的报告表来存储
            // 暂时使用日志记录
            Log::info('满意度报告已生成', [
                'type' => $type,
                'period' => $period,
                'summary' => [
                    'total_sessions' => $data['stats']['total_sessions'] ?? 0,
                    'avg_satisfaction' => $data['stats']['avg_satisfaction'] ?? 0
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('保存报告到数据库失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送报告通知
     * @param string $type
     * @param array $data
     */
    private function sendReportNotification($type, $data)
    {
        try {
            // 这里可以集成邮件、短信、企业微信等通知方式
            // 暂时使用日志记录
            Log::info('满意度报告通知', [
                'type' => $type,
                'period' => $data['date'] ?? $data['month'] ?? '',
                'avg_satisfaction' => $data['stats']['avg_satisfaction'] ?? 0
            ]);
        } catch (\Exception $e) {
            Log::error('发送报告通知失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送预警通知
     * @param array $alerts
     */
    private function sendAlertNotification($alerts)
    {
        try {
            // 这里可以集成各种通知方式
            // 暂时使用日志记录
            foreach ($alerts as $alert) {
                Log::warning('满意度预警通知', $alert);
            }
        } catch (\Exception $e) {
            Log::error('发送预警通知失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取报告历史
     * @return \think\Response
     */
    public function getReportHistory()
    {
        try {
            $type = Request::param('type', 'daily'); // daily, monthly
            $limit = Request::param('limit/d', 30);

            $cachePattern = 'satisfaction_' . $type . '_report_*';
            
            // 这里简化处理，实际应该从数据库获取
            return $this->success([
                'message' => '报告历史获取成功',
                'type' => $type,
                'reports' => []
            ]);

        } catch (\Exception $e) {
            Log::error('获取报告历史失败: ' . $e->getMessage());
            return $this->fail('获取报告历史失败');
        }
    }

    /**
     * 手动触发报告生成
     * @return \think\Response
     */
    public function triggerReport()
    {
        try {
            $type = Request::param('type', 'daily');
            $date = Request::param('date');

            if ($type === 'daily') {
                return $this->generateDailyReport();
            } elseif ($type === 'monthly') {
                return $this->generateMonthlyReport();
            } else {
                return $this->fail('不支持的报告类型');
            }

        } catch (\Exception $e) {
            Log::error('手动触发报告生成失败: ' . $e->getMessage());
            return $this->fail('触发报告生成失败');
        }
    }
}