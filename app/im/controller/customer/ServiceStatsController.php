<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\ChatSession;
use app\model\ChatMessage;
use app\model\CustomerService;
use think\App;
use think\facade\Log;
use think\facade\Request;

class ServiceStatsController extends BaseAdminController
{
    protected $sessionModel;
    protected $messageModel;
    protected $serviceModel;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->sessionModel = new ChatSession();
        $this->messageModel = new ChatMessage();
        $this->serviceModel = new CustomerService();
    }

    /**
     * 获取客服性能统计
     */
    public function getServiceStats()
    {
        try {
            $serviceId = Request::param('service_id/d');
            $startTime = Request::param('start_time');
            $endTime = Request::param('end_time');

            if (!$serviceId || !$startTime || !$endTime) {
                $this->fail('缺少必要参数');
            }

            $stats = $this->serviceModel->getPerformanceStats($serviceId, $startTime, $endTime);
            
        } catch (\Exception $e) {
            Log::error('获取客服统计失败：' . $e->getMessage());
            $this->fail('获取客服统计失败');
        }

        $this->ok('success', $stats);
    }

    /**
     * 获取会话统计
     */
    public function getSessionStats()
    {
        try {
            $condition = Request::param('condition/a', []);
            $stats = $this->sessionModel->getSessionStats($condition);
            
        } catch (\Exception $e) {
            Log::error('获取会话统计失败：' . $e->getMessage());
            $this->fail('获取会话统计失败');
        }

        $this->ok('success', $stats);
    }

    /**
     * 获取消息统计
     */
    public function getMessageStats()
    {
        try {
            $condition = Request::param('condition/a', []);
            $stats = $this->messageModel->getMessageStats($condition);
            
        } catch (\Exception $e) {
            Log::error('获取消息统计失败：' . $e->getMessage());
            $this->fail('获取消息统计失败');
        }

        $this->ok('success', $stats);
    }
}