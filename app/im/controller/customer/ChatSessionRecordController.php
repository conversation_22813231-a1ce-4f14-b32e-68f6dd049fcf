<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\ChatSessionRecord;
use think\App;

class ChatSessionRecordController extends BaseAdminController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new ChatSessionRecord();
    }

    /**
     * 获取用户最新的一条连接记录
     * @param int $user_id 用户ID
     * @return \think\response\Json
     */
    public function getLatestRecord($user_id)
    {
        try {
            $record = $this->model
                ->where('user_id', $user_id)
                ->order('createtime', 'desc')
                ->find();

            if (!$record) {
                return json([
                    'code' => 404,
                    'msg' => '未找到连接记录'
                ]);
            }

            return json([
                'code' => 200,
                'msg' => 'success',
                'data' => $record
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取连接记录失败'
            ]);
        }
    }
}