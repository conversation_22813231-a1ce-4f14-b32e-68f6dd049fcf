<?php

declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\ChatSession;
use app\model\CustomerService;
use think\App;
use think\facade\Request;
use think\facade\Log;
use think\facade\Db;

/**
 * 满意度报表控制器
 */
class SatisfactionReportController extends BaseAdminController
{
    protected $sessionModel;
    protected $serviceModel;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->sessionModel = new ChatSession();
        $this->serviceModel = new CustomerService();
    }

    /**
     * 获取满意度统计概览
     * @return \think\Response
     */
    public function getSatisfactionOverview()
    {
        try {
            $serviceId = Request::param('service_id/d', null);
            $serviceIds = Request::param('service_ids', null);
            $startTime = Request::param('start_time');
            $endTime = Request::param('end_time');
            $groupId = Request::param('group_id/d', null);

            $condition = [];
            $condition[] = ['satisfaction', '>', 0]; // 只统计有评分的会话

            // 处理客服ID筛选
            if ($serviceId !== null) {
                $condition[] = ['service_id', '=', $serviceId];
            } elseif ($serviceIds !== null) {
                $serviceIdArray = is_array($serviceIds) ? $serviceIds : explode(',', $serviceIds);
                $condition[] = ['service_id', 'in', $serviceIdArray];
            } elseif ($groupId !== null) {
                // 根据组ID获取客服列表
                $serviceIdsInGroup = $this->serviceModel->where('group_id', $groupId)->column('id');
                if (!empty($serviceIdsInGroup)) {
                    $condition[] = ['service_id', 'in', $serviceIdsInGroup];
                } else {
                    // 如果组内没有客服，返回空数据
                    $this->ok('success', $this->getEmptyStats());
                    return;
                }
            }

            // 时间筛选
            if ($startTime !== null) {
                $condition[] = ['start_time', '>=', strtotime($startTime)];
            }
            if ($endTime !== null) {
                $condition[] = ['end_time', '<=', strtotime($endTime)];
            }

            $stats = $this->calculateSatisfactionStats($condition);

        } catch (\Exception $e) {
            Log::error('获取满意度统计失败：' . $e->getMessage());
            $this->fail('获取满意度统计失败');
        }

        $this->ok('success', $stats);
    }

    /**
     * 获取满意度评分分布
     * @return \think\Response
     */
    public function getSatisfactionDistribution()
    {
        try {
            $serviceId = Request::param('service_id/d', null);
            $serviceIds = Request::param('service_ids', null);
            $startTime = Request::param('start_time');
            $endTime = Request::param('end_time');
            $groupId = Request::param('group_id/d', null);

            $condition = [];
            $condition[] = ['satisfaction', '>', 0];

            // 处理客服ID筛选
            if ($serviceId !== null) {
                $condition[] = ['service_id', '=', $serviceId];
            } elseif ($serviceIds !== null) {
                $serviceIdArray = is_array($serviceIds) ? $serviceIds : explode(',', $serviceIds);
                $condition[] = ['service_id', 'in', $serviceIdArray];
            } elseif ($groupId !== null) {
                $serviceIdsInGroup = $this->serviceModel->where('group_id', $groupId)->column('id');
                if (!empty($serviceIdsInGroup)) {
                    $condition[] = ['service_id', 'in', $serviceIdsInGroup];
                } else {
                    $this->ok('success', []);
                    return;
                }
            }

            // 时间筛选
            if ($startTime !== null) {
                $condition[] = ['start_time', '>=', strtotime($startTime)];
            }
            if ($endTime !== null) {
                $condition[] = ['end_time', '<=', strtotime($endTime)];
            }

            $distribution = $this->sessionModel->where($condition)
                ->field('satisfaction, COUNT(*) as count')
                ->group('satisfaction')
                ->order('satisfaction', 'asc')
                ->select()
                ->toArray();

            // 格式化数据，确保1-5分都有数据
            $result = [];
            for ($i = 1; $i <= 5; $i++) {
                $result[] = [
                    'score' => $i,
                    'count' => 0,
                    'percentage' => 0
                ];
            }

            $total = array_sum(array_column($distribution, 'count'));
            foreach ($distribution as $item) {
                $index = $item['satisfaction'] - 1;
                if ($index >= 0 && $index < 5) {
                    $result[$index]['count'] = (int)$item['count'];
                    $result[$index]['percentage'] = $total > 0 ? round(($item['count'] / $total) * 100, 2) : 0;
                }
            }

            $this->ok('success', $result);

        } catch (\Exception $e) {
            Log::error('获取满意度分布失败：' . $e->getMessage());
            $this->fail('获取满意度分布失败');
        }
    }

    /**
     * 获取按时间维度的满意度趋势
     * @return \think\Response
     */
    public function getSatisfactionTrend()
    {
        try {
            $serviceId = Request::param('service_id/d', null);
            $serviceIds = Request::param('service_ids', null);
            $startTime = Request::param('start_time');
            $endTime = Request::param('end_time');
            $groupId = Request::param('group_id/d', null);
            $dimension = Request::param('dimension', 'day'); // day, month, year

            if (!$startTime || !$endTime) {
                $this->fail('请提供开始时间和结束时间');
            }

            $condition = [];
            $condition[] = ['satisfaction', '>', 0];
            $condition[] = ['start_time', '>=', strtotime($startTime)];
            $condition[] = ['end_time', '<=', strtotime($endTime)];

            // 处理客服ID筛选
            if ($serviceId !== null) {
                $condition[] = ['service_id', '=', $serviceId];
            } elseif ($serviceIds !== null) {
                $serviceIdArray = is_array($serviceIds) ? $serviceIds : explode(',', $serviceIds);
                $condition[] = ['service_id', 'in', $serviceIdArray];
            } elseif ($groupId !== null) {
                $serviceIdsInGroup = $this->serviceModel->where('group_id', $groupId)->column('id');
                if (!empty($serviceIdsInGroup)) {
                    $condition[] = ['service_id', 'in', $serviceIdsInGroup];
                } else {
                    $this->ok('success', []);
                    return;
                }
            }

            $trend = $this->calculateSatisfactionTrend($condition, $dimension);

            $this->ok('success', $trend);

        } catch (\Exception $e) {
            Log::error('获取满意度趋势失败：' . $e->getMessage());
            $this->fail('获取满意度趋势失败');
        }
    }

    /**
     * 获取客服满意度排行
     * @return \think\Response
     */
    public function getServiceSatisfactionRanking()
    {
        try {
            $startTime = Request::param('start_time');
            $endTime = Request::param('end_time');
            $groupId = Request::param('group_id/d', null);
            $limit = Request::param('limit/d', 20);

            $condition = [];
            $condition[] = ['s.satisfaction', '>', 0];

            if ($startTime !== null) {
                $condition[] = ['s.start_time', '>=', strtotime($startTime)];
            }
            if ($endTime !== null) {
                $condition[] = ['s.end_time', '<=', strtotime($endTime)];
            }
            if ($groupId !== null) {
                $condition[] = ['cs.group_id', '=', $groupId];
            }

            $ranking = Db::table('ad_chat_session')
                ->alias('s')
                ->join('ad_customer_service cs', 's.service_id = cs.id')
                ->where($condition)
                ->field([
                    'cs.id as service_id',
                    'cs.name as service_name',
                    'cs.group_id',
                    'COUNT(*) as total_sessions',
                    'AVG(s.satisfaction) as avg_satisfaction',
                    'SUM(CASE WHEN s.satisfaction >= 4 THEN 1 ELSE 0 END) as good_count',
                    'SUM(CASE WHEN s.satisfaction <= 2 THEN 1 ELSE 0 END) as bad_count'
                ])
                ->group('cs.id')
                ->order('avg_satisfaction', 'desc')
                ->limit($limit)
                ->select();

            // 计算好评率和差评率
            $result = [];
            foreach ($ranking as $item) {
                $totalSessions = (int)$item['total_sessions'];
                $result[] = [
                    'service_id' => (int)$item['service_id'],
                    'service_name' => $item['service_name'],
                    'group_id' => (int)$item['group_id'],
                    'total_sessions' => $totalSessions,
                    'avg_satisfaction' => round((float)$item['avg_satisfaction'], 2),
                    'good_rate' => $totalSessions > 0 ? round(((int)$item['good_count'] / $totalSessions) * 100, 2) : 0,
                    'bad_rate' => $totalSessions > 0 ? round(((int)$item['bad_count'] / $totalSessions) * 100, 2) : 0,
                ];
            }

            $this->ok('success', $result);

        } catch (\Exception $e) {
            Log::error('获取客服满意度排行失败：' . $e->getMessage());
            $this->fail('获取客服满意度排行失败');
        }
    }

    /**
     * 获取满意度详细列表（用于查看具体的评价记录）
     * @return \think\Response
     */
    public function getSatisfactionDetailList()
    {
        try {
            $serviceId = Request::param('service_id/d', null);
            $serviceIds = Request::param('service_ids', null);
            $startTime = Request::param('start_time');
            $endTime = Request::param('end_time');
            $groupId = Request::param('group_id/d', null);
            $satisfaction = Request::param('satisfaction/d', null);
            $limit = Request::param('limit/d', 20);
            $page = Request::param('page/d', 1);

            $condition = [];
            $condition[] = ['s.satisfaction', '>', 0];

            if ($serviceId !== null) {
                $condition[] = ['s.service_id', '=', $serviceId];
            } elseif ($serviceIds !== null) {
                $serviceIdArray = is_array($serviceIds) ? $serviceIds : explode(',', $serviceIds);
                $condition[] = ['s.service_id', 'in', $serviceIdArray];
            } elseif ($groupId !== null) {
                $condition[] = ['cs.group_id', '=', $groupId];
            }

            if ($startTime !== null) {
                $condition[] = ['s.start_time', '>=', strtotime($startTime)];
            }
            if ($endTime !== null) {
                $condition[] = ['s.end_time', '<=', strtotime($endTime)];
            }
            if ($satisfaction !== null) {
                $condition[] = ['s.satisfaction', '=', $satisfaction];
            }

            $query = Db::table('ad_chat_session')
                ->alias('s')
                ->join('ad_customer_service cs', 's.service_id = cs.id')
                ->leftJoin('system_user u', 's.user_id = u.id')
                ->where($condition)
                ->field([
                    's.id as session_id',
                    's.user_id',
                    'u.username as user_name',
                    's.service_id',
                    'cs.name as service_name',
                    's.satisfaction',
                    's.start_time',
                    's.end_time',
                    's.duration',
                    's.source'
                ])
                ->order('s.start_time', 'desc');

            $total = $query->count();
            $list = $query->page($page, $limit)->select();

            // 格式化数据
            $result = [];
            foreach ($list as $item) {
                $result[] = [
                    'session_id' => (int)$item['session_id'],
                    'user_id' => (int)$item['user_id'],
                    'user_name' => $item['user_name'] ?? '未知用户',
                    'service_id' => (int)$item['service_id'],
                    'service_name' => $item['service_name'],
                    'satisfaction' => (int)$item['satisfaction'],
                    'start_time' => date('Y-m-d H:i:s', $item['start_time']),
                    'end_time' => $item['end_time'] ? date('Y-m-d H:i:s', $item['end_time']) : null,
                    'duration' => (int)$item['duration'],
                    'source' => $item['source']
                ];
            }

            $this->ok('success', [
                'list' => $result,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            Log::error('获取满意度详细列表失败：' . $e->getMessage());
            $this->fail('获取满意度详细列表失败');
        }
    }

    /**
     * 计算满意度统计数据
     * @param array $condition
     * @return array
     */
    private function calculateSatisfactionStats(array $condition): array
    {
        $sessions = $this->sessionModel->where($condition)->select();
        
        if ($sessions->isEmpty()) {
            return $this->getEmptyStats();
        }

        $total = $sessions->count();
        $satisfactionSum = $sessions->sum('satisfaction');
        $avgSatisfaction = $total > 0 ? round($satisfactionSum / $total, 2) : 0;
        
        // 统计各评分数量
        $scoreStats = [];
        for ($i = 1; $i <= 5; $i++) {
            $scoreStats[$i] = 0;
        }
        
        foreach ($sessions as $session) {
            $score = (int)$session['satisfaction'];
            if ($score >= 1 && $score <= 5) {
                $scoreStats[$score]++;
            }
        }
        
        // 计算好评率（4-5分）和差评率（1-2分）
        $goodCount = $scoreStats[4] + $scoreStats[5];
        $badCount = $scoreStats[1] + $scoreStats[2];
        $goodRate = $total > 0 ? round(($goodCount / $total) * 100, 2) : 0;
        $badRate = $total > 0 ? round(($badCount / $total) * 100, 2) : 0;
        
        return [
            'total_sessions' => $total,
            'avg_satisfaction' => $avgSatisfaction,
            'good_rate' => $goodRate,
            'bad_rate' => $badRate,
            'score_distribution' => [
                '1' => $scoreStats[1],
                '2' => $scoreStats[2],
                '3' => $scoreStats[3],
                '4' => $scoreStats[4],
                '5' => $scoreStats[5]
            ]
        ];
    }

    /**
     * 计算满意度趋势数据
     * @param array $condition
     * @param string $dimension
     * @return array
     */
    private function calculateSatisfactionTrend(array $condition, string $dimension): array
    {
        $formatMap = [
            'day' => '%Y-%m-%d',
            'month' => '%Y-%m',
            'year' => '%Y'
        ];
        
        $format = $formatMap[$dimension] ?? '%Y-%m-%d';
        
        $trend = Db::table('ad_chat_session')
            ->where($condition)
            ->field([
                "FROM_UNIXTIME(start_time, '{$format}') as period",
                'COUNT(*) as total_sessions',
                'AVG(satisfaction) as avg_satisfaction',
                'SUM(CASE WHEN satisfaction >= 4 THEN 1 ELSE 0 END) as good_count'
            ])
            ->group('period')
            ->order('period', 'asc')
            ->select();
        
        $result = [];
        foreach ($trend as $item) {
            $totalSessions = (int)$item['total_sessions'];
            $result[] = [
                'period' => $item['period'],
                'total_sessions' => $totalSessions,
                'avg_satisfaction' => round((float)$item['avg_satisfaction'], 2),
                'good_rate' => $totalSessions > 0 ? round(((int)$item['good_count'] / $totalSessions) * 100, 2) : 0
            ];
        }
        
        return $result;
    }

    /**
     * 获取空统计数据
     * @return array
     */
    private function getEmptyStats(): array
    {
        return [
            'total_sessions' => 0,
            'avg_satisfaction' => 0,
            'good_rate' => 0,
            'bad_rate' => 0,
            'score_distribution' => [
                '1' => 0,
                '2' => 0,
                '3' => 0,
                '4' => 0,
                '5' => 0
            ]
        ];
    }

    // ========== 预留的反馈分析和定时任务相关方法 ==========
    
    /**
     * 预留：反馈文本分析接口
     * 用于分析用户反馈内容，提取关键词、情感分析等
     * @return \think\Response
     */
    public function analyzeFeedbackText()
    {
        try {
            $serviceId = Request::param('service_id/d', null);
            $startTime = Request::param('start_time');
            $endTime = Request::param('end_time');
            $groupId = Request::param('group_id/d', null);
            $limit = Request::param('limit/d', 100);

            $condition = [];
            $condition[] = ['feedback', '<>', ''];
            $condition[] = ['feedback', 'not null'];

            // 处理客服ID筛选
            if ($serviceId !== null) {
                $condition[] = ['service_id', '=', $serviceId];
            }

            // 处理时间范围
            if ($startTime && $endTime) {
                $condition[] = ['end_time', 'between', [strtotime($startTime), strtotime($endTime)]];
            }

            // 处理客服组筛选
            if ($groupId !== null && !$this->isAdmin()) {
                $serviceIds = $this->serviceModel->where('group_id', $groupId)->column('id');
                if (empty($serviceIds)) {
                    return $this->success([
                        'total_feedback' => 0,
                        'keyword_stats' => [],
                        'sentiment_stats' => [],
                        'feedback_list' => []
                    ]);
                }
                $condition[] = ['service_id', 'in', $serviceIds];
            }

            // 获取反馈数据
            $feedbackData = $this->sessionModel
                ->field('id,service_id,satisfaction,feedback,end_time')
                ->where($condition)
                ->order('end_time desc')
                ->limit($limit)
                ->select()
                ->toArray();

            $totalFeedback = count($feedbackData);

            // 关键词统计
            $keywordStats = $this->extractKeywords($feedbackData);

            // 情感分析统计
            $sentimentStats = $this->analyzeSentiment($feedbackData);

            // 反馈列表（带客服信息）
            $serviceIds = array_unique(array_column($feedbackData, 'service_id'));
            $services = $this->serviceModel->whereIn('id', $serviceIds)->column('nickname', 'id');

            $feedbackList = array_map(function($item) use ($services) {
                return [
                    'id' => $item['id'],
                    'service_name' => $services[$item['service_id']] ?? '未知客服',
                    'satisfaction' => $item['satisfaction'],
                    'feedback' => $item['feedback'],
                    'end_time' => date('Y-m-d H:i:s', $item['end_time']),
                    'sentiment' => $this->getSentimentLabel($item['feedback'])
                ];
            }, $feedbackData);

            return $this->success([
                'total_feedback' => $totalFeedback,
                'keyword_stats' => $keywordStats,
                'sentiment_stats' => $sentimentStats,
                'feedback_list' => $feedbackList
            ]);

        } catch (\Exception $e) {
            Log::error('反馈文本分析失败: ' . $e->getMessage());
            return $this->fail('反馈文本分析失败');
        }
    }

    /**
     * 提取关键词
     * @param array $feedbackData
     * @return array
     */
    private function extractKeywords($feedbackData)
    {
        $keywords = [];
        $positiveWords = ['好', '满意', '不错', '棒', '优秀', '专业', '耐心', '及时', '快速', '解决'];
        $negativeWords = ['差', '不满意', '慢', '态度', '问题', '等待', '久', '烦', '麻烦', '失望'];
        
        foreach ($feedbackData as $item) {
            $feedback = $item['feedback'];
            
            // 统计正面关键词
            foreach ($positiveWords as $word) {
                if (strpos($feedback, $word) !== false) {
                    $keywords['positive'][$word] = ($keywords['positive'][$word] ?? 0) + 1;
                }
            }
            
            // 统计负面关键词
            foreach ($negativeWords as $word) {
                if (strpos($feedback, $word) !== false) {
                    $keywords['negative'][$word] = ($keywords['negative'][$word] ?? 0) + 1;
                }
            }
        }
        
        // 排序并取前10
        if (isset($keywords['positive'])) {
            arsort($keywords['positive']);
            $keywords['positive'] = array_slice($keywords['positive'], 0, 10, true);
        }
        if (isset($keywords['negative'])) {
            arsort($keywords['negative']);
            $keywords['negative'] = array_slice($keywords['negative'], 0, 10, true);
        }
        
        return $keywords;
    }

    /**
     * 情感分析
     * @param array $feedbackData
     * @return array
     */
    private function analyzeSentiment($feedbackData)
    {
        $sentimentStats = [
            'positive' => 0,
            'negative' => 0,
            'neutral' => 0
        ];
        
        foreach ($feedbackData as $item) {
            $sentiment = $this->getSentimentLabel($item['feedback']);
            $sentimentStats[$sentiment]++;
        }
        
        return $sentimentStats;
    }

    /**
     * 获取情感标签
     * @param string $feedback
     * @return string
     */
    private function getSentimentLabel($feedback)
    {
        $positiveWords = ['好', '满意', '不错', '棒', '优秀', '专业', '耐心', '及时', '快速', '解决', '谢谢', '感谢'];
        $negativeWords = ['差', '不满意', '慢', '态度', '问题', '等待', '久', '烦', '麻烦', '失望', '糟糕', '垃圾'];
        
        $positiveCount = 0;
        $negativeCount = 0;
        
        foreach ($positiveWords as $word) {
            if (strpos($feedback, $word) !== false) {
                $positiveCount++;
            }
        }
        
        foreach ($negativeWords as $word) {
            if (strpos($feedback, $word) !== false) {
                $negativeCount++;
            }
        }
        
        if ($positiveCount > $negativeCount) {
            return 'positive';
        } elseif ($negativeCount > $positiveCount) {
            return 'negative';
        } else {
            return 'neutral';
        }
    }
    
    /**
     * 注意：定时任务相关方法已迁移到 SatisfactionTaskController
     * - generateDailyReport() -> SatisfactionTaskController::generateDailyReport()
     * - generateMonthlyReport() -> SatisfactionTaskController::generateMonthlyReport()
     * - checkSatisfactionAlert() -> SatisfactionTaskController::checkSatisfactionAlert()
     */
}