<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\ChatMessage;
use app\model\ChatSession;
use app\model\CustomerService;
use app\model\system\User;
use app\vchat\core\MessageProtocol;
use app\admin\entity\UserEntity;
use think\App;
use think\facade\Log;
use think\facade\Request;

class ServiceSessionController extends BaseAdminController
{
    protected $sessionModel;
    protected $serviceModel;
    protected $messageModel;

    protected $filterParams = [
        'create_time'
    ];

    protected $buildParams = [
        'service_id',
        'group_id',
        'work_id',
        'enabled',
        'user_id'
    ];

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->sessionModel = new ChatSession();
        $this->serviceModel = new CustomerService();
        $this->messageModel = new ChatMessage();
        $this->model = $this->sessionModel;
    }

    /**
     * 初始化方法
     */
    protected function initialize()
    {
        parent::initialize();
    }

    /**
     * 列表查询前的钩子方法 - 根据当前管理员ID筛选会话记录
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        try {
            // 获取当前登录的管理员信息
            $userEntity = app(UserEntity::class);
            $adminId = $userEntity->id;
            
            if (!$adminId) {
                $this->fail('无法获取当前管理员信息');
            }

            // 添加管理员ID筛选条件
            $where[] = ['service_id', '=', $adminId];

            // 处理时间范围参数
            $createTime = Request::param('create_time/a', []);
            if (count($createTime) >= 2) {
                $startTime = strtotime($createTime[0] . ' 00:00:00');
                $endTime = strtotime($createTime[1] . ' 23:59:59');
                $where[] = ['start_time', '>', $startTime];
                $where[] = ['end_time', '<', $endTime];
            }

            // 设置默认排序为创建时间倒序
            if (empty($sort)) {
                $sort = ['create_time' => 'desc'];
            }
            
        } catch (\Exception $e) {
            Log::error('设置管理员会话筛选条件失败: ' . $e->getMessage());
            $this->fail('设置管理员会话筛选条件失败: ' . $e->getMessage());
        }
    }

    /**
     * 列表查询后的钩子方法 - 格式化会话数据
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        try {
            $sessionItems = $list->items();
            if (empty($sessionItems)) {
                return [
                    'record' => [],
                    'count' => $list->total()
                ];
            }

            // 收集所有需要的ID
            $sessionIds = [];
            $serviceIds = [];
            $userIds = [];
            foreach ($sessionItems as $session) {
                $sessionIds[] = $session['id'];
                $serviceIds[] = $session['service_id'];
                $userIds[] = $session['user_id'];
            }
            $serviceIds = array_unique($serviceIds);
            $userIds = array_unique($userIds);

            // 批量查询客服信息
            $services = [];
            if (!empty($serviceIds)) {
                $serviceList = $this->serviceModel
                    ->where('id', 'in', $serviceIds)
                    ->field('id,name,avatar')
                    ->select()
                    ->toArray();
                foreach ($serviceList as $service) {
                    $services[$service['id']] = $service;
                }
            }

            // 批量查询用户信息
            $users = [];
            if (!empty($userIds)) {
                $userModel = new User();
                $userList = $userModel
                    ->where('id', 'in', $userIds)
                    ->field('username,nickname,mobile,head_img,login_ip,mail,id')
                    ->select()
                    ->toArray();
                foreach ($userList as $user) {
                    $users[$user['id']] = $user;
                }
            }

            // 批量查询消息数量
            $messageCounts = [];
            if (!empty($sessionIds)) {
                $countResults = $this->messageModel
                    ->where('session_id', 'in', $sessionIds)
                    ->field('session_id, COUNT(*) as count')
                    ->group('session_id')
                    ->select()
                    ->toArray();
                foreach ($countResults as $result) {
                    $messageCounts[$result['session_id']] = $result['count'];
                }
            }

            // 批量查询最后一条消息
            $lastMessages = [];
            if (!empty($sessionIds)) {
                // 使用子查询获取每个会话的最后一条消息
                $lastMessageResults = $this->messageModel
                    ->alias('m1')
                    ->field('m1.session_id, m1.content, m1.createtime')
                    ->where('m1.session_id', 'in', $sessionIds)
                    ->where('m1.id', 'in', function($query) use ($sessionIds) {
                        $query->name('ad_chat_message')
                            ->alias('m2')
                            ->field('MAX(id)')
                            ->where('session_id', 'in', $sessionIds)
                            ->group('session_id');
                    })
                    ->select()
                    ->toArray();
                foreach ($lastMessageResults as $message) {
                    $lastMessages[$message['session_id']] = $message;
                }
            }

            // 组装数据
            $sessions = [];
            foreach ($sessionItems as $session) {
                $sessionData = $session->toArray();

                // 设置客服信息
                $serviceId = $session['service_id'];
                if (isset($services[$serviceId])) {
                    $sessionData['service_name'] = $services[$serviceId]['name'];
                    $sessionData['service_avatar'] = $services[$serviceId]['avatar'];
                } else {
                    $sessionData['service_name'] = '未知客服';
                    $sessionData['service_avatar'] = '';
                }

                // 设置用户信息
                $userId = $session['user_id'];
                if (isset($users[$userId])) {
                    $sessionData['user_name'] = $users[$userId]['username'];
                    $sessionData['user_nickname'] = $users[$userId]['nickname'];
                    $sessionData['user_mobile'] = $users[$userId]['mobile'];
                    $sessionData['user_avatar'] = $users[$userId]['head_img'];
                } else {
                    $sessionData['user_name'] = '未知用户';
                    $sessionData['user_nickname'] = '';
                    $sessionData['user_mobile'] = '';
                    $sessionData['user_avatar'] = '';
                }

                $sessionData['start_time'] = date('Y-m-d H:i:s', $session['start_time']);
                $sessionData['end_time'] = date('Y-m-d H:i:s', $session['end_time']);
                
                // 设置消息数量
                $sessionData['message_count'] = $messageCounts[$session['id']] ?? 0;
                
                // 设置最后一条消息
                if (isset($lastMessages[$session['id']])) {
                    $lastMessage = $lastMessages[$session['id']];
                    $sessionData['last_message'] = $lastMessage['content'];
                    $sessionData['last_message_time'] = $lastMessage['createtime'];
                } else {
                    $sessionData['last_message'] = '';
                    $sessionData['last_message_time'] = '';
                }
                
                $sessions[] = $sessionData;
            }

            return [
                'record' => $sessions,
                'count' => $list->total()
            ];
            
        } catch (\Exception $e) {
            Log::error('格式化会话数据失败: ' . $e->getMessage());
            return null; // 返回null使用默认格式
        }
    }

    /**
     * 获取客服当前会话列表
     */
    public function getServiceSessions()
    {
        try {
            $serviceId = Request::param('service_id/d');
            if (!$serviceId) {
                $this->fail('缺少客服ID参数');
            }

            $limit = Request::param('limit/d', 1000);
            $lastId = Request::param('last_id/d', null);

            $sessions = $this->sessionModel->getServiceActiveSessions($serviceId, $limit, $lastId);

            $sessions = array_map(function($sessions) {
                $sessions['last_message_time'] = date('Y-m-d H:i:s', $sessions['last_message_time']);
                return $sessions;
            }, $sessions);

        } catch (\Exception $e) {
            Log::error('获取客服会话列表失败：' . $e->getMessage());
            $this->fail('获取客服会话列表失败');
        }

        $this->ok('success', $sessions);
    }

    /**
     * 获取会话历史消息
     */
    public function getSessionMessages()
    {
        try {
            $sessionId = Request::param('session_id/d');
            $limit = Request::param('limit/d', 20);
            $lastId = Request::param('last_id/d', null);

            if (!$sessionId) {
                $this->fail('缺少会话ID参数');
            }

            // 验证会话是否属于当前用户
            $userId = 1;
            $session = $this->sessionModel->where([
                ['id', '=', $sessionId],
                ['user_id', '=', $userId]
            ])->find();

            if (!$session) {
                $this->fail('会话不存在或无权限访问');
            }

            $messages = $this->messageModel->getSessionMessages($sessionId, $limit, $lastId);
            // 添加direction字段
            $messages = array_map(function($message) {
                $message['direction'] = $message['from_type'] === 'service' ? MessageProtocol::DIRECTION_SEND : MessageProtocol::DIRECTION_RECEIVE;
                $message['read'] = $message['status'] == 3 ? true : false;
                return $message;
            }, $messages);

        } catch (\Exception $e) {
            dump($e);die;
            Log::error('获取会话消息失败：' . $e->getMessage());
            $this->fail('获取会话消息失败');
        }

        $this->ok('success', $messages);

    }

    /**
     * 转接会话
     */
    public function transferSession()
    {
        try {
            $sessionId = Request::param('session_id/d');
            $newServiceId = Request::param('new_service_id/d');

            if (!$sessionId || !$newServiceId) {
                $this->fail('缺少必要参数');
            }

            if (!$this->serviceModel->canAcceptNewSession($newServiceId)) {
                $this->fail('目标客服当前无法接收新会话');
            }

            $result = $this->sessionModel->transferSession($sessionId, $newServiceId);
            if (!$result) {
                $this->fail('转接会话失败');
            }

        } catch (\Exception $e) {
            Log::error('转接会话失败：' . $e->getMessage());
            $this->fail('转接会话失败');
        }
        $this->ok('success');
    }

    /**
     * 结束会话
     */
    public function endSession()
    {
        try {
            $sessionId = Request::param('session_id/d');
            if (!$sessionId) {
                $this->fail('缺少会话ID参数');
            }

            $result = $this->sessionModel->endSession($sessionId);
            if (!$result) {
                $this->fail('结束会话失败');
            }

        } catch (\Exception $e) {
            Log::error('结束会话失败：' . $e->getMessage());
            $this->fail('结束会话失败');
        }

        $this->ok('success');
    }

    /**
     * 更新会话满意度
     */
    public function updateSatisfaction()
    {
        try {
            $sessionId = Request::param('session_id/d');
            $satisfaction = Request::param('satisfaction/d');

            if (!$sessionId || !$satisfaction) {
                $this->fail('缺少必要参数');
            }

            $result = $this->sessionModel->updateSatisfaction($sessionId, $satisfaction);
            if (!$result) {
                $this->fail('更新满意度失败');
            }

        } catch (\Exception $e) {
            Log::error('更新满意度失败：' . $e->getMessage());
            $this->fail('更新满意度失败');
        }

        $this->ok('success');
    }
}