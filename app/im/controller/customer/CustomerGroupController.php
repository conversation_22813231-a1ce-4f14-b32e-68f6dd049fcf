<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\CustomerGroup;
use think\App;

/**
 * 客服分组管理控制器
 */
class CustomerGroupController extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'name|分组名称' => 'require|max:50',
        'description|分组描述' => 'max:255',
        'status|状态' => 'in:0,1',
        'sort|排序' => 'number|egt:0'
    ];

    /**
     * 允许修改的字段
     * @var array
     */
    protected $allowFields = [
        'name',
        'description',
        'status',
        'sort'
    ];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new CustomerGroup();
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit') {
            return [
                'name|分组名称' => 'max:50',
                'description|分组描述' => 'max:255',
                'status|状态' => 'in:0,1',
                'sort|排序' => 'number|egt:0'
            ];
        }
        
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 检查分组名称是否已存在
        if (!empty($data['name'])) {
            $exists = $this->model->where('name', $data['name'])->find();
            if ($exists) {
                $this->error('分组名称已存在');
            }
        }

        // 设置默认值
        if (!isset($data['status'])) {
            $data['status'] = 1;
        }
        if (!isset($data['sort'])) {
            $data['sort'] = 0;
        }
        
        return $data;
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 检查分组名称是否已存在（排除当前记录）
        if (!empty($data['name'])) {
            $exists = $this->model->where('name', $data['name'])
                                 ->where('id', '<>', $id)
                                 ->find();
            if ($exists) {
                $this->error('分组名称已存在');
            }
        }
        
        return $data;
    }

    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 支持按分组名称搜索
        $name = $this->request->param('name', '');
        if (!empty($name)) {
            $where[] = ['name', 'like', "%{$name}%"];
        }

        // 支持按状态筛选
        $status = $this->request->param('status', '');
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }

        // 默认按排序和ID倒序
        $sort = ['sort' => 'asc', 'id' => 'desc'];
    }

    /**
     * 获取所有客服分组列表
     * @return \think\Response
     */
    public function getAllGroups()
    {
        try {
            // 获取所有启用的客服分组
            $groups = $this->model
                ->where('enabled', 1)
                ->order('sort', 'asc') // 按排序字段升序排列
                ->select();
                
        } catch (\Exception $e) {
            $this->fail('获取分组列表失败');
        }

        $this->ok('success', $groups);

    }
}