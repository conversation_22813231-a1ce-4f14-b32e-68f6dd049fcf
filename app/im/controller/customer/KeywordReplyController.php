<?php

declare(strict_types=1);

namespace app\im\controller\customer;

use app\admin\entity\UserEntity;
use app\common\controller\BaseAdminController;
use app\model\AutoReplyRule;
use think\App;

/**
 * 关键词回复控制器
 */
class KeywordReplyController extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'keyword|关键词' => 'require|max:255',
        'replyContent|回复内容' => 'require|max:1000',
        'enabled|启用状态' => 'in:0,1',
        'matchType|匹配类型' => 'in:0,1,2',
        'priority|优先级' => 'number|egt:0'
    ];

    /**
     * 允许修改的字段
     * @var array
     */
    protected $allowFields = [
        'keyword',
        'replyContent',
        'enabled',
        'matchType',
        'priority'
    ];

    protected $filterParams = [
        'matchType'
    ];

    private $userEntity;

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app, UserEntity $userEntity)
    {
        parent::__construct($app);
        $this->model = new AutoReplyRule();
        $this->userEntity = $userEntity;
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit') {
            return [
                'keyword|关键词' => 'max:255',
                'replyContent|回复内容' => 'max:1000',
                'enabled|启用状态' => 'in:0,1',
                'matchType|匹配类型' => 'in:0,1,2',
                'priority|优先级' => 'number|egt:0'
            ];
        }
        
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 处理关键词字段
        if (isset($data['keyword'])) {
            $data['keyword'] = $this->formatArrData($data['keyword']);
        }

        // 检查关键词是否已存在
        if (!empty($data['keyword'])) {
            $exists = $this->model->where('keyword', $data['keyword'])->find();
            if ($exists) {
                $this->error('关键词已存在');
            }
        }

        // 设置默认值
        if (!isset($data['enabled'])) {
            $data['enabled'] = 1;
        }

        $data['user_id'] = $this->userEntity->id;
        
        // 格式化字段名
        return $this->formatToDatabase($data);
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 处理关键词字段
        if (isset($data['keyword'])) {
            $data['keyword'] = $this->formatArrData($data['keyword']);
        }

        // 检查关键词是否已存在（排除当前记录）
        if (!empty($data['keyword'])) {
            $exists = $this->model->where('keyword', $data['keyword'])
                                 ->where('id', '<>', $id)
                                 ->find();
            if ($exists) {
                $this->error('关键词已存在');
            }
        }
        
        // 格式化字段名
        return $this->formatToDatabase($data);
    }

    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 支持按关键词搜索
        $keyword = $this->request->param('keyword', '');
        if (!empty($keyword)) {
            $where[] = ['keyword', 'like', "%{$keyword}%"];
        }

        $matchType = $this->request->param('matchType', '');
        if (!empty($matchType)) {
            if (in_array($matchType, [1,2,3])) {
                $where[] = ['match_type', '=', $matchType];
            }
        }

        // 支持按内容搜索
        $content = $this->request->param('content', '');
        if (!empty($content)) {
            $where[] = ['content', 'like', "%{$content}%"];
        }

        // 支持按启用状态筛选
        $enabled = $this->request->param('enabled', '');
        if ($enabled !== '') {
            $where[] = ['enabled', '=', $enabled];
        }
    }

    /**
     * 获取关键词回复列表（兼容旧接口）
     */
    public function getList()
    {
        $list = $this->index();
        
        if (isset($list['data'])) {
            foreach ($list['data'] as &$item) {
                $item = $this->formatFromDatabase($item);
            }
        }
        
        return $list;
    }
}