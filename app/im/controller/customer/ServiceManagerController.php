<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\CustomerService;
use app\model\system\Admin;
use think\App;
use think\facade\Log;
use think\facade\Request;

class ServiceManagerController extends BaseAdminController
{
    protected $serviceModel;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->serviceModel = new CustomerService();
    }

    /**
     * 获取在线客服列表
     */
    public function getOnlineServices()
    {
        try {
            $groupId = Request::param('group_id/d', null);
            $services = $this->serviceModel->getOnlineServices($groupId);

        } catch (\Exception $e) {
            Log::error('获取在线客服列表失败：' . $e->getMessage());
            $this->fail('获取在线客服列表失败');
        }

        $this->ok('success', $services);
    }

    public function getOnlineServicesByGroup()
    {
        try {
            $services = $this->serviceModel->getOnlineServicesByGroup();

        } catch (\Exception $e) {
            Log::error('获取在线客服列表失败：' . $e->getMessage());
            $this->fail('获取在线客服列表失败');
        }

        $this->ok('success', $services);
    }

    /**
     * 获取当前用户的客服ID
     */
    public function getCurrentServiceId()
    {
        try {
            $adminId = 1;
            $admin = Admin::find($adminId);
            if (!$admin) {
                $this->fail('管理员信息不存在');
            }

            $service = CustomerService::where('admin_id', $adminId)->find();
            
            if (!$service) {
                $this->fail('请联系管理员开通客服账号');
//                $service = new CustomerService([
//                    'admin_id' => $adminId,
//                    'name' => $admin->username,
//                    'status' => 1,
//                    'max_sessions' => 5,
//                    'auto_reply' => 0,
//                    'group_id' => 1,
//                ]);
//
//                if (!$service->save()) {
//                    $this->fail('创建客服信息失败');
//                }
            }

        } catch (\Exception $e) {
            Log::error('获取客服ID失败：' . $e->getMessage());
            $this->fail('获取客服ID失败');
        }

        $this->ok('success', [
            'service_id' => $service->id,
            'name' => $service->name,
            'status' => $service->status,
            'max_sessions' => $service->max_sessions,
            'auto_reply' => $service->auto_reply,
            'group_id' => $service->group_id
        ]);
    }
}