<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\CustomerService;
use app\service\CustomerService as CustomerServiceService;
use think\App;

class CustomerServiceController extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'admin_id|管理员ID' => 'require|number',
        'name|客服名称' => 'require|max:50',
        'avatar|头像' => 'max:255',
        'status|状态' => 'in:0,1',
        'max_sessions|最大会话数' => 'number|egt:0',
        'auto_reply|自动回复' => 'max:1000',
        'group_id|分组ID' => 'number'
    ];

    /**
     * 允许修改的字段
     * @var array
     */
    protected $allowFields = [
        'admin_id',
        'name',
        'avatar',
        'status',
        'max_sessions',
        'auto_reply',
        'group_id'
    ];

    protected $service;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new CustomerService();
        $this->service = new CustomerServiceService();
    }

    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        $groupId = $this->request->param('group_id');
        if ($groupId !== null) {
            $where[] = ['group_id', '=', $groupId];
        }

        $status = $this->request->param('status');
        if ($status !== null) {
            $where[] = ['status', '=', $status];
        }

        $sort = ['createtime' => 'desc'];
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 检查管理员ID是否已存在
        if (!empty($data['admin_id'])) {
            $exists = $this->model->where('admin_id', $data['admin_id'])->find();
            if ($exists) {
                $this->error('该管理员已是客服');
            }
        }

        return $data;
    }

    /**
     * 编辑前的钩子方法
     * @param array $data 表单数据
     * @param int $id 记录ID
     * @return array|null
     */
    protected function beforeEdit($data, $id)
    {
        // 检查管理员ID是否已存在（排除当前记录）
        if (!empty($data['admin_id'])) {
            $exists = $this->model->where('admin_id', $data['admin_id'])
                                 ->where('id', '<>', $id)
                                 ->find();
            if ($exists) {
                $this->error('该管理员已是客服');
            }
        }

        return $data;
    }

    /**
     * 获取客服统计信息
     */
    public function stats()
    {
        $id = $this->request->param('id');
        $startTime = $this->request->param('start_time');
        $endTime = $this->request->param('end_time');
        
        $stats = $this->service->getServiceStatistics($id, $startTime, $endTime);
        
        $this->success('获取成功', $stats);
    }
}