<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\ChatMessage;
use app\model\ChatSession;
use app\model\CustomerService;
use app\model\system\User;
use think\App;
use think\facade\Db;
use think\facade\Request;
use think\facade\Log;

/**
 * 客服消息管理控制器
 * 提供后台管理客服消息数据的接口
 */
class CustomerMessageController extends BaseAdminController
{
    /**
     * 消息模型
     * @var ChatMessage
     */
    protected $messageModel;
    
    /**
     * 会话模型
     * @var ChatSession
     */
    protected $sessionModel;
    
    /**
     * 客服模型
     * @var CustomerService
     */
    protected $serviceModel;
    
    /**
     * 用户模型
     * @var User
     */
    protected $userModel;
    
    /**
     * 过滤参数
     * @var array
     */
    protected $filterParams = [
        'create_time',
        'content_type',
        'from_type'
    ];
    
    /**
     * 构建参数
     * @var array
     */
    protected $buildParams = [
        'session_id',
        'from_id',
        'to_id',
        'content_type',
        'from_type',
        'to_type',
        'read_status'
    ];
    
    /**
     * 构造方法
     * @param App $app 应用对象
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->messageModel = new ChatMessage();
        $this->sessionModel = new ChatSession();
        $this->serviceModel = new CustomerService();
        $this->userModel = new User();
        $this->model = $this->messageModel;
    }
    
    /**
     * 初始化方法
     */
    protected function initialize()
    {
        parent::initialize();
    }
    
    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 处理时间范围参数
        $createTime = Request::param('create_time/a', []);
        if (count($createTime) >= 2) {
            $startTime = strtotime($createTime[0] . ' 00:00:00');
            $endTime = strtotime($createTime[1] . ' 23:59:59');
            $where[] = ['createtime', '>=', $startTime];
            $where[] = ['createtime', '<=', $endTime];
        }
        
        // 处理内容类型筛选
        $contentType = Request::param('content_type');
        if (!empty($contentType)) {
            $where[] = ['content_type', '=', $contentType];
        }
        
        // 处理发送者类型筛选
        $fromType = Request::param('from_type');
        if (!empty($fromType)) {
            $where[] = ['from_type', '=', $fromType];
        }
        
        // 处理会话ID筛选
        $sessionId = Request::param('session_id/d');
        if (!empty($sessionId)) {
            $where[] = ['session_id', '=', $sessionId];
        }
        
        // 设置默认排序为创建时间倒序
        if (empty($sort)) {
            $sort = ['createtime' => 'desc'];
        }
    }
    
    /**
     * 列表查询后的钩子方法
     * @param \think\Paginator $list 分页对象
     * @return array|null
     */
    protected function afterIndex($list)
    {
        $messages = $list->items();
        if (empty($messages)) {
            return ['record' => [], 'count' => 0];
        }
        
        // 提取所有客服ID和用户ID
        $serviceIds = [];
        $userIds = [];
        $sessionIds = [];
        
        foreach ($messages as $message) {
            $sessionIds[] = $message['session_id'];
            
            if ($message['from_type'] == 'service') {
                $serviceIds[] = $message['from_id'];
            } else {
                $userIds[] = $message['from_id'];
            }
            
            if ($message['to_type'] == 'service') {
                $serviceIds[] = $message['to_id'];
            } else {
                $userIds[] = $message['to_id'];
            }
        }
        
        // 去重
        $serviceIds = array_unique($serviceIds);
        $userIds = array_unique($userIds);
        $sessionIds = array_unique($sessionIds);
        
        // 批量查询客服信息
        $services = [];
        if (!empty($serviceIds)) {
            $serviceList = $this->serviceModel->whereIn('id', $serviceIds)->select();
            foreach ($serviceList as $service) {
                $services[$service['id']] = $service;
            }
        }
        
        // 批量查询用户信息
        $users = [];
        if (!empty($userIds)) {
            $userList = $this->userModel->whereIn('id', $userIds)->select();
            foreach ($userList as $user) {
                $users[$user['id']] = $user;
            }
        }
        
        // 批量查询会话信息
        $sessions = [];
        if (!empty($sessionIds)) {
            $sessionList = $this->sessionModel->whereIn('id', $sessionIds)->select();
            foreach ($sessionList as $session) {
                $sessions[$session['id']] = $session;
            }
        }
        
        // 组装数据
        $records = [];
        foreach ($messages as $message) {
            // 添加发送者信息
            if ($message['from_type'] == 'service' && isset($services[$message['from_id']])) {
                $message['sender'] = $services[$message['from_id']];
            } elseif ($message['from_type'] == 'user' && isset($users[$message['from_id']])) {
                $message['sender'] = $users[$message['from_id']];
            } else {
                $message['sender'] = null;
            }
            
            // 添加接收者信息
            if ($message['to_type'] == 'service' && isset($services[$message['to_id']])) {
                $message['receiver'] = $services[$message['to_id']];
            } elseif ($message['to_type'] == 'user' && isset($users[$message['to_id']])) {
                $message['receiver'] = $users[$message['to_id']];
            } else {
                $message['receiver'] = null;
            }
            
            // 添加会话信息
            $message['session'] = $sessions[$message['session_id']] ?? null;
            
            $records[] = $message;
        }
        
        return ['record' => $records, 'count' => $list->total()];
    }
    
    /**
     * 获取消息详情
     * @param int $id 消息ID
     * @return \think\Response
     */
    public function read($id)
    {
        $message = $this->messageModel->find($id);
        if (!$message) {
            $this->fail('消息不存在');
        }
        
        // 获取发送者信息
        if ($message['from_type'] == 'service') {
            $message['sender'] = $this->serviceModel->find($message['from_id']);
        } else {
            $message['sender'] = $this->userModel->find($message['from_id']);
        }
        
        // 获取接收者信息
        if ($message['to_type'] == 'service') {
            $message['receiver'] = $this->serviceModel->find($message['to_id']);
        } else {
            $message['receiver'] = $this->userModel->find($message['to_id']);
        }
        
        // 获取会话信息
        $message['session'] = $this->sessionModel->find($message['session_id']);
        
        $this->ok('获取成功', $message);
    }
    
    /**
     * 获取会话消息列表
     * @return \think\Response
     */
    public function sessionMessages()
    {
        $sessionId = Request::param('session_id/d');
        if (empty($sessionId)) {
            $this->fail('会话ID不能为空');
        }
        
        $limit = Request::param('limit/d', 20);
        $page = Request::param('page/d', 1);
        
        $messages = $this->messageModel->with(['sender', 'receiver'])
            ->where('session_id', $sessionId)
            ->order('createtime', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page,
            ]);
        
        $this->ok('获取成功', [
            'record' => $messages->items(),
            'count' => $messages->total()
        ]);
    }
    
    /**
     * 获取消息统计数据
     * @return \think\Response
     */
    public function statistics()
    {
        // 获取时间范围
        $createTime = Request::param('create_time/a', []);
        $where = [];
        
        if (count($createTime) >= 2) {
            $startTime = strtotime($createTime[0] . ' 00:00:00');
            $endTime = strtotime($createTime[1] . ' 23:59:59');
            $where[] = ['createtime', '>=', $startTime];
            $where[] = ['createtime', '<=', $endTime];
        }
        
        // 获取消息统计数据
        $stats = $this->messageModel->getMessageStats($where);
        
        // 获取各类型消息占比
        $typeStats = [];
        $totalMessages = $stats['total_messages'] ?: 1; // 避免除以零
        
        foreach ($stats['type_stats'] as $typeStat) {
            $typeStats[] = [
                'type' => $typeStat['content_type'],
                'count' => $typeStat['count'],
                'percentage' => round(($typeStat['count'] / $totalMessages) * 100, 2)
            ];
        }
        
        $stats['type_stats'] = $typeStats;
        
        // 获取平均响应时间（秒）
        $stats['avg_response_time'] = round($stats['avg_response_time'] ?: 0, 2);
        
        $this->ok('获取成功', $stats);
    }
    
    /**
     * 标记消息为已读
     * @return \think\Response
     */
    public function markAsRead()
    {
        $messageId = Request::param('id/d');
        if (empty($messageId)) {
            $this->fail('消息ID不能为空');
        }
        
        $result = $this->messageModel->where('id', $messageId)->update(['read_status' => 1]);
        
        if ($result !== false) {
            $this->ok('标记成功');
        } else {
            $this->fail('标记失败');
        }
    }
    
    /**
     * 批量标记消息为已读
     * @return \think\Response
     */
    public function batchMarkAsRead()
    {
        $sessionId = Request::param('session_id/d');
        $toId = Request::param('to_id/d');
        
        if (empty($sessionId) || empty($toId)) {
            $this->fail('参数不完整');
        }
        
        $where = [
            ['session_id', '=', $sessionId],
            ['to_id', '=', $toId],
            ['read_status', '=', 0]
        ];
        
        $result = $this->messageModel->where($where)->update(['read_status' => 1]);
        
        if ($result !== false) {
            $this->ok('标记成功', ['affected_rows' => $result]);
        } else {
            $this->fail('标记失败');
        }
    }
}