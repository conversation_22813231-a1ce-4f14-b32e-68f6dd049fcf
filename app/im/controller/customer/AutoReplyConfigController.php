<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\admin\controller\config\BaseConfigController;
use think\App;

class AutoReplyConfigController extends BaseConfigController
{
    protected function initConfig(): void
    {
        $this->configPath = config_path();
        $this->configFile = 'auto_reply';
        $this->configKey = 'auto_reply';
    }

    protected function validateConfig(array $config): void
    {
        // 验证自动回复策略
        if (isset($config['strategies'])) {
            if (!is_array($config['strategies']) || empty($config['strategies'])) {
                throw new \Exception('自动回复策略不能为空');
            }
            
            foreach ($config['strategies'] as $strategy) {
                if (!class_exists($strategy)) {
                    throw new \Exception(sprintf('自动回复策略类 %s 不存在', $strategy));
                }
            }
        }

        // 验证AI自动回复配置
        if (isset($config['ai'])) {
            $ai = $config['ai'];
            if (!isset($ai['enabled']) || !is_bool($ai['enabled'])) {
                throw new \Exception('AI自动回复启用状态必须设置');
            }

            if (isset($ai['triggers'])) {
                $triggers = $ai['triggers'];
                if (!isset($triggers['min_length']) || $triggers['min_length'] < 0) {
                    throw new \Exception('最小触发长度不能为负数');
                }
                if (!isset($triggers['max_length']) || $triggers['max_length'] <= $triggers['min_length']) {
                    throw new \Exception('最大触发长度必须大于最小触发长度');
                }
            }

            if (isset($ai['context'])) {
                if (!isset($ai['context']['max_history']) || $ai['context']['max_history'] <= 0) {
                    throw new \Exception('最大历史记录数必须大于0');
                }
            }
        }
    }

    protected function transformConfig(array $data): array
    {
        // 设置默认值
        $defaults = [
            'enabled' => true,
            'debug' => false,
            'strategies' => [
                '\\app\\vchat\\auto_reply\\WelcomeReply',
                '\\app\\vchat\\auto_reply\\DatabaseReply',
                '\\app\\vchat\\auto_reply\\AiAutoReply',
                '\\app\\vchat\\auto_reply\\RobotReply',
                '\\app\\vchat\\auto_reply\\HelpReply'
            ],
            'ai' => [
                'enabled' => true,
                'provider' => 'deepseek',
                'model' => 'deepseek-chat',
                'temperature' => 0.7,
                'max_tokens' => 500,
                'timeout' => 10,
                'cache_ttl' => 300,
                'triggers' => [
                    'keywords' => [],
                    'patterns' => [],
                    'always_reply' => false,
                    'min_length' => 3,
                    'max_length' => 1000
                ],
                'context' => [
                    'use_memory' => true,
                    'session_prefix' => 'ai_auto_reply_',
                    'max_history' => 10,
                    'clear_after' => 3600
                ],
                'prompts' => [
                    'system' => '你是一个友好的智能客服助手，请用简洁、专业、温暖的语言回复用户的问题。回复长度控制在100字以内，语气要亲切自然。',
                    'welcome' => '你好！我是AI智能助手，有什么可以帮助您的吗？😊',
                    'error' => '抱歉，我暂时无法理解您的问题，请稍后再试或联系人工客服。',
                    'busy' => '系统繁忙，请稍后再试。'
                ],
                'filters' => [
                    'blocked_keywords' => [],
                    'blocked_patterns' => [],
                    'min_interval' => 5
                ],
                'advanced' => [
                    'emotion_analysis' => false,
                    'intent_recognition' => false,
                    'multi_turn' => true,
                    'quality_threshold' => 0.7
                ]
            ]
        ];

        return array_merge_recursive($defaults, $data);
    }

    protected function processConfig(array $config, array $data): array
    {
        // 深度合并配置
        $result = array_replace_recursive($config, $data);
        
        // 特殊处理数组类型的配置项
        if (isset($data['strategies'])) {
            $result['strategies'] = $data['strategies'];
        }
        
        if (isset($data['ai']['triggers']['keywords'])) {
            $result['ai']['triggers']['keywords'] = $data['ai']['triggers']['keywords'];
        }
        
        if (isset($data['ai']['triggers']['patterns'])) {
            $result['ai']['triggers']['patterns'] = $data['ai']['triggers']['patterns'];
        }
        
        return $result;
    }
}