<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\vchat\services\AdminService;
use think\App;
use think\facade\Log;
use think\facade\Request;
use think\facade\Validate;

/**
 * 后台管理控制器
 * 提供系统概览、队列管理、会话监控、客服状态等功能
 */
class CustomerAdminController extends BaseAdminController
{
    /**
     * @var AdminService
     */
    protected $adminService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->adminService = new AdminService();
    }

    /**
     * 获取系统概览
     * @return void
     */
    public function getSystemOverview()
    {
        try {
            $data = $this->adminService->getSystemOverview();
        } catch (\Exception $e) {
            Log::error('获取系统概览失败：' . $e->getMessage());
            $this->fail('获取系统概览失败：' . $e->getMessage());
        }
        $this->ok('获取系统概览成功', $data);

    }

    /**
     * 获取队列详细信息
     * @return void
     */
    public function getQueueDetails()
    {
        try {
            $data = $this->adminService->getQueueDetails();
        } catch (\Exception $e) {
            Log::error('获取队列详细信息失败：' . $e->getMessage());
            $this->fail('获取队列详细信息失败：' . $e->getMessage());
        }
        $this->ok('获取队列详细信息成功', $data);

    }

    /**
     * 获取会话监控信息
     * @return void
     */
    public function getSessionMonitoring()
    {

        $name = Request::param('name');
        $status = Request::param('status');

        // 参数验证
        $validate = Validate::rule([
            'name' => 'max:60',
            'status' => 'integer|in:0,1'
        ])->message([
            'name.max' => '客服名太长',
            'status.integer' => '客服状态必须为整数',
            'status.in' => '客服状态必须为0或1'
        ]);

        if (!$validate->check(['name' => $name, 'status' => $status])) {
            $this->fail($validate->getError());
        }

        try {
            $data = $this->adminService->getSessionMonitoring(['name' => $name, 'status' => $status]);
        } catch (\Exception $e) {
            Log::error('获取会话监控信息失败：' . $e->getMessage());
            $this->fail('获取会话监控信息失败：' . $e->getMessage());
        }
        $this->ok('获取会话监控信息成功', $data);

    }

    /**
     * 获取客服状态
     * @return void
     */
    public function getServiceStatus()
    {
        try {
            $data = $this->adminService->getServiceStatus();
        } catch (\Exception $e) {
            Log::error('获取客服状态失败：' . $e->getMessage());
            $this->fail('获取客服状态失败：' . $e->getMessage());
        }
        $this->ok('获取客服状态成功', $data);

    }

    /**
     * 手动分配用户到客服
     * @return void
     */
    public function manualAssignUser()
    {
        try {
            $userId = Request::param('user_id/d');
            $serviceId = Request::param('service_id/d');

            // 参数验证
            $validate = Validate::rule([
                'user_id' => 'require|integer|gt:0',
                'service_id' => 'require|integer|gt:0'
            ])->message([
                'user_id.require' => '用户ID不能为空',
                'user_id.integer' => '用户ID必须为整数',
                'user_id.gt' => '用户ID必须大于0',
                'service_id.require' => '客服ID不能为空',
                'service_id.integer' => '客服ID必须为整数',
                'service_id.gt' => '客服ID必须大于0'
            ]);

            if (!$validate->check(['user_id' => $userId, 'service_id' => $serviceId])) {
                $this->fail($validate->getError());
            }

            $result = $this->adminService->manualAssignUser($userId, $serviceId);
            
            if ($result) {
            } else {
                $this->fail('手动分配用户失败');
            }
        } catch (\Exception $e) {
            Log::error('手动分配用户失败：' . $e->getMessage());
            $this->fail('手动分配用户失败：' . $e->getMessage());
        }
        $this->ok('手动分配用户成功');

    }

    /**
     * 强制结束会话
     * @return void
     */
    public function forceEndSession()
    {
        try {
            $sessionId = Request::param('session_id/d');

            // 参数验证
            $validate = Validate::rule([
                'session_id' => 'require|integer|gt:0'
            ])->message([
                'session_id.require' => '会话ID不能为空',
                'session_id.integer' => '会话ID必须为整数',
                'session_id.gt' => '会话ID必须大于0'
            ]);

            if (!$validate->check(['session_id' => $sessionId])) {
                $this->fail($validate->getError());
            }

            $result = $this->adminService->forceEndSession($sessionId);
            
            if ($result) {
            } else {
                $this->fail('强制结束会话失败');
            }
        } catch (\Exception $e) {
            Log::error('强制结束会话失败：' . $e->getMessage());
            $this->fail('强制结束会话失败：' . $e->getMessage());
        }
        $this->ok('强制结束会话成功');

    }

    /**
     * 清空队列
     * @return void
     */
    public function clearQueue()
    {
        try {
            $result = $this->adminService->clearQueue();
            
            if ($result) {
            } else {
                $this->fail('清空队列失败');
            }
        } catch (\Exception $e) {
            Log::error('清空队列失败：' . $e->getMessage());
            $this->fail('清空队列失败：' . $e->getMessage());
        }
        $this->ok('清空队列成功');

    }

    /**
     * 获取今日统计数据
     * @return void
     */
    public function getTodayStats()
    {
        try {
            // 使用反射调用受保护的方法来获取统计数据
            $reflection = new \ReflectionClass($this->adminService);
            
            $getTodaySessionsCount = $reflection->getMethod('getTodaySessionsCount');
            $getTodaySessionsCount->setAccessible(true);
            
            $getAverageSessionDuration = $reflection->getMethod('getAverageSessionDuration');
            $getAverageSessionDuration->setAccessible(true);
            
            $calculateAverageWaitTime = $reflection->getMethod('calculateAverageWaitTime');
            $calculateAverageWaitTime->setAccessible(true);
            
            $getTimeoutRate = $reflection->getMethod('getTimeoutRate');
            $getTimeoutRate->setAccessible(true);
            
            $data = [
                'today_sessions_count' => $getTodaySessionsCount->invoke($this->adminService),
                'average_session_duration' => $getAverageSessionDuration->invoke($this->adminService),
                'average_wait_time' => $calculateAverageWaitTime->invoke($this->adminService),
                'timeout_rate' => $getTimeoutRate->invoke($this->adminService)
            ];
            
        } catch (\Exception $e) {
            Log::error('获取今日统计数据失败：' . $e->getMessage());
            $this->fail('获取今日统计数据失败：' . $e->getMessage());
        }
        $this->ok('获取今日统计数据成功', $data);

    }

    /**
     * 获取客服最后活动时间
     * @return void
     */
    public function getServiceLastActivity()
    {
        try {
            $serviceId = Request::param('service_id/d');

            // 参数验证
            $validate = Validate::rule([
                'service_id' => 'require|integer|gt:0'
            ])->message([
                'service_id.require' => '客服ID不能为空',
                'service_id.integer' => '客服ID必须为整数',
                'service_id.gt' => '客服ID必须大于0'
            ]);

            if (!$validate->check(['service_id' => $serviceId])) {
                $this->fail($validate->getError());
            }

            // 使用反射调用受保护的方法
            $reflection = new \ReflectionClass($this->adminService);
            $getServiceLastActivity = $reflection->getMethod('getServiceLastActivity');
            $getServiceLastActivity->setAccessible(true);
            
            $lastActivity = $getServiceLastActivity->invoke($this->adminService, $serviceId);
            
        } catch (\Exception $e) {
            Log::error('获取客服最后活动时间失败：' . $e->getMessage());
            $this->fail('获取客服最后活动时间失败：' . $e->getMessage());
        }
        $this->ok('获取客服最后活动时间成功', ['last_activity' => $lastActivity]);

    }

    /**
     * 获取实时数据（用于仪表板刷新）
     * @return void
     */
    public function getRealTimeData()
    {
        try {
            $data = [
                'system_overview' => $this->adminService->getSystemOverview(),
                'queue_summary' => [
                    'length' => $this->adminService->getQueueDetails()['total_count'],
                    'average_wait_time' => $this->adminService->getQueueDetails()['average_wait_time']
                ],
                'service_summary' => $this->adminService->getServiceStatus()['summary'],
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
        } catch (\Exception $e) {
            Log::error('获取实时数据失败：' . $e->getMessage());
            $this->fail('获取实时数据失败：' . $e->getMessage());
        }
        $this->ok('获取实时数据成功', $data);

    }

    /**
     * 批量操作
     * @return void
     */
    public function batchOperation()
    {
        try {
            $operation = Request::param('operation');
            $ids = Request::param('ids/a', []);

            if (empty($operation) || empty($ids)) {
                $this->fail('操作类型和ID列表不能为空');
            }

            $successCount = 0;
            $failCount = 0;
            $errors = [];

            switch ($operation) {
                case 'force_end_sessions':
                    foreach ($ids as $sessionId) {
                        try {
                            if ($this->adminService->forceEndSession((int)$sessionId)) {
                                $successCount++;
                            } else {
                                $failCount++;
                                $errors[] = "会话 {$sessionId} 结束失败";
                            }
                        } catch (\Exception $e) {
                            $failCount++;
                            $errors[] = "会话 {$sessionId} 结束失败：" . $e->getMessage();
                        }
                    }
                    break;
                    
                default:
                    $this->fail('不支持的操作类型');
            }

            $result = [
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'errors' => $errors
            ];


        } catch (\Exception $e) {
            Log::error('批量操作失败：' . $e->getMessage());
            $this->fail('批量操作失败：' . $e->getMessage());
        }

        if ($failCount > 0) {
            $this->ok("批量操作完成，成功 {$successCount} 个，失败 {$failCount} 个", $result);
        } else {
            $this->ok("批量操作全部成功，共处理 {$successCount} 个", $result);
        }
    }

    /**
     * 获取按小时分组的统计数据
     * @return void
     */
    public function getHourlyStats()
    {
        try {
            $date = Request::param('date', '');
            
            // 参数验证
            if (!empty($date)) {
                $validate = Validate::rule([
                    'date' => 'date'
                ])->message([
                    'date.date' => '日期格式不正确，请使用Y-m-d格式'
                ]);
                
                if (!$validate->check(['date' => $date])) {
                    $this->fail($validate->getError());
                }
            }
            
            $data = $this->adminService->getHourlyStats($date);
            
        } catch (\Exception $e) {
            Log::error('获取按小时统计数据失败：' . $e->getMessage());
            $this->fail('获取按小时统计数据失败：' . $e->getMessage());
        }
        
        $this->ok('获取按小时统计数据成功', $data);
    }
}