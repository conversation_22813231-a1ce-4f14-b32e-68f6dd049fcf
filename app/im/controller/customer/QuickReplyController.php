<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\admin\entity\UserEntity;
use app\common\controller\BaseAdminController;
use app\model\QuickReply;
use think\App;

/**
 * 快速回复控制器
 */
class QuickReplyController extends BaseAdminController
{
    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'content|回复内容' => 'require|max:1000',
        'sort|排序' => 'number|egt:0',
        'enabled|状态' => 'in:0,1'
    ];

    /**
     * 允许修改的字段
     * @var array
     */
    protected $allowFields = [
        'content',
        'sort',
        'enabled'
    ];

    private $userEntity;

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app, UserEntity $userEntity)
    {
        parent::__construct($app);
        $this->model = new QuickReply();
        $this->userEntity = $userEntity;
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @param int|null $id 记录ID（编辑时使用）
     * @return array
     */
    protected function getValidateRule($scene = '', $id = null)
    {
        if ($scene == 'edit') {
            return [
                'content|回复内容' => 'max:1000',
                'sort|排序' => 'number|egt:0',
                'enabled|状态' => 'in:0,1'
            ];
        }
        
        return $this->validateRule;
    }

    /**
     * 添加前的钩子方法
     * @param array $data 表单数据
     * @return array|null
     */
    protected function beforeAdd($data)
    {
        // 设置默认值
        if (!isset($data['enabled'])) {
            $data['enabled'] = 1;
        }
        if (!isset($data['sort'])) {
            $data['sort'] = 0;
        }

        $data['user_id'] = $this->userEntity->id;

        return $data;
    }

    /**
     * 列表查询前的钩子方法
     * @param array &$where 查询条件
     * @param array &$sort 排序条件
     */
    protected function beforeIndex(&$where, &$sort)
    {
        // 支持按内容搜索
        $content = $this->request->param('content', '');
        if (!empty($content)) {
            $where[] = ['content', 'like', "%{$content}%"];
        }

        // 支持按状态筛选
        $enabled = $this->request->param('enabled', '');
        if ($enabled !== '') {
            $where[] = ['enabled', '=', $enabled];
        }

        // 默认按排序和ID倒序
        $sort = ['sort' => 'asc', 'id' => 'desc'];
    }

    /**
     * 获取快速回复列表（兼容旧接口）
     */
    public function getList()
    {
        return $this->index();
    }

    /**
     * 获取所有快速回复列表
     * @return \think\Response
     */
    public function getAllQuickReply()
    {
        try {
            // 获取所有启用的客服分组
            $groups = $this->model
                ->where('enabled', 1)
                ->where('user_id', $this->userEntity->id)
                ->order('sort', 'asc') // 按排序字段升序排列
                ->select();

        } catch (\Exception $e) {
            $this->fail('获取快速回复失败');
        }

        $this->ok('success', $groups);

    }
}