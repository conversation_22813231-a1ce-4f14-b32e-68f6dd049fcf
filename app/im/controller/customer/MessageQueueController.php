<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\vchat\services\MessageQueueService;
use think\App;
use think\Request;
use think\Response;
use think\facade\Log;
use think\facade\Validate;

/**
 * 消息队列管理控制器
 * 提供HTTP接口来管理和发送消息到队列
 */
class MessageQueueController extends BaseAdminController
{
    /**
     * @var MessageQueueService
     */
    protected $messageQueueService;

    /**
     * 构造方法
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->messageQueueService = new MessageQueueService();
    }

    /**
     * 发送Socket.IO事件消息到队列
     * POST /admin/message-queue/send-socketio
     * @param Request $request
     * @return Response
     */
    public function sendSocketIoMessage(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证参数
            $validate = Validate::rule([
                'user_id' => 'require|integer|gt:0',
                'event' => 'require|string|max:50',
                'user_type' => 'in:user,service',
                'data' => 'array'
            ]);
            
            if (!$validate->check($data)) {
                $this->fail($validate->getError());
            }
            
            $userId = (int)$data['user_id'];
            $event = $data['event'];
            $userType = $data['user_type'] ?? 'user';
            $eventData = $data['data'] ?? [];
            
            // 发送消息到队列
            $result = $this->messageQueueService->pushSocketIoMessage($userId, $event, $userType, $eventData);
            
            if ($result) {
                Log::info('[后台管理] Socket.IO消息发送成功', [
                    'user_id' => $userId,
                    'event' => $event,
                    'user_type' => $userType,
                    'admin_user' => $this->getAdminUser()['id'] ?? 'unknown'
                ]);

            } else {
                $this->fail('消息入队失败');
            }
        } catch (\Exception $e) {
            Log::error('[后台管理] 发送Socket.IO消息异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->post()
            ]);
            $this->fail('系统异常：' . $e->getMessage());
        }

        $this->ok('消息已加入队列', [
            'user_id' => $userId,
            'event' => $event,
            'user_type' => $userType,
            'timestamp' => time()
        ]);
    }

    /**
     * 发送常规消息到队列
     * POST /admin/message-queue/send-message
     * @param Request $request
     * @return Response
     */
    public function sendMessage(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证参数
            $validate = Validate::rule([
                'to_user_id' => 'require|integer|gt:0',
                'to_user_type' => 'in:user,service',
                'message_type' => 'require|string|max:20',
                'content' => 'require|string',
                'from_user_id' => 'integer|gt:0',
                'from_user_type' => 'in:user,service,system'
            ]);
            
            if (!$validate->check($data)) {
                $this->fail($validate->getError());
            }
            
            // 构建消息数据
            $message = [
                'to_user_id' => (int)$data['to_user_id'],
                'to_user_type' => $data['to_user_type'] ?? 'user',
                'message_type' => $data['message_type'],
                'content' => $data['content'],
                'from_user_id' => $data['from_user_id'] ?? 0,
                'from_user_type' => $data['from_user_type'] ?? 'system',
                'timestamp' => time(),
                'extra_data' => $data['extra_data'] ?? []
            ];
            
            // 发送消息到队列
            $result = $this->messageQueueService->pushMessage($message);
            
            if ($result) {
                Log::info('[后台管理] 常规消息发送成功', [
                    'to_user_id' => $message['to_user_id'],
                    'message_type' => $message['message_type'],
                    'admin_user' => $this->getAdminUser()['id'] ?? 'unknown'
                ]);
                

            } else {
                $this->fail('消息入队失败');
            }
        } catch (\Exception $e) {
            Log::error('[后台管理] 发送常规消息异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->post()
            ]);
            $this->fail('系统异常：' . $e->getMessage());
        }

        $this->ok('消息已加入队列', [
            'to_user_id' => $message['to_user_id'],
            'message_type' => $message['message_type'],
            'timestamp' => time()
        ]);
    }

    /**
     * 获取消息状态
     * GET /admin/message-queue/status/{messageId}
     * @param Request $request
     * @param string $messageId
     * @return Response
     */
    public function getMessageStatus(Request $request, string $messageId): Response
    {
        try {
            if (empty($messageId)) {
                $this->fail('消息ID不能为空');
            }
            
            $status = $this->messageQueueService->getMessageStatus($messageId);
            
            if ($status === null) {
                $this->fail('未找到消息');
            }
            

        } catch (\Exception $e) {
            Log::error('[后台管理] 获取消息状态异常', [
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            $this->fail('系统异常：' . $e->getMessage());
        }
        $this->ok('获取成功', [
            'message_id' => $messageId,
            'status' => $status,
            'timestamp' => time()
        ]);
    }

    /**
     * 获取消息详情
     * GET /admin/message-queue/detail/{messageId}
     * @param Request $request
     * @param string $messageId
     * @return Response
     */
    public function getMessageDetail(Request $request, string $messageId): Response
    {
        try {
            if (empty($messageId)) {
                $this->fail('消息ID不能为空');
            }
            
            $detail = $this->messageQueueService->getMessageDetail($messageId);
            
            if ($detail === null) {
                $this->fail('未找到消息');
            }
            

        } catch (\Exception $e) {
            Log::error('[后台管理] 获取消息详情异常', [
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            $this->fail('系统异常：' . $e->getMessage());
        }

        $this->ok('获取成功', $detail);
    }

    /**
     * 获取队列状态
     * GET /admin/message-queue/queue-status
     * @param Request $request
     * @return Response
     */
    public function getQueueStatus(Request $request): Response
    {
        try {
            $status = $this->messageQueueService->getQueueStatus();
            
        } catch (\Exception $e) {
            Log::error('[后台管理] 获取队列状态异常', [
                'error' => $e->getMessage()
            ]);
            $this->fail('系统异常：' . $e->getMessage());
        }

        $this->ok('获取成功', $status);

    }

    /**
     * 重试失败的消息
     * POST /admin/message-queue/retry/{messageId}
     * @param Request $request
     * @param string $messageId
     * @return Response
     */
    public function retryFailedMessage(Request $request, string $messageId): Response
    {
        try {
            if (empty($messageId)) {
                $this->fail('消息ID不能为空');
            }
            
            $result = $this->messageQueueService->retryFailedMessage($messageId);
            
            if ($result) {
                Log::info('[后台管理] 重试失败消息成功', [
                    'message_id' => $messageId,
                    'admin_user' => $this->getAdminUser()['id'] ?? 'unknown'
                ]);
                

            } else {
                $this->fail('重试失败，消息可能不存在或不在失败队列中');
            }
        } catch (\Exception $e) {
            Log::error('[后台管理] 重试失败消息异常', [
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            $this->fail('系统异常：' . $e->getMessage());
        }
        $this->ok('消息已重新加入队列', [
            'message_id' => $messageId,
            'timestamp' => time()
        ]);
    }

    /**
     * 清理过期消息
     * POST /admin/message-queue/clean-expired
     * @param Request $request
     * @return Response
     */
    public function cleanExpiredMessages(Request $request): Response
    {
        try {
            $data = $request->post();
            $expireTime = (int)($data['expire_time'] ?? 86400); // 默认24小时
            
            // 验证过期时间
            if ($expireTime < 3600) {
                $this->fail('过期时间不能少于1小时');
            }
            
            if ($expireTime > 7 * 24 * 3600) {
                $this->fail('过期时间不能超过7天');
            }
            
            $result = $this->messageQueueService->cleanExpiredMessages($expireTime);
            
            if ($result) {
                Log::info('[后台管理] 清理过期消息成功', [
                    'expire_time' => $expireTime,
                    'admin_user' => $this->getAdminUser()['id'] ?? 'unknown'
                ]);
                

            } else {
                $this->fail('清理过期消息失败');
            }
        } catch (\Exception $e) {
            Log::error('[后台管理] 清理过期消息异常', [
                'error' => $e->getMessage(),
                'request_data' => $request->post()
            ]);
            $this->fail('系统异常：' . $e->getMessage());
        }
        $this->ok('过期消息清理完成', [
            'expire_time' => $expireTime,
            'timestamp' => time()
        ]);
    }

    /**
     * 批量发送通知消息
     * POST /admin/message-queue/batch-notify
     * @param Request $request
     * @return Response
     */
    public function batchNotify(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证参数
            $validate = Validate::rule([
                'user_ids' => 'require|array',
                'user_type' => 'in:user,service',
                'event' => 'require|string|max:50',
                'data' => 'array'
            ]);
            
            if (!$validate->check($data)) {
                $this->fail($validate->getError());
            }
            
            $userIds = $data['user_ids'];
            $userType = $data['user_type'] ?? 'user';
            $event = $data['event'];
            $eventData = $data['data'] ?? [];
            
            if (count($userIds) > 100) {
                $this->fail('单次批量发送不能超过100个用户');
            }
            
            $successCount = 0;
            $failedCount = 0;
            
            foreach ($userIds as $userId) {
                if (!is_numeric($userId) || $userId <= 0) {
                    $failedCount++;
                    continue;
                }
                
                $result = $this->messageQueueService->pushSocketIoMessage((int)$userId, $event, $userType, $eventData);
                
                if ($result) {
                    $successCount++;
                } else {
                    $failedCount++;
                }
            }
            
            Log::info('[后台管理] 批量通知发送完成', [
                'event' => $event,
                'user_type' => $userType,
                'total' => count($userIds),
                'success' => $successCount,
                'failed' => $failedCount,
                'admin_user' => $this->getAdminUser()['id'] ?? 'unknown'
            ]);

        } catch (\Exception $e) {
            Log::error('[后台管理] 批量通知发送异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->post()
            ]);
            $this->fail('系统异常：' . $e->getMessage());
        }

        $this->ok('批量通知发送完成', [
            'total' => count($userIds),
            'success' => $successCount,
            'failed' => $failedCount,
            'timestamp' => time()
        ]);
    }

    /**
     * 获取当前管理员用户信息
     * @return array
     */
    protected function getAdminUser(): array
    {
        // 这里应该从session或token中获取管理员信息
        // 具体实现根据项目的认证机制而定
        return [
            'id' => 1,
            'username' => 'admin'
        ];
    }
}