<?php
declare(strict_types=1);

namespace app\im\controller\customer;

use app\common\controller\BaseAdminController;
use app\model\ChatComment;
use think\App;
use think\facade\Request;

class ChatCommentController extends BaseAdminController
{

    /**
     * 验证规则
     * @var array
     */
    protected $validateRule = [
        'user_id|用户id' => 'require|number',
        'mobile|手机号' => 'require|mobile',
        'status|状态' => 'in:0,1',
        'content|留言内容' => 'require|max:1024',
        'wechat|微信号' => 'max:64',
    ];

    /**
     * 允许修改的字段
     * @var array
     */
    protected $allowFields = [
        'user_id',
        'mobile',
        'content',
        'status',
        'wechat'
    ];

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->model = new ChatComment();
    }

    /**
     * 处理留言
     */
    public function beforeAdd($data)
    {
        return $data;
    }

    public function confirm()
    {

        $id = Request::param('id/d');

        // 验证参数
        $this->validate([
            'id' => $id
        ], [
            'id' => 'require|number',
        ], [
            'id.require' => '缺少满意度参数',
            'id.number' => '满意度参数错误',
        ]);

        if ($this->model->confirm($id)) {
            $this->ok('确认成功');
        }

        $this->fail('确认失败');
    }

}