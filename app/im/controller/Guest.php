<?php
declare(strict_types=1);

namespace app\im\controller;

use app\common\Auth;
use app\model\system\User;
use think\App;
use think\facade\Log;

class Guest
{
    protected $userModel;

    public function __construct(App $app)
    {
        $this->userModel = new User();
    }

    /**
     * 生成临时游客ID
     */
    public function createTempGuest()
    {
        try {
            // 生成临时游客数据
            $guestData = [
                'username' => 'guest_'.time().'_'.bin2hex(random_bytes(4)),
                'nickname' => '临时用户',
                'user_type' => 2,
                'enabled' => 1,
                'createtime' => date('Y-m-d H:i:s'),
                'updatetime' => date('Y-m-d H:i:s')
            ];

            // 创建临时用户记录
            $guest = $this->userModel->create($guestData);

            // 生成临时token (有效期24小时)
            $token = Auth::createToken($guest->id, ['is_temporary' => true], 86400);

        } catch (\Exception $e) {
            Log::error('创建临时游客失败: '.$e->getMessage());
            return json([
                'code' => 1000,
                'msg' => '创建临时游客失败'
            ]);
        }

        return json([
            'code' => 200,
            'msg' => 'success',
            'data' => [
                'guest_id' => $guest->id,
                'username' => $guest->username,
                'access_token' => $token
            ]
        ]);

    }
}