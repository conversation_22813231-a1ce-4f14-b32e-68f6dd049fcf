<?php
declare(strict_types=1);

namespace app\im\controller;

use app\common\controller\BaseApiController;
use app\model\ChatMessage;
use app\model\ChatSession;
use app\model\CustomerService;
use app\vchat\core\MessageProtocol;
use think\App;
use think\facade\Log;
use think\facade\Request;

/**
 * 用户端客服控制器
 */
class UserServiceController extends BaseApiController
{
    /**
     * @var ChatSession
     */
    protected $sessionModel;

    /**
     * @var ChatMessage
     */
    protected $messageModel;

    /**
     * @var CustomerService
     */
    protected $serviceModel;

    /**
     * 构造方法
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        
        $this->sessionModel = new ChatSession();
        $this->messageModel = new ChatMessage();
        $this->serviceModel = new CustomerService();
    }

    /**
     * 获取当前用户的会话列表
     */
    public function getUserSessions()
    {
        try {
            $userId = 1;

            $limit = Request::param('limit/d', 20);
            $lastId = Request::param('last_id/d', null);
            $status = Request::param('status/d', null); // 可选：1-进行中，2-已结束

            $sessions = $this->sessionModel->getUserSessions($userId, $limit, $lastId, $status);
            
        } catch (\Exception $e) {
            Log::error('获取用户会话列表失败：' . $e->getMessage());
            $this->fail('获取用户会话列表失败');
        }

        $this->ok('success', $sessions);

    }

    /**
     * 获取会话消息记录
     */
    public function getSessionMessages()
    {
        try {
            $sessionId = Request::param('session_id/d');
            $limit = Request::param('limit/d', 20);
            $lastId = Request::param('last_id/d', null);

            if (!$sessionId) {
                $this->fail('缺少会话ID参数');
            }

            // 验证会话是否属于当前用户
            $userId = 1;
            $session = $this->sessionModel->where([
                ['id', '=', $sessionId],
                ['user_id', '=', $userId]
            ])->find();

            if (!$session) {
                $this->fail('会话不存在或无权限访问');
            }

            $messages = $this->messageModel->getSessionMessages($sessionId, $limit, $lastId);

            // 添加direction字段
            $messages = array_map(function($message) {
                $message['direction'] = $message['from_type'] === 'user' ? MessageProtocol::DIRECTION_SEND : MessageProtocol::DIRECTION_RECEIVE;
                $message['read'] = $message['status'] == 3 ? true : false;
                return $message;
            }, $messages);
            
        } catch (\Exception $e) {
            Log::error('获取会话消息失败：' . $e->getMessage());
            $this->fail('获取会话消息失败');
        }

        $this->ok('success', $messages);

    }

    /**
     * 创建新会话
     */
    public function createSession()
    {
        try {
            $userId = 1;

            $source = Request::param('source', 'web');

            // 检查用户是否有进行中的会话
            $activeSession = $this->sessionModel->getUserActiveSession($userId);
            if ($activeSession) {
                $this->ok('success', [
                    'session_id' => $activeSession['id'],
                    'service_id' => $activeSession['service_id']
                ]);
            }

            // 分配客服
            $service = $this->serviceModel->assignService();
            if (!$service) {
                $this->fail('当前无可用客服');
            }

            // 创建新会话
            $session = ChatSession::createSession($userId, $service['id'], $source);

            if (!$session) {
                $this->fail('创建会话失败');
            }

        } catch (\Exception $e) {
            Log::error('创建会话失败：' . $e->getMessage());
            $this->fail('创建会话失败: ' . $e->getMessage());
        }

        $this->ok('success', [
            'session_id' => $session['id'],
            'service_id' => $service['id']
        ]);
    }

    /**
     * 结束会话
     */
    public function endSession()
    {
        try {
            $sessionId = Request::param('session_id/d');
            if (!$sessionId) {
                $this->fail('缺少会话ID参数');
            }

            // 验证会话是否属于当前用户
            $userId = 1;
            $session = $this->sessionModel->where([
                ['id', '=', $sessionId],
                ['user_id', '=', $userId]
            ])->find();

            if (!$session) {
                $this->fail('会话不存在或无权限操作');
            }

            if ($session['status'] == 2) {
                $this->fail('会话已结束');
            }

            $result = $this->sessionModel->endSession($sessionId);
            if (!$result) {
                $this->fail('结束会话失败');
            }

        } catch (\Exception $e) {
            Log::error('结束会话失败：' . $e->getMessage());
            $this->fail('结束会话失败');
        }

        $this->ok('success');

    }

    /**
     * 评价会话
     */
    public function rateSession()
    {
        try {

            $sessionId = Request::param('session_id/d');
            $rating = Request::param('rating/d');
            $comment = Request::param('feedback/s', '');

            // 验证参数
            $this->validate([
                'session_id' => $sessionId,
                'rating' => $rating,
                'feedback' => $comment
            ], [
                'session_id' => 'require|number',
                'rating' => 'require|number|between:1,5',
                'feedback' => 'max:500'
            ], [
                'session_id.require' => '缺少会话ID参数',
                'rating.require' => '缺少满意度参数',
                'rating.number' => '满意度参数错误',
                'rating.between' => '满意度参数错误',
                'feedback.max' => '反馈信息过长'
            ]);

            // 验证会话是否属于当前用户
            $userId = 1;
            $session = $this->sessionModel->where([
                ['id', '=', $sessionId],
                ['user_id', '=', $userId]
            ])->find();

            if (!$session) {
                $this->fail('会话不存在或无权限操作');
            }

            if ($session['status'] != 2) {
                $this->fail('只能评价已结束的会话');
            }

            if ($session['satisfaction'] > 0) {
                $this->fail('该会话已评价');
            }

            $result = $this->sessionModel->updateSatisfaction($sessionId, $rating, $comment);
            if (!$result) {
                $this->fail('评价失败');
            }

        } catch (\Exception $e) {
            Log::error('评价会话失败：' . $e->getMessage());
            $this->fail('评价会话失败');
        }

        $this->ok('success');
    }

    /**
     * 获取用户会话统计
     */
    public function getUserStats()
    {
        try {
            $userId = 1;

            $startTime = Request::param('start_time');
            $endTime = Request::param('end_time');

            if (!$startTime || !$endTime) {
                $this->fail('缺少时间参数');
            }

            $stats = $this->sessionModel->getUserSessionStats($userId, $startTime, $endTime);
            
        } catch (\Exception $e) {
            Log::error('获取用户统计失败：' . $e->getMessage());
            $this->fail('获取用户统计失败');
        }

        $this->ok('success', $stats);

    }
}