<?php

use think\facade\Route;

// 客服系统路由
Route::group('v1', function () {
    //创建临时用户token
    Route::get('/guest/token', 'Guest/createTempGuest')->middleware(\app\api\middleware\CommonConfigInit::class);

    // 客服管理
    Route::get('/customer/service', 'customer.ServiceManagerController/getCurrentServiceId');  // 获取当前用户的客服ID
    Route::get('/customer/service/online', 'customer.ServiceManagerController/getOnlineServices');  // 获取在线客服列表
    Route::get('/customer/service/group/online', 'customer.ServiceManagerController/getOnlineServicesByGroup');  // 获取在线客服列表
    Route::get('/customer/service/sessions', 'customer.ServiceSessionController/getServiceSessions');  // 获取客服会话列表
    Route::get('/customer/service/sessions/messages', 'customer.ServiceSessionController/getSessionMessages');  // 获取会话消息
    Route::post('/customer/service/transfer', 'customer.ServiceSessionController/transferSession');  // 转接会话
    Route::post('/customer/service/end', 'customer.ServiceSessionController/endSession');  // 结束会话
    Route::post('/customer/service/satisfaction', 'customer.ServiceSessionController/updateSatisfaction');  // 更新满意度

    // 用户端接口
    Route::get('/user/service/sessions', 'UserServiceController/getUserSessions');  // 获取用户会话列表
    Route::get('/user/service/sessions/messages', 'UserServiceController/getSessionMessages');  // 获取会话消息
    Route::post('/user/service/session/request', 'UserServiceController/createSession');  // 创建新会话
    Route::post('/user/service/session/end', 'UserServiceController/endSession');  // 结束会话
    Route::post('/user/service/session/rate', 'UserServiceController/rateSession');  // 评价会话
    Route::get('/user/service/stats', 'UserServiceController/getUserStats');  // 获取用户统计

    // 统计相关
    Route::get('/customer/service/stats', 'customer.ServiceStatsController/getServiceStats');  // 获取客服统计
    Route::get('/customer/session/stats', 'customer.ServiceStatsController/getSessionStats');  // 获取会话统计
    Route::get('/customer/message/stats', 'customer.ServiceStatsController/getMessageStats');  // 获取消息统计

    Route::group('/customer/service', function() {
        Route::get('/conversations', 'customer.ServiceSessionController/index');  // 获取客服会话列表

        Route::get('/staff', 'customer.CustomerServiceController/index');
        Route::get('/staff/:id$', 'customer.CustomerServiceController/info')->pattern(['id' => '\d+']);;
        Route::post('/staff', 'customer.CustomerServiceController/add');
        Route::put('/staff/:id$', 'customer.CustomerServiceController/update')->pattern(['id' => '\d+']);;
        Route::delete('/staff/:id$', 'customer.CustomerServiceController/delete')->pattern(['id' => '\d+']);;

        Route::get('/group', 'customer.CustomerGroupController/index');
        Route::get('/group/list', 'customer.CustomerGroupController/getAllGroups');
        Route::get('/group/:id$', 'customer.CustomerGroupController/info')->pattern(['id' => '\d+']);;
        Route::post('/group', 'customer.CustomerGroupController/add');
        Route::put('/group/:id$', 'customer.CustomerGroupController/update')->pattern(['id' => '\d+']);;
        Route::delete('/group/:id$', 'customer.CustomerGroupController/delete')->pattern(['id' => '\d+']);;

        Route::get('/keyword_reply', 'customer.KeywordReplyController/index');
        Route::get('/keyword_reply/:id$', 'customer.KeywordReplyController/info')->pattern(['id' => '\d+']);;
        Route::post('/keyword_reply', 'customer.KeywordReplyController/add');
        Route::put('/keyword_reply/:id$', 'customer.KeywordReplyController/update')->pattern(['id' => '\d+']);;
        Route::delete('/keyword_reply/:id$', 'customer.KeywordReplyController/delete')->pattern(['id' => '\d+']);;

        Route::get('/quick_reply', 'customer.QuickReplyController/index');
        Route::get('/quick_reply/list', 'customer.QuickReplyController/getAllQuickReply');
        Route::get('/quick_reply/:id$', 'customer.QuickReplyController/info')->pattern(['id' => '\d+']);;
        Route::post('/quick_reply', 'customer.QuickReplyController/add');
        Route::put('/quick_reply/:id$', 'customer.QuickReplyController/update')->pattern(['id' => '\d+']);;
        Route::delete('/quick_reply/:id$', 'customer.QuickReplyController/delete')->pattern(['id' => '\d+']);;

        Route::get('/reception/settings', 'customer.ServiceConfigController/index');
        Route::put('/reception/settings', 'customer.ServiceConfigController/update');

        //评价和满意度

        //留言管理
        Route::get('comment', 'customer.ChatCommentController/index');
        Route::post('comment', 'customer.ChatCommentController/add');
        Route::post('comment/confirm', 'customer.ChatCommentController/confirm');
        Route::put('comment/:id$', 'customer.ChatCommentController/edit')->pattern(['id' => '\d+']);
        Route::delete('comment/:id$', 'customer.ChatCommentController/delete')->pattern(['id' => '\d+']);

        //客服管理数据接口
        // 系统概览
        Route::get('system/overview', 'customer.CustomerAdminController/getSystemOverview');
        // 队列详情
        Route::get('queue/details', 'customer.CustomerAdminController/getQueueDetails');
        // 会话监控
        Route::get('session/monitoring', 'customer.CustomerAdminController/getSessionMonitoring');
        // 按小时统计数据
        Route::get('session/hourly/stats', 'customer.CustomerAdminController/getHourlyStats');
        // 客服状态
        Route::get('service/status', 'customer.CustomerAdminController/getServiceStatus');
        // 手动分配用户
        Route::post('manual/assign', 'customer.CustomerAdminController/manualAssignUser');
        // 强制结束会话
        Route::post('force/end/session', 'customer.CustomerAdminController/forceEndSession');
        // 清空队列
        Route::post('clear/queue', 'customer.CustomerAdminController/clearQueue');
        // 今日统计
        Route::get('today/stats', 'customer.CustomerAdminController/getTodayStats');
        // 客服最后活动时间
        Route::get('last/activity/:service_id', 'customer.CustomerAdminController/getServiceLastActivity');
        // 实时数据
        Route::get('realtime/data', 'customer.CustomerAdminController/getRealTimeData');
        // 批量操作
        Route::post('operation/batch', 'customer.CustomerAdminController/batchOperation');
    });
})->middleware(\app\admin\middleware\CheckPermission::class);
