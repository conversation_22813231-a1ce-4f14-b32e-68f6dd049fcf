<?php

namespace app\middleware;

use app\vchat\auto_reply\AutoReplyManager;
use app\vchat\core\MessageProtocol;
use app\vchat\events\EventManager;
use app\vchat\services\MessageService;
use app\vchat\services\ScheduleService;
use app\vchat\utils\Logger;
use Closure;
use think\App;
use think\Request;
use think\Response;
use think\swoole\Websocket;

class VChatInitMiddleware
{

    protected $app;
    public function __construct(App $app)
    {
        $this->app = $app;
    }

    /**
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next)
    {
        $this->app->bind(AutoReplyManager::class, AutoReplyManager::class);
        $this->app->bind(EventManager::class, EventManager::class);

        $eventManager = $this->app->make(EventManager::class);

        // 注册用户消息处理后的自动回复事件
        $eventManager->register('message_auto_reply', function (Websocket $websocket, array $messageData, MessageService $messageService) {
            // 在这里处理自动回复逻辑
            $replyContent = app()->make(AutoReplyManager::class)->getAutoReply($messageData);
            (new Logger())->info('AutoReplyManager', [$replyContent]);

            if ($replyContent) {
                // 构建自动回复消息并发送
                // 你需要根据实际情况确定 from_id, to_id, session_id 等
                $replyMessage = [
                    // 从原始消息中获取 to_id (通常是用户自己) 和 session_id
                    'session_id' => $messageData['session_id'] ?? 0, // 确保有session_id
                    'from_id' => $messageData['to_id'] ?? 0, // 假设自动回复from是客服/系统
                    'from_type' => $messageData['to_type'] ?? 'service', // 假设自动回复是客服类型发出
                    'to_id' => $messageData['from_id'] ?? 0, // 回复给发送消息的用户
                    'to_type' => $messageData['from_type'] ?? 'user',
                    'content' => $replyContent,
                    'type' => $messageData['type'] ?? MessageProtocol::TYPE_TEXT, // 自动回复通常是文本
                    'message_type' => $messageData['message_type'] ?? MessageProtocol::MESSAGE_TYPE,
                    'status' => $messageData['status'],
                    'timestamp' => time(), // 或使用消息中的时间戳
                ];

                // 保存自动回复消息 (可选，如果需要记录的话)
                // $messageService = app()->make(\app\vchat\services\MessageService::class);
                // $savedReplyMessage = $messageService->saveMessage($replyMessage);
                // 保存消息并获取message_id
                $savedMessage = $messageService->saveMessage($replyMessage);
                if ($savedMessage) {
                    $replyMessage['id'] = $savedMessage['id'];
                    $replyMessage['event'] = 'auto_reply';
                    $replyMessage['from_id'] = $replyMessage['to_id'];
                    $replyMessage['to_id'] = $replyMessage['from_id'];
                    $replyMessage['from_type'] = 'service';
                    $replyMessage['to_type'] = 'user';
                    $messageService->sendMessage($websocket, $replyMessage);
                }

            }
        });
        return $next($request);
    }

}