<?php

namespace app\middleware;

use Closure;
use think\Request;

class CrossOrigin
{
    /**
     * 处理跨域请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next)
    {
        header('Access-Control-Allow-Origin:*');
        header('Access-Control-Allow-Methods:GET,POST,OPTIONS,PUT,DELETE');
        header('Access-Control-Allow-Headers:sign-time,app-id,app-key,cms-token,plat-v,token,pid,Authorization,Content-Type,Depth,User-Agent,X-File-Size,X-Requested-With,X-Requested-By,If-Modified-Since,X-File-Name,X-File-Type,Cache-Control,Origin');
        header('Access-Control-Request-Headers:*');
        header('Access-Control-Allow-Credentials:true');

        // 处理OPTIONS预检请求
        if ($request->isOptions()) {
            return response()->code(204);
        }

        return $next($request);
    }
}