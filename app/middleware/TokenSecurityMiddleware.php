<?php

namespace app\middleware;

use app\common\Auth;
use app\common\TokenSecurity;
use app\common\ApiCode;
use think\facade\Config;
use think\facade\Request;

/**
 * Token安全中间件
 * 提供Token验证的安全增强功能
 */
class TokenSecurityMiddleware
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, \Closure $next)
    {
        // 检查是否启用安全功能
        $securityConfig = Config::get('token.security', []);
        
        if (!($securityConfig['suspicious_detection']['enabled'] ?? true)) {
            return $next($request);
        }
        
        $ip = $request->ip();
        $userAgent = $request->header('user-agent', '');
        
        // 检查IP是否可疑
        if (TokenSecurity::isSuspiciousIP($ip)) {
            return json([
                'code' => ApiCode::FORBIDDEN,
                'message' => '访问被拒绝：检测到可疑活动',
                'data' => []
            ], 403);
        }
        
        // 检查速率限制
        $rateLimitConfig = $securityConfig['rate_limit'] ?? [];
        $maxAttempts = $rateLimitConfig['max_attempts'] ?? 60;
        $timeWindow = $rateLimitConfig['time_window'] ?? 3600;
        
        if (!TokenSecurity::checkRateLimit($ip, $maxAttempts, $timeWindow)) {
            return json([
                'code' => ApiCode::TOO_MANY_REQUESTS,
                'message' => '请求过于频繁，请稍后再试',
                'data' => []
            ], 429);
        }
        
        // 检查来源是否可信
        if (!TokenSecurity::checkTrustedSource()) {
            TokenSecurity::recordFailedAttempt(null, 'UNTRUSTED_SOURCE', $ip);
            return json([
                'code' => ApiCode::FORBIDDEN,
                'message' => '访问被拒绝：不可信的请求来源',
                'data' => []
            ], 403);
        }
        
        // 获取Token
        $token = $this->getTokenFromRequest($request);
        
        if ($token) {
            // 验证Token格式
            if (!TokenSecurity::validateTokenFormat($token)) {
                TokenSecurity::recordFailedAttempt($token, 'INVALID_FORMAT', $ip);
                return json([
                    'code' => ApiCode::ACCESS_TOKEN_INVALID,
                    'message' => 'Token格式无效',
                    'data' => []
                ], 401);
            }
            
            // 尝试验证Token
            try {
                $tokenData = Auth::checkToken($token);
                // 将用户信息添加到请求中
                $request->tokenData = $tokenData;
            } catch (\Exception $e) {
                // 记录失败尝试
                TokenSecurity::recordFailedAttempt($token, $e->getMessage(), $ip);
                
                return json([
                    'code' => $e->getCode() ?: ApiCode::ACCESS_TOKEN_INVALID,
                    'message' => $e->getMessage(),
                    'data' => []
                ], 401);
            }
        }
        
        return $next($request);
    }
    
    /**
     * 从请求中获取Token
     *
     * @param \think\Request $request
     * @return string|null
     */
    protected function getTokenFromRequest($request)
    {
        // 从Authorization头获取
        $authorization = $request->header('authorization', '');
        if (preg_match('/Bearer\s+(\S+)/', $authorization, $matches)) {
            return $matches[1];
        }
        
        // 从GET参数获取
        $token = $request->get('token', '');
        if (!empty($token)) {
            return $token;
        }
        
        // 从POST参数获取
        $token = $request->post('token', '');
        if (!empty($token)) {
            return $token;
        }
        
        return null;
    }
    
    /**
     * 获取错误响应
     *
     * @param int $code 错误码
     * @param string $message 错误信息
     * @param int $httpCode HTTP状态码
     * @return \think\Response
     */
    protected function errorResponse($code, $message, $httpCode = 401)
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => []
        ], $httpCode);
    }
}