<?php
declare (strict_types = 1);

namespace app\middleware;

use app\plugin\constant\AppsName;
use app\plugin\core\PluginManager;
use app\plugin\core\PluginEventListener;
use app\plugin\core\HookManager;
use think\App;
use think\facade\Route;

class PluginInitMiddleware
{
    /**
     * @var App
     */
    protected $app;

    /**
     * @var PluginManager
     */
    protected $pluginManager;

    /**
     * @var HookManager
     */
    protected $hookManager;

    /**
     * @var PluginEventListener
     */
    protected $eventListener;

    /**
     * @var string
     */
    protected $pluginName;

    public function __construct(App $app)
    {
        $this->app = $app;
        // 定义应用目录常量
        if (!defined('__APPS_PATH__')) {
            define('__APPS_PATH__', $this->app->getRootPath() . AppsName::APPS_NAME . DIRECTORY_SEPARATOR);
        }
        $this->registerCoreServices();
    }

    /**
     * 中间件处理方法
     *
     * @param \think\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, \Closure $next)
    {
        try {

            // 获取当前访问的插件名称
            $path = $request->baseUrl();

            // 从URL路径中解析插件名称
            if (preg_match('#^/apps/([^/]+)/#', $path, $matches)) {
                $this->pluginName = $matches[1];
            }

            $request->pluginName = $this->pluginName;

            // 加载插件基础信息
            $this->loadPlugins();

            // 初始化插件系统
            $this->initPlugins();

            return $next($request);
        } catch (\Throwable $e) {
            // 记录详细的错误信息
            $errorMessage = sprintf(
                "插件系统初始化失败:\n错误类型: %s\n错误信息: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                get_class($e),
                $e->getMessage(),
                $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );

            $this->app->log->error($errorMessage);
            throw $e; // 重新抛出异常以确保错误能够正确传播
        }
    }

    /**
     * 注册核心服务
     */
    protected function registerCoreServices(): void
    {
        $this->app->bind(PluginManager::class, PluginManager::class);
        $this->pluginManager = $this->app->make(PluginManager::class);

        $this->app->bind(HookManager::class, HookManager::class);
        $this->hookManager = $this->app->make(HookManager::class);
    }

    /**
     * 加载插件基础信息
     */
    protected function loadPlugins(): void
    {
        try {
            // 只加载插件元数据，不初始化业务逻辑
            $this->pluginManager->loadPlugins(true, $this->pluginName);

            // 初始化事件监听器
            $this->eventListener = $this->pluginManager->initEventListener($this->pluginName);

            // 执行预加载钩子
            $this->hookManager->execute('plugin.preload');
        } catch (\Exception $e) {
            $errorMessage = sprintf(
                "加载插件失败: %s\n插件名称: %s\n堆栈跟踪:\n%s",
                $e->getMessage(),
                $this->pluginName ?? '未指定',
                $e->getTraceAsString()
            );

            $this->app->log->error($errorMessage);
            throw $e;
        }
    }

    /**
     * 初始化插件系统
     */
    protected function initPlugins(): void
    {
        // 1. 执行初始化钩子
        $this->hookManager->execute('plugin.init');

        // 2. 按依赖顺序启动插件
        $this->pluginManager->bootPlugins();

        // 3. 执行后置初始化钩子
        $this->hookManager->execute('plugin.after_init');
    }
}