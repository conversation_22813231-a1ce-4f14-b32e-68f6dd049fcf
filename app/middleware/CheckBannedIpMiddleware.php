<?php

namespace app\middleware;

use think\Request;

class CheckBannedIpMiddleware
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, \Closure $next)
    {
        // 获取配置的禁止IP列表
        $bannedIPs = config('site.bannedIPs');
        
        // 如果配置了禁止IP
        if (!empty($bannedIPs)) {
            // 将IP字符串转换为数组（支持逗号、分号、换行符分隔）
            $bannedIPList = preg_split('/[,;\n]/', $bannedIPs);
            $bannedIPList = array_map('trim', $bannedIPList);
            $bannedIPList = array_filter($bannedIPList);
            
            // 获取当前请求的IP
            $currentIP = $request->ip();
            
            // 检查当前IP是否在禁止列表中
            if (in_array($currentIP, $bannedIPList)) {
                // 返回403禁止访问响应
                return response('Access Denied: Your IP is banned.', 403);
            }
        }
        
        return $next($request);
    }
}