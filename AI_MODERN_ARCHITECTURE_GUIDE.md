# AI功能现代化重构指南

## 概述

本文档描述了AI功能的现代化重构，将原有的AI系统升级为更加现代化、可扩展和易于维护的架构。

## 重构目标

### 1. 现代化架构
- **依赖注入容器**：实现IoC容器，支持自动依赖解析
- **服务提供者模式**：模块化服务注册和启动
- **事件驱动架构**：解耦组件，提高可扩展性
- **中间件管道**：可组合的请求处理链

### 2. 可扩展性
- **智能路由**：根据请求类型自动选择合适的服务
- **插件化设计**：易于添加新的AI提供商和功能
- **配置管理**：统一的配置系统，支持环境变量
- **缓存策略**：多层缓存，提高性能

### 3. 可观测性
- **性能监控**：详细的指标收集和分析
- **事件追踪**：完整的请求生命周期追踪
- **日志系统**：结构化日志，便于调试和监控
- **健康检查**：服务状态监控

## 架构组件

### 1. 服务容器 (ServiceContainer)
```php
// 位置: app/ai/container/ServiceContainer.php
$container = ServiceContainer::getInstance();
$container->bind('ai.service', AiService::class);
$service = $container->make('ai.service');
```

**特性:**
- 单例模式
- 自动依赖解析
- 支持别名和标签
- 延迟加载

### 2. 服务提供者 (ServiceProvider)
```php
// 位置: app/ai/providers/AiServiceProvider.php
class AiServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->container->singleton('ai.unified', UnifiedAiService::class);
    }
}
```

**特性:**
- 模块化服务注册
- 启动生命周期管理
- 延迟加载支持

### 3. 统一AI服务 (UnifiedAiService)
```php
// 位置: app/ai/services/UnifiedAiService.php
$aiService = new UnifiedAiService();
$response = $aiService->process('你好', ['provider' => 'deepseek']);
```

**特性:**
- 智能路由
- 自动缓存
- 性能监控
- 批量处理

### 4. 事件系统 (EventDispatcher)
```php
// 位置: app/ai/events/EventDispatcher.php
$events->listen('ai.request.completed', function($event, $payload) {
    // 处理事件
});
$events->dispatch('ai.request.completed', $data);
```

**特性:**
- 事件监听和分发
- 优先级支持
- 通配符匹配
- 异步处理支持

### 5. 缓存管理器 (CacheManager)
```php
// 位置: app/ai/cache/CacheManager.php
$cache->remember('ai_request_key', function() {
    return $aiService->chat($messages);
}, 3600);
```

**特性:**
- 智能缓存键生成
- TTL管理
- 缓存统计
- 多层缓存支持

### 6. 指标收集器 (MetricsCollector)
```php
// 位置: app/ai/monitoring/MetricsCollector.php
$metrics->counter('ai.requests.total', 1, ['provider' => 'deepseek']);
$metrics->timer('ai.requests.duration', $duration);
```

**特性:**
- 计数器、计时器、直方图
- 标签支持
- 统计分析
- 导出功能

### 7. 配置管理器 (ConfigManager)
```php
// 位置: app/ai/config/ConfigManager.php
ConfigManager::set('default_provider', 'deepseek');
$provider = ConfigManager::get('default_provider');
```

**特性:**
- 统一配置接口
- 环境变量支持
- 配置验证
- 热重载

## 使用指南

### 1. 基础使用

```php
use app\ai\bootstrap\AiApplication;

// 初始化应用
$app = new AiApplication();
$app->boot();

// 获取AI服务
$aiService = $app->make('ai.unified');

// 智能聊天
$response = $aiService->process('你好，请介绍一下你自己', [
    'provider' => 'deepseek',
    'cache' => true,
]);

echo $response->content;
```

### 2. 带记忆的对话

```php
// 第一次对话
$response1 = $aiService->process('我的名字是张三', [
    'session_id' => 'user_123',
    'type' => 'memory',
]);

// 第二次对话
$response2 = $aiService->process('你还记得我的名字吗？', [
    'session_id' => 'user_123',
    'type' => 'memory',
]);
```

### 3. 批量处理

```php
$inputs = [
    '1+1等于多少？',
    '今天天气怎么样？',
    '推荐一本好书',
];

$responses = $aiService->batchProcess($inputs, [
    'provider' => 'deepseek',
]);

foreach ($responses as $response) {
    echo $response->content . "\n";
}
```

### 4. 事件监听

```php
$events = $app->make('ai.events');

$events->listen('ai.request.completed', function($event, $payload) {
    echo "请求完成，耗时: {$payload['duration']}ms\n";
});
```

### 5. 自定义中间件

```php
$middleware = $app->make('ai.middleware');

$middleware->addMiddleware(function($request, $next) {
    // 请求前处理
    $response = $next($request);
    // 请求后处理
    return $response;
});
```

## 配置说明

### 1. 环境变量配置

```bash
# .env 文件
AI_DEFAULT_PROVIDER=deepseek
AI_CACHE_ENABLED=true
AI_CACHE_TTL=3600
AI_RATE_LIMIT_ENABLED=true
AI_RATE_LIMIT_RPM=60
AI_MEMORY_TYPE=mysql
DEEPSEEK_API_KEY=your_api_key
```

### 2. 配置文件

```php
// config/ai.php
return [
    'default_provider' => 'deepseek',
    'cache' => [
        'enabled' => true,
        'ttl' => 3600,
    ],
    'providers' => [
        'deepseek' => [
            'api_key' => env('DEEPSEEK_API_KEY'),
            'base_url' => 'https://api.deepseek.com',
            'model' => 'deepseek-chat',
        ],
    ],
];
```

## 性能优化

### 1. 缓存策略
- **请求缓存**: 相同请求自动缓存
- **记忆缓存**: 对话历史缓存
- **配置缓存**: 配置信息缓存

### 2. 智能路由
- **简单对话**: 路由到BasicAiService
- **复杂推理**: 路由到LangChainService
- **工具调用**: 自动选择合适的工具

### 3. 批量处理
- **并发处理**: 支持批量请求
- **错误隔离**: 单个请求失败不影响其他
- **资源优化**: 复用连接和配置

## 监控和调试

### 1. 指标监控
```php
$metrics = $app->make('ai.metrics');
$summary = $metrics->getSummary();
// 查看请求数量、响应时间等指标
```

### 2. 事件追踪
```php
$events->listen('ai.*', function($event, $payload) {
    // 监听所有AI相关事件
});
```

### 3. 日志分析
```php
// 自动记录请求和响应日志
// 支持结构化日志格式
```

## 扩展开发

### 1. 添加新的AI提供商
```php
// 1. 在配置中添加提供商信息
// 2. 实现相应的API调用逻辑
// 3. 注册到服务容器
```

### 2. 自定义事件监听器
```php
$events->listen('custom.event', CustomEventListener::class);
```

### 3. 添加中间件
```php
$middleware->addMiddleware(CustomMiddleware::class);
```

## 迁移指南

### 从旧版本迁移

1. **更新依赖**: 确保所有依赖项已更新
2. **配置迁移**: 将旧配置转换为新格式
3. **代码更新**: 使用新的API接口
4. **测试验证**: 确保功能正常工作

### 兼容性说明

- 保持向后兼容的API
- 渐进式迁移支持
- 详细的迁移文档

## 最佳实践

1. **使用依赖注入**: 通过容器获取服务
2. **事件驱动**: 使用事件解耦组件
3. **配置管理**: 统一管理配置信息
4. **性能监控**: 定期检查性能指标
5. **错误处理**: 完善的异常处理机制

## 总结

现代化重构后的AI系统具有以下优势:

- **更好的可维护性**: 清晰的架构和模块化设计
- **更高的可扩展性**: 易于添加新功能和提供商
- **更强的性能**: 智能缓存和路由优化
- **更好的可观测性**: 完整的监控和日志系统
- **更现代的开发体验**: 依赖注入、事件驱动等现代模式
