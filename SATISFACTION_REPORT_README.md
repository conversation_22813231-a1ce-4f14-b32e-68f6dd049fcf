# 满意度报表系统使用说明

## 概述

满意度报表系统提供了完整的客服满意度统计、分析和报告功能，包括实时查询、定时报告生成和预警机制。

## 功能模块

### 1. 满意度报表控制器 (SatisfactionReportController)

位置：`app/im/controller/customer/SatisfactionReportController.php`

#### 主要功能：
- **满意度概览统计** - `getSatisfactionOverview()`
- **满意度分布统计** - `getSatisfactionDistribution()`
- **满意度趋势分析** - `getSatisfactionTrend()`
- **客服满意度排名** - `getServiceRanking()`
- **满意度详细列表** - `getSatisfactionList()`
- **反馈文本分析** - `analyzeFeedbackText()`

#### API接口示例：

```bash
# 获取满意度概览
GET /api/satisfaction/overview?start_time=2024-01-01&end_time=2024-01-31&service_id=1

# 获取满意度分布
GET /api/satisfaction/distribution?start_time=2024-01-01&end_time=2024-01-31

# 获取满意度趋势
GET /api/satisfaction/trend?start_time=2024-01-01&end_time=2024-01-31&granularity=day

# 获取客服排名
GET /api/satisfaction/ranking?start_time=2024-01-01&end_time=2024-01-31&limit=20

# 反馈文本分析
GET /api/satisfaction/feedback-analysis?start_time=2024-01-01&end_time=2024-01-31&limit=100
```

### 2. 满意度定时任务控制器 (SatisfactionTaskController)

位置：`app/im/controller/customer/SatisfactionTaskController.php`

#### 主要功能：
- **日报生成** - `generateDailyReport()`
- **月报生成** - `generateMonthlyReport()`
- **预警检查** - `checkSatisfactionAlert()`
- **报告历史查询** - `getReportHistory()`
- **手动触发报告** - `triggerReport()`

### 3. 命令行任务 (SatisfactionTask)

位置：`app/command/SatisfactionTask.php`

#### 使用方法：

```bash
# 生成日报
php think satisfaction daily-report

# 生成月报
php think satisfaction monthly-report

# 执行预警检查
php think satisfaction alert-check

# 数据清理
php think satisfaction data-cleanup

# 强制执行（忽略缓存）
php think satisfaction daily-report --force

# 指定日期
php think satisfaction daily-report --date=2024-01-15
```

## 定时任务配置

### 1. 导入任务配置

执行SQL文件导入预定义的定时任务：

```bash
mysql -u username -p database_name < satisfaction_tasks.sql
```

### 2. 任务列表

| 任务名称 | 执行时间 | 命令 | 描述 |
|---------|---------|------|------|
| 满意度日报生成 | 每天凌晨1点 | `php think satisfaction daily-report` | 生成前一天的满意度统计报告 |
| 满意度月报生成 | 每月1号凌晨2点 | `php think satisfaction monthly-report` | 生成上月的满意度统计报告 |
| 满意度预警检查 | 每小时 | `php think satisfaction alert-check` | 检查满意度是否低于阈值 |
| 满意度数据清理 | 每周日凌晨3点 | `php think satisfaction data-cleanup` | 清理过期的缓存数据 |

### 3. Cron表达式说明

- `0 1 * * *` - 每天凌晨1点
- `0 2 1 * *` - 每月1号凌晨2点
- `0 * * * *` - 每小时
- `0 3 * * 0` - 每周日凌晨3点

## 数据库字段说明

### ad_chat_session 表相关字段：

- `satisfaction` - 满意度评分 (1-5分)
- `feedback` - 反馈文本内容
- `service_id` - 客服ID
- `user_id` - 用户ID
- `start_time` - 会话开始时间
- `end_time` - 会话结束时间
- `duration` - 会话时长

## 功能特性

### 1. 反馈文本分析

- **关键词提取**：自动识别正面和负面关键词
- **情感分析**：将反馈分类为正面、负面、中性
- **统计分析**：提供关键词频次和情感分布统计

### 2. 预警机制

- **阈值配置**：可配置满意度预警阈值（默认3.5分）
- **多维度检查**：支持整体和单个客服的满意度检查
- **通知方式**：支持邮件、webhook等多种通知方式

### 3. 报告生成

- **自动生成**：通过定时任务自动生成日报和月报
- **数据对比**：提供与历史数据的对比分析
- **趋势分析**：展示满意度变化趋势
- **排名统计**：客服满意度排名

### 4. 数据缓存

- **报告缓存**：生成的报告会缓存到Redis中
- **过期清理**：定期清理过期的缓存数据
- **性能优化**：减少重复计算，提高查询效率

## 配置说明

### 1. 预警阈值配置

在 `SatisfactionTaskController::checkSatisfactionAlert()` 方法中：

```php
$alertThreshold = 3.5; // 满意度低于3.5分预警
$minSessions = 10; // 最少会话数量
$checkPeriod = 24; // 检查最近24小时
```

### 2. 通知配置

在 `satisfaction_tasks.sql` 中配置通知方式和目标：

```sql
'notify_type' => '["email","webhook"]'
'notify_target' => '["<EMAIL>","https://your-webhook-url.com/alert"]'
```

### 3. 缓存配置

报告缓存键名格式：
- 日报：`satisfaction_daily_report_YYYYMMDD`
- 月报：`satisfaction_monthly_report_YYYYMM`

## 权限控制

- 继承 `BaseAdminController`，支持管理员权限验证
- 支持按客服组进行数据隔离
- 管理员可查看所有数据，普通用户只能查看所属组的数据

## 扩展说明

### 1. 添加新的统计维度

在控制器中添加新的方法，参考现有方法的实现模式。

### 2. 自定义通知方式

在 `SatisfactionTaskController` 中的通知方法中添加新的通知渠道。

### 3. 扩展反馈分析

在 `analyzeFeedbackText()` 方法中添加更复杂的文本分析算法。

## 注意事项

1. **数据库性能**：大量数据查询时注意添加适当的索引
2. **缓存管理**：定期清理过期缓存，避免内存占用过多
3. **通知配置**：确保邮件和webhook配置正确
4. **权限验证**：确保API接口有适当的权限控制
5. **日志记录**：重要操作都有日志记录，便于问题排查

## 故障排查

### 1. 定时任务不执行

- 检查 `sys_task` 表中任务状态是否为启用
- 检查cron表达式是否正确
- 查看任务执行日志

### 2. 报告生成失败

- 检查数据库连接
- 查看错误日志
- 确认数据表结构正确

### 3. 预警不触发

- 检查预警阈值配置
- 确认有足够的数据样本
- 查看通知配置是否正确

## 更新日志

- **v1.0.0** - 初始版本，包含基础的满意度统计和报告功能
- 完善了反馈文本分析功能
- 添加了完整的定时任务支持
- 实现了预警机制