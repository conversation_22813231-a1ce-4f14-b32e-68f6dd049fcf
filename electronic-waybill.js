// 模拟电子面单模板数据
const waybillTemplateData = [
  {
    id: 1,
    name: '顺丰标准模板',
    logisticsId: 1,
    logisticsName: '顺丰速运',
    senderName: '张三',
    senderPhone: '13800138000',
    senderAddress: '北京市海淀区西二旗街道XX号',
    printerType: 1,
    isDefault: true,
    status: 1,
    createTime: '2024-07-20 17:36:56'
  },
  {
    id: 2,
    name: '韵达标准模板',
    logisticsId: 2,
    logisticsName: '韵达快递',
    senderName: '李四',
    senderPhone: '13900139000',
    senderAddress: '上海市浦东新区陆家嘴XX号',
    printerType: 1,
    isDefault: false,
    status: 1,
    createTime: '2024-07-20 17:34:32'
  },
  {
    id: 3,
    name: '圆通标准模板',
    logisticsId: 3,
    logisticsName: '圆通速递',
    senderName: '王五',
    senderPhone: '13700137000',
    senderAddress: '广州市天河区体育西路XX号',
    printerType: 2,
    isDefault: false,
    status: 1,
    createTime: '2024-07-20 17:32:51'
  },
  {
    id: 4,
    name: '中通标准模板',
    logisticsId: 4,
    logisticsName: '中通快递',
    senderName: '赵六',
    senderPhone: '13600136000',
    senderAddress: '深圳市南山区科技园XX号',
    printerType: 3,
    isDefault: false,
    status: 0,
    createTime: '2024-07-20 17:31:33'
  },
  {
    id: 5,
    name: '申通标准模板',
    logisticsId: 5,
    logisticsName: '申通快递',
    senderName: '钱七',
    senderPhone: '13500135000',
    senderAddress: '杭州市西湖区文三路XX号',
    printerType: 1,
    isDefault: false,
    status: 0,
    createTime: '2024-07-20 17:30:53'
  }
]

// 模拟发件人信息数据
const senderData = [
  {
    id: 1,
    name: '张三',
    phone: '010-12345678',
    mobile: '13800138000',
    province: '110000',
    city: '110100',
    district: '110108',
    address: '西二旗街道XX号',
    postcode: '100085',
    isDefault: true,
    createTime: '2024-07-20 17:36:56'
  },
  {
    id: 2,
    name: '李四',
    phone: '021-87654321',
    mobile: '13900139000',
    province: '310000',
    city: '310100',
    district: '310115',
    address: '陆家嘴XX号',
    postcode: '200120',
    isDefault: false,
    createTime: '2024-07-20 17:34:32'
  },
  {
    id: 3,
    name: '王五',
    phone: '020-12345678',
    mobile: '13700137000',
    province: '440000',
    city: '440100',
    district: '440106',
    address: '体育西路XX号',
    postcode: '510000',
    isDefault: false,
    createTime: '2024-07-20 17:32:51'
  }
]

// 模拟打印配置数据
const printConfigData = {
  defaultPrinter: 'hp_laser',
  printCopies: 1,
  autoPrint: false,
  printTime: 1,
  printSize: 1,
  customWidth: 100,
  customHeight: 180,
  printContent: ['sender', 'receiver', 'goods', 'order', 'barcode'],
  printPreview: true
}

// 模拟API配置数据
const apiConfigData = {
  apiType: 1,
  appId: 'test_app_id',
  appKey: 'test_app_key',
  requestUrl: 'https://api.example.com/waybill',
  ipWhitelist: '***********,***********'
}

// 获取电子面单模板列表
export function getWaybillTemplateList(params) {
  const { name, logisticsId, status, page = 1, pageSize = 10 } = params
  
  let filteredData = [...waybillTemplateData]
  
  // 根据查询条件筛选
  if (name) {
    filteredData = filteredData.filter(item => item.name.includes(name))
  }
  if (logisticsId) {
    filteredData = filteredData.filter(item => item.logisticsId === Number(logisticsId))
  }
  if (status !== undefined && status !== '') {
    filteredData = filteredData.filter(item => item.status === Number(status))
  }
  
  // 计算分页数据
  const total = filteredData.length
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)
  
  return {
    code: 200,
    data: {
      list,
      total,
      page,
      pageSize
    }
  }
}

// 添加电子面单模板
export function addWaybillTemplate(data) {
  const newTemplate = {
    ...data,
    id: waybillTemplateData.length + 1,
    createTime: new Date().toLocaleString()
  }
  waybillTemplateData.push(newTemplate)
  return {
    code: 200,
    message: '添加成功',
    data: newTemplate
  }
}

// 更新电子面单模板
export function updateWaybillTemplate(data) {
  const index = waybillTemplateData.findIndex(item => item.id === data.id)
  if (index > -1) {
    waybillTemplateData[index] = {
      ...waybillTemplateData[index],
      ...data
    }
    return {
      code: 200,
      message: '更新成功',
      data: waybillTemplateData[index]
    }
  }
  return {
    code: 400,
    message: '模板不存在'
  }
}

// 删除电子面单模板
export function deleteWaybillTemplate(id) {
  const index = waybillTemplateData.findIndex(item => item.id === id)
  if (index > -1) {
    waybillTemplateData.splice(index, 1)
    return {
      code: 200,
      message: '删除成功'
    }
  }
  return {
    code: 400,
    message: '模板不存在'
  }
}

// 设置默认模板
export function setDefaultTemplate(id) {
  // 先将所有模板设置为非默认
  waybillTemplateData.forEach(item => {
    item.isDefault = false
  })
  
  // 将指定ID的模板设置为默认
  const index = waybillTemplateData.findIndex(item => item.id === id)
  if (index > -1) {
    waybillTemplateData[index].isDefault = true
    return {
      code: 200,
      message: '设置默认模板成功',
      data: waybillTemplateData[index]
    }
  }
  return {
    code: 400,
    message: '模板不存在'
  }
}

// 获取发件人列表
export function getSenderList(params) {
  const { name, mobile, isDefault, page = 1, pageSize = 10 } = params
  
  let filteredData = [...senderData]
  
  // 根据查询条件筛选
  if (name) {
    filteredData = filteredData.filter(item => item.name.includes(name))
  }
  if (mobile) {
    filteredData = filteredData.filter(item => item.mobile.includes(mobile))
  }
  if (isDefault !== undefined && isDefault !== '') {
    filteredData = filteredData.filter(item => item.isDefault === Boolean(isDefault))
  }
  
  // 计算分页数据
  const total = filteredData.length
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = filteredData.slice(start, end)
  
  return {
    code: 200,
    data: {
      list,
      total,
      page,
      pageSize
    }
  }
}

// 添加发件人
export function addSender(data) {
  const newSender = {
    ...data,
    id: senderData.length + 1,
    createTime: new Date().toLocaleString()
  }
  senderData.push(newSender)
  return {
    code: 200,
    message: '添加成功',
    data: newSender
  }
}

// 更新发件人
export function updateSender(data) {
  const index = senderData.findIndex(item => item.id === data.id)
  if (index > -1) {
    senderData[index] = {
      ...senderData[index],
      ...data
    }
    return {
      code: 200,
      message: '更新成功',
      data: senderData[index]
    }
  }
  return {
    code: 400,
    message: '发件人不存在'
  }
}

// 删除发件人
export function deleteSender(id) {
  const index = senderData.findIndex(item => item.id === id)
  if (index > -1) {
    senderData.splice(index, 1)
    return {
      code: 200,
      message: '删除成功'
    }
  }
  return {
    code: 400,
    message: '发件人不存在'
  }
}

// 设置默认发件人
export function setDefaultSender(id) {
  // 先将所有发件人设置为非默认
  senderData.forEach(item => {
    item.isDefault = false
  })
  
  // 将指定ID的发件人设置为默认
  const index = senderData.findIndex(item => item.id === id)
  if (index > -1) {
    senderData[index].isDefault = true
    return {
      code: 200,
      message: '设置默认发件人成功',
      data: senderData[index]
    }
  }
  return {
    code: 400,
    message: '发件人不存在'
  }
}

// 获取打印配置
export function getPrintConfig() {
  return {
    code: 200,
    data: printConfigData
  }
}

// 更新打印配置
export function updatePrintConfig(data) {
  Object.assign(printConfigData, data)
  return {
    code: 200,
    message: '更新成功',
    data: printConfigData
  }
}

// 获取API配置
export function getApiConfig() {
  return {
    code: 200,
    data: apiConfigData
  }
}

// 更新API配置
export function updateApiConfig(data) {
  Object.assign(apiConfigData, data)
  return {
    code: 200,
    message: '更新成功',
    data: apiConfigData
  }
}

// 测试API连接
export function testApiConnection() {
  return {
    code: 200,
    message: '连接测试成功'
  }
}

// 测试打印
export function testPrint() {
  return {
    code: 200,
    message: '测试打印成功'
  }
}