# VChat第三方平台集成配置示例
# 复制此文件内容到 .env 文件中

# ================================
# VChat第三方平台集成总开关
# ================================
VCHAT_THIRD_PARTY_ENABLED=true

# ================================
# 微信小程序配置
# ================================
WECHAT_MINIPROGRAM_ENABLED=true
WECHAT_MINIPROGRAM_APP_ID=your_miniprogram_app_id
WECHAT_MINIPROGRAM_SECRET=your_miniprogram_secret
WECHAT_MINIPROGRAM_TOKEN=your_miniprogram_token
WECHAT_MINIPROGRAM_AES_KEY=your_miniprogram_aes_key

# ================================
# 微信公众号配置
# ================================
WECHAT_OFFICIAL_ACCOUNT_ENABLED=true
WECHAT_OFFICIAL_ACCOUNT_APP_ID=your_official_account_app_id
WECHAT_OFFICIAL_ACCOUNT_SECRET=your_official_account_secret
WECHAT_OFFICIAL_ACCOUNT_TOKEN=your_official_account_token
WECHAT_OFFICIAL_ACCOUNT_AES_KEY=your_official_account_aes_key

# ================================
# 企业微信配置
# ================================
WECHAT_WORK_ENABLED=true
WECHAT_WORK_CORP_ID=your_work_corp_id
WECHAT_WORK_AGENT_ID=your_work_agent_id
WECHAT_WORK_SECRET=your_work_secret
WECHAT_WORK_TOKEN=your_work_token
WECHAT_WORK_AES_KEY=your_work_aes_key

# ================================
# QQ机器人配置（可选）
# ================================
QQ_BOT_ENABLED=false
QQ_BOT_APP_ID=your_qq_bot_app_id
QQ_BOT_APP_KEY=your_qq_bot_app_key
QQ_BOT_SECRET=your_qq_bot_secret

# ================================
# 钉钉机器人配置（可选）
# ================================
DINGTALK_BOT_ENABLED=false
DINGTALK_APP_KEY=your_dingtalk_app_key
DINGTALK_APP_SECRET=your_dingtalk_app_secret

# ================================
# 飞书机器人配置（可选）
# ================================
FEISHU_BOT_ENABLED=false
FEISHU_APP_ID=your_feishu_app_id
FEISHU_APP_SECRET=your_feishu_app_secret

# ================================
# 日志和安全配置
# ================================
VCHAT_THIRD_PARTY_LOG_ENABLED=true
VCHAT_THIRD_PARTY_LOG_LEVEL=info
VCHAT_THIRD_PARTY_IP_WHITELIST=127.0.0.1,***********/24

# ================================
# 配置说明
# ================================
# 1. 只需要配置您实际使用的平台
# 2. 将对应平台的 ENABLED 设置为 true
# 3. 填入从各平台获取的密钥信息
# 4. Webhook URL 配置：
#    微信小程序：    https://yourdomain.com/vchat/webhook/wechat/miniprogram
#    微信公众号：    https://yourdomain.com/vchat/webhook/wechat/officialaccount
#    企业微信：      https://yourdomain.com/vchat/webhook/wechat/work
#    QQ机器人：      https://yourdomain.com/vchat/webhook/qq/bot
#    钉钉机器人：    https://yourdomain.com/vchat/webhook/dingtalk/bot
#    飞书机器人：    https://yourdomain.com/vchat/webhook/feishu/bot
