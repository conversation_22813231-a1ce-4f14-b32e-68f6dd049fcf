-- 商品规格表
CREATE TABLE `ad_goods_spec` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '规格名称',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品规格表';

-- 商品规格值表
CREATE TABLE `ad_goods_spec_value` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '规格值ID',
  `spec_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '规格ID',
  `value` varchar(50) NOT NULL DEFAULT '' COMMENT '规格值',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_spec_id` (`spec_id`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品规格值表';

-- 商品SKU表
CREATE TABLE `ad_goods_sku` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'SKU ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `spec_value_ids` varchar(255) NOT NULL DEFAULT '' COMMENT '规格值ID组合',
  `spec_value_str` varchar(255) NOT NULL DEFAULT '' COMMENT '规格值组合描述',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '销售价格',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '原价',
  `cost_price` decimal(10,2) DEFAULT '0.00' COMMENT '成本价',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
  `code` varchar(100) DEFAULT '' COMMENT '商品编码',
  `weight` decimal(10,2) DEFAULT '0.00' COMMENT '重量(kg)',
  `volume` decimal(10,2) DEFAULT '0.00' COMMENT '体积(m³)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品SKU表';