<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
return [
    // 指令定义
    'commands' => [
        'import:region' => 'app\\command\\ImportRegion',
        'convert:region-json' => 'app\\command\\ConvertRegionToJson',
        'task:region' => 'app\\command\\Task'
    ],
    'output' => [
        'driver' => 'console',
        'ignore_errors' => true,
        'force' => true, // 强制使用默认值，避免检测终端
        'width' => 120,  // 默认宽度
        'height' => 40   // 默认高度
    ]
];
