<?php

// +----------------------------------------------------------------------
// | 验证码配置
// +----------------------------------------------------------------------

return [
    // 默认验证码类型
    'default_type' => env('CAPTCHA_TYPE', 'default'),
    
    // 验证码默认有效期（秒）
    'expire' => 300,
    
    // 验证码长度
    'code_length' => 4,
    
    // 验证码类型 numeric-纯数字, alpha-纯字母, alphanumeric-数字字母混合
    'code_type' => 'alphanumeric',
    
    // 是否区分大小写
    'case_sensitive' => false,
    
    // 验证后是否删除验证码
    'remove_after_verify' => true,
    
    // 图片宽度
    'width' => 120,
    
    // 图片高度
    'height' => 40,
    
    // 字体大小
    'font_size' => 5,
    
    // 干扰线数量
    'line_num' => 3,
    
    // 干扰点数量
    'pixel_num' => 50,
    
    // 验证码类型配置
    'types' => [
        // 默认图片验证码
        'default' => [
            // 可以在这里添加特定的配置
        ],
        
        // 数学计算验证码
        'math' => [
            // 数学计算验证码特定配置
        ],
        
        // 第三方验证码
        'third_party' => [
            // 默认第三方验证码提供商
            'provider' => 'tencent',
            
            // 腾讯云验证码配置
            'tencent' => [
                'app_id' => env('TENCENT_CAPTCHA_APP_ID', ''),
                'app_secret_key' => env('TENCENT_CAPTCHA_APP_SECRET_KEY', ''),
            ],
            
            // 阿里云验证码配置
            'aliyun' => [
                'access_key_id' => env('ALIYUN_CAPTCHA_ACCESS_KEY_ID', ''),
                'access_key_secret' => env('ALIYUN_CAPTCHA_ACCESS_KEY_SECRET', ''),
                'scene' => 'default',
            ],
        ],
    ],
];