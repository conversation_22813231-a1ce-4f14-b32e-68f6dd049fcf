<?php

/**
 * 第三方平台客服对接配置
 */
return [
    // 默认平台
    'default' => 'wechat.miniprogram',
    
    // 全局配置
    'timeout' => 30,
    'retry_times' => 3,
    'cache_ttl' => 3600,
    'log_enabled' => true,
    
    // 平台配置
    'platforms' => [
        // 微信小程序
        'wechat.miniprogram' => [
            'app_id' => env('WECHAT_MINIPROGRAM_APP_ID', ''),
            'secret' => env('WECHAT_MINIPROGRAM_SECRET', ''),
            'token' => env('WECHAT_MINIPROGRAM_TOKEN', ''),
            'aes_key' => env('WECHAT_MINIPROGRAM_AES_KEY', ''),
            'timeout' => 30,
            'retry_times' => 3,
            'log_level' => 'info',
            'cache_path' => runtime_path('cache/wechat/miniprogram'),
        ],
        
        // 微信公众号
        'wechat.officialaccount' => [
            'app_id' => env('WECHAT_OFFICIAL_ACCOUNT_APP_ID', ''),
            'secret' => env('WECHAT_OFFICIAL_ACCOUNT_SECRET', ''),
            'token' => env('WECHAT_OFFICIAL_ACCOUNT_TOKEN', ''),
            'aes_key' => env('WECHAT_OFFICIAL_ACCOUNT_AES_KEY', ''),
            'timeout' => 30,
            'retry_times' => 3,
            'log_level' => 'info',
            'cache_path' => runtime_path('cache/wechat/officialaccount'),
        ],
        
        // 企业微信
        'wechat.work' => [
            'corp_id' => env('WECHAT_WORK_CORP_ID', ''),
            'agent_id' => env('WECHAT_WORK_AGENT_ID', ''),
            'secret' => env('WECHAT_WORK_SECRET', ''),
            'token' => env('WECHAT_WORK_TOKEN', ''),
            'aes_key' => env('WECHAT_WORK_AES_KEY', ''),
            'timeout' => 30,
            'retry_times' => 3,
            'log_level' => 'info',
            'cache_path' => runtime_path('cache/wechat/work'),
        ],
        
        // QQ机器人（预留）
        'qq.bot' => [
            'app_id' => env('QQ_BOT_APP_ID', ''),
            'app_key' => env('QQ_BOT_APP_KEY', ''),
            'secret' => env('QQ_BOT_SECRET', ''),
            'timeout' => 30,
            'retry_times' => 3,
            'enabled' => false,
        ],
        
        // 钉钉机器人（预留）
        'dingtalk.bot' => [
            'app_key' => env('DINGTALK_APP_KEY', ''),
            'app_secret' => env('DINGTALK_APP_SECRET', ''),
            'timeout' => 30,
            'retry_times' => 3,
            'enabled' => false,
        ],
        
        // 飞书机器人（预留）
        'feishu.bot' => [
            'app_id' => env('FEISHU_APP_ID', ''),
            'app_secret' => env('FEISHU_APP_SECRET', ''),
            'timeout' => 30,
            'retry_times' => 3,
            'enabled' => false,
        ],
    ],
    
    // AI集成配置
    'ai' => [
        'enabled' => env('THIRD_OFFICIAL_LINK_AI_ENABLED', false),
        'auto_reply' => env('THIRD_OFFICIAL_LINK_AUTO_REPLY', false),
        'model' => env('THIRD_OFFICIAL_LINK_AI_MODEL', 'gpt-3.5-turbo'),
        'temperature' => 0.7,
        'max_tokens' => 800,
        'timeout' => 30,
    ],
    
    // 消息队列配置
    'queue' => [
        'enabled' => env('THIRD_OFFICIAL_LINK_QUEUE_ENABLED', false),
        'driver' => env('THIRD_OFFICIAL_LINK_QUEUE_DRIVER', 'redis'),
        'connection' => env('THIRD_OFFICIAL_LINK_QUEUE_CONNECTION', 'default'),
        'queue_name' => 'third_official_link',
        'retry_times' => 3,
        'retry_delay' => 60, // 秒
    ],
    
    // 数据库配置
    'database' => [
        'connection' => env('THIRD_OFFICIAL_LINK_DB_CONNECTION', 'mysql'),
        'table_prefix' => 'tol_', // third_official_link
        'tables' => [
            'sessions' => 'sessions',
            'messages' => 'messages',
            'users' => 'users',
            'transfers' => 'transfers',
            'statistics' => 'statistics',
        ],
    ],
    
    // 缓存配置
    'cache' => [
        'driver' => env('THIRD_OFFICIAL_LINK_CACHE_DRIVER', 'redis'),
        'connection' => env('THIRD_OFFICIAL_LINK_CACHE_CONNECTION', 'default'),
        'prefix' => 'tol:',
        'ttl' => [
            'session' => 3600,      // 会话缓存1小时
            'user_info' => 1800,    // 用户信息缓存30分钟
            'statistics' => 1800,   // 统计数据缓存30分钟
            'config' => 86400,      // 配置缓存24小时
        ],
    ],
    
    // 日志配置
    'log' => [
        'enabled' => env('THIRD_OFFICIAL_LINK_LOG_ENABLED', true),
        'level' => env('THIRD_OFFICIAL_LINK_LOG_LEVEL', 'info'),
        'channel' => 'third_official_link',
        'path' => runtime_path('logs/third_official_link'),
        'max_files' => 30,
        'file_permission' => 0644,
    ],
    
    // 安全配置
    'security' => [
        'signature_verification' => true,
        'ip_whitelist' => env('THIRD_OFFICIAL_LINK_IP_WHITELIST', ''),
        'rate_limit' => [
            'enabled' => true,
            'max_requests' => 1000,
            'window' => 3600, // 1小时
        ],
        'encryption' => [
            'enabled' => false,
            'algorithm' => 'AES-256-CBC',
            'key' => env('THIRD_OFFICIAL_LINK_ENCRYPT_KEY', ''),
        ],
    ],
    
    // 监控配置
    'monitoring' => [
        'enabled' => env('THIRD_OFFICIAL_LINK_MONITORING_ENABLED', false),
        'metrics' => [
            'message_count',
            'response_time',
            'error_rate',
            'user_activity',
        ],
        'alerts' => [
            'error_rate_threshold' => 0.05, // 5%
            'response_time_threshold' => 5000, // 5秒
        ],
    ],
    
    // Webhook配置
    'webhook' => [
        'enabled' => true,
        'timeout' => 10,
        'retry_times' => 3,
        'verify_ssl' => true,
        'endpoints' => [
            'wechat.miniprogram' => '/webhook/wechat/miniprogram',
            'wechat.officialaccount' => '/webhook/wechat/officialaccount',
            'wechat.work' => '/webhook/wechat/work',
        ],
    ],
    
    // 多租户配置
    'multi_tenant' => [
        'enabled' => env('THIRD_OFFICIAL_LINK_MULTI_TENANT', false),
        'tenant_field' => 'tenant_id',
        'default_tenant' => 'default',
    ],
    
    // 开发配置
    'debug' => [
        'enabled' => env('THIRD_OFFICIAL_LINK_DEBUG', false),
        'log_requests' => false,
        'log_responses' => false,
        'mock_responses' => false,
    ],
];
