<?php

/**
 * 知识库回复配置
 * 控制AI回复的格式和内容
 */

return [
    // 默认配置
    'default' => [
        'mode' => 'simple',                    // 回复模式：simple|formal|detailed
        'include_sources' => false,            // 是否包含来源信息
        'include_suggestions' => true,         // 是否包含相关建议
        'include_fallback_message' => false,   // 是否包含联系客服提示
        'max_content_length' => 300,          // 最大内容长度
        'confidence_threshold' => 0.3         // 置信度阈值
    ],

    // 预设配置方案
    'presets' => [
        // 简洁模式 - 推荐用于大多数场景
        'simple' => [
            'mode' => 'simple',
            'include_fallback_message' => false,
            'include_suggestions' => true,
            'max_content_length' => 200,
            'confidence_threshold' => 0.4
        ],

        // 客服模式 - 正式客服场景
        'customer_service' => [
            'mode' => 'formal',
            'include_fallback_message' => true,
            'include_suggestions' => true,
            'max_content_length' => 400,
            'confidence_threshold' => 0.3
        ],

        // 帮助文档模式 - 详细说明
        'documentation' => [
            'mode' => 'detailed',
            'include_sources' => true,
            'include_suggestions' => true,
            'max_content_length' => 600,
            'confidence_threshold' => 0.2
        ],

        // 极简模式 - 最简洁的回复
        'minimal' => [
            'mode' => 'simple',
            'include_fallback_message' => false,
            'include_suggestions' => false,
            'max_content_length' => 150,
            'confidence_threshold' => 0.5
        ]
    ],

    // 场景配置 - 根据不同场景自动选择配置
    'scenarios' => [
        // 网站客服聊天
        'website_chat' => 'simple',
        
        // 微信客服
        'wechat_service' => 'customer_service',
        
        // 帮助中心
        'help_center' => 'documentation',
        
        // 快速问答
        'quick_qa' => 'minimal',
        
        // 技术支持
        'technical_support' => 'documentation'
    ],

    // 动态调整规则
    'dynamic_rules' => [
        // 根据问题长度调整
        'question_length' => [
            'short' => ['max_length' => 20, 'mode' => 'simple'],
            'medium' => ['max_length' => 50, 'mode' => 'formal'],
            'long' => ['max_length' => 999, 'mode' => 'detailed']
        ],

        // 根据关键词调整
        'keywords' => [
            'complex' => ['详细', '具体', '完整', '全面', '步骤', '教程', '指南'],
            'simple' => ['是什么', '多少', '什么时候', '在哪里'],
            'urgent' => ['紧急', '急', '马上', '立即', '赶紧']
        ],

        // 根据置信度调整
        'confidence_levels' => [
            'high' => ['min' => 0.7, 'action' => 'use_simple'],
            'medium' => ['min' => 0.4, 'action' => 'use_formal'],
            'low' => ['min' => 0.0, 'action' => 'suggest_human']
        ]
    ],

    // 回复模板
    'templates' => [
        'simple' => [
            'system_prompt' => '你是一个智能助手，请直接、简洁地回答用户问题。',
            'requirements' => [
                '直接回答问题，不要添加多余的格式',
                '基于参考信息回答，如果没有相关信息就说不知道',
                '回答要简洁明了，不超过{max_length}字',
                '不要添加"根据知识库内容"等前缀',
                '不要添加联系客服的建议'
            ]
        ],

        'formal' => [
            'system_prompt' => '您好，我是智能客服助手，很高兴为您服务。',
            'requirements' => [
                '使用正式、礼貌的语言',
                '基于帮助信息提供准确回答',
                '如果信息不足，建议联系人工客服',
                '可以提供相关建议'
            ]
        ],

        'detailed' => [
            'system_prompt' => '你是一个专业的助手，需要提供详细、全面的回答。',
            'requirements' => [
                '直接回答用户问题',
                '提供相关的背景信息',
                '给出具体的操作步骤（如适用）',
                '提供相关建议或注意事项',
                '如果需要进一步帮助，建议联系专业支持'
            ]
        ]
    ],

    // 回复后处理
    'post_processing' => [
        // 内容过滤
        'content_filters' => [
            'remove_prefixes' => ['根据知识库内容', '基于帮助文档'],
            'remove_suffixes' => ['如需进一步帮助', '建议联系人工客服'],
            'replace_phrases' => [
                '根据知识库内容，' => '',
                '基于帮助文档，' => '',
                '若当前回答未解决您的问题，建议联系人工客服为您详细解答。' => ''
            ]
        ],

        // 长度控制
        'length_control' => [
            'max_length' => 500,
            'truncate_method' => 'smart', // smart|simple
            'add_ellipsis' => true
        ],

        // 格式优化
        'formatting' => [
            'remove_extra_newlines' => true,
            'trim_whitespace' => true,
            'normalize_punctuation' => true
        ]
    ],

    // 调试和监控
    'debug' => [
        'log_prompts' => false,          // 是否记录提示词
        'log_responses' => true,         // 是否记录回复
        'log_confidence' => true,        // 是否记录置信度
        'performance_tracking' => true   // 是否跟踪性能
    ]
];
