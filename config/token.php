<?php

// +----------------------------------------------------------------------
// | Token配置
// +----------------------------------------------------------------------

return [
    // 默认存储驱动，可选 'redis' 或 'mysql'
    'driver' => env('TOKEN_DRIVER', 'mysql'),
    
    // Redis配置
    'redis' => [
        // 缓存前缀
        'prefix' => 'token:',
        // 默认过期时间（秒）
        'expire' => 7200,
    ],
    
    // MySQL配置
    'mysql' => [
        // 数据表名
        'table' => 'sys_admin_token',
        // 刷新令牌表名
        'refresh_table' => 'sys_admin_refresh_token',
        // 默认过期时间（秒）
        'expire' => 7200,
    ],
    
    // 加密配置
    'encryption' => [
        // 是否启用数据加密存储
        'enabled' => env('TOKEN_ENCRYPTION_ENABLED', true),
        // 加密算法
        'method' => 'AES-256-CBC',
    ],
    
    // 安全配置
    'security' => [
        // 速率限制配置
        'rate_limit' => [
            // 每小时最大验证次数
            'max_attempts' => env('TOKEN_RATE_LIMIT_MAX', 60),
            // 时间窗口（秒）
            'time_window' => env('TOKEN_RATE_LIMIT_WINDOW', 3600),
        ],
        
        // 黑名单配置
        'blacklist' => [
            // 是否启用黑名单
            'enabled' => env('TOKEN_BLACKLIST_ENABLED', true),
            // 默认黑名单过期时间（秒）
            'default_expire' => 86400,
        ],
        
        // 日志配置
        'logging' => [
            // 是否启用详细日志
            'enabled' => env('TOKEN_LOGGING_ENABLED', true),
            // 是否记录敏感操作
            'log_sensitive' => env('TOKEN_LOG_SENSITIVE', false),
        ],
        
        // 可疑活动检测
        'suspicious_detection' => [
            // 是否启用
            'enabled' => env('TOKEN_SUSPICIOUS_DETECTION', true),
            // IP失败阈值
            'ip_failure_threshold' => 20,
            // 检测时间窗口（秒）
            'detection_window' => 3600,
        ],
    ],
    
    // JWT配置
    'jwt' => [
        // 签发者
        'issuer' => env('TOKEN_JWT_ISSUER', 'anchor_system'),
        // 接收者
        'audience' => env('TOKEN_JWT_AUDIENCE', 'anchor_client'),
        // 主题
        'subject' => env('TOKEN_JWT_SUBJECT', 'user_auth'),
        // 默认访问令牌过期时间（秒）
        'access_expire' => env('TOKEN_ACCESS_EXPIRE', 7200),
        // 默认刷新令牌过期时间（秒）
        'refresh_expire' => env('TOKEN_REFRESH_EXPIRE', 604800),
    ],
];