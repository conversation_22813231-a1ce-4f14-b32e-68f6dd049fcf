<?php

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => env('cache.driver', 'file'),

    // 缓存连接配置
    'stores' => [
        'file' => [
            'type'       => 'File',
            'path'       => app()->getRuntimePath() . 'cache', // 缓存目录
            'expire'     => 0,
            'prefix'     => env('file.prefix', 'goee:'),
            'serialize'  => true,
        ],
        'redis' => [
            // 驱动方式
            'type'       => 'redis',
            // 服务器地址
            'host'       => env('redis.host', '**************'),
            // 端口
            'port'       => env('redis.port', 51091),
            // 密码
            'password'   => env('redis.password', 'liugene0301'),
            // 数据库索引
            'select'     => env('redis.database', 0),
            // 超时时间
            'timeout'    => 0,
            // 缓存前缀
            'prefix'     => env('redis.prefix', 'goee:'),
            // 持久化连接
            'persistent' => false,
        ],
    ],
];
