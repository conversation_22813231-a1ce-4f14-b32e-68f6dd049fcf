<?php

return [
    // 是否启用自动回复系统
    'enabled' => true,

    // 自动回复策略类列表（按匹配优先级排序）
    'strategies' => [
        '\app\vchat\auto_reply\WelcomeReply',
        '\app\vchat\auto_reply\AiAutoReply',  // AI自动回复
        '\app\vchat\auto_reply\RobotReply',
        '\app\vchat\auto_reply\DatabaseReply',
        '\app\vchat\auto_reply\HelpReply'
    ],

    // 是否启用调试模式（记录更多日志）
    'debug' => false,

    // AI自动回复配置
    'ai' => [
        // 是否启用AI自动回复
        'enabled' => env('AI_AUTO_REPLY_ENABLED', true),

        // AI服务配置
        'provider' => env('AI_AUTO_REPLY_PROVIDER', 'deepseek'),
        'model' => env('AI_AUTO_REPLY_MODEL', 'deepseek-chat'),
        'temperature' => env('AI_AUTO_REPLY_TEMPERATURE', 0.7),
        'max_tokens' => env('AI_AUTO_REPLY_MAX_TOKENS', 500),
        'timeout' => env('AI_AUTO_REPLY_TIMEOUT', 10),

        // 缓存配置
        'cache_ttl' => env('AI_AUTO_REPLY_CACHE_TTL', 300), // 5分钟

        // 触发条件
        'triggers' => [
            // 触发关键词
            'keywords' => [
                '@AI', '@ai', '智能助手', '机器人', 'AI助手',
                '人工智能', 'chatbot', 'bot', '小助手'
            ],
            // 触发模式（正则表达式）
            'patterns' => [
                '/^AI[：:]/i',
                '/^智能助手[：:]/i',
                '/^机器人[：:]/i',
                '/^小助手[：:]/i'
            ],
            // 是否对所有消息都回复（谨慎开启）
            'always_reply' => env('AI_AUTO_REPLY_ALWAYS', false),
            // 消息长度限制
            'min_length' => 3,
            'max_length' => 1000,
        ],

        // 上下文和记忆配置
        'context' => [
            // 是否使用记忆功能
            'use_memory' => env('AI_AUTO_REPLY_USE_MEMORY', true),
            // 会话ID前缀
            'session_prefix' => 'ai_auto_reply_',
            // 最大历史记录数
            'max_history' => 10,
            // 会话清除时间（秒）
            'clear_after' => 3600, // 1小时
        ],

        // 提示词配置
        'prompts' => [
            'system' => '你是一个友好的智能客服助手，请用简洁、专业、温暖的语言回复用户的问题。回复长度控制在100字以内，语气要亲切自然。',
            'welcome' => '你好！我是AI智能助手，有什么可以帮助您的吗？😊',
            'error' => '抱歉，我暂时无法理解您的问题，请稍后再试或联系人工客服。',
            'busy' => '系统繁忙，请稍后再试。',
        ],

        // 内容过滤配置
        'filters' => [
            // 屏蔽关键词
            'blocked_keywords' => [
                // 可以在这里添加需要屏蔽的关键词
            ],
            // 屏蔽模式
            'blocked_patterns' => [
                // 可以在这里添加需要屏蔽的正则模式
            ],
            // 同一用户最小回复间隔（秒）
            'min_interval' => env('AI_AUTO_REPLY_MIN_INTERVAL', 5),
        ],

        // 高级配置
        'advanced' => [
            // 是否启用情感分析
            'emotion_analysis' => false,
            // 是否启用意图识别
            'intent_recognition' => false,
            // 是否启用多轮对话
            'multi_turn' => true,
            // 回复质量评分阈值
            'quality_threshold' => 0.7,
        ]
    ],
];