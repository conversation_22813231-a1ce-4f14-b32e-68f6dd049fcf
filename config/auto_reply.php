<?php

return array (
  'enabled' => true,
  'strategies' => 
  array (
    0 => '\\app\\vchat\\auto_reply\\WelcomeReply',
    1 => '\\app\\vchat\\auto_reply\\DatabaseReply',
    2 => '\\app\\vchat\\auto_reply\\AiAutoReply',
    3 => '\\app\\vchat\\auto_reply\\RobotReply',
    4 => '\\app\\vchat\\auto_reply\\HelpReply',
  ),
  'debug' => false,
  'ai' => 
  array (
    'enabled' => true,
    'provider' => 'deepseek',
    'model' => 'deepseek-chat',
    'temperature' => 0.7,
    'max_tokens' => 501,
    'timeout' => 10,
    'cache_ttl' => 300,
    'triggers' => 
    array (
      'keywords' => 
      array (
        0 => '@AI',
        1 => '@ai',
        2 => '智能助手',
        3 => '机器人',
        4 => 'AI助手',
        5 => '人工智能',
        6 => 'chatbot',
        7 => 'bot',
        8 => '小助手',
      ),
      'patterns' => 
      array (
        0 => '/^AI[：:]/i',
        1 => '/^智能助手[：:]/i',
        2 => '/^机器人[：:]/i',
        3 => '/^小助手[：:]/i',
      ),
      'always_reply' => false,
      'min_length' => 3,
      'max_length' => 1000,
    ),
    'context' => 
    array (
      'use_memory' => true,
      'session_prefix' => 'ai_auto_reply_',
      'max_history' => 10,
      'clear_after' => 3600,
    ),
    'prompts' => 
    array (
      'system' => '你是一个友好的智能客服助手，请用简洁、专业、温暖的语言回复用户的问题。回复长度控制在100字以内，语气要亲切自然。',
      'welcome' => '你好！我是AI智能助手，有什么可以帮助您的吗？😊',
      'error' => '抱歉，我暂时无法理解您的问题，请稍后再试或联系人工客服。',
      'busy' => '系统繁忙，请稍后再试。',
    ),
    'filters' => 
    array (
      'blocked_keywords' => 
      array (
      ),
      'blocked_patterns' => 
      array (
      ),
      'min_interval' => 5,
    ),
    'advanced' => 
    array (
      'emotion_analysis' => false,
      'intent_recognition' => false,
      'multi_turn' => true,
      'quality_threshold' => 0.7,
    ),
  ),
);
