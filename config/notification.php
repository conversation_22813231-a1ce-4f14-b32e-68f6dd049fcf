<?php

// +----------------------------------------------------------------------
// | 通知配置
// +----------------------------------------------------------------------

return [
    // 是否全局启用通知
    'enabled' => env('NOTIFICATION_ENABLED', true),
    
    // 登录通知配置
    'login' => [
        'enabled' => env('LOGIN_NOTIFICATION_ENABLED', true),
        'email' => [
            'enabled' => env('LOGIN_NOTIFICATION_EMAIL_ENABLED', true),
            'template' => env('LOGIN_NOTIFICATION_EMAIL_TEMPLATE', 'default'),
        ],
        'sms' => [
            'enabled' => env('LOGIN_NOTIFICATION_SMS_ENABLED', false),
            'template_code' => env('LOGIN_NOTIFICATION_SMS_TEMPLATE_CODE', ''),
        ],
        'wechat' => [
            'enabled' => env('LOGIN_NOTIFICATION_WECHAT_ENABLED', false),
            'template_id' => env('LOGIN_NOTIFICATION_WECHAT_TEMPLATE_ID', ''),
        ],
    ],
    
    // 注册通知配置
    'register' => [
        'enabled' => env('REGISTER_NOTIFICATION_ENABLED', true),
        'email' => [
            'enabled' => env('REGISTER_NOTIFICATION_EMAIL_ENABLED', true),
            'template' => env('REGISTER_NOTIFICATION_EMAIL_TEMPLATE', 'default'),
        ],
        'sms' => [
            'enabled' => env('REGISTER_NOTIFICATION_SMS_ENABLED', false),
            'template_code' => env('REGISTER_NOTIFICATION_SMS_TEMPLATE_CODE', ''),
        ],
        'wechat' => [
            'enabled' => env('REGISTER_NOTIFICATION_WECHAT_ENABLED', false),
            'template_id' => env('REGISTER_NOTIFICATION_WECHAT_TEMPLATE_ID', ''),
        ],
    ],
    
    // 通用通知配置（作为默认值）
    'common' => [
        'email' => [
            'enabled' => env('NOTIFICATION_EMAIL_ENABLED', true),
            'template' => env('NOTIFICATION_EMAIL_TEMPLATE', 'default'),
        ],
        'sms' => [
            'enabled' => env('NOTIFICATION_SMS_ENABLED', false),
            'template_code' => env('NOTIFICATION_SMS_TEMPLATE_CODE', ''),
        ],
        'wechat' => [
            'enabled' => env('NOTIFICATION_WECHAT_ENABLED', false),
            'template_id' => env('NOTIFICATION_WECHAT_TEMPLATE_ID', ''),
        ],
    ],
    
    // 系统通知配置
    'system' => [
        'email' => [
            'enabled' => env('SYSTEM_NOTIFICATION_EMAIL_ENABLED', true),
        ],
        'sms' => [
            'enabled' => env('SYSTEM_NOTIFICATION_SMS_ENABLED', false),
        ],
    ],
    
    // 订单通知配置
    'order' => [
        'email' => [
            'enabled' => env('ORDER_NOTIFICATION_EMAIL_ENABLED', true),
        ],
        'sms' => [
            'enabled' => env('ORDER_NOTIFICATION_SMS_ENABLED', true),
        ],
    ],
    
    // 活动通知配置
    'activity' => [
        'email' => [
            'enabled' => env('ACTIVITY_NOTIFICATION_EMAIL_ENABLED', true),
        ],
        'sms' => [
            'enabled' => env('ACTIVITY_NOTIFICATION_SMS_ENABLED', false),
        ],
    ],
    
    // 优惠券通知配置
    'coupon' => [
        'email' => [
            'enabled' => env('COUPON_NOTIFICATION_EMAIL_ENABLED', true),
        ],
        'sms' => [
            'enabled' => env('COUPON_NOTIFICATION_SMS_ENABLED', false),
        ],
    ],
    
];