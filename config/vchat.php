<?php

/**
 * VChat 聊天系统配置
 */
return [
    // 自动分配等待时间配置
    'waiting' => [
        // 基础等待时间（秒）
        'base_time' => 30,
        // 每个排队用户增加的等待时间（秒）
        'increment_per_user' => 15,
        // 最大等待时间（秒）
        'max_time' => 300,
        // 最小等待时间（秒）
        'min_time' => 10,
        // 客服负载权重因子（0-1，越高等待时间越长）
        'load_factor' => 0.5,
    ],

    // 会话超时配置
    'session_timeout' => [
        // 用户无响应超时时间（秒）
        'user_inactive_timeout' => 600,
        // 客服无响应超时时间（秒）
        'service_inactive_timeout' => 300,
        // 会话最大持续时间（秒）
        'max_duration' => 3600,
        // 超时检查间隔（秒）
        'check_interval' => 60,
        // 超时前警告时间（秒）
        'warning_before_timeout' => 60,
        // 时间检查间隔 (秒)
        'time_check_interval' => 30
    ],

    // 排队管理配置
    'queue' => [
        // 全局最大排队人数
        'max_global_queue' => 100,
        // 单个客服最大排队人数
        'max_per_service_queue' => 10,
        // 排队优先级策略：'fifo'(先进先出), 'priority'(优先级), 'vip'(VIP优先)
        'strategy' => 'fifo',
        // VIP用户排队权重
        'vip_weight' => 2,
        // 排队状态更新间隔（秒）
        'status_update_interval' => 5,
        // 自动分配检查间隔（秒）
        'auto_assign_interval' => 30,
        // 队列清理间隔（秒）
        'cleanup_interval' => 30,
        // 最大等待时间（秒）
        'max_wait_time' => 20,
    ],

    // 客服分配策略
    'assignment' => [
        // 分配策略：'round_robin'(轮询), 'least_load'(最少负载), 'random'(随机)
        'strategy' => 'least_load',
        // 负载均衡权重
        'load_balance_weight' => [
            'current_sessions' => 0.6,  // 当前会话数权重
            'queue_length' => 0.3,      // 排队长度权重
            'response_time' => 0.1,     // 响应时间权重
        ],
    ],

    // 通知配置
    'notification' => [
        // 是否启用排队通知
        'queue_notification' => true,
        // 是否启用超时警告
        'timeout_warning' => true,
        // 通知发送间隔（秒）
        'notification_interval' => 30,
    ],

    // Redis配置（用于队列管理）
    'redis' => [
        'host' => '127.0.0.1',
        'port' => 6379,
        'password' => '',
        'database' => 0,
        'prefix' => 'vchat:',
    ],

    // 日志配置
    'log' => [
        // 是否记录排队日志
        'queue_log' => true,
        // 是否记录超时日志
        'timeout_log' => true,
        // 日志级别
        'level' => 'info',
    ],

    // 第三方平台集成配置
    'third_party_integration' => [
        // 集成开关
        'enabled' => env('VCHAT_THIRD_PARTY_ENABLED', true),

        // 支持的第三方平台
        'platforms' => [
            'wechat.miniprogram' => [
                'name' => '微信小程序',
                'enabled' => env('WECHAT_MINIPROGRAM_ENABLED', true),
                'icon' => 'wechat',
                'color' => '#07C160',
                'user_id_prefix' => 'wx_mini_',
                'webhook_url' => '/vchat/webhook/wechat/miniprogram',
                // 第三方平台配置
                'app_id' => env('WECHAT_MINIPROGRAM_APP_ID', ''),
                'secret' => env('WECHAT_MINIPROGRAM_SECRET', ''),
                'token' => env('WECHAT_MINIPROGRAM_TOKEN', ''),
                'aes_key' => env('WECHAT_MINIPROGRAM_AES_KEY', ''),
            ],
            'wechat.officialaccount' => [
                'name' => '微信公众号',
                'enabled' => env('WECHAT_OFFICIAL_ACCOUNT_ENABLED', true),
                'icon' => 'wechat',
                'color' => '#07C160',
                'user_id_prefix' => 'wx_mp_',
                'webhook_url' => '/vchat/webhook/wechat/officialaccount',
                // 第三方平台配置
                'app_id' => env('WECHAT_OFFICIAL_ACCOUNT_APP_ID', ''),
                'secret' => env('WECHAT_OFFICIAL_ACCOUNT_SECRET', ''),
                'token' => env('WECHAT_OFFICIAL_ACCOUNT_TOKEN', ''),
                'aes_key' => env('WECHAT_OFFICIAL_ACCOUNT_AES_KEY', ''),
            ],
            'wechat.work' => [
                'name' => '企业微信',
                'enabled' => env('WECHAT_WORK_ENABLED', true),
                'icon' => 'work-wechat',
                'color' => '#1485EE',
                'user_id_prefix' => 'wx_work_',
                'webhook_url' => '/vchat/webhook/wechat/work',
                // 第三方平台配置
                'corp_id' => env('WECHAT_WORK_CORP_ID', ''),
                'agent_id' => env('WECHAT_WORK_AGENT_ID', ''),
                'secret' => env('WECHAT_WORK_SECRET', ''),
                'token' => env('WECHAT_WORK_TOKEN', ''),
                'aes_key' => env('WECHAT_WORK_AES_KEY', ''),
            ],
            'xiaohongshu' => [
                'name' => '小红书',
                'enabled' => env('XIAOHONGSHU_ENABLED', false),
                'icon' => 'xiaohongshu',
                'color' => '#FF2442',
                'user_id_prefix' => 'xhs_',
                'webhook_url' => '/vchat/webhook/xiaohongshu',
                // 第三方平台配置
                'app_id' => env('XIAOHONGSHU_APP_ID', ''),
                'app_secret' => env('XIAOHONGSHU_APP_SECRET', ''),
                'access_token' => env('XIAOHONGSHU_ACCESS_TOKEN', ''),
            ],
            'douyin' => [
                'name' => '抖音',
                'enabled' => env('DOUYIN_ENABLED', false),
                'icon' => 'douyin',
                'color' => '#000000',
                'user_id_prefix' => 'dy_',
                'webhook_url' => '/vchat/webhook/douyin',
                // 第三方平台配置
                'client_key' => env('DOUYIN_CLIENT_KEY', ''),
                'client_secret' => env('DOUYIN_CLIENT_SECRET', ''),
                'access_token' => env('DOUYIN_ACCESS_TOKEN', ''),
            ],
            'weibo' => [
                'name' => '微博',
                'enabled' => env('WEIBO_ENABLED', false),
                'icon' => 'weibo',
                'color' => '#E6162D',
                'user_id_prefix' => 'wb_',
                'webhook_url' => '/vchat/webhook/weibo',
                // 第三方平台配置
                'app_key' => env('WEIBO_APP_KEY', ''),
                'app_secret' => env('WEIBO_APP_SECRET', ''),
                'access_token' => env('WEIBO_ACCESS_TOKEN', ''),
            ],
            'kuaishou' => [
                'name' => '快手',
                'enabled' => env('KUAISHOU_ENABLED', false),
                'icon' => 'kuaishou',
                'color' => '#FF6600',
                'user_id_prefix' => 'ks_',
                'webhook_url' => '/vchat/webhook/kuaishou',
                // 第三方平台配置
                'app_id' => env('KUAISHOU_APP_ID', ''),
                'app_secret' => env('KUAISHOU_APP_SECRET', ''),
                'access_token' => env('KUAISHOU_ACCESS_TOKEN', ''),
            ]
        ],

        // 全局配置
        'timeout' => 30,
        'retry_times' => 3,

        // 缓存配置
        'cache' => [
            'user_mapping_ttl' => 86400 * 30, // 用户ID映射缓存30天
            'platform_status_ttl' => 300      // 平台状态缓存5分钟
        ],

        // 日志配置
        'log' => [
            'enabled' => env('VCHAT_THIRD_PARTY_LOG_ENABLED', true),
            'level' => env('VCHAT_THIRD_PARTY_LOG_LEVEL', 'info'),
            'path' => runtime_path('logs/vchat_third_party'),
        ],

        // 安全配置
        'security' => [
            'signature_verification' => true,
            'ip_whitelist' => env('VCHAT_THIRD_PARTY_IP_WHITELIST', ''),
        ]
    ],
];