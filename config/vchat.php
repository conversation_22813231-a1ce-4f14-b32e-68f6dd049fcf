<?php

/**
 * VChat 聊天系统配置
 */
return [
    // 自动分配等待时间配置
    'waiting' => [
        // 基础等待时间（秒）
        'base_time' => 30,
        // 每个排队用户增加的等待时间（秒）
        'increment_per_user' => 15,
        // 最大等待时间（秒）
        'max_time' => 300,
        // 最小等待时间（秒）
        'min_time' => 10,
        // 客服负载权重因子（0-1，越高等待时间越长）
        'load_factor' => 0.5,
    ],

    // 会话超时配置
    'session_timeout' => [
        // 用户无响应超时时间（秒）
        'user_inactive_timeout' => 600,
        // 客服无响应超时时间（秒）
        'service_inactive_timeout' => 300,
        // 会话最大持续时间（秒）
        'max_duration' => 3600,
        // 超时检查间隔（秒）
        'check_interval' => 60,
        // 超时前警告时间（秒）
        'warning_before_timeout' => 60,
        // 时间检查间隔 (秒)
        'time_check_interval' => 30
    ],

    // 排队管理配置
    'queue' => [
        // 全局最大排队人数
        'max_global_queue' => 100,
        // 单个客服最大排队人数
        'max_per_service_queue' => 10,
        // 排队优先级策略：'fifo'(先进先出), 'priority'(优先级), 'vip'(VIP优先)
        'strategy' => 'fifo',
        // VIP用户排队权重
        'vip_weight' => 2,
        // 排队状态更新间隔（秒）
        'status_update_interval' => 5,
        // 自动分配检查间隔（秒）
        'auto_assign_interval' => 30,
        // 队列清理间隔（秒）
        'cleanup_interval' => 30,
        // 最大等待时间（秒）
        'max_wait_time' => 20,
    ],

    // 客服分配策略
    'assignment' => [
        // 分配策略：'round_robin'(轮询), 'least_load'(最少负载), 'random'(随机)
        'strategy' => 'least_load',
        // 负载均衡权重
        'load_balance_weight' => [
            'current_sessions' => 0.6,  // 当前会话数权重
            'queue_length' => 0.3,      // 排队长度权重
            'response_time' => 0.1,     // 响应时间权重
        ],
    ],

    // 通知配置
    'notification' => [
        // 是否启用排队通知
        'queue_notification' => true,
        // 是否启用超时警告
        'timeout_warning' => true,
        // 通知发送间隔（秒）
        'notification_interval' => 30,
    ],

    // Redis配置（用于队列管理）
    'redis' => [
        'host' => '127.0.0.1',
        'port' => 6379,
        'password' => '',
        'database' => 0,
        'prefix' => 'vchat:',
    ],

    // 日志配置
    'log' => [
        // 是否记录排队日志
        'queue_log' => true,
        // 是否记录超时日志
        'timeout_log' => true,
        // 日志级别
        'level' => 'info',
    ],

    // 第三方平台集成配置
    'third_party_integration' => [
        // 集成开关
        'enabled' => env('VCHAT_THIRD_PARTY_ENABLED', true),

        // 支持的第三方平台
        'platforms' => [
            'wechat.miniprogram' => [
                'name' => '微信小程序',
                'enabled' => env('WECHAT_MINIPROGRAM_ENABLED', true),
                'icon' => 'wechat',
                'color' => '#07C160',
                'user_id_prefix' => 'wx_mini_',
                'webhook_url' => '/vchat/webhook/wechat/miniprogram',
                // 第三方平台配置
                'app_id' => env('WECHAT_MINIPROGRAM_APP_ID', ''),
                'secret' => env('WECHAT_MINIPROGRAM_SECRET', ''),
                'token' => env('WECHAT_MINIPROGRAM_TOKEN', ''),
                'aes_key' => env('WECHAT_MINIPROGRAM_AES_KEY', ''),
            ],
            'wechat.officialaccount' => [
                'name' => '微信公众号',
                'enabled' => env('WECHAT_OFFICIAL_ACCOUNT_ENABLED', true),
                'icon' => 'wechat',
                'color' => '#07C160',
                'user_id_prefix' => 'wx_mp_',
                'webhook_url' => '/vchat/webhook/wechat/officialaccount',
                // 第三方平台配置
                'app_id' => env('WECHAT_OFFICIAL_ACCOUNT_APP_ID', ''),
                'secret' => env('WECHAT_OFFICIAL_ACCOUNT_SECRET', ''),
                'token' => env('WECHAT_OFFICIAL_ACCOUNT_TOKEN', ''),
                'aes_key' => env('WECHAT_OFFICIAL_ACCOUNT_AES_KEY', ''),
            ],
            'wechat.work' => [
                'name' => '企业微信',
                'enabled' => env('WECHAT_WORK_ENABLED', true),
                'icon' => 'work-wechat',
                'color' => '#1485EE',
                'user_id_prefix' => 'wx_work_',
                'webhook_url' => '/vchat/webhook/wechat/work',
                // 第三方平台配置
                'corp_id' => env('WECHAT_WORK_CORP_ID', ''),
                'agent_id' => env('WECHAT_WORK_AGENT_ID', ''),
                'secret' => env('WECHAT_WORK_SECRET', ''),
                'token' => env('WECHAT_WORK_TOKEN', ''),
                'aes_key' => env('WECHAT_WORK_AES_KEY', ''),
            ],
            'qq.bot' => [
                'name' => 'QQ机器人',
                'enabled' => env('QQ_BOT_ENABLED', false),
                'icon' => 'qq',
                'color' => '#12B7F5',
                'user_id_prefix' => 'qq_',
                'webhook_url' => '/vchat/webhook/qq/bot',
                // 第三方平台配置
                'app_id' => env('QQ_BOT_APP_ID', ''),
                'app_key' => env('QQ_BOT_APP_KEY', ''),
                'secret' => env('QQ_BOT_SECRET', ''),
            ],
            'dingtalk.bot' => [
                'name' => '钉钉机器人',
                'enabled' => env('DINGTALK_BOT_ENABLED', false),
                'icon' => 'dingtalk',
                'color' => '#2EABFF',
                'user_id_prefix' => 'dt_',
                'webhook_url' => '/vchat/webhook/dingtalk/bot',
                // 第三方平台配置
                'app_key' => env('DINGTALK_APP_KEY', ''),
                'app_secret' => env('DINGTALK_APP_SECRET', ''),
            ],
            'feishu.bot' => [
                'name' => '飞书机器人',
                'enabled' => env('FEISHU_BOT_ENABLED', false),
                'icon' => 'feishu',
                'color' => '#00D6B9',
                'user_id_prefix' => 'fs_',
                'webhook_url' => '/vchat/webhook/feishu/bot',
                // 第三方平台配置
                'app_id' => env('FEISHU_APP_ID', ''),
                'app_secret' => env('FEISHU_APP_SECRET', ''),
            ]
        ],

        // 全局配置
        'timeout' => 30,
        'retry_times' => 3,

        // 缓存配置
        'cache' => [
            'user_mapping_ttl' => 86400 * 30, // 用户ID映射缓存30天
            'platform_status_ttl' => 300      // 平台状态缓存5分钟
        ],

        // 日志配置
        'log' => [
            'enabled' => env('VCHAT_THIRD_PARTY_LOG_ENABLED', true),
            'level' => env('VCHAT_THIRD_PARTY_LOG_LEVEL', 'info'),
            'path' => runtime_path('logs/vchat_third_party'),
        ],

        // 安全配置
        'security' => [
            'signature_verification' => true,
            'ip_whitelist' => env('VCHAT_THIRD_PARTY_IP_WHITELIST', ''),
        ]
    ],
];