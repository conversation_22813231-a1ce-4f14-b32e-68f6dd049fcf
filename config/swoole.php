<?php

use Swoole\Table;

return [
    'http' => [
        'enable' => true,
        'host' => '0.0.0.0',
        'port' => 9502,
        'worker_num' => swoole_cpu_num(),
//        'worker_num' => 4,
        'options' => [
            'pid_file' => runtime_path() . 'swoole.pid',
            'log_file' => runtime_path('swoole.log'),
            'log_level' => SWOOLE_LOG_DEBUG,  // 确保开启DEBUG级别
            'trace_flags' => SWOOLE_TRACE_ALL,
           'buffer_output_size' => 2 * 1024 * 1024,
            // 'buffer_output_size' => 0, // 完全禁用缓冲
            'max_request' => 20000,
            'max_conn' => 20000,
            'heartbeat_check_interval' => 30,
            'heartbeat_idle_time' => 60,
            'enable_coroutine' => true,
            'task_enable_coroutine' => true,
            'task_worker_num' => 4,
            'task_max_request' => 5000,
            'enable_static_handler' => true,
            'document_root' => root_path('public'),
            'static_handler_locations' => ['/static', '/uploads', '/favicon.ico'],
            'dispatch_mode' => 2,
            'enable_reuse_port' => true,
            'enable_dispatch' => true,
        ],
        'onRequest' => function ($req, $resp) {
            $resp->header('Access-Control-Allow-Origin', '*');
        },
    ],
    'websocket' => [
        'enable' => true,
        'route' => true,         // 启用路由调度
        'handler' => \think\swoole\websocket\socketio\Handler::class,  // WebSocket 处理器
        'socketio' => [
            'version' => 4, // 强制使用 Socket.IO v4（兼容 v3）
            'ping_interval' => 25000,
            'ping_timeout' => 60000,
        ],
        'ssl' => false,
        'room' => [
            'type' => 'table',
            'table' => [
                'table_name' => 'rooms',  // 必须与上方表名一致
                'room_rows' => 8192,      // 房间数量上限
                'client_rows' => 8192,     // 客户端数量上限
                // 移除 room_size 和 client_size
            ],
        ],
        'listen' => [
        ],
        'subscribe' => [],
    ],
    'rpc' => [
        'server' => [
            'enable' => false,
            'host' => '0.0.0.0',
            'port' => 9000,
            'worker_num' => swoole_cpu_num(),
            'services' => [],
        ],
        'client' => [],
    ],
    'queue' => [
        'enable' => false,
        'workers' => [
            'default' => [
                'delay' => 0,
                'sleep' => 3,
                'tries' => 0,
                'timeout' => 60,
                'worker_num' => 1,
            ],
        ],
    ],
    'hot_update' => [
        'enable' => env('APP_DEBUG', true),
        'name' => ['.php'],
        'include' => [app_path()],
        'exclude' => [],
    ],
    'pool' => [
        'db' => [
            'enable' => true,
            'max_active' => 3,
            'max_wait_time' => 5,
        ],
        'cache' => [
            'enable' => true,
            'max_active' => 3,
            'max_wait_time' => 5,
        ],
        'redis' => [
            'enable' => false,
            'max_active' => 3,
            'max_wait_time' => 5,
        ],
    ],
    'tables' => [
        'fd' => [
            'size' => 8192,
            'columns' => [
                ['name' => 'fd', 'type' => Table::TYPE_INT, 'size' => 4],
                ['name' => 'type', 'type' => Table::TYPE_STRING, 'size' => 32],  // 'user_' or 'service_' prefix
                ['name' => 'user_id', 'type' => Table::TYPE_STRING, 'size' => 64],
            ],
        ],
        'rooms' => [  // 表名必须为 rooms
            'size' => 8192,  // 表容量
            'columns' => [
                ['name' => 'fd', 'type' => \Swoole\Table::TYPE_INT, 'size' => 4],  // 必需字段
                ['name' => 'room', 'type' => \Swoole\Table::TYPE_STRING, 'size' => 100],  // 必需字段
            ],
        ],
        // 可以移除 room_fd_list 表，除非您有特殊用途
    ],
    'concretes' => [],
    'resetters' => [],
    'instances' => [],
    'services' => [],
];