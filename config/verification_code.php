<?php

// +----------------------------------------------------------------------
// | 验证码配置
// +----------------------------------------------------------------------

return [
    // 默认存储驱动
    'default_storage' => env('VERIFICATION_CODE_STORAGE', 'mysql'),
    
    // 验证码默认有效期（秒）
    'code_expire' => 300,
    
    // 验证码长度
    'code_length' => 6,

    // 验证最大次数
    'max_attempts' => 5,
    
    // 验证码类型 numeric-纯数字, alpha-纯字母, alphanumeric-数字字母混合
    'code_type' => 'numeric',
    
    // 存储配置
    'storage' => [
        // 默认存储（使用系统默认缓存）
        'default' => [
            // 可以在这里添加特定的配置
        ],
        
        // Redis存储
        'redis' => [
            // 使用的缓存存储
            'store' => 'redis',
            // 键前缀
            'prefix' => 'verification_code:',
        ],
        
        // MySQL存储
        'mysql' => [
            // 表名
            'table' => 'sys_sms_code',
            // 数据库连接
            'connection' => null, // 为null时使用默认数据库连接
        ],
    ],
];