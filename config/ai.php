<?php

/**
 * AI服务配置文件
 */
return [
    // 默认AI服务提供商
    'default_provider' => env('AI_DEFAULT_PROVIDER', 'deepseek'),
    
    // 主要服务提供商
    'primary_provider' => env('AI_PRIMARY_PROVIDER', 'deepseek'),
    
    // 备用服务提供商列表（用于故障转移）
    'fallback_providers' => [
        // 'openai',
        // 'claude',
    ],
    
    // 启用故障转移
    'failover_enabled' => env('AI_FAILOVER_ENABLED', true),
    
    // 启用负载均衡
    'load_balancing_enabled' => env('AI_LOAD_BALANCING_ENABLED', false),
    
    // 各服务提供商配置
    'providers' => [
        // DeepSeek配置
        'deepseek' => [
            'api_key' => env('DEEPSEEK_API_KEY', '***********************************'),
            'base_url' => env('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
            'default_model' => env('DEEPSEEK_DEFAULT_MODEL', 'deepseek-chat'),
            'timeout' => env('DEEPSEEK_TIMEOUT', 30),
            'max_retries' => env('DEEPSEEK_MAX_RETRIES', 3),
            'extra' => [
                // DeepSeek特有的配置参数
            ]
        ],
        
        // OpenAI配置（示例）
        'openai' => [
            'api_key' => env('OPENAI_API_KEY', ''),
            'base_url' => env('OPENAI_BASE_URL', 'https://api.openai.com/v1'),
            'default_model' => env('OPENAI_DEFAULT_MODEL', 'gpt-3.5-turbo'),
            'timeout' => env('OPENAI_TIMEOUT', 30),
            'max_retries' => env('OPENAI_MAX_RETRIES', 3),
            'extra' => [
                'organization' => env('OPENAI_ORGANIZATION', ''),
            ]
        ],
        
        // Claude配置（示例）
        'claude' => [
            'api_key' => env('CLAUDE_API_KEY', ''),
            'base_url' => env('CLAUDE_BASE_URL', 'https://api.anthropic.com'),
            'default_model' => env('CLAUDE_DEFAULT_MODEL', 'claude-3-sonnet-20240229'),
            'timeout' => env('CLAUDE_TIMEOUT', 30),
            'max_retries' => env('CLAUDE_MAX_RETRIES', 3),
            'extra' => [
                'version' => env('CLAUDE_VERSION', '2023-06-01'),
            ]
        ],
    ],
    
    // 默认请求参数
    'default_options' => [
        'temperature' => 0.7,
        'max_tokens' => 2048,
        'top_p' => 1.0,
        'frequency_penalty' => 0.0,
        'presence_penalty' => 0.0,
    ],
    
    // 记忆存储器配置
    'memory_type' => env('AI_MEMORY_TYPE', 'buffer'), // 可选值: 'buffer', 'mysql'
    'memory_environment' => env('AI_MEMORY_ENVIRONMENT', 'auto'), // MySQL环境: 'development', 'production', 'testing', 'auto'
    
    // MySQL存储器特定配置
    'mysql_memory' => [
        // 是否启用自动清理过期会话
        'auto_cleanup' => env('AI_MYSQL_AUTO_CLEANUP', true),
        
        // 自动清理的天数阈值
        'cleanup_days' => env('AI_MYSQL_CLEANUP_DAYS', 30),
        
        // 最大历史记录长度
        'max_history_length' => env('AI_MYSQL_MAX_HISTORY', 100),
        
        // 是否启用批量操作
        'enable_batch_operations' => env('AI_MYSQL_BATCH_OPERATIONS', true),
        
        // 批量操作大小
        'batch_size' => env('AI_MYSQL_BATCH_SIZE', 50),
        
        // 是否启用查询缓存
        'enable_cache' => env('AI_MYSQL_ENABLE_CACHE', true),
        
        // 缓存TTL（秒）
        'cache_ttl' => env('AI_MYSQL_CACHE_TTL', 300),
    ],
    
    // 提示模板配置
    'templates' => [
        // 模板目录
        'directory' => app_path('ai/prompts/templates'),
        
        // 是否启用模板缓存
        'enable_cache' => env('AI_TEMPLATE_CACHE', true),
        
        // 模板缓存TTL（秒）
        'cache_ttl' => env('AI_TEMPLATE_CACHE_TTL', 600),
        
        // 是否自动初始化预定义模板
        'auto_initialize' => env('AI_TEMPLATE_AUTO_INIT', true),
    ],
    
    // 日志配置
    'logging' => [
        'enabled' => env('AI_LOGGING_ENABLED', true),
        'level' => env('AI_LOGGING_LEVEL', 'info'), // debug, info, warning, error
        'log_requests' => env('AI_LOG_REQUESTS', true),
        'log_responses' => env('AI_LOG_RESPONSES', false), // 注意：可能包含敏感信息
    ],
    
    // 缓存配置
    'cache' => [
        'enabled' => env('AI_CACHE_ENABLED', true),
        'ttl' => env('AI_CACHE_TTL', 3600), // 缓存时间（秒）
        'prefix' => env('AI_CACHE_PREFIX', 'ai_service_'),
    ],
    
    // 限流配置
    'rate_limit' => [
        'enabled' => env('AI_RATE_LIMIT_ENABLED', true),
        'requests_per_minute' => env('AI_RATE_LIMIT_RPM', 60),
        'requests_per_hour' => env('AI_RATE_LIMIT_RPH', 1000),
    ],
    
    // 安全配置
    'security' => [
        // 内容过滤
        'content_filter' => [
            'enabled' => env('AI_CONTENT_FILTER_ENABLED', true),
            'blocked_keywords' => [
                // 在这里添加需要过滤的关键词
            ],
        ],
        
        // 请求大小限制
        'max_message_length' => env('AI_MAX_MESSAGE_LENGTH', 10000),
        'max_messages_count' => env('AI_MAX_MESSAGES_COUNT', 50),
    ],
];