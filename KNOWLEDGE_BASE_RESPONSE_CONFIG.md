# 知识库回复格式配置指南

## 🎯 问题描述

原始的知识库回复格式过于固定，总是包含：
```
根据知识库内容，您提到的"测试"相关内容如下：
**测试...**
如需进一步帮助或了解其他信息（如购买流程），可随时告知。
若当前回答未解决您的问题，建议联系人工客服为您详细解答。
```

## 🔧 解决方案

现在支持3种回复模式，可以灵活控制输出格式：

### 1. 简洁模式 (simple) - 推荐
**特点**：直接回答，无多余格式
```php
$knowledgeBase = new KnowledgeBaseService();
$knowledgeBase->setResponseMode('simple');

$result = $knowledgeBase->ask('如何测试功能？');
// 输出：直接说明测试步骤，简洁明了
```

**回复示例**：
```
测试功能的步骤如下：
1. 登录系统
2. 进入测试页面
3. 点击测试按钮
4. 查看测试结果
```

### 2. 正式模式 (formal)
**特点**：礼貌正式，适合客服场景
```php
$knowledgeBase->setResponseMode('formal');
```

**回复示例**：
```
您好，关于测试功能的问题，我为您提供以下信息：

根据帮助文档，测试功能的操作步骤是...

如需进一步帮助，建议联系人工客服。
```

### 3. 详细模式 (detailed)
**特点**：全面详细，包含背景和建议
```php
$knowledgeBase->setResponseMode('detailed');
```

## 📋 完整配置选项

```php
$config = [
    'mode' => 'simple',              // 回复模式：simple|formal|detailed
    'include_sources' => false,      // 是否包含来源信息
    'include_suggestions' => true,   // 是否包含相关建议
    'include_fallback_message' => false, // 是否包含联系客服提示
    'max_content_length' => 500      // 最大内容长度
];

$knowledgeBase = new KnowledgeBaseService($config);
```

## 🚀 使用示例

### 示例1：简洁回复（推荐）
```php
use app\ai\services\KnowledgeBaseService;

// 创建知识库服务，使用简洁模式
$kb = new KnowledgeBaseService([
    'mode' => 'simple',
    'include_fallback_message' => false
]);

$result = $kb->ask('如何充值？');
echo $result['content'];
// 输出：充值方法：1. 点击充值按钮 2. 选择金额 3. 选择支付方式 4. 完成支付
```

### 示例2：动态切换模式
```php
$kb = new KnowledgeBaseService();

// 对于简单问题，使用简洁模式
$kb->setResponseMode('simple');
$simpleResult = $kb->ask('营业时间？');

// 对于复杂问题，使用详细模式
$kb->setResponseMode('detailed');
$detailedResult = $kb->ask('如何申请退款？');
```

### 示例3：自定义配置
```php
$kb = new KnowledgeBaseService();

$kb->setResponseConfig([
    'mode' => 'simple',
    'max_content_length' => 200,
    'include_fallback_message' => false
]);

$result = $kb->ask('用户问题');
```

## 🎨 回复模式对比

| 模式 | 特点 | 适用场景 | 示例输出 |
|------|------|----------|----------|
| **simple** | 直接简洁 | 大多数情况 | "充值步骤：1. 点击充值 2. 选择金额..." |
| **formal** | 礼貌正式 | 正式客服 | "您好，关于充值问题，操作步骤如下..." |
| **detailed** | 详细全面 | 复杂问题 | "充值功能说明：[背景] [步骤] [注意事项] [建议]" |

## 🔧 在现有系统中应用

### 修改自动回复配置
```php
// 在 HelpReply.php 或相关文件中
$knowledgeBase = new KnowledgeBaseService([
    'mode' => 'simple',  // 改为简洁模式
    'include_fallback_message' => false  // 不显示联系客服提示
]);
```

### 根据问题类型动态选择
```php
public function handleMessage($message) {
    $kb = new KnowledgeBaseService();
    
    // 根据问题复杂度选择模式
    if (strlen($message) < 20) {
        $kb->setResponseMode('simple');
    } else {
        $kb->setResponseMode('formal');
    }
    
    return $kb->ask($message);
}
```

## 💡 最佳实践

### 1. 推荐设置（大多数场景）
```php
$config = [
    'mode' => 'simple',
    'include_fallback_message' => false,
    'max_content_length' => 300
];
```

### 2. 客服场景设置
```php
$config = [
    'mode' => 'formal',
    'include_fallback_message' => true,
    'include_suggestions' => true
];
```

### 3. 帮助文档场景
```php
$config = [
    'mode' => 'detailed',
    'max_content_length' => 800
];
```

## 🎯 解决原始问题

**原始问题**：固定格式 "根据知识库内容..." 和强制的客服提示

**解决方案**：
1. 使用 `simple` 模式 - 直接回答，无固定前缀
2. 设置 `include_fallback_message: false` - 不显示客服提示
3. 控制 `max_content_length` - 限制回复长度

**修改后的效果**：
- ❌ 原来：根据知识库内容，您提到的"测试"相关内容如下：**测试...** 如需进一步帮助...
- ✅ 现在：测试功能位于系统设置页面，点击"功能测试"按钮即可开始测试。

## 📝 配置文件示例

创建 `config/knowledge_base.php`：
```php
<?php
return [
    'response' => [
        'mode' => 'simple',
        'include_sources' => false,
        'include_suggestions' => true,
        'include_fallback_message' => false,
        'max_content_length' => 300
    ]
];
```

然后在代码中使用：
```php
$config = config('knowledge_base.response');
$kb = new KnowledgeBaseService($config);
```

## 🎉 总结

通过这个配置系统，你可以：
- ✅ 完全控制回复格式
- ✅ 去除固定的前缀和后缀
- ✅ 根据场景选择合适的回复模式
- ✅ 动态调整回复风格
- ✅ 提供更自然的用户体验
