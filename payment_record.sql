-- 支付记录表
CREATE TABLE `payment_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `record_no` varchar(32) NOT NULL COMMENT '记录编号',
  `type` tinyint(1) NOT NULL COMMENT '类型：1-收入，2-支出',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `category` tinyint(1) NOT NULL COMMENT '分类：1-销售收入，2-服务收入，3-退款，4-其他收入，5-采购支出，6-工资支出，7-租金支出，8-其他支出',
  `pay_method` tinyint(1) NOT NULL COMMENT '支付方式：1-微信支付，2-支付宝，3-银行转账，4-现金',
  `status` tinyint(1) NOT NULL DEFAULT '2' COMMENT '状态：1-已确认，2-待确认，3-已取消',
  `operator_name` varchar(50) NOT NULL COMMENT '经办人姓名',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_no` (`record_no`),
  KEY `idx_type` (`type`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';