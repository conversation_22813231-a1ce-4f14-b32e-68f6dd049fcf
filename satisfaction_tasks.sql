-- 满意度报表定时任务配置
-- 插入到 sys_task 表中

-- 1. 满意度日报生成任务（每天凌晨1点执行）
INSERT INTO `sys_task` (`name`, `command`, `rule`, `status`, `concurrent`, `retry_times`, `retry_interval`, `notify_type`, `notify_target`, `description`, `createtime`, `updatetime`) VALUES
('满意度日报生成', 'php think satisfaction:daily-report', '0 1 * * *', 1, 0, 3, 300, '["email"]', '["<EMAIL>"]', '每日凌晨1点生成前一天的满意度统计报告', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 2. 满意度月报生成任务（每月1号凌晨2点执行）
INSERT INTO `sys_task` (`name`, `command`, `rule`, `status`, `concurrent`, `retry_times`, `retry_interval`, `notify_type`, `notify_target`, `description`, `createtime`, `updatetime`) VALUES
('满意度月报生成', 'php think satisfaction:monthly-report', '0 2 1 * *', 1, 0, 3, 600, '["email"]', '["<EMAIL>"]', '每月1号凌晨2点生成上月的满意度统计报告', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 3. 满意度预警检查任务（每小时执行一次）
INSERT INTO `sys_task` (`name`, `command`, `rule`, `status`, `concurrent`, `retry_times`, `retry_interval`, `notify_type`, `notify_target`, `description`, `createtime`, `updatetime`) VALUES
('满意度预警检查', 'php think satisfaction:alert-check', '0 * * * *', 1, 0, 2, 180, '["email","webhook"]', '["<EMAIL>","https://your-webhook-url.com/alert"]', '每小时检查满意度是否低于阈值并发送预警', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 4. 满意度数据清理任务（每周日凌晨3点执行）
INSERT INTO `sys_task` (`name`, `command`, `rule`, `status`, `concurrent`, `retry_times`, `retry_interval`, `notify_type`, `notify_target`, `description`, `createtime`, `updatetime`) VALUES
('满意度数据清理', 'php think satisfaction:data-cleanup', '0 3 * * 0', 1, 0, 1, 0, '[]', '[]', '每周日凌晨3点清理过期的满意度缓存数据', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 注意事项：
-- 1. rule 字段使用 cron 表达式格式：分 时 日 月 周
-- 2. status: 1=启用, 0=禁用
-- 3. concurrent: 0=不允许并发, 1=允许并发
-- 4. retry_times: 失败重试次数
-- 5. retry_interval: 重试间隔（秒）
-- 6. notify_type: 通知方式，JSON数组格式
-- 7. notify_target: 通知目标，JSON数组格式
-- 8. 请根据实际需求修改邮箱地址和webhook地址