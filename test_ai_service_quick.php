<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\ai\services\BasicAiService;
use app\ai\config\ConfigManager;
use app\ai\utils\Logger;

echo "🔧 AI服务快速测试\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    // 设置调试模式
    ConfigManager::set('debug', true);
    
    // 设置DeepSeek配置（请替换为实际的API密钥）
    ConfigManager::set('providers.deepseek.api_key', 'sk-your-api-key-here');
    ConfigManager::set('providers.deepseek.base_url', 'https://api.deepseek.com');
    ConfigManager::set('providers.deepseek.model', 'deepseek-chat');
    ConfigManager::set('providers.deepseek.timeout', 15); // 15秒超时
    
    // 检查API密钥
    $apiKey = ConfigManager::get('providers.deepseek.api_key');
    if ($apiKey === 'sk-your-api-key-here') {
        echo "❌ 请先设置DeepSeek API密钥\n";
        echo "   修改脚本中的 api_key 配置\n\n";
        exit(1);
    }
    
    echo "✅ 配置检查通过\n";
    echo "🔑 API密钥: " . substr($apiKey, 0, 10) . "...\n";
    echo "🌐 API地址: " . ConfigManager::get('providers.deepseek.base_url') . "\n";
    echo "⏱️  超时时间: " . ConfigManager::get('providers.deepseek.timeout') . "秒\n\n";
    
    // 创建AI服务
    echo "🤖 创建AI服务实例...\n";
    $aiService = new BasicAiService();
    
    echo "✅ AI服务创建成功\n\n";
    
    // 测试简单对话
    echo "💬 测试AI对话...\n";
    echo "📤 发送消息: 'Hello, please respond with just OK'\n\n";
    
    $startTime = microtime(true);
    
    try {
        $response = $aiService->chat('Hello, please respond with just OK', [
            'max_tokens' => 10,
            'temperature' => 0.1
        ], 'deepseek');
        
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        echo "✅ AI响应成功!\n";
        echo "📥 响应内容: {$response}\n";
        echo "⏱️  响应时间: {$duration}ms\n";
        
    } catch (Exception $e) {
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        echo "❌ AI响应失败!\n";
        echo "⏱️  失败时间: {$duration}ms\n";
        echo "🔍 错误信息: " . $e->getMessage() . "\n";
        
        // 错误分析
        $errorMsg = $e->getMessage();
        echo "\n🔍 错误分析:\n";
        
        if (strpos($errorMsg, 'timeout') !== false || strpos($errorMsg, 'timed out') !== false) {
            echo "  📊 错误类型: 请求超时\n";
            echo "  💡 可能原因:\n";
            echo "    - 网络连接慢\n";
            echo "    - DeepSeek服务响应慢\n";
            echo "    - 防火墙阻止连接\n";
            echo "  🔧 建议解决方案:\n";
            echo "    - 检查网络连接\n";
            echo "    - 增加超时时间\n";
            echo "    - 检查防火墙设置\n";
        } elseif (strpos($errorMsg, 'SSL') !== false || strpos($errorMsg, 'certificate') !== false) {
            echo "  📊 错误类型: SSL证书问题\n";
            echo "  💡 可能原因:\n";
            echo "    - SSL证书验证失败\n";
            echo "    - 系统时间不正确\n";
            echo "  🔧 建议解决方案:\n";
            echo "    - 检查系统时间\n";
            echo "    - 更新CA证书\n";
        } elseif (strpos($errorMsg, '401') !== false) {
            echo "  📊 错误类型: 认证失败\n";
            echo "  💡 可能原因:\n";
            echo "    - API密钥错误\n";
            echo "    - API密钥已过期\n";
            echo "  🔧 建议解决方案:\n";
            echo "    - 检查API密钥是否正确\n";
            echo "    - 重新生成API密钥\n";
        } elseif (strpos($errorMsg, '403') !== false) {
            echo "  📊 错误类型: 权限不足\n";
            echo "  💡 可能原因:\n";
            echo "    - 账户余额不足\n";
            echo "    - API密钥权限不够\n";
            echo "  🔧 建议解决方案:\n";
            echo "    - 检查账户余额\n";
            echo "    - 检查API密钥权限\n";
        } elseif (strpos($errorMsg, 'Connection') !== false || strpos($errorMsg, 'connect') !== false) {
            echo "  📊 错误类型: 连接失败\n";
            echo "  💡 可能原因:\n";
            echo "    - 网络连接问题\n";
            echo "    - DNS解析失败\n";
            echo "    - 代理设置问题\n";
            echo "  🔧 建议解决方案:\n";
            echo "    - 检查网络连接\n";
            echo "    - 检查DNS设置\n";
            echo "    - 检查代理配置\n";
        } else {
            echo "  📊 错误类型: 未知错误\n";
            echo "  🔧 建议:\n";
            echo "    - 运行详细诊断工具\n";
            echo "    - 检查DeepSeek服务状态\n";
        }
    }
    
    echo "\n🎯 测试完成!\n";
    echo "=" . str_repeat("=", 40) . "\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n💡 下一步建议:\n";
echo "1. 如果测试失败，运行: php test_deepseek_api.php\n";
echo "2. 检查网络连接和防火墙设置\n";
echo "3. 确认API密钥正确且有效\n";
echo "4. 检查DeepSeek账户余额\n";
