-- 物流公司管理表
CREATE TABLE `ad_logistics_company` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '物流公司名称',
  `logo` varchar(255) NOT NULL DEFAULT '' COMMENT '物流公司LOGO',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '官方网址',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `kdn_code` varchar(50) NOT NULL DEFAULT '' COMMENT '快递鸟编码',
  `kd100_free_code` varchar(50) NOT NULL DEFAULT '' COMMENT '快递100免费版编码',
  `kd100_pay_code` varchar(50) NOT NULL DEFAULT '' COMMENT '快递100付费版编码',
  `cainiao_bird_code` varchar(50) NOT NULL DEFAULT '' COMMENT '菜鸟物流接口编码',
  `express_query_code` varchar(50) NOT NULL DEFAULT '' COMMENT '快递查询接口编码',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `createtime` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updatetime` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物流公司管理表';

-- 初始数据
INSERT INTO `logistics_company` (`id`, `name`, `logo`, `url`, `sort`, `kdn_code`, `kd100_free_code`, `kd100_pay_code`, `cainiao_bird_code`, `express_query_code`, `status`, `createtime`, `updatetime`) VALUES
(1, '顺丰速运', 'https://www.sf-express.com/cn/sc/dynamic_function/images/logo.png', 'https://www.sf-express.com/cn/sc/', 1, 'SF', '100免费版编码', '100付费版编码', 'SF', 'SF', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());