<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\vchat\services\ScheduleService;
use app\vchat\services\MessageQueueService;
use think\facade\Cache;

// 初始化 ThinkPHP 应用
$app = new \think\App();
$app->initialize();

echo "=== 定时任务消息发送测试 ===\n";

// 创建 ScheduleService 实例
$scheduleService = new ScheduleService();

// 测试发送 Socket.IO 事件消息
echo "\n1. 测试定时任务发送消息\n";
$scheduleService->sendSocketIoEventMessage('test_user_789', 'timeout_notification', [
    'message' => '您的会话即将超时',
    'remaining_time' => 60,
    'timestamp' => time()
]);

// 检查消息是否成功添加到队列
echo "\n2. 检查队列状态\n";
$messageQueueService = new MessageQueueService();
$status = $messageQueueService->getQueueStatus();
echo "待处理消息数: " . $status['pending'] . "\n";
echo "失败消息数: " . $status['failed'] . "\n";

// 查看队列中的最新消息
echo "\n3. 查看队列中的最新消息\n";
$pendingMessages = Cache::lRange(MessageQueueService::QUEUE_PENDING, -1, -1);
if (!empty($pendingMessages)) {
    $latestMessage = json_decode($pendingMessages[0], true);
    echo "最新消息: " . json_encode($latestMessage, JSON_UNESCAPED_UNICODE) . "\n";
} else {
    echo "队列中没有消息\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n提示: 如果 Swoole 服务器正在运行，消息队列处理器会自动处理这些消息并发送给客户端。\n";