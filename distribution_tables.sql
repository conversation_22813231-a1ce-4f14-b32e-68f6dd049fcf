-- 分销规则表
CREATE TABLE `distribution_rule` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '规则名称',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '分销比例',
  `min_order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低订单金额',
  `start_time` int(11) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(11) DEFAULT NULL COMMENT '结束时间',
  `description` varchar(255) DEFAULT '' COMMENT '规则说明',
  `goods_ids` varchar(255) DEFAULT '' COMMENT '关联商品ID，多个用逗号分隔',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=禁用，1=启用',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销规则表';

-- 分销订单表
CREATE TABLE `distribution_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分销订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `rule_id` int(11) unsigned NOT NULL COMMENT '规则ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `distributor_id` int(11) unsigned NOT NULL COMMENT '分销员ID',
  `distributor_name` varchar(100) NOT NULL DEFAULT '' COMMENT '分销员姓名',
  `distributor_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '分销员手机号',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `goods_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '分销比例',
  `commission_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '佣金金额',
  `order_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态：0=待付款，1=已付款，2=已发货，3=已完成，4=已取消',
  `settlement_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '结算状态：0=未结算，1=已结算',
  `settlement_time` int(11) DEFAULT NULL COMMENT '结算时间',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_distributor_id` (`distributor_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_settlement_status` (`settlement_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销订单表';