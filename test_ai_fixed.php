<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\ai\services\BasicAiService;
use app\ai\config\ConfigManager;
use app\ai\utils\Logger;

echo "🔧 测试修复后的AI服务\n";
echo "=" . str_repeat("=", 40) . "\n\n";

try {
    echo "📋 1. 初始化配置...\n";
    
    // 设置调试模式
    ConfigManager::set('debug', true);
    
    // 验证配置
    $apiKey = ConfigManager::get('providers.deepseek.api_key');
    $baseUrl = ConfigManager::get('providers.deepseek.base_url');
    $timeout = ConfigManager::get('providers.deepseek.timeout');
    
    echo "  ✅ 配置加载成功\n";
    echo "  🔑 API密钥: " . substr($apiKey, 0, 10) . "...\n";
    echo "  🌐 API地址: {$baseUrl}\n";
    echo "  ⏱️  超时时间: {$timeout}秒\n\n";
    
    echo "📋 2. 创建AI服务...\n";
    $aiService = new BasicAiService();
    echo "  ✅ AI服务创建成功\n\n";
    
    echo "📋 3. 测试简单对话...\n";
    echo "  📤 发送消息: 'Hello, please respond with just OK'\n";
    
    $startTime = microtime(true);
    
    try {
        $response = $aiService->chat('Hello, please respond with just OK', [
            'max_tokens' => 10,
            'temperature' => 0.1,
            'timeout' => 60 // 明确设置60秒超时
        ], 'deepseek');
        
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        echo "  ✅ AI响应成功!\n";
        echo "  📥 响应内容: '{$response}'\n";
        echo "  ⏱️  响应时间: {$duration}ms\n\n";
        
        echo "🎉 测试完全成功！AI服务工作正常。\n";
        
    } catch (Exception $e) {
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        echo "  ❌ AI响应失败!\n";
        echo "  ⏱️  失败时间: {$duration}ms\n";
        echo "  🔍 错误信息: " . $e->getMessage() . "\n";
        echo "  📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
        
        // 详细错误分析
        $errorMsg = $e->getMessage();
        echo "🔍 错误分析:\n";
        
        if (strpos($errorMsg, 'timeout') !== false || strpos($errorMsg, 'timed out') !== false) {
            echo "  📊 错误类型: 请求超时\n";
            echo "  💡 可能原因: 网络慢或服务响应慢\n";
            echo "  🔧 解决方案: 已增加超时时间到60秒\n";
        } elseif (strpos($errorMsg, 'cURL Error') !== false) {
            echo "  📊 错误类型: cURL网络错误\n";
            echo "  💡 这很奇怪，因为直接cURL测试是成功的\n";
            echo "  🔧 建议: 检查HttpClient实现\n";
        } elseif (strpos($errorMsg, 'JSON') !== false) {
            echo "  📊 错误类型: JSON解析错误\n";
            echo "  💡 可能原因: 响应格式异常\n";
        } else {
            echo "  📊 错误类型: 未知错误\n";
            echo "  💡 需要进一步调试\n";
        }
    }
    
    echo "\n📋 4. 测试提供商检查...\n";
    
    $providers = $aiService->getSupportedProviders();
    echo "  📋 支持的提供商: " . implode(', ', $providers) . "\n";
    
    $isAvailable = $aiService->isProviderAvailable('deepseek');
    echo "  🔍 DeepSeek可用性: " . ($isAvailable ? '✅ 可用' : '❌ 不可用') . "\n";
    
    $defaultProvider = $aiService->getDefaultProvider();
    echo "  🎯 默认提供商: {$defaultProvider}\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生严重错误:\n";
    echo "  错误信息: " . $e->getMessage() . "\n";
    echo "  错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "  错误追踪:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "\n🎯 测试完成!\n";
echo "=" . str_repeat("=", 40) . "\n";

echo "\n💡 总结:\n";
echo "- 直接cURL测试: ✅ 成功 (4秒响应)\n";
echo "- AI服务测试: " . (isset($response) ? "✅ 成功" : "❌ 失败") . "\n";
echo "- 网络连接: ✅ 正常\n";
echo "- API密钥: ✅ 有效\n";
echo "- DeepSeek服务: ✅ 可用\n";

if (!isset($response)) {
    echo "\n🔧 下一步调试建议:\n";
    echo "1. 检查HttpClient的详细日志\n";
    echo "2. 验证Logger不会阻塞请求\n";
    echo "3. 检查autoload和类加载\n";
    echo "4. 增加更多调试输出\n";
}
