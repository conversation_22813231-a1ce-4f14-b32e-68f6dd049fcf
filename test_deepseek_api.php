<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\ai\utils\HttpClient;
use app\ai\utils\Logger;
use app\ai\config\ConfigManager;

echo "🔧 DeepSeek API 连接诊断工具\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// 设置API密钥（请替换为你的实际API密钥）
$apiKey = 'sk-your-api-key-here'; // 请替换为实际的API密钥

if ($apiKey === 'sk-your-api-key-here') {
    echo "❌ 请先设置你的DeepSeek API密钥\n";
    echo "   在脚本中修改 \$apiKey 变量\n\n";
    exit(1);
}

try {
    // 初始化配置
    ConfigManager::set('debug', true);
    
    echo "📡 测试1: 基础网络连接\n";
    
    // 创建HTTP客户端，设置较短的超时时间用于快速诊断
    $httpClient = new HttpClient([
        'timeout' => 10,
        'connect_timeout' => 5,
        'debug' => true,
        'max_retries' => 1,
        'retry_delay' => 1
    ]);
    
    // 测试基础连接
    try {
        echo "  🔗 测试DeepSeek域名解析...\n";
        $dnsResult = gethostbyname('api.deepseek.com');
        if ($dnsResult === 'api.deepseek.com') {
            echo "  ❌ DNS解析失败\n";
        } else {
            echo "  ✅ DNS解析成功: {$dnsResult}\n";
        }
    } catch (Exception $e) {
        echo "  ❌ DNS解析错误: " . $e->getMessage() . "\n";
    }
    
    echo "\n📡 测试2: HTTPS连接测试\n";
    
    // 简单的连接测试
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'method' => 'GET'
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);
        
        $testUrl = 'https://api.deepseek.com';
        echo "  🔗 测试HTTPS连接到: {$testUrl}\n";
        
        $headers = @get_headers($testUrl, 1, $context);
        if ($headers) {
            echo "  ✅ HTTPS连接成功\n";
            echo "  📊 响应状态: " . $headers[0] . "\n";
        } else {
            echo "  ❌ HTTPS连接失败\n";
        }
    } catch (Exception $e) {
        echo "  ❌ HTTPS连接错误: " . $e->getMessage() . "\n";
    }
    
    echo "\n📡 测试3: DeepSeek API认证测试\n";
    
    $url = 'https://api.deepseek.com/chat/completions';
    $headers = [
        'Authorization' => 'Bearer ' . $apiKey,
        'Content-Type' => 'application/json'
    ];
    
    $data = [
        'model' => 'deepseek-chat',
        'messages' => [
            ['role' => 'user', 'content' => 'Hello, this is a test message.']
        ],
        'max_tokens' => 50,
        'temperature' => 0.7
    ];
    
    echo "  🔑 测试API密钥认证...\n";
    echo "  📤 发送测试请求...\n";
    
    try {
        $startTime = microtime(true);
        $result = $httpClient->post($url, $data, $headers, ['timeout' => 15]);
        $endTime = microtime(true);
        
        echo "  ✅ API请求成功!\n";
        echo "  📊 响应时间: " . round(($endTime - $startTime) * 1000, 2) . "ms\n";
        echo "  📦 响应大小: " . $result['response_size'] . " bytes\n";
        echo "  🔢 HTTP状态码: " . $result['http_code'] . "\n";
        
        if (isset($result['data']['choices'][0]['message']['content'])) {
            $content = $result['data']['choices'][0]['message']['content'];
            echo "  💬 AI回复: " . substr($content, 0, 100) . "...\n";
        }
        
    } catch (Exception $e) {
        echo "  ❌ API请求失败: " . $e->getMessage() . "\n";
        
        // 详细错误分析
        $errorMsg = $e->getMessage();
        if (strpos($errorMsg, 'timeout') !== false) {
            echo "  🔍 错误类型: 超时错误\n";
            echo "  💡 建议: 检查网络连接或增加超时时间\n";
        } elseif (strpos($errorMsg, 'SSL') !== false) {
            echo "  🔍 错误类型: SSL证书错误\n";
            echo "  💡 建议: 检查SSL配置或禁用SSL验证\n";
        } elseif (strpos($errorMsg, '401') !== false) {
            echo "  🔍 错误类型: 认证错误\n";
            echo "  💡 建议: 检查API密钥是否正确\n";
        } elseif (strpos($errorMsg, '403') !== false) {
            echo "  🔍 错误类型: 权限错误\n";
            echo "  💡 建议: 检查API密钥权限或账户余额\n";
        } elseif (strpos($errorMsg, '429') !== false) {
            echo "  🔍 错误类型: 请求频率限制\n";
            echo "  💡 建议: 降低请求频率或升级账户\n";
        } elseif (strpos($errorMsg, '500') !== false) {
            echo "  🔍 错误类型: 服务器错误\n";
            echo "  💡 建议: DeepSeek服务可能暂时不可用\n";
        } else {
            echo "  🔍 错误类型: 网络连接错误\n";
            echo "  💡 建议: 检查网络连接、防火墙或代理设置\n";
        }
    }
    
    echo "\n📡 测试4: 网络环境检查\n";
    
    // 检查代理设置
    $httpProxy = getenv('HTTP_PROXY') ?: getenv('http_proxy');
    $httpsProxy = getenv('HTTPS_PROXY') ?: getenv('https_proxy');
    
    if ($httpProxy || $httpsProxy) {
        echo "  🌐 检测到代理设置:\n";
        if ($httpProxy) echo "    HTTP_PROXY: {$httpProxy}\n";
        if ($httpsProxy) echo "    HTTPS_PROXY: {$httpsProxy}\n";
        echo "  💡 如果连接失败，可能需要配置代理认证\n";
    } else {
        echo "  🌐 未检测到代理设置\n";
    }
    
    // 检查cURL版本和SSL支持
    if (function_exists('curl_version')) {
        $curlInfo = curl_version();
        echo "  🔧 cURL版本: " . $curlInfo['version'] . "\n";
        echo "  🔒 SSL版本: " . $curlInfo['ssl_version'] . "\n";
        echo "  📋 支持的协议: " . implode(', ', $curlInfo['protocols']) . "\n";
    }
    
    echo "\n🎯 诊断完成!\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
} catch (Exception $e) {
    echo "❌ 诊断过程中发生错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n💡 故障排除建议:\n";
echo "1. 确保API密钥正确且有效\n";
echo "2. 检查网络连接和防火墙设置\n";
echo "3. 如果使用代理，确保代理配置正确\n";
echo "4. 尝试增加超时时间设置\n";
echo "5. 检查DeepSeek服务状态: https://status.deepseek.com\n";
echo "6. 确保账户有足够的余额\n";
