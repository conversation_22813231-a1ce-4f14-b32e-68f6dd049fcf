# Token 安全升级指南

本文档详细说明了 Token 系统的安全升级内容、配置方法和使用指南。

## 升级概述

本次升级主要针对 Token 系统的安全性进行了全面增强，包括：

### 🔐 核心安全功能

1. **JWT 密钥增强**
   - 动态密钥生成
   - 多层密钥混合
   - 密钥轮换支持

2. **Token 黑名单机制**
   - 实时黑名单检查
   - 自动过期清理
   - 批量撤销支持

3. **数据加密存储**
   - AES-256-CBC 加密
   - 自动加解密
   - 向后兼容

4. **安全监控与日志**
   - 详细操作日志
   - 安全事件记录
   - 异常行为检测

5. **速率限制与防护**
   - IP 级别限制
   - 失败尝试记录
   - 自动锁定机制

## 文件结构

```
app/
├── common/
│   ├── Auth.php                    # 核心认证类（已升级）
│   ├── TokenStorage.php            # Token存储管理（已升级）
│   ├── TokenSecurity.php           # 新增：安全功能类
│   └── token/
│       └── EncryptedTokenStorage.php # 新增：加密存储类
├── middleware/
│   └── TokenSecurityMiddleware.php  # 新增：安全中间件
└── command/
    └── TokenManagement.php          # 新增：管理命令

config/
└── token.php                       # Token配置（已升级）

database/migrations/
└── 20241201_token_security_upgrade.sql # 数据库升级脚本

docs/
└── TOKEN_SECURITY_UPGRADE.md        # 本文档
```

## 安装与配置

### 1. 数据库升级

执行数据库迁移脚本：

```bash
# 备份现有数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 执行升级脚本
mysql -u username -p database_name < database/migrations/20241201_token_security_upgrade.sql
```

### 2. 配置文件更新

检查并更新 `config/token.php` 配置：

```php
<?php
return [
    // 基础配置
    'driver' => 'mysql', // 或 'redis'
    
    // 加密配置
    'encryption' => [
        'enabled' => true,
        'method' => 'AES-256-CBC',
        'key' => env('TOKEN_ENCRYPTION_KEY', ''), // 建议使用环境变量
    ],
    
    // 安全配置
    'security' => [
        'enabled' => true,
        'rate_limit' => [
            'max_attempts' => 5,
            'lockout_duration' => 300, // 5分钟
            'cleanup_interval' => 3600, // 1小时
        ],
        'blacklist' => [
            'enabled' => true,
            'prefix' => 'token_blacklist:',
            'default_ttl' => 86400, // 24小时
        ],
        // ... 其他配置
    ],
];
```

### 3. 环境变量配置

在 `.env` 文件中添加：

```env
# Token 加密密钥（32字符）
TOKEN_ENCRYPTION_KEY=your-32-character-encryption-key

# JWT 密钥增强
JWT_SECRET_SALT=your-additional-salt-string

# 安全功能开关
TOKEN_SECURITY_ENABLED=true
TOKEN_ENCRYPTION_ENABLED=true
```

### 4. 中间件注册

在需要安全检查的路由中注册中间件：

```php
// 在路由文件中
Route::group(function () {
    // 你的受保护路由
})->middleware(['token_security']);

// 或在控制器中
class YourController
{
    public function __construct()
    {
        $this->middleware('token_security');
    }
}
```

## 使用指南

### Token 管理命令

新增的管理命令提供了丰富的 Token 维护功能：

```bash
# 查看帮助
php think token:manage

# 清理过期 Token（默认7天前）
php think token:manage cleanup
php think token:manage cleanup --days=30 --force

# 查看统计信息
php think token:manage stats

# 撤销用户的所有 Token
php think token:manage revoke --user-id=123

# 撤销指定 Token
php think token:manage revoke --token=xxx-xxx-xxx

# 测试安全功能
php think token:manage test

# 检查黑名单状态
php think token:manage blacklist --token=xxx-xxx-xxx
```

### API 使用示例

#### 1. 生成 Token

```php
use app\common\Auth;

// 生成 Token
$tokenData = Auth::generateToken([
    'user_id' => 123,
    'type' => 'user',
    'permissions' => ['read', 'write']
]);

// 返回结果
[
    'access_token' => 'xxx-xxx-xxx',
    'refresh_token' => 'yyy-yyy-yyy',
    'expires_in' => 3600,
    'token_type' => 'Bearer'
]
```

#### 2. 验证 Token

```php
try {
    $tokenData = Auth::checkToken($accessToken);
    $userId = $tokenData['user_id'];
    // Token 有效，继续处理
} catch (\Exception $e) {
    // Token 无效或已过期
    return json(['error' => 'Invalid token'], 401);
}
```

#### 3. 刷新 Token

```php
try {
    $newTokenData = Auth::refreshToken($refreshToken);
    // 返回新的 Token 信息
} catch (\Exception $e) {
    // 刷新失败，需要重新登录
    return json(['error' => 'Token refresh failed'], 401);
}
```

#### 4. 撤销 Token

```php
// 撤销单个 Token
Auth::removeToken($accessToken, true); // true 表示加入黑名单

// 撤销用户所有 Token
Auth::revokeAllUserTokens($userId);
```

### 安全功能使用

#### 1. 速率限制检查

```php
use app\common\TokenSecurity;

$clientIp = request()->ip();
if (!TokenSecurity::checkRateLimit($clientIp)) {
    return json(['error' => 'Too many requests'], 429);
}
```

#### 2. 可疑活动检测

```php
// 检查可疑 IP
if (TokenSecurity::isSuspiciousIp($clientIp)) {
    TokenSecurity::triggerSecurityAlert('suspicious_ip', [
        'ip' => $clientIp,
        'user_agent' => request()->header('User-Agent')
    ]);
}
```

#### 3. Token 格式验证

```php
if (!TokenSecurity::validateTokenFormat($token)) {
    return json(['error' => 'Invalid token format'], 400);
}
```

## 监控与维护

### 1. 日志监控

Token 操作日志存储在 `sys_token_logs` 表中，包括：

- Token 创建/验证/刷新/撤销
- 失败尝试和错误信息
- IP 地址和用户代理
- 操作时间和结果

### 2. 安全事件

安全事件记录在 `sys_security_events` 表中：

- 可疑 IP 访问
- 频繁失败尝试
- Token 格式异常
- 其他安全相关事件

### 3. 性能监控

建议监控以下指标：

- Token 验证响应时间
- 黑名单命中率
- 加密/解密性能
- 数据库查询性能

### 4. 定期维护

```bash
# 每日清理过期数据
0 2 * * * cd /path/to/project && php think token:manage cleanup

# 每周生成统计报告
0 9 * * 1 cd /path/to/project && php think token:manage stats > /var/log/token_stats.log

# 每月安全检查
0 10 1 * * cd /path/to/project && php think token:manage test
```

## 安全建议

### 1. 密钥管理

- 使用强随机密钥
- 定期轮换密钥
- 密钥存储在环境变量中
- 不要在代码中硬编码密钥

### 2. 网络安全

- 使用 HTTPS 传输
- 配置适当的 CORS 策略
- 实施 IP 白名单（如适用）
- 使用 CDN 和 DDoS 防护

### 3. 应用安全

- 定期更新依赖包
- 实施输入验证
- 使用安全的会话管理
- 定期安全审计

### 4. 监控告警

- 设置异常登录告警
- 监控失败尝试频率
- 跟踪可疑 IP 活动
- 定期检查安全日志

## 故障排除

### 常见问题

#### 1. 加密功能不可用

```bash
# 检查 OpenSSL 扩展
php -m | grep openssl

# 检查加密密钥
php think token:manage test
```

#### 2. Token 验证失败

- 检查系统时间同步
- 验证 JWT 密钥配置
- 查看错误日志
- 检查黑名单状态

#### 3. 性能问题

- 优化数据库索引
- 调整缓存配置
- 监控慢查询
- 考虑使用 Redis

#### 4. 内存使用过高

- 调整清理频率
- 优化日志保留策略
- 检查内存泄漏
- 使用对象池

## 版本兼容性

- **向后兼容**：现有 Token 仍然有效
- **渐进升级**：可以逐步启用新功能
- **配置驱动**：通过配置控制功能开关
- **平滑迁移**：支持在线升级

## 更新日志

### v2.0.0 (2024-12-01)

- ✨ 新增 JWT 密钥增强机制
- ✨ 新增 Token 黑名单功能
- ✨ 新增数据加密存储
- ✨ 新增安全监控与日志
- ✨ 新增速率限制功能
- ✨ 新增管理命令工具
- ✨ 新增安全中间件
- 🐛 修复 Token 重放攻击漏洞
- 🐛 修复并发访问问题
- 📝 完善文档和示例

## 技术支持

如果在升级过程中遇到问题，请：

1. 查看错误日志
2. 运行诊断命令
3. 检查配置文件
4. 参考故障排除指南
5. 联系技术支持

---

**注意**：升级前请务必备份数据库和配置文件！