# Token 安全升级部署检查清单

本检查清单帮助您确保 Token 安全升级的正确部署和配置。请按顺序完成每个步骤。

## 📋 部署前准备

### 1. 环境检查

- [ ] **PHP 版本**: 确保 PHP >= 7.4
- [ ] **扩展检查**: 确认已安装 OpenSSL 扩展
  ```bash
  php -m | grep openssl
  ```
- [ ] **权限检查**: 确认应用目录有读写权限
- [ ] **磁盘空间**: 确保有足够空间存储日志和缓存

### 2. 备份数据

- [ ] **数据库备份**:
  ```bash
  mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
  ```
- [ ] **配置文件备份**:
  ```bash
  cp config/token.php config/token.php.backup
  cp .env .env.backup
  ```
- [ ] **应用代码备份**:
  ```bash
  tar -czf app_backup_$(date +%Y%m%d_%H%M%S).tar.gz app/
  ```

### 3. 依赖检查

- [ ] **Composer 依赖**: 确认所有依赖已安装
  ```bash
  composer install --no-dev --optimize-autoloader
  ```
- [ ] **缓存驱动**: 确认 Redis/MySQL 连接正常
- [ ] **日志目录**: 确认日志目录存在且可写

## 🚀 部署步骤

### 1. 文件部署

- [ ] **核心文件**: 上传所有新增和修改的文件
  - [ ] `app/common/Auth.php`
  - [ ] `app/common/TokenStorage.php`
  - [ ] `app/common/TokenSecurity.php`
  - [ ] `app/common/token/EncryptedTokenStorage.php`
  - [ ] `app/middleware/TokenSecurityMiddleware.php`
  - [ ] `app/command/TokenManagement.php`
  - [ ] `app/command/TestTokenSecurity.php`
  - [ ] `config/token.php`

- [ ] **测试文件**: 上传测试相关文件
  - [ ] `tests/TokenSecurityTest.php`

- [ ] **文档文件**: 上传文档
  - [ ] `docs/TOKEN_SECURITY_UPGRADE.md`
  - [ ] `docs/DEPLOYMENT_CHECKLIST.md`

### 2. 数据库升级

- [ ] **执行迁移脚本**:
  ```bash
  mysql -u username -p database_name < database/migrations/20241201_token_security_upgrade.sql
  ```

- [ ] **验证表结构**:
  ```sql
  SHOW TABLES LIKE 'sys_token_%';
  DESCRIBE sys_token_blacklist;
  DESCRIBE sys_token_logs;
  DESCRIBE sys_security_events;
  ```

- [ ] **检查索引**:
  ```sql
  SHOW INDEX FROM qi_user_token;
  SHOW INDEX FROM sys_admin_token;
  ```

### 3. 配置更新

- [ ] **环境变量**: 更新 `.env` 文件
  ```env
  TOKEN_ENCRYPTION_KEY=your-32-character-encryption-key
  JWT_SECRET_SALT=your-additional-salt-string
  TOKEN_SECURITY_ENABLED=true
  TOKEN_ENCRYPTION_ENABLED=true
  ```

- [ ] **Token 配置**: 更新 `config/token.php`
  - [ ] 启用加密功能
  - [ ] 配置安全选项
  - [ ] 设置速率限制
  - [ ] 配置黑名单

- [ ] **中间件注册**: 在需要的路由中注册安全中间件

### 4. 权限设置

- [ ] **文件权限**:
  ```bash
  chmod 755 app/command/
  chmod 644 app/command/*.php
  chmod 755 app/common/token/
  chmod 644 app/common/token/*.php
  ```

- [ ] **日志权限**:
  ```bash
  chmod 755 runtime/log/
  chmod 666 runtime/log/*.log
  ```

## ✅ 部署后验证

### 1. 功能测试

- [ ] **运行安全测试**:
  ```bash
  php think test:token-security --cleanup
  ```

- [ ] **测试 Token 生成**:
  ```bash
  php think token:manage test
  ```

- [ ] **检查加密功能**:
  ```php
  // 在控制台或测试脚本中
  use app\common\token\EncryptedTokenStorage;
  var_dump(EncryptedTokenStorage::isEncryptionAvailable());
  ```

### 2. 接口测试

- [ ] **登录接口**: 测试用户登录获取 Token
- [ ] **Token 验证**: 测试 Token 验证接口
- [ ] **Token 刷新**: 测试 Token 刷新功能
- [ ] **Token 撤销**: 测试 Token 撤销功能

### 3. 安全验证

- [ ] **黑名单功能**: 验证撤销的 Token 无法使用
- [ ] **速率限制**: 验证频繁请求被限制
- [ ] **加密存储**: 验证数据库中的 Token 数据已加密
- [ ] **日志记录**: 检查安全日志是否正常记录

### 4. 性能测试

- [ ] **响应时间**: 测试 Token 验证响应时间
- [ ] **并发测试**: 测试高并发下的 Token 处理
- [ ] **内存使用**: 监控内存使用情况
- [ ] **数据库性能**: 检查数据库查询性能

## 🔧 配置优化

### 1. 缓存配置

- [ ] **Redis 配置**: 优化 Redis 连接池和超时设置
- [ ] **缓存策略**: 配置适当的缓存过期时间
- [ ] **内存限制**: 设置合理的内存使用限制

### 2. 日志配置

- [ ] **日志级别**: 设置适当的日志级别
- [ ] **日志轮转**: 配置日志文件轮转策略
- [ ] **日志存储**: 确保日志存储空间充足

### 3. 安全配置

- [ ] **密钥管理**: 确保密钥安全存储
- [ ] **HTTPS**: 确保生产环境使用 HTTPS
- [ ] **防火墙**: 配置适当的防火墙规则
- [ ] **监控告警**: 设置安全事件监控告警

## 📊 监控设置

### 1. 应用监控

- [ ] **错误监控**: 设置错误日志监控
- [ ] **性能监控**: 监控应用响应时间
- [ ] **资源监控**: 监控 CPU、内存使用

### 2. 安全监控

- [ ] **失败尝试**: 监控登录失败次数
- [ ] **可疑活动**: 监控异常 IP 访问
- [ ] **Token 使用**: 监控 Token 使用模式

### 3. 数据库监控

- [ ] **慢查询**: 监控数据库慢查询
- [ ] **连接数**: 监控数据库连接数
- [ ] **存储空间**: 监控数据库存储使用

## 🚨 故障排除

### 常见问题检查

- [ ] **加密失败**:
  - 检查 OpenSSL 扩展
  - 验证加密密钥长度
  - 检查文件权限

- [ ] **Token 验证失败**:
  - 检查系统时间同步
  - 验证 JWT 密钥配置
  - 查看错误日志

- [ ] **性能问题**:
  - 检查数据库索引
  - 优化缓存配置
  - 监控慢查询

- [ ] **内存泄漏**:
  - 检查对象引用
  - 优化缓存策略
  - 监控内存使用

## 📝 维护计划

### 日常维护

- [ ] **日志检查**: 每日检查错误日志
- [ ] **性能监控**: 每日检查性能指标
- [ ] **安全事件**: 每日检查安全事件

### 定期维护

- [ ] **数据清理**: 每周清理过期数据
  ```bash
  php think token:manage cleanup --days=7
  ```

- [ ] **统计报告**: 每周生成统计报告
  ```bash
  php think token:manage stats
  ```

- [ ] **安全检查**: 每月进行安全检查
  ```bash
  php think test:token-security
  ```

### 长期维护

- [ ] **密钥轮换**: 每季度考虑密钥轮换
- [ ] **依赖更新**: 每季度更新依赖包
- [ ] **安全审计**: 每年进行安全审计

## ✅ 部署完成确认

完成以上所有检查项后，请确认：

- [ ] 所有测试通过
- [ ] 功能正常工作
- [ ] 性能满足要求
- [ ] 安全功能启用
- [ ] 监控正常运行
- [ ] 文档已更新

**部署负责人**: ________________  
**部署时间**: ________________  
**验证人**: ________________  
**验证时间**: ________________  

---

**注意事项**:
1. 在生产环境部署前，请先在测试环境完整验证
2. 部署过程中如遇问题，请及时回滚并查看日志
3. 建议在低峰期进行部署，减少对用户的影响
4. 部署完成后持续监控系统状态至少24小时