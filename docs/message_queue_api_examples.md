# 消息队列HTTP API使用示例

本文档展示如何通过HTTP接口与消息队列服务交互，实现后台管理系统发送消息到队列的功能。

## API接口列表

### 1. 发送Socket.IO事件消息

**接口地址：** `POST /admin/message-queue/send-socketio`

**功能说明：** 发送Socket.IO事件消息到队列，用于实时通知用户

**请求参数：**
```json
{
    "user_id": 123,
    "event": "queue_update",
    "user_type": "user",
    "data": {
        "position": 5,
        "wait_time": 300
    }
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "消息已加入队列",
    "data": {
        "user_id": 123,
        "event": "queue_update",
        "user_type": "user",
        "timestamp": 1703123456
    }
}
```

**cURL示例：**
```bash
curl -X POST http://localhost/admin/message-queue/send-socketio \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": 123,
    "event": "queue_update",
    "user_type": "user",
    "data": {
        "position": 5,
        "wait_time": 300
    }
  }'
```

### 2. 发送常规消息

**接口地址：** `POST /admin/message-queue/send-message`

**功能说明：** 发送常规聊天消息到队列

**请求参数：**
```json
{
    "to_user_id": 456,
    "to_user_type": "user",
    "message_type": "text",
    "content": "您好，有什么可以帮助您的吗？",
    "from_user_id": 789,
    "from_user_type": "service",
    "extra_data": {
        "session_id": "sess_123456"
    }
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "消息已加入队列",
    "data": {
        "to_user_id": 456,
        "message_type": "text",
        "timestamp": 1703123456
    }
}
```

### 3. 获取消息状态

**接口地址：** `GET /admin/message-queue/status/{messageId}`

**功能说明：** 查询指定消息的处理状态

**响应示例：**
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "message_id": "msg_64b8f1234567890",
        "status": "sent",
        "timestamp": 1703123456
    }
}
```

**状态说明：**
- `pending`: 待处理
- `processing`: 处理中
- `sent`: 已发送
- `failed`: 发送失败
- `retry`: 重试中
- `user_offline`: 用户离线
- `fd_invalid`: 连接无效
- `send_failed`: 发送失败
- `exception`: 异常

### 4. 获取消息详情

**接口地址：** `GET /admin/message-queue/detail/{messageId}`

**功能说明：** 获取消息的详细信息

**响应示例：**
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "id": "msg_64b8f1234567890",
        "status": "sent",
        "data": {
            "id": "msg_64b8f1234567890",
            "type": "socketio_event",
            "user_id": 123,
            "user_type": "user",
            "event": "queue_update",
            "data": {
                "position": 5,
                "wait_time": 300
            },
            "retry_times": 0,
            "max_retry": 3,
            "status": "sent",
            "created_at": 1703123456
        },
        "timestamp": 1703123456
    }
}
```

### 5. 获取队列状态

**接口地址：** `GET /admin/message-queue/queue-status`

**功能说明：** 获取消息队列的整体状态

**响应示例：**
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "pending_count": 15,
        "failed_count": 3,
        "timestamp": 1703123456
    }
}
```

### 6. 重试失败消息

**接口地址：** `POST /admin/message-queue/retry/{messageId}`

**功能说明：** 重新处理失败的消息

**响应示例：**
```json
{
    "code": 200,
    "msg": "消息已重新加入队列",
    "data": {
        "message_id": "msg_64b8f1234567890",
        "timestamp": 1703123456
    }
}
```

### 7. 清理过期消息

**接口地址：** `POST /admin/message-queue/clean-expired`

**功能说明：** 清理队列中的过期消息

**请求参数：**
```json
{
    "expire_time": 86400
}
```

**参数说明：**
- `expire_time`: 过期时间（秒），默认86400（24小时），最小3600（1小时），最大604800（7天）

**响应示例：**
```json
{
    "code": 200,
    "msg": "过期消息清理完成",
    "data": {
        "expire_time": 86400,
        "timestamp": 1703123456
    }
}
```

### 8. 批量发送通知

**接口地址：** `POST /admin/message-queue/batch-notify`

**功能说明：** 批量发送通知消息给多个用户

**请求参数：**
```json
{
    "user_ids": [123, 456, 789],
    "user_type": "user",
    "event": "system_notice",
    "data": {
        "title": "系统维护通知",
        "content": "系统将于今晚22:00-24:00进行维护",
        "type": "warning"
    }
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "批量通知发送完成",
    "data": {
        "total": 3,
        "success": 3,
        "failed": 0,
        "timestamp": 1703123456
    }
}
```

## 使用场景示例

### 场景1：排队通知

当用户加入客服队列时，后台管理系统可以发送排队位置更新通知：

```bash
curl -X POST http://localhost/admin/message-queue/send-socketio \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123,
    "event": "queue_position_update",
    "user_type": "user",
    "data": {
        "position": 3,
        "estimated_wait_time": 180,
        "queue_length": 8
    }
  }'
```

### 场景2：客服消息推送

客服人员通过后台系统发送消息给用户：

```bash
curl -X POST http://localhost/admin/message-queue/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "to_user_id": 123,
    "to_user_type": "user",
    "message_type": "text",
    "content": "您好，我是客服小王，有什么可以帮助您的吗？",
    "from_user_id": 456,
    "from_user_type": "service"
  }'
```

### 场景3：系统公告

向所有在线用户发送系统公告：

```bash
curl -X POST http://localhost/admin/message-queue/batch-notify \
  -H "Content-Type: application/json" \
  -d '{
    "user_ids": [123, 456, 789, 101, 102],
    "user_type": "user",
    "event": "system_announcement",
    "data": {
        "title": "重要通知",
        "content": "平台将于明日凌晨2:00-4:00进行系统升级",
        "level": "important",
        "show_popup": true
    }
  }'
```

### 场景4：监控队列状态

定期检查队列状态，用于监控和告警：

```bash
curl -X GET http://localhost/admin/message-queue/queue-status
```

### 场景5：消息状态追踪

检查特定消息的处理状态：

```bash
curl -X GET http://localhost/admin/message-queue/status/msg_64b8f1234567890
```

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `404`: 消息不存在
- `500`: 服务器内部错误

### 错误响应示例

```json
{
    "code": 400,
    "msg": "user_id必须是大于0的整数",
    "data": null
}
```

## 注意事项

1. **认证授权**：所有管理接口都需要管理员权限认证
2. **频率限制**：建议对API调用进行频率限制，防止滥用
3. **批量限制**：批量发送接口单次最多支持100个用户
4. **消息格式**：确保消息内容符合MessageProtocol的验证规则
5. **错误重试**：对于失败的请求，建议实现指数退避重试机制
6. **日志监控**：所有操作都会记录日志，便于问题排查和审计

## 集成建议

### 1. 在后台管理界面中集成

```javascript
// 发送排队通知的JavaScript示例
function sendQueueNotification(userId, position, waitTime) {
    fetch('/admin/message-queue/send-socketio', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + getAuthToken()
        },
        body: JSON.stringify({
            user_id: userId,
            event: 'queue_update',
            user_type: 'user',
            data: {
                position: position,
                wait_time: waitTime
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            console.log('通知发送成功');
        } else {
            console.error('通知发送失败:', data.msg);
        }
    })
    .catch(error => {
        console.error('请求异常:', error);
    });
}
```

### 2. 定时任务集成

```php
// 定时清理过期消息的PHP示例
use app\vchat\services\MessageQueueService;

class MessageQueueCleanTask
{
    public function execute()
    {
        $messageQueueService = new MessageQueueService();
        
        // 清理24小时前的过期消息
        $result = $messageQueueService->cleanExpiredMessages(86400);
        
        if ($result) {
            echo "过期消息清理完成\n";
        } else {
            echo "过期消息清理失败\n";
        }
    }
}
```

这套HTTP API接口提供了完整的消息队列管理功能，可以很好地与后台管理系统集成，实现消息的发送、状态查询、重试和清理等操作。