<?php

echo "🔧 简单cURL测试 - DeepSeek API\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// 检查cURL扩展
if (!extension_loaded('curl')) {
    echo "❌ cURL扩展未安装\n";
    exit(1);
}

echo "✅ cURL扩展已安装\n";

// DeepSeek API配置
$apiKey = '***********************************';
$url = 'https://api.deepseek.com/chat/completions';

// 请求数据
$data = json_encode([
    'model' => 'deepseek-chat',
    'messages' => [
        ['role' => 'user', 'content' => 'Hello, please respond with just "OK"']
    ],
    'max_tokens' => 10,
    'temperature' => 0.1
]);

echo "📡 测试配置:\n";
echo "  🌐 URL: {$url}\n";
echo "  🔑 API密钥: " . substr($apiKey, 0, 10) . "...\n";
echo "  📦 数据大小: " . strlen($data) . " bytes\n\n";

echo "🚀 开始测试...\n";

// 记录开始时间
$startTime = microtime(true);

// 初始化cURL
$ch = curl_init();

if (!$ch) {
    echo "❌ cURL初始化失败\n";
    exit(1);
}

echo "✅ cURL初始化成功\n";

// 设置cURL选项
$options = [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $data,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ],
    CURLOPT_TIMEOUT => 15,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_MAXREDIRS => 3
];

echo "🔧 设置cURL选项...\n";
curl_setopt_array($ch, $options);

echo "📡 发送请求...\n";

// 执行请求
$response = curl_exec($ch);

// 获取请求信息
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
$curlErrno = curl_errno($ch);
$info = curl_getinfo($ch);

// 关闭cURL
curl_close($ch);

// 计算执行时间
$endTime = microtime(true);
$duration = round(($endTime - $startTime) * 1000, 2);

echo "✅ 请求执行完成\n\n";

echo "📊 执行结果:\n";
echo "  ⏱️  总执行时间: {$duration}ms\n";
echo "  🔢 HTTP状态码: {$httpCode}\n";
echo "  🔧 cURL错误码: {$curlErrno}\n";
echo "  📝 cURL错误信息: " . ($curlError ?: '无') . "\n";

// 详细时间信息
echo "\n⏱️  详细时间分析:\n";
echo "  DNS解析: " . round($info['namelookup_time'] * 1000, 2) . "ms\n";
echo "  建立连接: " . round($info['connect_time'] * 1000, 2) . "ms\n";
echo "  SSL握手: " . round(($info['appconnect_time'] ?? 0) * 1000, 2) . "ms\n";
echo "  开始传输: " . round($info['starttransfer_time'] * 1000, 2) . "ms\n";
echo "  总时间: " . round($info['total_time'] * 1000, 2) . "ms\n";

// 检查错误
if ($curlErrno !== 0) {
    echo "\n❌ cURL错误分析:\n";
    
    $errorMessages = [
        1 => 'CURLE_UNSUPPORTED_PROTOCOL - 不支持的协议',
        3 => 'CURLE_URL_MALFORMAT - URL格式错误',
        6 => 'CURLE_COULDNT_RESOLVE_HOST - 无法解析主机名',
        7 => 'CURLE_COULDNT_CONNECT - 无法连接到服务器',
        28 => 'CURLE_OPERATION_TIMEDOUT - 操作超时',
        35 => 'CURLE_SSL_CONNECT_ERROR - SSL连接错误',
        51 => 'CURLE_PEER_FAILED_VERIFICATION - SSL证书验证失败',
        52 => 'CURLE_GOT_NOTHING - 服务器返回空响应',
        56 => 'CURLE_RECV_ERROR - 接收数据失败'
    ];
    
    echo "  错误码: {$curlErrno}\n";
    echo "  错误信息: {$curlError}\n";
    
    if (isset($errorMessages[$curlErrno])) {
        echo "  详细说明: " . $errorMessages[$curlErrno] . "\n";
    }
    
    // 针对性建议
    echo "\n💡 解决建议:\n";
    switch ($curlErrno) {
        case 6:
            echo "  - 检查网络连接\n";
            echo "  - 检查DNS设置\n";
            echo "  - 尝试ping api.deepseek.com\n";
            break;
        case 7:
            echo "  - 检查防火墙设置\n";
            echo "  - 检查代理配置\n";
            echo "  - 确认端口443是否开放\n";
            break;
        case 28:
            echo "  - 增加超时时间\n";
            echo "  - 检查网络速度\n";
            echo "  - 稍后重试\n";
            break;
        case 35:
        case 51:
            echo "  - 检查系统时间是否正确\n";
            echo "  - 更新CA证书包\n";
            echo "  - 临时禁用SSL验证测试\n";
            break;
    }
} else {
    echo "\n✅ cURL执行成功，无错误\n";
}

// 检查HTTP响应
if ($httpCode > 0) {
    echo "\n📥 HTTP响应分析:\n";
    echo "  状态码: {$httpCode}\n";
    
    if ($httpCode === 200) {
        echo "  ✅ 请求成功\n";
        
        if ($response) {
            echo "  📦 响应大小: " . strlen($response) . " bytes\n";
            
            // 尝试解析JSON
            $responseData = json_decode($response, true);
            if ($responseData) {
                echo "  ✅ JSON解析成功\n";
                
                if (isset($responseData['choices'][0]['message']['content'])) {
                    $aiResponse = $responseData['choices'][0]['message']['content'];
                    echo "  💬 AI回复: {$aiResponse}\n";
                    echo "  🎉 测试完全成功！\n";
                } else {
                    echo "  ⚠️  响应格式异常\n";
                    echo "  📄 响应内容: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n";
                }
            } else {
                echo "  ❌ JSON解析失败\n";
                echo "  📄 原始响应: " . substr($response, 0, 200) . "...\n";
            }
        } else {
            echo "  ❌ 响应为空\n";
        }
    } else {
        echo "  ❌ HTTP错误\n";
        
        $httpErrors = [
            400 => '请求格式错误',
            401 => 'API密钥无效',
            403 => '权限不足或余额不足',
            404 => 'API端点不存在',
            429 => '请求频率超限',
            500 => '服务器内部错误',
            502 => '网关错误',
            503 => '服务不可用'
        ];
        
        if (isset($httpErrors[$httpCode])) {
            echo "  说明: " . $httpErrors[$httpCode] . "\n";
        }
        
        if ($response) {
            echo "  📄 错误响应: " . substr($response, 0, 300) . "\n";
        }
    }
} else {
    echo "\n❌ 未收到HTTP响应\n";
    echo "  这通常表示连接在HTTP层面之前就失败了\n";
}

echo "\n🎯 测试完成!\n";
echo "=" . str_repeat("=", 40) . "\n";

// 环境信息
echo "\n🔍 环境信息:\n";
echo "  PHP版本: " . PHP_VERSION . "\n";
echo "  cURL版本: " . curl_version()['version'] . "\n";
echo "  SSL版本: " . curl_version()['ssl_version'] . "\n";
echo "  支持协议: " . implode(', ', curl_version()['protocols']) . "\n";

// 网络检查建议
echo "\n💡 如果测试失败，请检查:\n";
echo "1. 网络连接是否正常\n";
echo "2. 防火墙是否阻止HTTPS连接\n";
echo "3. 是否需要配置代理\n";
echo "4. DNS是否能正确解析api.deepseek.com\n";
echo "5. 系统时间是否正确\n";
echo "6. API密钥是否有效\n";
