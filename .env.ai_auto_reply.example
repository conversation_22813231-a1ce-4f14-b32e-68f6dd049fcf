# AI自动回复环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# ================================
# AI自动回复基础配置
# ================================

# 是否启用AI自动回复功能
AI_AUTO_REPLY_ENABLED=true

# AI服务提供商 (deepseek/openai/claude)
AI_AUTO_REPLY_PROVIDER=deepseek

# AI模型名称
AI_AUTO_REPLY_MODEL=deepseek-chat

# 回复创造性 (0.0-2.0, 越高越有创造性)
AI_AUTO_REPLY_TEMPERATURE=0.7

# 最大回复长度 (token数量)
AI_AUTO_REPLY_MAX_TOKENS=500

# 请求超时时间（秒）
AI_AUTO_REPLY_TIMEOUT=10

# ================================
# 缓存配置
# ================================

# 回复缓存时间（秒，300=5分钟）
AI_AUTO_REPLY_CACHE_TTL=300

# ================================
# 记忆和上下文配置
# ================================

# 是否使用记忆功能（保持对话上下文）
AI_AUTO_REPLY_USE_MEMORY=true

# ================================
# 触发条件配置
# ================================

# 是否对所有消息都回复（谨慎开启，建议false）
AI_AUTO_REPLY_ALWAYS=false

# ================================
# 频率限制配置
# ================================

# 同一用户最小回复间隔（秒）
AI_AUTO_REPLY_MIN_INTERVAL=5

# ================================
# AI服务API配置
# ================================

# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# OpenAI API配置（如果使用OpenAI）
OPENAI_API_KEY=your_openai_api_key_here

# Claude API配置（如果使用Claude）
CLAUDE_API_KEY=your_claude_api_key_here

# ================================
# 高级配置
# ================================

# AI服务默认提供商
AI_DEFAULT_PROVIDER=deepseek

# AI缓存启用
AI_CACHE_ENABLED=true

# AI缓存TTL
AI_CACHE_TTL=3600

# AI记忆类型 (buffer/mysql)
AI_MEMORY_TYPE=mysql

# AI记忆环境
AI_MEMORY_ENVIRONMENT=auto

# ================================
# 日志配置
# ================================

# AI日志启用
AI_LOGGING_ENABLED=true

# AI日志级别 (debug/info/warning/error)
AI_LOGGING_LEVEL=info

# ================================
# 监控配置
# ================================

# AI监控启用
AI_MONITORING_ENABLED=true

# 指标导出间隔（秒）
AI_METRICS_EXPORT_INTERVAL=300
