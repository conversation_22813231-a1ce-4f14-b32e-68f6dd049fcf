-- 电子面单模板表
CREATE TABLE `ad_waybill_template` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '模板名称',
  `logistics_id` int(10) unsigned NOT NULL COMMENT '物流公司ID',
  `sender_id` bigint(20) unsigned NOT NULL COMMENT '发件人ID，关联ad_waybill_sender表id',
  `printer_id` bigint(20) unsigned NOT NULL COMMENT '打印配置ID，关联ad_waybill_print_config表id',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认：0否，1是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `createtime` int(10) unsigned NOT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_default` (`is_default`),
  KEY `idx_sender_id` (`sender_id`),
  KEY `idx_printer_id` (`printer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子面单模板表';

-- 发件人信息表
CREATE TABLE `ad_waybill_sender` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '发件人姓名',
  `phone` varchar(20) NOT NULL COMMENT '固定电话',
  `mobile` varchar(20) NOT NULL COMMENT '手机号码',
  `province` varchar(6) NOT NULL COMMENT '省份编码',
  `city` varchar(6) NOT NULL COMMENT '城市编码',
  `district` varchar(6) NOT NULL COMMENT '区县编码',
  `address` varchar(255) NOT NULL COMMENT '详细地址',
  `postcode` varchar(6) NOT NULL COMMENT '邮政编码',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认：0否，1是',
  `createtime` int(10) unsigned NOT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发件人信息表';

-- 打印配置表
CREATE TABLE `ad_waybill_print_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `default_printer` varchar(100) NOT NULL COMMENT '默认打印机',
  `print_copies` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '打印份数',
  `auto_print` tinyint(1) NOT NULL DEFAULT '0' COMMENT '自动打印：0否，1是',
  `print_time` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '打印时机',
  `print_size` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '打印尺寸',
  `custom_width` int(10) unsigned DEFAULT NULL COMMENT '自定义宽度(mm)',
  `custom_height` int(10) unsigned DEFAULT NULL COMMENT '自定义高度(mm)',
  `print_content` varchar(255) NOT NULL DEFAULT '' COMMENT '打印内容项',
  `print_preview` tinyint(1) NOT NULL DEFAULT '1' COMMENT '打印预览：0否，1是',
  `createtime` int(10) unsigned NOT NULL COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打印配置表';