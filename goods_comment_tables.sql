-- 商品评价表
CREATE TABLE `goods_comment` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `order_id` int(11) unsigned NOT NULL COMMENT '订单ID',
  `rating` tinyint(1) NOT NULL DEFAULT '5' COMMENT '评分：1-5星',
  `content` text NOT NULL COMMENT '评价内容',
  `images` text COMMENT '评价图片，多个图片用逗号分隔',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否匿名：0=否，1=是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `create_time` int(11) DEFAULT NULL COMMENT '评价时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品评价表';