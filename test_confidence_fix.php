<?php

require_once __DIR__ . '/vendor/autoload.php';

use app\ai\services\KnowledgeBaseService;

echo "🔧 测试置信度计算修复\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // 创建知识库服务
    $kb = new KnowledgeBaseService([
        'mode' => 'simple',
        'include_fallback_message' => false,
        'confidence_threshold' => 0.3
    ]);
    
    // 测试问题
    $testQuestions = [
        '怎么购买',
        '测试',
        '如何测试功能',
        '购买流程是什么',
        '不存在的问题xyz123'
    ];
    
    echo "📋 测试不同问题的置信度计算\n";
    echo "-" . str_repeat("-", 40) . "\n\n";
    
    foreach ($testQuestions as $question) {
        echo "🔍 问题: \"{$question}\"\n";
        
        $result = $kb->ask($question, [
            'session_id' => 'test_' . uniqid(),
            'use_memory' => false
        ]);
        
        if ($result['success']) {
            echo "✅ 成功匹配\n";
            echo "   置信度: {$result['confidence']}\n";
            echo "   来源数量: " . count($result['sources']) . "\n";
            echo "   回复长度: " . mb_strlen($result['content']) . "字\n";
            
            // 显示来源信息
            if (!empty($result['sources'])) {
                echo "   相关来源:\n";
                foreach (array_slice($result['sources'], 0, 3) as $source) {
                    $score = $source['relevance_score'] ?? 0;
                    echo "   - {$source['title']} (相关性: {$score})\n";
                }
            }
            
            // 判断置信度是否合理
            if ($result['confidence'] >= 0.5) {
                echo "   📊 置信度评级: 高 ⭐⭐⭐\n";
            } elseif ($result['confidence'] >= 0.3) {
                echo "   📊 置信度评级: 中 ⭐⭐\n";
            } else {
                echo "   📊 置信度评级: 低 ⭐\n";
            }
            
        } else {
            echo "❌ 匹配失败\n";
            echo "   错误: {$result['error']}\n";
        }
        
        echo "\n" . str_repeat("-", 40) . "\n\n";
    }
    
    // 测试置信度阈值的影响
    echo "🎯 测试不同置信度阈值的影响\n";
    echo "=" . str_repeat("=", 50) . "\n\n";
    
    $testQuestion = "怎么购买";
    $thresholds = [0.1, 0.3, 0.5, 0.7];
    
    foreach ($thresholds as $threshold) {
        echo "阈值: {$threshold}\n";
        
        $kb->setConfig(['confidence_threshold' => $threshold]);
        $result = $kb->ask($testQuestion, [
            'session_id' => 'threshold_test_' . uniqid(),
            'use_memory' => false
        ]);
        
        if ($result['success']) {
            $confidence = $result['confidence'];
            $passThreshold = $confidence >= $threshold ? '✅ 通过' : '❌ 未通过';
            echo "   置信度: {$confidence} - {$passThreshold}\n";
        } else {
            echo "   ❌ 查询失败\n";
        }
        echo "\n";
    }
    
    // 性能测试
    echo "⚡ 性能测试\n";
    echo "=" . str_repeat("=", 50) . "\n\n";
    
    $startTime = microtime(true);
    $iterations = 10;
    
    for ($i = 0; $i < $iterations; $i++) {
        $kb->ask("测试问题 {$i}", [
            'session_id' => 'perf_test_' . $i,
            'use_memory' => false
        ]);
    }
    
    $endTime = microtime(true);
    $totalTime = $endTime - $startTime;
    $avgTime = $totalTime / $iterations;
    
    echo "总时间: " . number_format($totalTime, 3) . "秒\n";
    echo "平均时间: " . number_format($avgTime, 3) . "秒/次\n";
    echo "QPS: " . number_format($iterations / $totalTime, 2) . "\n\n";
    
    // 总结
    echo "📊 修复总结\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo "✅ 改进的相关性计算算法\n";
    echo "   - 直接字符串匹配（权重最高）\n";
    echo "   - 标题匹配加分\n";
    echo "   - 关键词匹配\n";
    echo "   - 长度惩罚机制\n\n";
    
    echo "✅ 改进的置信度计算\n";
    echo "   - 最高分和平均分加权\n";
    echo "   - 数量加成机制\n";
    echo "   - 质量阈值判断\n";
    echo "   - 详细日志记录\n\n";
    
    echo "✅ 预期效果\n";
    echo "   - 置信度更稳定\n";
    echo "   - 匹配准确性提高\n";
    echo "   - 减少误判\n";
    echo "   - 更好的用户体验\n\n";
    
    echo "💡 建议配置\n";
    echo "   - 置信度阈值: 0.3 (平衡准确性和覆盖率)\n";
    echo "   - 简洁模式: 提供直接回答\n";
    echo "   - 关闭客服提示: 避免冗余信息\n\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误:\n";
    echo "错误信息: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "\n堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "🎉 测试完成!\n";
echo "现在置信度计算应该更加稳定和准确了。\n";
