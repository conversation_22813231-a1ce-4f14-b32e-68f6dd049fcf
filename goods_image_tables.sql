-- 商品图片表
CREATE TABLE `goods_image` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `goods_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '图片URL',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `is_thumb` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为缩略图：0=否，1=是',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品图片表';