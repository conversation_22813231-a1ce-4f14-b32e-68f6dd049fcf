<?php
/**
 * AI自动回复功能测试脚本
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\vchat\auto_reply\AiAutoReply;
use app\vchat\auto_reply\AutoReplyManager;

function testAiAutoReply()
{
    echo "=== AI自动回复功能测试 ===\n\n";

    try {
        // 1. 基础功能测试
        echo "1. 基础功能测试:\n";
        $aiReply = new AiAutoReply();
        
        // 获取服务状态
        $status = $aiReply->getStatus();
        echo "服务状态: " . json_encode($status, JSON_PRETTY_PRINT) . "\n\n";

        // 2. 触发条件测试
        echo "2. 触发条件测试:\n";
        $testMessages = [
            '@AI 你好',
            '智能助手：今天天气如何？',
            '机器人：请推荐一本书',
            '普通消息，不应该触发',
            'AI：请介绍一下功能',
            '@ai 小写也能触发吗？',
        ];

        foreach ($testMessages as $index => $content) {
            $message = [
                'content' => $content,
                'from_id' => 10000 + $index,
                'type' => 'text',
                'timestamp' => time(),
            ];

            $shouldReply = $aiReply->shouldReply($message);
            echo "消息: \"{$content}\" -> " . ($shouldReply ? '✓ 触发' : '✗ 不触发') . "\n";
        }
        echo "\n";

        // 3. 实际回复测试
        echo "3. 实际回复测试:\n";
        $testMessage = [
            'content' => '@AI 你好，请介绍一下你自己',
            'from_id' => 12345,
            'type' => 'text',
            'timestamp' => time(),
        ];

        if ($aiReply->shouldReply($testMessage)) {
            echo "用户: {$testMessage['content']}\n";
            $reply = $aiReply->getReply($testMessage);
            echo "AI回复: {$reply}\n";
        } else {
            echo "消息未触发AI回复\n";
        }
        echo "\n";

        // 4. 记忆功能测试
        echo "4. 记忆功能测试:\n";
        $userId = 12345;
        
        // 第一次对话
        $message1 = [
            'content' => '@AI 我的名字是张三',
            'from_id' => $userId,
            'type' => 'text',
            'timestamp' => time(),
        ];

        if ($aiReply->shouldReply($message1)) {
            $reply1 = $aiReply->getReply($message1);
            echo "用户: {$message1['content']}\n";
            echo "AI: {$reply1}\n\n";
        }

        // 第二次对话（测试记忆）
        $message2 = [
            'content' => '@AI 你还记得我的名字吗？',
            'from_id' => $userId,
            'type' => 'text',
            'timestamp' => time() + 10,
        ];

        if ($aiReply->shouldReply($message2)) {
            $reply2 = $aiReply->getReply($message2);
            echo "用户: {$message2['content']}\n";
            echo "AI: {$reply2}\n\n";
        }

        // 5. 集成测试
        echo "5. 自动回复管理器集成测试:\n";
        $manager = new AutoReplyManager();
        
        $integrationMessage = [
            'content' => '@AI 通过管理器测试',
            'from_id' => 99999,
            'type' => 'text',
            'timestamp' => time(),
        ];

        $managerReply = $manager->getAutoReply($integrationMessage);
        if ($managerReply) {
            echo "用户: {$integrationMessage['content']}\n";
            echo "管理器回复: {$managerReply}\n";
        } else {
            echo "管理器未返回回复\n";
        }
        echo "\n";

        // 6. 性能测试
        echo "6. 性能测试:\n";
        $performanceTestCount = 3;
        $totalTime = 0;

        for ($i = 0; $i < $performanceTestCount; $i++) {
            $perfMessage = [
                'content' => "@AI 性能测试消息 {$i}",
                'from_id' => 20000 + $i,
                'type' => 'text',
                'timestamp' => time(),
            ];

            $startTime = microtime(true);
            
            if ($aiReply->shouldReply($perfMessage)) {
                $perfReply = $aiReply->getReply($perfMessage);
                $endTime = microtime(true);
                $duration = ($endTime - $startTime) * 1000;
                $totalTime += $duration;
                
                echo "测试 " . ($i + 1) . ": {$duration:.2f}ms\n";
            }
        }

        $avgTime = $totalTime / $performanceTestCount;
        echo "平均响应时间: {$avgTime:.2f}ms\n\n";

        echo "=== 测试完成 ===\n";
        echo "AI自动回复功能测试结果:\n";
        echo "✓ 服务状态正常\n";
        echo "✓ 触发条件工作正常\n";
        echo "✓ AI回复功能正常\n";
        echo "✓ 记忆功能正常\n";
        echo "✓ 管理器集成正常\n";
        echo "✓ 性能表现良好\n";

    } catch (\Exception $e) {
        echo "测试过程中出现错误: " . $e->getMessage() . "\n";
        echo "错误追踪: " . $e->getTraceAsString() . "\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    testAiAutoReply();
} else {
    echo "请在命令行环境下运行此测试脚本\n";
    echo "使用方法: php test_ai_auto_reply.php\n";
}
