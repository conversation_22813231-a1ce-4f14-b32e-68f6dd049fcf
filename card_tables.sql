-- 卡密管理表
CREATE TABLE `card` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '卡密ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '卡名称',
  `card_key` varchar(50) NOT NULL DEFAULT '' COMMENT '卡密',
  `type` varchar(20) NOT NULL DEFAULT '' COMMENT '卡类型：recharge=充值卡，member=会员卡，gift=礼品卡',
  `value` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '面值',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=已禁用，1=未使用，2=已使用',
  `valid_start_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期开始时间',
  `valid_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期结束时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `use_time` int(11) DEFAULT NULL COMMENT '使用时间',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_card_key` (`card_key`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡密管理表';

-- 初始数据
INSERT INTO `card` (`name`, `card_key`, `type`, `value`, `status`, `valid_start_time`, `valid_end_time`, `create_time`, `use_time`, `remark`) VALUES
('新年充值卡', 'ABCD-EFGH-IJKL-MNOP', 'recharge', 100.00, 1, UNIX_TIMESTAMP('2024-01-01'), UNIX_TIMESTAMP('2024-12-31'), UNIX_TIMESTAMP('2024-01-01 10:00:00'), NULL, '新年促销活动专用充值卡');