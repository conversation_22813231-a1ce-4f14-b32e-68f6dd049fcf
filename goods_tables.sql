-- 商品表
CREATE TABLE `goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `category_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分类ID',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '商品标题',
  `description` text COMMENT '商品描述',
  `keywords` varchar(255) DEFAULT '' COMMENT '关键词',
  `thumb` varchar(255) DEFAULT '' COMMENT '商品缩略图',
  `images` text COMMENT '商品图片，JSON格式',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '商品原价',
  `cost_price` decimal(10,2) DEFAULT '0.00' COMMENT '成本价',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
  `sales` int(11) DEFAULT '0' COMMENT '销量',
  `weight` decimal(10,2) DEFAULT '0.00' COMMENT '重量(kg)',
  `volume` decimal(10,2) DEFAULT '0.00' COMMENT '体积(m³)',
  `specs` text COMMENT '规格参数，JSON格式',
  `params` text COMMENT '商品参数，JSON格式',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=下架，1=上架',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `views` int(11) DEFAULT '0' COMMENT '浏览量',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`enabled`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 商品分类表
CREATE TABLE `goods_category` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '分类名称',
  `icon` varchar(255) DEFAULT '' COMMENT '分类图标',
  `image` varchar(255) DEFAULT '' COMMENT '分类图片',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `level` tinyint(1) DEFAULT '1' COMMENT '层级',
  `is_show` tinyint(1) DEFAULT '1' COMMENT '是否显示：0=否，1=是',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_pid` (`pid`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- 商品库存日志表
CREATE TABLE `goods_stock_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `goods_title` varchar(255) NOT NULL DEFAULT '' COMMENT '商品标题',
  `change_num` int(11) NOT NULL DEFAULT '0' COMMENT '变更数量',
  `type` varchar(20) NOT NULL DEFAULT '' COMMENT '变更类型：increase=增加，decrease=减少，set_increase=设置增加，set_decrease=设置减少',
  `before_stock` int(11) NOT NULL DEFAULT '0' COMMENT '变更前库存',
  `after_stock` int(11) NOT NULL DEFAULT '0' COMMENT '变更后库存',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `operator_id` int(11) DEFAULT '0' COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT '' COMMENT '操作人名称',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品库存日志表';

-- 商品价格日志表
CREATE TABLE `goods_price_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `goods_title` varchar(255) NOT NULL DEFAULT '' COMMENT '商品标题',
  `old_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '原价格',
  `new_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '新价格',
  `old_original_price` decimal(10,2) DEFAULT '0.00' COMMENT '原原价',
  `new_original_price` decimal(10,2) DEFAULT '0.00' COMMENT '新原价',
  `change_amount` decimal(10,2) DEFAULT '0.00' COMMENT '变更金额',
  `change_percentage` decimal(10,2) DEFAULT '0.00' COMMENT '变更百分比',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `operator_id` int(11) DEFAULT '0' COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT '' COMMENT '操作人名称',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品价格日志表';