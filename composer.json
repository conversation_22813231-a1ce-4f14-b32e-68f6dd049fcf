{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.0.0", "topthink/framework": "^8.0", "topthink/think-orm": "^3.0", "topthink/think-filesystem": "^2.0", "firebase/php-jwt": "^6.8", "topthink/think-multi-app": "^1.0", "phpmailer/phpmailer": "^6.9", "ext-curl": "*", "yansongda/pay": "~3.7.0", "ext-zip": "*", "topthink/think-cache": "^3.0", "guzzlehttp/guzzle": "^7.9", "topthink/think-migration": "^3.0", "ext-posix": "*", "topthink/think-swoole": "^4.1", "ext-openssl": "*", "w7corp/easywechat": "^6.0"}, "require-dev": {"symfony/var-dumper": ">=4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app", "apps\\": "apps"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}